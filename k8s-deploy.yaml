apiVersion: apps/v1
kind: Deployment
metadata:
  name: price
#  namespace: ketanyun
spec:
  replicas: 1
  selector:
    matchLabels:
      app: price
  template:
    metadata:
      labels:
        app: price
    spec:
      containers:
        - name: price
          image: oci.ketanyun.cn/product/images/price:latest
          imagePullPolicy: Always
          envFrom:
            # from QApp auto generated oauth secret
            - secretRef:
                name: qapp-secret-price
            # from QService auto generated service-URLs
            - configMapRef:
                name: qservice-discovery
          env:
            - name: TZ
              value: "Asia/Shanghai"
          ports:
            - containerPort: 8080
      imagePullSecrets:
        - name: gitlab-registry-qtgl
        - name: gitlab-registry-product


