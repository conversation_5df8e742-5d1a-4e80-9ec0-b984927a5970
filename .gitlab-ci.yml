image: docker:latest
variables:
  GIT_SSL_NO_VERIFY: "1"
  GIT_DEPTH: "5"

stages:
  - build
  - deploy


build:master:
  stage: build
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - .m2/repository
      - ide/.gradle
      - ide/node_modules/
  before_script:
    - export COMMIT_TIME=latest
    - export JIB_TO_IMAGE=oci.ketanyun.cn/ceibs/price:${COMMIT_TIME}
  dependencies: []
  script:
    - "gradle npmBuild build jib -i -PJIB_FROM_IMAGE=oci.ketanyun.cn/open/openjdk:17-jdk -Djib.from.auth.username='' -Djib.from.auth.password='' -Djib.to.auth.username=${CI_REGISTRY_USER} -Djib.to.auth.password=${CI_REGISTRY_PASSWORD} -Djib.to.image=${JIB_TO_IMAGE}"
  only:
    - master
  artifacts:
    paths:
      - operator/build/libs/*.jar
  image: oci.ketanyun.cn/open/gradle:7.5.0-jdk17


############build###################
### build for devel
deploy:dev:
  image: oci.ketanyun.cn/open/kubectl:1.24.16
  stage: deploy
  dependencies: []
  before_script:
    - export COMMIT_TIME=latest
    - export JIB_TO_IMAGE=oci.ketanyun.cn/ceibs/price:${COMMIT_TIME}
  script:
    - kubectl -n ketanyun set image deploy/price price=${JIB_TO_IMAGE}
    - kubectl -n ketanyun rollout restart deploy price
  only:
    - master
  environment:
    name: cloud
    url: https://cloud.ketanyun.cn/price

