import { resolve } from "path";
import { defineConfig, loadEnv } from 'vite'
import { readFileSync } from 'fs'
import path from 'path'
import vue from '@vitejs/plugin-vue'

export default defineConfig(({ mode, command }) => {
    return {
        plugins: [vue()],
        // 动态替换主js文件引入js和css文件的路径地址（contextPath）
        // experimental: {
        //     renderBuiltUrl(filename, { hostType }) {
        //         if (hostType === 'js' || hostType === 'css') {
        //             if (!(filename.indexOf('/') == 0)) {
        //                 filename = '/' + filename
        //             }
        //             return { runtime: `window.__toCtxUrl(${JSON.stringify(filename)})` }
        //         } else {
        //             return { relative: true }
        //         }
        //     }
        // },
        resolve: {
            alias: {
                '@': resolve('src'), // 用@代替src目录，
            },
        },
        // assetsInclude: ['**/*.png'],

        server: {
            // https: {
            //     key: readFileSync(path.resolve('/Users/<USER>/localhost+2-key.pem')),
            //     cert: readFileSync(path.resolve('/Users/<USER>/localhost+2.pem')),
            // },
            // https: {
            //     key: readFileSync(path.resolve('/Users/<USER>/priceagent.ceibs.edu-key.pem')),
            //     cert: readFileSync(path.resolve('/Users/<USER>/priceagent.ceibs.edu.pem')),
            // },
            host: 'localhost',
            // host: '127.0.0.1',
            port: 8001,
            proxy: {
                '/sso': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/builtin': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/iam': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/iga': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/appcenter': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/contact': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/apps_tabler': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/hub-igatest': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/hub-ychen': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/mdm': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/maker': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus/graphql/hub': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/hub-price/graphql': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/bus': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    secure: false,
                    pathRewrite: {
                        '^/bus': "/bus"
                    }
                },
                '/copilot-api': {
                    target: 'https://cloud.ketanyun.cn',
                    changeOrigin: true,
                    secure: false,
                    pathRewrite: {
                        '^/copilot-api': "/copilot-api"
                    }
                },

            }
        },
        base: loadEnv(mode, process.cwd()).VITE_BASE,
        build: {
            assetsInlineLimit: 100 * 1024,
            rollupOptions: {
                output: {
                    // 打包后js,css和img资源分别分门别类在js/css/img文件夹中
                    entryFileNames: 'js/[name].js',
                    chunkFileNames: 'js/[name].js',
                    assetFileNames: '[ext]/[name].[ext]',
                }
            }
        }

    }
})