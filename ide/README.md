
## 目录结构

| 文件夹 | 说明 | 举例 |
|----|----|----|
|/src/assets/*|资源文件整合(突图片资源文件、scss、服务性js、发起接口请求文件server)|可以按需修改|
|/src/components/*|全局菜单及顶部操作栏及面包线|可以按需修改|
|/src/hooks/*|钩子文件，如语言切换辅助、store文件快捷使用|可以按需修改|
|/src/locale/*|多语言切换文件支持，目前支持中英|可以按需修改|
|/src/router/*|路由文件，静态路由问oidc登录相关|可以按需修改|
|/src/store/*|全局储存文件，辅助实现动态路由|可以按需修改|
|/src/utils/*|工具文件，主要提取全局可用js方法|可以按需修改|
|/src/views/view.vue|模板入口文件，此文件引入了侧边菜单以及顶部操作栏，决定基本布局|可以按需修改|
|/src/views/oidc/*|登录相关文件|----|
|/src/views/*除上两项外|你的开发文件|（主要修改！！！）需自己完善，开发文件需要和菜单路由保持一致！！！以note为例：如果你有一个/note/logs的菜单，它的展示内容为logs.vue。为了动态路由的实现，你的开发文件路径需要为：views/note(需自己新增)/logs.vue(需自己新增)|
|/src/App.vue|主入口vue|----|
|/src/main.js|主入口js|----|

## 使用说明

npm i
npm run dev

## 隐藏或显示菜单+顶部菜单+面包线(?hideoutter=true)

eg. /path?hideoutter=true