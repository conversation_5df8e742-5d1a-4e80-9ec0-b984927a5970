<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟人会议室</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        header {
            text-align: center;
            padding: 20px 0;
            margin-bottom: 30px;
        }

        h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        .panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 25px;
            transition: transform 0.3s ease;
        }

        .panel:hover {
            transform: translateY(-5px);
        }

        .panel-title {
            font-size: 1.5rem;
            color: #3498db;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }

        .options-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .option {
            background: #e3f2fd;
            color: #1976d2;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: #bbdefb;
            transform: scale(1.05);
        }

        .option.selected {
            background: #1976d2;
            color: white;
            border-color: #0d47a1;
        }

        .create-btn {
            background: linear-gradient(45deg, #3498db, #2c3e50);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            width: 100%;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .create-btn:hover {
            transform: scale(1.03);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .create-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .meeting-room {
            grid-column: span 2;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 25px;
            position: relative;
            overflow: hidden;
        }

        @media (max-width: 768px) {
            .meeting-room {
                grid-column: span 1;
            }
        }

        .room-title {
            font-size: 1.5rem;
            color: #27ae60;
            margin-bottom: 20px;
            text-align: center;
        }

        /* 会议室背景 */
        .meeting-room::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNlZWYxZjYiLz48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Q0ZThmZiIvPjxyZWN0IHg9IjIwMCIgeT0iMjAwIiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Q0ZThmZiIvPjxyZWN0IHg9IjQwMCIgeT0iNDAwIiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Q0ZThmZiIvPjwvc3ZnPg==');
            opacity: 0.2;
            z-index: 0;
        }

        .meeting-room-content {
            position: relative;
            z-index: 1;
        }

        /* 会议室桌子 */
        .table-container {
            position: relative;
            width: 100%;
            height: 500px;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI4MDAiIGhlaWdodD0iNjAwIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgZmlsbD0iI2NjYyIvPjxjaXJjbGUgY3g9IjQwMCIgY3k9IjMwMCIgcj0iMTUwIiBmaWxsPSIjOGI0NTFjIi8+PHJlY3QgeD0iMjUwIiB5PSIxNTAiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjQTUyQTJBIi8+PHJlY3QgeD0iNDUwIiB5PSIxNTAiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjQTUyQTJBIi8+PHJlY3QgeD0iMjUwIiB5PSIzNTAiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjQTUyQTJBIi8+PHJlY3QgeD0iNDUwIiB5PSIzNTAiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjQTUyQTJBIi8+PHJlY3QgeD0iMTUwIiB5PSIyNTAiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjQTUyQTJBIi8+PHJlY3QgeD0iNTUwIiB5PSIyNTAiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjQTUyQTJBIi8+PC9zdmc+');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            margin: 0 auto;
        }


        .seats-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .seat {
            background: rgba(165, 42, 42, 0.8);
            border: 3px solid #5D2906;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: absolute;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            width: 80px;
            height: 80px;
            pointer-events: auto;
        }

        .seat:nth-child(1) { top: 150px; left: 250px; }
        .seat:nth-child(2) { top: 150px; left: 450px; }
        .seat:nth-child(3) { top: 350px; left: 250px; }
        .seat:nth-child(4) { top: 350px; left: 450px; }
        .seat:nth-child(5) { top: 250px; left: 150px; }
        .seat:nth-child(6) { top: 250px; left: 550px; }

        .seat.occupied {
            background: rgba(46, 139, 87, 0.8);
            border: 3px solid #145214;
        }

        .avatar-placeholder {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #3498db, #8e44ad);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-bottom: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
        }

        .avatar-name {
            font-size: 0.8rem;
            font-weight: bold;
            color: white;
            text-align: center;
            background: rgba(0, 0, 0, 0.5);
            padding: 2px 8px;
            border-radius: 10px;
            margin-top: 5px;
        }

        .avatar-tags {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 3px;
            margin-top: 5px;
            max-width: 90%;
        }

        .tag-badge {
            background: rgba(255, 255, 255, 0.8);
            color: #2c3e50;
            font-size: 0.6rem;
            padding: 2px 6px;
            border-radius: 10px;
        }

        .creating-animation {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .falling-animation {
            animation: fall 1s ease-out;
        }

        @keyframes fall {
            0% {
                transform: translateY(-100px) scale(0.5);
                opacity: 0;
            }
            70% {
                transform: translateY(10px) scale(1.1);
            }
            100% {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transform: translateX(200%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #4caf50;
        }

        .notification.error {
            background: #f44336;
        }
    </style>
</head>
<body>
<div class="container">
    <header>
        <h1>虚拟人会议室</h1>
        <p class="subtitle">创建虚拟人并安排会议</p>
    </header>

    <div class="main-content">
        <div class="panel">
            <h2 class="panel-title">岗位池</h2>
            <div class="options-container" id="positions">
                <div class="option" data-value="产品经理">产品经理</div>
                <div class="option" data-value="UI设计师">UI设计师</div>
                <div class="option" data-value="前端工程师">前端工程师</div>
                <div class="option" data-value="后端工程师">后端工程师</div>
                <div class="option" data-value="测试工程师">测试工程师</div>
                <div class="option" data-value="运维工程师">运维工程师</div>
                <div class="option" data-value="数据分析师">数据分析师</div>
                <div class="option" data-value="项目经理">项目经理</div>
            </div>

            <h2 class="panel-title">标签池</h2>
            <div class="options-container" id="tags">
                <div class="option" data-value="经验丰富">经验丰富</div>
                <div class="option" data-value="创新思维">创新思维</div>
                <div class="option" data-value="沟通能力强">沟通能力强</div>
                <div class="option" data-value="技术专家">技术专家</div>
                <div class="option" data-value="团队协作">团队协作</div>
                <div class="option" data-value="问题解决">问题解决</div>
                <div class="option" data-value="项目管理">项目管理</div>
                <div class="option" data-value="用户体验">用户体验</div>
            </div>

            <button class="create-btn" id="createBtn" disabled>创建虚拟人</button>
        </div>

        <div class="panel">
            <h2 class="panel-title">虚拟人预览</h2>
            <div id="preview" style="text-align: center; padding: 30px; min-height: 200px;">
                <p style="color: #7f8c8d;">请选择岗位和标签来创建虚拟人</p>
            </div>
        </div>

        <div class="meeting-room">
            <h2 class="room-title">会议室 (6个座位)</h2>
            <div class="meeting-room-content">
                <div class="table-container">
                    <div class="seats-container" id="seats">
                        <!-- 座位将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="notification" id="notification"></div>

<script>
    // 数据存储
    const state = {
        selectedPosition: null,
        selectedTags: [],
        avatars: []
    };

    // DOM元素
    const elements = {
        positions: document.getElementById('positions'),
        tags: document.getElementById('tags'),
        createBtn: document.getElementById('createBtn'),
        preview: document.getElementById('preview'),
        seats: document.getElementById('seats'),
        notification: document.getElementById('notification')
    };

    // 初始化座位
    function initSeats() {
        elements.seats.innerHTML = '';
        for (let i = 0; i < 6; i++) {
            const seat = document.createElement('div');
            seat.className = 'seat';
            seat.dataset.index = i;
            seat.innerHTML = `
          <div class="avatar-placeholder">${i+1}</div>
          <div class="avatar-name">空座位</div>
        `;
            elements.seats.appendChild(seat);
        }
    }

    // 选择岗位
    elements.positions.addEventListener('click', (e) => {
        if (e.target.classList.contains('option')) {
            // 清除之前的选择
            document.querySelectorAll('#positions .option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // 选择当前岗位
            e.target.classList.add('selected');
            state.selectedPosition = e.target.dataset.value;

            updatePreview();
            checkCreateButton();
        }
    });

    // 选择标签
    elements.tags.addEventListener('click', (e) => {
        if (e.target.classList.contains('option')) {
            e.target.classList.toggle('selected');

            const tag = e.target.dataset.value;
            const index = state.selectedTags.indexOf(tag);

            if (index > -1) {
                // 移除标签
                state.selectedTags.splice(index, 1);
            } else {
                // 添加标签
                state.selectedTags.push(tag);
            }

            updatePreview();
            checkCreateButton();
        }
    });

    // 检查创建按钮状态
    function checkCreateButton() {
        elements.createBtn.disabled = !(state.selectedPosition && state.selectedTags.length > 0);
    }

    // 更新预览
    function updatePreview() {
        if (!state.selectedPosition && state.selectedTags.length === 0) {
            elements.preview.innerHTML = '<p style="color: #7f8c8d;">请选择岗位和标签来创建虚拟人</p>';
            return;
        }

        let html = '<div style="padding: 20px;">';

        if (state.selectedPosition) {
            html += `<h3 style="color: #3498db; margin-bottom: 15px;">岗位: ${state.selectedPosition}</h3>`;
        }

        if (state.selectedTags.length > 0) {
            html += '<div style="margin-top: 15px;"><strong>标签:</strong></div>';
            html += '<div style="display: flex; flex-wrap: wrap; gap: 5px; justify-content: center; margin-top: 10px;">';
            state.selectedTags.forEach(tag => {
                html += `<span style="background: #e3f2fd; color: #1976d2; padding: 5px 10px; border-radius: 15px; font-size: 0.9rem;">${tag}</span>`;
            });
            html += '</div>';
        }

        html += '</div>';
        elements.preview.innerHTML = html;
    }

    // 创建虚拟人
    elements.createBtn.addEventListener('click', () => {
        if (!state.selectedPosition || state.selectedTags.length === 0) return;

        // 创建虚拟人数据
        const avatar = {
            id: Date.now(),
            position: state.selectedPosition,
            tags: [...state.selectedTags],
            name: `${state.selectedPosition}${state.selectedTags[0] || ''}`
        };

        // 显示创建动画
        showNotification('正在创建虚拟人...', 'success');
        elements.createBtn.disabled = true;
        elements.createBtn.textContent = '创建中...';

        // 模拟创建过程
        setTimeout(() => {
            state.avatars.push(avatar);
            placeAvatarInMeetingRoom(avatar);

            // 重置选择
            state.selectedPosition = null;
            state.selectedTags = [];
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });
            elements.preview.innerHTML = '<p style="color: #7f8c8d;">请选择岗位和标签来创建虚拟人</p>';
            elements.createBtn.textContent = '创建虚拟人';
            checkCreateButton();
        }, 2000);
    });

    // 将虚拟人放置到会议室
    function placeAvatarInMeetingRoom(avatar) {
        // 查找空座位
        const seats = document.querySelectorAll('.seat');
        let emptySeat = null;

        for (let seat of seats) {
            if (!seat.classList.contains('occupied')) {
                emptySeat = seat;
                break;
            }
        }

        if (!emptySeat) {
            showNotification('会议室已满，请移除一些虚拟人', 'error');
            return;
        }

        // 添加创建动画类
        emptySeat.classList.add('creating-animation');

        // 模拟创建动画
        setTimeout(() => {
            emptySeat.classList.remove('creating-animation');
            emptySeat.classList.add('occupied', 'falling-animation');

            // 更新座位内容
            emptySeat.innerHTML = `
          <div class="avatar-placeholder">${avatar.position.charAt(0)}</div>
          <div class="avatar-name">${avatar.position}</div>
          <div class="avatar-tags">
            ${avatar.tags.slice(0, 2).map(tag =>
                `<span class="tag-badge">${tag}</span>`
            ).join('')}
            ${avatar.tags.length > 2 ? `<span class="tag-badge">+${avatar.tags.length - 2}</span>` : ''}
          </div>
        `;

            showNotification(`虚拟人 ${avatar.position} 已加入会议室`, 'success');

            // 移除动画类
            setTimeout(() => {
                emptySeat.classList.remove('falling-animation');
            }, 1000);
        }, 1000);
    }

    // 显示通知
    function showNotification(message, type) {
        elements.notification.textContent = message;
        elements.notification.className = `notification ${type} show`;

        setTimeout(() => {
            elements.notification.classList.remove('show');
        }, 3000);
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
        initSeats();
    });
</script>
</body>
</html>
