import gql from "graphql-tag";
import apolloclient from "@/assets/js/apolloclientcreater.js";
import store from '@/store/index'
import { v4 as uuidv4 } from 'uuid';
import { ElMessage } from 'element-plus'
import moment from 'moment'

// hash 获取
export const getHashParams = (hashkey, hash) => {
    // 获取当前URL的hash部分
    // const hash = window.location.hash;
    hash = hash ? hash : window.location.hash

    // 去掉开头的 '#' 符号
    const hashWithoutHash = hash.substring(1);

    // 将hash部分按 '&' 分割成多个键值对
    const params = hashWithoutHash.split('&');

    // 创建一个对象来存储参数
    const paramsObj = {};

    // 遍历每个键值对
    params.forEach(param => {
        // 按 '=' 分割键和值
        const [key, value] = param.split('=');

        // 解码并存储到对象中
        if (key) {
            paramsObj[decodeURIComponent(key)] = decodeURIComponent(value || '');
        }
    });
    return paramsObj[hashkey];
};
export const updateUrlHash = (url, hashkey, hashvalue) => {
    if (url.indexOf('price/') >= 0) {
        const [baseUrl, hash] = url.split('#');
        let newHash = '';
        if (hash) {
            const params = new URLSearchParams(hash);
            params.set(hashkey, hashvalue);
            newHash = params.toString();
        } else {
            newHash = `${hashkey}=${hashvalue}`;
        }
        return `${baseUrl}#${newHash}`;
    }
    return url;
};
export const debounceCustom = (fn, delay = 500) => {
    let timer = null;
    return function(...args) {
        if (timer != null) {
            clearTimeout(timer);
            timer = null;
        }
        timer = setTimeout(() => {
            fn.call(this, ...args);
        }, delay);
    };
};
// 系统排序
export const systemsort = (list, akey, bkey = akey) => {
    list = list.sort((a, b) => {
        let aname = a[akey] ? a[akey] : a[bkey];
        let bname = b[akey] ? b[akey] : b[bkey];
        return (aname < bname ? -1 : 1)
    })
    return list
};
export const versionLabel = (item, index) => {
    return `${index?'版本：':'【新】版本：'}${moment(item.createdAt).format('YYYY-MM-DD HH:mm:ss')}`
};
// img 转base64 
export const toSVGDataUrl = (imgraw) => {
    const base64 = btoa(imgraw);
    return `data:image/svg+xml;base64,${base64}`;
};
export const toImageDataUrl = async(imgUrl) => {
    const response = await fetch(imgUrl);
    const blob = await response.blob();
    return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.readAsDataURL(blob);
    })
};

export const takeoffEdges = (data, key) => {
    if (data[key].edges && data[key].edges.length) {
        return data[key].edges.map(item => item.node)
    } else {
        return [];
    }
}