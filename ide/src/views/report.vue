<template>
  <div class="full-report">
    <div class="report-item"
    v-for="(item,key) in viewsreportdata"
    :key="key">
    <template v-if="key==='竞争分析'">
      <matchviewreport :reportdata="item"></matchviewreport>
    </template>
    <template v-if="key==='头脑风暴'">
      <differentiationreport :reportdata="item"></differentiationreport>
    </template>
    <template v-if="key==='市场分析'">
      <atypicareport :reportdata="item"></atypicareport>
    </template>
    <template v-if="key==='市场分析提取'">
      <marketsegmentsreport :reportdata="item"></marketsegmentsreport>
    </template>
    <template v-if="key==='成本定价'">
      <costpricingreport :reportdata="item"></costpricingreport>
    </template>
    </div>
  </div>
</template>
<script setup>
import { computed, ref,inject,onMounted, watch,} from 'vue';
import {getHashParams,versionLabel} from '@/utils'
import {getPriceSessionInfo,getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import { ElMessage, ElMessageBox } from 'element-plus'

import matchviewreport from './report/matchview_report.vue'
import differentiationreport from './report/differentiation_report.vue'
import atypicareport from './report/atypica_report.vue'
import marketsegmentsreport from './report/marketsegments_report.vue'
import costpricingreport from './report/costpricing_report.vue'

let viewsreportdata =ref({
  "竞争分析":{},
  "头脑风暴":{},
  "市场分析":{},
  "市场分析提取":{},
  "成本定价":{},
})

const getSessionID=()=>{
  const parenturl = parent.document.location.href;
  const searchParams = new URL(parenturl).searchParams
  return searchParams.get('sessionId')
};
const filterByLatestCreatedAt=(items)=> {
  const latestItemsMap = new Map();
  items.forEach(item => {
    const existingItem = latestItemsMap.get(item.viewName);
    if (!existingItem || item.createdAt > existingItem.createdAt) {
      latestItemsMap.set(item.viewName, item);
    }
  });
  return Array.from(latestItemsMap.values());
};
// 
const getViewList=()=>{
  let sessionId=getSessionID()
  if (process.env.NODE_ENV !== "production") {
    sessionId="01K0C6PAJV28DPZFXNMZCYH7XV"
  } 
  if(!sessionId){
    console.log('======sessionID获取失败')
    ElMessage.warning("获取内容失败");
  }
  getPriceSessionInfo({
      sessionId,
    }).then((res) => {
        if (res.data?.priceSessionInfo) {
          let views = res.data.priceSessionInfo?.views ? JSON.parse(JSON.stringify(res.data.priceSessionInfo.views)) : [];
          let reportviews = views.filter((item)=>['竞争分析','头脑风暴','市场分析','市场分析提取','成本定价'].includes(item.viewName));
          if(reportviews?.length){
            reportviews=filterByLatestCreatedAt(reportviews);
            getViewsDetail(reportviews)
          }
        }
    }).catch((err) => {
      
        ElMessage.warning("获取内容失败" + err);
    });
};
// 
const getViewsDetail=(reportviews)=>{
  Promise.all([...reportviews.map((item)=>{
    return getPriceViewInfo({viewId:item.id})
  })]).then((res) => {
    if(res.length){
      res.forEach((item)=>{
        if(item?.data?.priceSessionVIew?.data){
          let reportdata =item.data.priceSessionVIew.data;
          let matched =reportviews.find((fitem)=>fitem.id ===item.data.priceSessionVIew.id);
          let chartdatas=[]
          try{
              chartdatas=JSON.parse(reportdata)
          }catch (err){
              ElMessage.warning("数据解析出错")
          }
          if(matched){
            if(matched.viewName==='竞争分析'){
              if(chartdatas&&chartdatas[0]){
                viewsreportdata.value[matched.viewName]=chartdatas[0];
              }else{
                ElMessage.warning("竞争分析——数据解析失败")
              }
            }else if(matched.viewName==='市场分析提取'){
              if(chartdatas&&chartdatas.length){
                viewsreportdata.value[matched.viewName]=chartdatas[0];
              }else{
                  ElMessage.warning("市场分析提取——数据解析失败")
              }
            }else if(matched.viewName==='成本定价'){
              if(chartdatas&&chartdatas.length){
                viewsreportdata.value[matched.viewName]=chartdatas[0];
              }else{
                
                ElMessage.warning("成本定价——数据解析失败")
              }
            }else if(matched.viewName==='头脑风暴'){
              if(chartdatas){
                viewsreportdata.value[matched.viewName]=chartdatas;
              }else{
                
                ElMessage.warning("头脑风暴——数据解析失败")
              }
            }else if(matched.viewName==='市场分析'){
              if(chartdatas){
                viewsreportdata.value[matched.viewName]=chartdatas;
              }else{
                
                ElMessage.warning("市场分析——数据解析失败")
              }
            }
          }
          console.log(item.data)
        }
      })
    }
  })
};
getViewList()
</script>