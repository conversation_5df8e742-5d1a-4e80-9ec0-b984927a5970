<template>
<div class="market-segments-wrap">
    <div class="excel-area">
        <div class="row-header" ref="headerRef">
            <div class="header-item"
            v-for="(item,index) in rowheaders"
            :class="[item.value,item.value.startsWith('price_')?'header-item-price':'']"
            :key="item+index"
            >
            <div class="header-inner" v-if="item.value.startsWith('price_')">
                <div class="header-inner-label">{{item.label?item.label:item.value}}
                    </div>
                <div class="header-inner-types">
                    <div>支付概率</div>
                    <div>预期需求</div>
                </div>
            </div>
            <div class="header-inner-others" v-else>{{item.label?item.label:item.value}}</div>
            </div>
            
        </div>
        <div class="row-content" ref="contentRef">
                <div class="content-single-row"
                v-for="(item,index) in rowdatas"
                :key="'item'+index">
                <template v-for="(iitem,iikey) in item" :key="iikey">
                    <div v-if="rowheaders.find((ritem)=>ritem.value===iikey)"
                        class="row-column column-cell "
                        :class="['row-column-'+iikey,
                            iikey.startsWith('price_')?'row-column-price':''
                        ]">
                        <div class="row-column-consumer_profile-inner" v-if="iikey=='consumer_profile'"  >
                            <div class="column-label">
                                <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey"  @click="editCell(index, iikey)">{{iitem}}</span>
                                <el-input 
                                    v-else
                                    v-model="rowdatas[index][iikey]"
                                    @blur="saveEdit"
                                    @keyup.enter="saveEdit"
                                    ref="inputRef"
                                    class="edit-input"
                                />
                                <!-- {{item.consumer_profile}} -->
                            </div>
                            <div class="column-description" >
                                <span v-if="!editingCell || editingCell.row !== index || (editingCell.col !== 'description')"
                                 @click="editCell(index, 'description')">{{rowdatas[index].description}}</span>
                                <el-input 
                                    v-else
                                    v-model="rowdatas[index].description"
                                    @blur="saveEdit"
                                    @keyup.enter="saveEdit"
                                    ref="inputRef"
                                    class="edit-input"
                                />
                                <!-- {{item.description}} -->
                            </div>
                        </div>
                        <div class="edit-able" v-else-if="iikey=='market_size'" @click="editCell(index, iikey)">
                            <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey">{{iitem}}</span>
                            <el-input 
                                v-else
                                v-model="rowdatas[index][iikey]"
                                @blur="saveEdit"
                                @keyup.enter="saveEdit"
                                ref="inputRef"
                                class="edit-input"
                            />
                        </div>
                        <div class="edit-able" v-else-if="iikey=='market_percent'" @click="editCell(index, iikey)">
                            <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey">{{iitem}}%</span>
                            <el-input 
                                v-else
                                v-model="rowdatas[index][iikey]"
                                @blur="saveEdit"
                                @keyup.enter="saveEdit"
                                ref="inputRef"
                                class="edit-input"
                            />
                        </div>
                        <div class="edit-able" v-else-if="iikey.startsWith('price_')">
                            <div class="edit-able-price">
                                <div class="column-probability-pair">
                                <!-- 支付概率： -->
                                <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey|| editingCell.key !== 'price_probability_pairs'"  @click="editCell(index, iikey,'price_probability_pairs')">
                                    {{iitem[0]&&iitem[0]!='-'?iitem[0]+"%":iitem[0]}}
                                </span>
                                <el-input 
                                    v-else
                                    v-model="rowdatas[index][iikey][0]"
                                    @blur="saveEdit"
                                    @keyup.enter="saveEdit"
                                    ref="inputRef"
                                    class="edit-input"
                                />
                                </div>
                                <div class="column-demand-pair" style="cursor:not-allowed">
                                    <!-- 预期需求： -->
                                    <span  v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey|| editingCell.key !== 'price_demand_pairs'" >
                                        {{iitem[1]}}
                                    </span>
                                    <el-input 
                                        v-else
                                        v-model="rowdatas[index][iikey][1]"
                                        @blur="saveEdit"
                                        @keyup.enter="saveEdit"
                                        ref="inputRef"
                                        class="edit-input"
                                    />
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </template>
                </div>
        </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch,nextTick } from 'vue';
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel,debounceCustom} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
// import { startsWith } from 'lodash';


// 编辑相关状态
const editingCell = ref(null);
const inputRef = ref(null);
let baseData=ref({
    "flag": true,
    "extracted_data": {
        "market_segments": [
            {
                "consumer_profile": "艺术鉴赏家",
                "description": "艺术品拍卖行总监，追求'灵魂的艺术'",
                "market_size": 12000,
                "price_demand_pairs": [
                    [
                        2000,
                        85
                    ],
                    [
                        3000,
                        78
                    ],
                    [
                        4000,
                        65
                    ],
                    [
                        5000,
                        45
                    ]
                ],
                "price_probability_pairs": [
                    [
                        2000,
                        0.85
                    ],
                    [
                        3000,
                        0.78
                    ],
                    [
                        4000,
                        0.65
                    ],
                    [
                        5000,
                        0.45
                    ]
                ]
            },
            {
                "consumer_profile": "高端奢游玩家",
                "description": "跨国公司管理人员，追求深度文化沉浸",
                "market_size": 8000,
                "price_demand_pairs": [
                    [
                        2000,
                        85
                    ],
                    [
                        3000,
                        78
                    ],
                    [
                        4000,
                        65
                    ],
                    [
                        5000,
                        45
                    ]
                ],
                "price_probability_pairs": [
                    [
                        2000,
                        0.92
                    ],
                    [
                        3000,
                        0.88
                    ],
                    [
                        4000,
                        0.82
                    ],
                    [
                        5000,
                        0.75
                    ]
                ]
            },
        ]
    }
})
let rowheaders=ref([]);
let rowdatas=ref([]);
let rowcolumns=ref([]);
let dataid=ref('');

const toGetViewData=(viewId)=>{
    dataid.value=viewId
  getPriceViewInfo({viewId}).then((res)=>{
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            if(chartdatas&&chartdatas.length){
              baseData.value=chartdatas[0];
              countInitInfo()
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    }).catch((err)=>{
        ElMessage.warning("获取内容失败"+err)
    });
};
defineExpose({
    toGetViewData
})
const  processMarketSegments=(market_segments)=> {
    const headers = [
        { label: '消费者画像', value: 'consumer_profile' },
        { label: '市场规模', value: 'market_size' },
      { label: '市场份额', value: 'market_percent' }
    ];

    // 从第一个数据项中提取价格点作为表头的其余部分
    if (market_segments.length > 0 && market_segments[0].price_probability_pairs.length > 0) {
        market_segments[0].price_probability_pairs.forEach(pair => {
            headers.push({
                label: `${pair[0]?.toLocaleString()?pair[0].toLocaleString():'-'}元`,
                value: `price_${pair[0]}`
            });
        });
    }

    // 构建内容列表
    const content = market_segments.map((item) => {
        const mainRow = {};
        headers.forEach(header => {
            switch (header.value) {
                case 'consumer_profile':
                    mainRow[header.value] = item.consumer_profile;
                    mainRow.description = item.description;
                    break;
                case 'market_size':
                    mainRow[header.value] = item?.market_size?.toLocaleString()?item.market_size.toLocaleString():'-';
                    break;
                case 'market_percent':
                    mainRow[header.value] = item.market_percent?.toLocaleString()?item.market_percent.toLocaleString():'-';
                    break;
                default:
                    if (header.value.startsWith('price_')) {
                        const price = parseInt(header.value.split('_')[1]);
                        const probPair = item.price_probability_pairs.find(p => p[0] === price);
                        let probability=probPair&&probPair[1]?`${Math.round(probPair[1] * 100)}`:'-';
                        const demandPair = item.price_demand_pairs?.find(p => p[0] === price);
                        let demand=demandPair&&demandPair[1]?demandPair[1]:'-';
                        mainRow[header.value] = [probability,demand]
                    } else {
                        mainRow[header.value] = ['-','-'];
                    }
            }
        });

        return [mainRow];
    }).flat();

    return {
        headers,
        content
    };
};
const reverseProcessMarketSegments=(processedData)=>{
   // 提取所有以price_开头的键
        const priceKeys = Object.keys(processedData).filter(key => key.startsWith('price_'));
        
        // 将价格概率对和价格需求对分开处理
        const priceProbabilityPairs = [];
        const priceDemandPairs = [];
        let market_size=parseInt(processedData.market_size.replace(/,/g, ''));
        let market_percent=parseFloat(processedData.market_percent.replace('%', ''));

        priceKeys.forEach(key => {
            const price = parseInt(key.split('_')[1]);
            const [probabilityStr, demandStr] = processedData[key];
            
            // 处理概率
            if (probabilityStr && probabilityStr !== '-') {
                const probability = parseFloat(probabilityStr.replace('%', '')) / 100;
                const market_percent_p = market_percent / 100;

                priceProbabilityPairs.push([price, probability]);
                if(probability){
                    const demand =Math.round(market_size *market_percent_p* probability);
                    priceDemandPairs.push([price, demand]);
                }
            }
        });
        
        // 按价格排序
        priceProbabilityPairs.sort((a, b) => a[0] - b[0]);
        priceDemandPairs.sort((a, b) => a[0] - b[0]);
        
        return {
            consumer_profile: processedData.consumer_profile,
            market_size:market_size,
            market_percent:market_percent,

            price_probability_pairs: priceProbabilityPairs,
            price_demand_pairs: priceDemandPairs.length > 0 ? priceDemandPairs : [],
            description: processedData.description
        };
};
const countInitInfo=()=>{
  let {extracted_data}=JSON.parse(JSON.stringify(baseData.value));
  let {market_segments}=extracted_data;
  
    let {headers,content}=processMarketSegments(market_segments);
   
    rowheaders.value=headers
    rowdatas.value=content
    
}
const editCell = (rowIndex, colIndex,key) => {
    // 排除序号列和操作列
    if(colIndex < 0 || colIndex >= rowheaders.value.length) return;
    
    editingCell.value = {
        row: rowIndex,
        col: colIndex,
        key:key
    };
    
    nextTick(() => {
        if(inputRef.value && inputRef.value[0]) {
            inputRef.value[0].focus();
        }
    });
};
const saveEdit= debounceCustom(()=>{
    editingCell.value = null;
    // 这里可以添加数据验证逻辑
    updateBaseData('inputchange');
})
// 更新baseData
const updateBaseData = (inputchange) => {
    const market_segments = [];
    rowdatas.value.forEach((item)=>{
        item.market_size=item.market_size?item.market_size:'0';
        let market_size=parseInt(item.market_size.replace(/,/g, ''))
        for (let key in item){
            if(key.startsWith('price_')){
                if(item?.[key]?.[0]&&item[key][0]!='-'){
                    const probability = parseFloat(item[key][0].replace('%', '')) / 100;
                    if(probability){
                        item[key][1]=Math.round(market_size * probability)
                    }
                }
            }
        }
        let newitem =reverseProcessMarketSegments(item)
        market_segments.push(newitem)
    })
    baseData.value = {
        flag:true,
        extracted_data:{
            market_segments
        }
    };
    console.log("我要保存了",baseData.value)
    if(inputchange=='inputchange'){
        toSaveChange()
    }
};
let $emit = defineEmits();
const toSaveChange=debounceCustom(()=>{
    updatePriceSessionView({
        entity:{
            id:dataid.value,
            data:JSON.stringify([baseData.value])
        }
    }).then((res)=>{
        if(res?.data?.updatePriceSessionView){
            // ElMessage.success('修改成功')
            $emit('tableChanged')
        }
    })
})
// 增加价格点
// space 0 向前添加 1为向后添加
const addPricePoint=(target,index,space)=>{
    ElMessageBox.prompt('请输入一个价格点', '新增价格点', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPattern:/^\d*$/,//只能输入数字
        inputErrorMessage: '请输入有效价格',
        inputValidator: (value) => {
            if (!value) return true; // 允许空输入
            const newpricevalue = `price_${value}`;
            const isExisting = rowheaders.value?.some(item => item.value === newpricevalue);
            return isExisting ? '该价格点已存在，请更换' : true;
        },
    }).then(({ value }) => {
        rowheaders.value.splice(index+space, 0, {
            value:`price_${value}`,
            label:`${value?.toLocaleString()?value.toLocaleString():'-'}元`
        });
        rowdatas.value=rowdatas.value.map((item)=>{
            return {...insertKeyAfterTarget(target,`price_${value}`,item,space)}
        })
        updateBaseData('inputchange');
    })
};
const preDeletePricePoint=(target,index)=>{
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
      deletePricePoint(target,index)
    })
};
const deletePricePoint=(target,index)=>{
    rowheaders.value.splice(index, 1);
    rowdatas.value.forEach((item)=>{
        delete item[target]
    })
    updateBaseData('inputchange')
}
// 
const insertKeyAfterTarget=(target,newkey,obj,space)=>{
    const entries = Object.entries(obj);
    const index = entries.findIndex(([key]) => key === target);

    entries.splice(index + space, 0, [newkey, ["-", "-"]]);
    const newData = Object.fromEntries(entries);

    return newData;
}
// 增加行
const preAddData=(item,index)=>{
    let newRow = {}
    for(let key in item){
        if(key.startsWith('price_')){
            newRow[key]=['-','-']
        }else if(key==='consumer_profile'){
            newRow[key]="新增消费者画像"
        }else if(key==='description'){
            newRow[key]="新增消费者说明"
        }else{
            newRow[key]=""
        }
    }
    rowdatas.value.splice(index+1, 0, newRow);
    updateBaseData();
}
// 
const preDelData=(index)=>{
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
        rowdatas.value.splice(index,1);
        updateBaseData('inputchange');
    }).catch(() => {
      // 取消编辑
    })
    
};
</script>

