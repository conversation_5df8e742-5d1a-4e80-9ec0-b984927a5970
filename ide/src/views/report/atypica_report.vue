

<template>
    <div class="atypica-wrap" v-loading="loading">
        <div class="version-change">
          <h2 class="report-title">市场分析报告</h2>
          <div>
          </div>
          <el-select v-model="currentversion" @change="changeTabView"  v-if="false&&viewitems.length>1">
              <el-option
              v-for="(item,index) in viewitems"
              :key="item.id"
              :label="versionLabel(item,index)"
              :value="item.id">
              </el-option>
          </el-select>
        </div>
        <div v-if="false&&reportshtml" v-html="reportshtml"></div>
        <iframe 
          v-show="reporturl"
          class="iframe-atypica-inner"
          src="about:blank" 
          id="atypica-iframe"
          width="100%" 
          height="100%"
          ref="iframeRef"
          frameborder="0"></iframe>
        <el-empty v-if="!reporturl" description="暂无内容" />
     </div>
</template>

<script setup ts>
import { ref, watch, computed, render, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'

import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment';
const router = useRouter();

const loading = ref(false);
let currentUrl=ref('');
let reportshtml=ref('');
let reporturl=ref('');
let iframeRef=ref(null);
const props = defineProps({
  reportdata: Object
})
const currentpath = router.currentRoute._value.path;
// 

// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
// 
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
currentversion.value=getHashParams('viewid')
let dataid=ref('')
watch(viewid.value,()=>{
  if(viewid.value){
    currentversion.value=viewid.value
  }
},{immediate:true})
let hasviewid = getHashParams('viewid')
let baseData=ref({
    started:false,
    "currentUrl": "https://atypica.musedam.cc/auth/impersonation-login?token=qb8czZGqyiALFPQY3KEbQITE1rUSJUIrMZFikqJFjnnIOIHZNQO2MozrLp3Sl_KWeZIWA-q1Bsp-k8ryjvsGMlJI0b7kpIeR37kIvNj1V_NNSRgK_o2Cjv-3RzKPg0Qk",
    "marketPositioning": "1:利用高海拔地理优势，提供专业的高原养生设施和服务（高海拔养生设施）2:相比竞争对手的纳西建筑风格和茶马古道文化体验，文化特色不够突出（文化沉浸体验）",
    "prompt": "调研范围：本次调研、采访范围[全国范围]\n\n你好，我是一名商业分析顾问，现在接到了一个重要项目，请帮我根据以下诉求进行消费者调研，并输出分析报告：\n\n背景：丽江作为著名的旅游目的地，拥有丰富的历史文化资源和独特的自然景观，吸引了众多高端游客。在这样的背景下，丽江金茂璞修·雪山酒店坐落于玉龙雪山景区海拔3100米的甘海子草甸区，以独特的地理位置和环境吸引着寻求静谧和高端体验的游客。该酒店拥有89间客房及别墅，配备餐厅、泳池和水疗中心等设施。\n\n酒店优势：丽江金茂璞修·雪山酒店位于海拔较高的位置，能为游客提供远离喧嚣的静谧体验，同时也能享受玉龙雪山的壮丽景色。此外，酒店还配备了餐厅、泳池和水疗中心，满足高端游客的休闲需求。\n\n酒店劣势：由于酒店位于高海拔地区，可能会受到气候条件的影响，而且缺乏大规模的装修投资，可能在设施的新颖性和现代化程度上略显不足。\n\n现在我有3个核心诉求：\n1. 现在酒店希望做“**静谧高端**”主题的自身定位，通过新主题重构消费者价值感知，突破区位劣势；请帮我找到哪些消费者群体会对**静谧高端**主题感兴趣，并有意愿入住酒店。\n\n2. 请结合这个定位，与丽江古城的其他知名度假酒店如**Amanvista Resort**、**Ficus Grove Hotel**、**Cerulean Lodge**、**Globemar Hotel**、**Aurea Serenity Lijiang**品牌相对比：提取人有我无（竞争劣势）、人有我有（同质化）、人有我优（直面竞争）、人无我有（差异化）的价值要素。\n\n3. 请帮我分析这些消费者群体，分别对2中分析出的哪些价值要素敏感；“人有我无”的部分是否要投入精力追赶，如果不做任何动作，是否会对支付意愿打折扣；以及“人有我优”、“人无我有”的核心优势有多大的溢价，用户的支付意愿有多强。\n\n请生成一份关于**丽江金茂璞修·雪山酒店** **静谧高端**主题定位的完整conjoint分析报告，包含以下内容：\n\n1. 研究背景和目标\n2. Conjoint分析方法概述\n3. 调查问卷设计详情\n   - 完整的conjoint分析调查问卷内容\n   - 属性选择和水平设计说明\n   - 产品组合方案设计逻辑\n4. 调查实施过程\n   - 受访者选择和分布\n   - 数据收集方法\n5. 调查结果分析\n   - 各用户群体对不同产品方案的偏好分析\n   - 各属性重要性排序结果\n   - 价格敏感度和支付意愿分析\n6. 关键发现和洞察\n   - 消费者新主题的价值感知\n   - 不同客群的偏好差异\n   - 最优产品组合建议\n7. 战略建议和实施建议\n   - 产品设计优化建议\n   - 定价策略建议\n   - 目标客群营销策略\n\n报告应包含详细的调查问卷内容、数据分析图表，以及基于conjoint分析结果的具体战略建议。报告风格应专业、数据驱动，并提供清晰的执行指导。\n\n请根据附件的这个格式，给出conjoint分析的产出：包含不同细分市场人群的支付意愿：给虚拟persona bots做Conjoint或者PSM问卷，得出不同细分市场在不同价位的支付概率；呈现方式按参考的表格形式。"
})
const getData=(ischange)=>{
    if(ischange=='ischange'&&currentversion.value){
        iframeRef.value.src = 'about:blank';
        toGetViewData(currentversion.value)
    }else if(hasviewid){
        toGetViewData(hasviewid)
    }
};
const toGetViewData=(viewId)=>{
  loading.value=true;
  baseData.value={}
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
    dataid.value=res?.data?.priceSessionVIew?.id
    if(res?.data?.priceSessionVIew?.data){
        let chartdatas=[]
        try{
            chartdatas=JSON.parse(res.data.priceSessionVIew.data)
        }catch (err){
            ElMessage.warning("数据解析出错")
        }
        if(chartdatas){
            baseData.value=chartdatas;
            countInitInfo()
        }else{
            ElMessage.warning("数据解析失败")
        }
    }
    }).catch((err)=>{
        loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
};
// 增加渲染前逻辑处理
const countInitInfo=()=>{
  let {started,prompt,reports,currentUrl}=baseData.value;
  if(reports){//
    reportshtml.value=reports
  }
  if(currentUrl){
    reporturl.value=currentUrl;
    iframeRef.value.src = currentUrl
  }
};

// countInitInfo() //测试
getData()
const changeTabView=()=>{
  getData('ischange')
}
watch(() => props.reportdata,(newVal, oldVal) => {
    if(currentpath!='/atypicareport'){
        countInitInfo(newVal)
    }
  },
{ deep: true,immediate:true})
</script>