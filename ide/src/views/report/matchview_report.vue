<template>
  <div class="carousel-container readonly" 
   v-loading="loading"
    >
    <div class="comparison-header">
      <div class="version-change">
        <h2 class="report-title">竞争分析报告</h2>
        <div class="nav-btns-wrap" v-if="false">
          <el-icon class="nav-button left" :class="isAtStart?'disabled':''"  @click="scrollLeft" :disabled="isAtStart"><ArrowLeftBold /></el-icon>
          <el-icon class="nav-button right" :class="isAtEnd&&!isAtStart?'disabled':''"   @click="scrollRight" :disabled="isAtEnd"><ArrowRightBold /></el-icon>
        </div>
      </div>
    </div>
    <div class="carousel-wrapper" >
      <div class="carousel" ref="carousel">
        <div class="comparison-column our-hotel" v-if="meinfo?.[0]">
          <div class="hotel-card">
            <div class="hotel-info">
              <h2 class="hotel-name">{{ meinfo[0].公司名称 }}</h2>
              <template v-if="false">
              <div class="price" v-if="false">¥{{ meinfo[0].平均价格 }}</div>
              <template v-if="(typeof meinfo[0].平均价格 !== 'object')">
                <div class="price">¥{{ meinfo[0].平均价格 }}</div>
              </template>
              <template v-else>
                <div v-for="(price, roomType) in meinfo[0].平均价格" :key="roomType" class="price">
                  {{ roomType }}: ¥{{ price }}
                </div>
              </template>
              <div class="location">{{ meinfo[0].地理位置 }}</div>
              </template>
            </div>
            <div class="comparison-items">
              <template
              v-for="key in allmatchkeys" >
              <div 
                v-if="!['公司名称'].includes(key)"
                :key="key" 
                class="comparison-item"
              >
                <div class="item-title">
                  {{ key}}
                </div>
                <div class="item-value">
                  <RenderNestedValue
                      :value="meinfo[0][key]" 
                      :path="['me', 0, key]"
                      :editabled="false"
                      @edit="handleEdit"
                      @add="preHandleAdd"
                      @delete="preHandleDel"
                    />
                </div>
              </div>
              </template>
            </div>
          </div>
        </div>
        <!-- 右侧：竞争对手 -->
        <div class="comparison-column competitor"
          v-for="(competitor, competitorIndex) in allothers"
          :key="'hotel'+competitorIndex"
        >
          <div class="hotel-card">
            <div class="hotel-info">
              <h2 class="hotel-name">{{ competitor.公司名称 }}</h2>
              <template v-if="false">
              <div class="price" v-if="false">¥{{ competitor.平均价格 }}</div>
              <template  v-if="(typeof competitor.平均价格 !== 'object')">
                <div class="price">¥{{ competitor.平均价格 }}</div>
              </template>
              <template v-else>
                <div v-for="(price, roomType) in competitor.平均价格" :key="roomType" class="price">
                  {{ roomType }}: ¥{{ price }}
                </div>
              </template>
              <div class="location">{{ competitor.地理位置 }}</div>
              </template>
            </div>
            <div class="comparison-items">
              <template
              v-for="key in allmatchkeys" >
              <div 
                v-if="!['公司名称'].includes(key)"
                :key="key" 
                class="comparison-item"
              >
                <div class="item-title">
                  {{ key}}
                </div>
                <div class="item-value">
                  <RenderNestedValue
                      :value="competitor[key]" 
                      :editabled="false"
                      :path="['competitor', competitorIndex, key]"
                      @edit="handleEdit"
                      @add="preHandleAdd"
                      @delete="preHandleDel"
                    />
                </div>
              </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted,watch,watchEffect, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import RenderNestedValue from '@/views/competitiveanalysis/renderNested/index.vue'
const router = useRouter();
const carousel = ref(null);
const scrollPosition = ref(0);
const cardWidth = 320; // 300px宽度 + 20px边距

const isAtStart = computed(() => scrollPosition.value === 0);
const isAtEnd = computed(() => {
  if (!carousel.value) return true;
  return scrollPosition.value >= carousel.value.scrollWidth - carousel.value.clientWidth;
});
const currentpath = router.currentRoute._value.path;

// 响应式数据
const meinfo = ref([])
const allothers = ref([])
const allmatchkeys = ref([])
const selectedCompetitorIndex = ref(0)
const selectedCompetitor = ref(null)
const props = defineProps({
  reportdata: Object
})
// 判断是否为简单值
const isSimpleValue = (value) => {
  return value !== null && !isObject(value) && !Array.isArray(value)
}

// 判断是否为对象
const isObject = (value) => {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}


// 初始化数据
const countInitInfo = (baseData) => {
  meinfo.value = [baseData['本公司']]
  // allothers.value = baseData['竞争对手']?baseData['竞争对手']:[]
  if(baseData['竞争对手']?.constructor === Array){
    allothers.value = baseData['竞争对手']?baseData['竞争对手']:[]
  }else if(baseData['竞争对手']?.constructor === Object){
    allothers.value = baseData['竞争对手']?[baseData['竞争对手']]:[]
  }
  let keys = []
  meinfo.value?.forEach(m => m&&keys.push(...Object.keys(m)))
  allothers.value?.forEach(m => keys.push(...Object.keys(m)))
  allmatchkeys.value = [...new Set(keys)]
  // 
  nextTick(()=>{
    syncComparisonItemHeights()
  })
}
defineExpose({
  countInitInfo
})
let dataid =ref('')
let loading =ref(false)


const scrollLeft = () => {
  if (scrollPosition.value > 0) {
    scrollPosition.value = Math.max(0, scrollPosition.value - cardWidth);
    carousel.value.scrollTo({
      left: scrollPosition.value,
      behavior: 'smooth'
    });
  }
};

const scrollRight = () => {
  if (!carousel.value) return;
  const maxScroll = carousel.value.scrollWidth - carousel.value.clientWidth;
  if (scrollPosition.value < maxScroll) {
    scrollPosition.value = Math.min(maxScroll, scrollPosition.value + cardWidth);
    carousel.value.scrollTo({
      left: scrollPosition.value,
      behavior: 'smooth'
    });
  }
};

watch(() => props.reportdata,(newVal, oldVal) => {
  if(currentpath!='/matchviewreport'){
    countInitInfo(newVal)
  }
  // 
},{ deep: true,immediate:true})

const getData=()=>{
  loading.value=true;
  let viewId = getHashParams('viewid')
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
    dataid.value=res?.data?.priceSessionVIew?.id
      if(res?.data?.priceSessionVIew?.data){
          let chartdatas=[]
          try{
              chartdatas=JSON.parse(res.data.priceSessionVIew.data)
          }catch (err){
              ElMessage.warning("数据解析出错")
          }
          if(chartdatas&&chartdatas[0]){
            countInitInfo(chartdatas[0])
          }else{
              ElMessage.warning("数据解析失败")
          }
      }
  }).catch((err)=>{
    loading.value=false;
      ElMessage.warning("获取内容失败"+err)
  });
}
if(currentpath=='/matchviewreport'){
  getData()
}

const  syncComparisonItemHeights=()=> {
    // 获取所有酒店卡片
    const hotelCards = document.querySelectorAll('.hotel-card');
    if (hotelCards.length < 2) return; // 至少需要两个卡片进行比较
    
    // 获取每个卡片的比较项列表
    const comparisonLists = Array.from(hotelCards).map(card => 
        card.querySelectorAll('.comparison-items .comparison-item')
    );
    
    // 找到最短的比较项列表长度（避免索引越界）
    const minLength = Math.min(...comparisonLists.map(list => list.length));
    
    // 遍历每个索引位置，同步高度
    for (let i = 0; i < minLength; i++) {
        // 获取当前索引下的所有比较项
        const itemsAtSameIndex = comparisonLists.map(list => list[i]);
        
        // 计算这些项中的最大高度
        const heights = itemsAtSameIndex.map(item => {
            // 先移除可能设置的固定高度，以获取真实内容高度
            item.style.height = 'auto';
            return item.offsetHeight;
        });
        
        const maxHeight = Math.max(...heights);
        
        // 将最大高度应用到当前索引的所有比较项
        itemsAtSameIndex.forEach(item => {
            item.style.height = `${maxHeight}px`;
        });
    }
}
onMounted(()=>{
  syncComparisonItemHeights()
})
</script>