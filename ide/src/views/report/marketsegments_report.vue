<template>
<div class="market-segments-wrap market-segments-withchart-wrap readonly course-report-readonly" v-loading="loading">
   <div class="comparison-header">
        <h2>细分市场报告</h2>
        <div class="version-change">
        </div>
    </div>
    <div class="excel-area">
        <div class="row-header" ref="headerRef">
            <div class="header-item"
            v-for="(item,index) in rowheaders"
            :class="[item.value,item.value.startsWith('price_')?'header-item-price':'']"
            :key="item+index"
            >
            <div class="header-inner" v-if="item.value.startsWith('price_')">
                <div class="header-inner-label">{{item.label?item.label:item.value}}
                    </div>
                <div class="header-inner-types">
                    <div>支付概率</div>
                    <div>预期需求</div>
                </div>
            </div>
            <div class="header-inner-others" v-else>{{item.label?item.label:item.value}}</div>
            </div>
        </div>
        <div class="row-content" ref="contentRef">
            
                <div class="content-single-row"
                v-for="(item,index) in rowdatas"
                :key="'item'+index">
                <template v-for="(iitem,iikey) in item" :key="iikey">
                    <div v-if="rowheaders.find((ritem)=>ritem.value===iikey)"
                        class="row-column column-cell "
                        :class="['row-column-'+iikey,
                            iikey.startsWith('price_')?'row-column-price':''
                        ]">
                        <div class="row-column-consumer_profile-inner" v-if="iikey=='consumer_profile'"  >
                            <div class="column-label">
                                <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey" 
                                 >{{iitem}}</span>
                                <el-input 
                                    v-else
                                    v-model="rowdatas[index][iikey]"
                                    @blur="saveEdit"
                                    @keyup.enter="saveEdit"
                                    ref="inputRef"
                                    class="edit-input"
                                />
                                <!-- {{item.consumer_profile}} -->
                            </div>
                            <div class="column-description">
                                <span v-if="!editingCell || editingCell.row !== index || (editingCell.col !== 'description')">{{rowdatas[index].description}}</span>
                                <el-input 
                                    v-else
                                    v-model="rowdatas[index].description"
                                    @blur="saveEdit"
                                    @keyup.enter="saveEdit"
                                    ref="inputRef"
                                    class="edit-input"
                                />
                                <!-- {{item.description}} -->
                            </div>
                        </div>
                        <div class="edit-able" v-else-if="iikey=='market_size'">
                            <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey">{{iitem}}</span>
                            <el-input 
                                v-else
                                v-model="rowdatas[index][iikey]"
                                @blur="saveEdit"
                                @keyup.enter="saveEdit"
                                ref="inputRef"
                                class="edit-input"
                            />
                        </div>
                        <div class="edit-able" v-else-if="iikey=='market_percent'" >
                            <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey">{{iitem}}%</span>
                            <el-input 
                                v-else
                                v-model="rowdatas[index][iikey]"
                                @blur="saveEdit"
                                @keyup.enter="saveEdit"
                                ref="inputRef"
                                class="edit-input"
                            />
                        </div>
                        <div class="edit-able" v-else-if="iikey.startsWith('price_')">
                            <div class="edit-able-price">
                                <div class="column-probability-pair">
                                <!-- 支付概率： -->
                                <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey|| editingCell.key !== 'price_probability_pairs'">
                                    {{iitem[0]&&iitem[0]!='-'?iitem[0]+"%":iitem[0]}}
                                </span>
                                <el-input 
                                    v-else
                                    v-model="rowdatas[index][iikey][0]"
                                    @blur="saveEdit"
                                    @keyup.enter="saveEdit"
                                    ref="inputRef"
                                    class="edit-input"
                                />
                                </div>
                                <div class="column-demand-pair" style="cursor:not-allowed">
                                    <!-- 预期需求： -->
                                    <span  v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey|| editingCell.key !== 'price_demand_pairs'" >
                                        {{iitem[1]}}
                                    </span>
                                    <el-input 
                                        v-else
                                        v-model="rowdatas[index][iikey][1]"
                                        @blur="saveEdit"
                                        @keyup.enter="saveEdit"
                                        ref="inputRef"
                                        class="edit-input"
                                    />
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </template>

                </div>
        </div>
    </div>
    <div class="chart-area">
        <div class="chart-inner" id="chartinner"></div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch,nextTick } from 'vue';
import { useRouter } from 'vue-router'
import {getPriceViewInfo,updatePriceSessionView,executeTool} from '@/assets/api/api.js'
import {getHashParams,versionLabel,debounceCustom} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import * as echarts from 'echarts';
const router = useRouter();
const currentpath = router.currentRoute._value.path;

const editingCell = ref(null);
const inputRef = ref(null);
let hasviewid = getHashParams('viewid')
let rowheaders=ref([]);
let rowdatas=ref([]);

let loading =ref(false)
let dataid =ref('');
let currentversion =ref('');
let textval =ref('');
const props = defineProps({
  reportdata: Object
})
// 
currentversion.value=getHashParams('viewid');
// 
const  processMarketSegments=(market_segments)=> {
  const headers = [
      { label: '消费者画像', value: 'consumer_profile' },
      { label: '市场规模', value: 'market_size' },
      { label: '市场份额', value: 'market_percent' }
  ];

  // 从第一个数据项中提取价格点作为表头的其余部分
  if (market_segments.length > 0 && market_segments[0].price_probability_pairs.length > 0) {
      market_segments[0].price_probability_pairs.forEach(pair => {
          headers.push({
              label: `${pair?.[0]?.toLocaleString()?pair[0].toLocaleString():'-'}元`,
              value: `price_${pair[0]}`
          });
      });
  }

  // 构建内容列表
  const content =market_segments&&market_segments?.map((item) => {
      const mainRow = {};
      headers.forEach(header => {
          switch (header.value) {
              case 'consumer_profile':
                  mainRow[header.value] = item.consumer_profile;
                  mainRow.description = item.description;
                  break;
              case 'market_size':
                  mainRow[header.value] = item.market_size?.toLocaleString()?item.market_size.toLocaleString():'-';
                  break;
              case 'market_percent':
                    mainRow[header.value] = item.market_percent?.toLocaleString()?item.market_percent.toLocaleString():'-';
                    break;
              default:
                  if (header.value.startsWith('price_')) {
                      const price = parseInt(header.value.split('_')[1]);
                      const probPair = item.price_probability_pairs.find(p => p[0] === price);
                      let probability=probPair&&probPair[1]?`${Math.round(probPair[1] * 100)}`:'-';
                      const demandPair = item.price_demand_pairs?.find(p => p[0] === price);
                      let demand=demandPair&&demandPair[1]?demandPair[1]:'-';
                      mainRow[header.value] = [probability,demand]
                  } else {
                      mainRow[header.value] = ['-','-'];
                  }
          }
      });

      return [mainRow];
  }).flat();

  return {
      headers,
      content
  };
};
const reverseProcessMarketSegments=(processedData)=>{
   // 提取所有以price_开头的键
    const priceKeys = Object.keys(processedData).filter(key => key.startsWith('price_'));
    // 将价格概率对和价格需求对分开处理
    const priceProbabilityPairs = [];
    const priceDemandPairs = [];
    let market_size=parseInt(processedData.market_size.replace(/,/g, ''))
    let market_percent=parseFloat(processedData.market_percent.replace('%', ''));

    priceKeys.forEach(key => {
        const price = parseInt(key.split('_')[1]);
        const [probabilityStr, demandStr] = processedData[key];
        
        // 处理概率
        if (probabilityStr && probabilityStr !== '-') {
            const probability = parseFloat(probabilityStr.replace('%', '')) / 100;
            const market_percent_p = market_percent / 100;
            priceProbabilityPairs.push([price, probability]);
            if(probability){
                const demand =Math.round(market_size *market_percent_p* probability);
                priceDemandPairs.push([price, demand]);
            }
        }
    });
    
    // 按价格排序
    priceProbabilityPairs.sort((a, b) => a[0] - b[0]);
    priceDemandPairs.sort((a, b) => a[0] - b[0]);
    
    return {
        consumer_profile: processedData.consumer_profile,
        market_size:market_size,
        market_percent:market_percent,
        price_probability_pairs: priceProbabilityPairs,
        price_demand_pairs: priceDemandPairs.length > 0 ? priceDemandPairs : [],
        description: processedData.description
    };
};
const countInitInfo=(baseData)=>{
  let {extracted_data}=JSON.parse(JSON.stringify(baseData));
  let market_segments=''
  if(extracted_data?.market_segments){
      market_segments=extracted_data.market_segments
  }
  let {headers,content}=processMarketSegments(market_segments);
  rowheaders.value=headers
  rowdatas.value=content
  toGetChartInfo()
}
const toGetChartInfo=(auto)=>{
    loading.value=true;
    const urlParams = new URLSearchParams(window.parent.location.search);
    let sessionId = urlParams.get("sessionId");
    if (process.env.NODE_ENV !== "production") {
        sessionId="01K2Y99K7TSM1QFRAX14BRN03M"//测试
    } 
    // 
    if (!sessionId) {
        loading.value=false;
        ElMessage.warning("SessionId 获取失败，无法发起计算，请检查");
        return
    }
    executeTool({
      toolName: 'forecast_price_sales',
      autoTask:auto==='auto',
      sessionId
    }).then((res)=>{
        loading.value=false;
      if(res?.data?.executeTool){
          if(res.data.executeTool.isError){
            ElMessage.warning(res.data.executeTool.errorMessage)
          }else if(res?.data?.executeTool?.datas){
            let chartdatas=res.data.executeTool.datas
            console.log('chartdatas',chartdatas)
            if(chartdatas&&chartdatas.length){
                let predict_price_and_sales=chartdatas[0].predict_price_and_sales;
                let xval=[]
                let pval=[]
                predict_price_and_sales.forEach((item)=>{
                    xval.push(item[0])
                    pval.push(item[1])
                })
                if(xval?.length&&pval?.length){
                    initChart(xval,pval)
                }else{
                    ElMessage.warning("暂无数据"+predict_price_and_sales)
                }
            }
        }
      }
      if(auto=='auto'){
          setTimeout(()=>{
            toAutoParent()
          },2000)
      }
    }).catch((err)=>{
      loading.value=false;
      ElMessage.error('err: ' + err)
    })
}
let myChart=null;
// 初始化图表
const initChart = (xval,pval) => {
    const chartDom = document.getElementById('chartinner');
    if (!chartDom) return;
    // 如果图表已存在，则先销毁
    if (myChart) {
        myChart.dispose();
    }
    myChart = echarts.init(chartDom);
    const option = getChartOption({xval,pval})
    myChart.setOption(option);
};
const getChartOption = ({xval,pval}) => {
  return {
    xAxis: {
        type: 'category',
        data: xval
    },
    yAxis: {
        type: 'value'
    },
    tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                let result = `价格: ${params[0].axisValue}<br>`;
                params.forEach(param => {
                    result += `销量: ${param.value.toLocaleString()}<br>`;
                });
                return result;
            }
        },
    title: {
            text: '预期价格与销量',
            left: 'center'
        },
    series: [
        {
        data: pval,
        type: 'line'
        }
    ]
    }
};
const getData=(ischange)=>{
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      currentversion.value=hasviewid
      toGetViewData(hasviewid)
    }
};
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            if(chartdatas?.length){
              countInitInfo(chartdatas[0])
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
}
if(currentpath=='/marketsegmentsreport'){
  getData()
}
watch(() => props.reportdata,(newVal, oldVal) => {
    if(currentpath!='/marketsegmentsreport'){
        countInitInfo(newVal)
    }
  },
{ deep: true,immediate:true})
</script>

