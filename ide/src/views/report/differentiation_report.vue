<template>
  <div class="differentiation-container" v-loading="loading">
    <div class="version-change">
        <h2 class="report-title">差异化价值报告</h2>
      <div class="vc-title">
        <el-button type="primary" plain size="small" @click="openChart">得分统计</el-button>
        <el-badge :value="summaryTopic?.filter((item)=>item?.type==='否定主题')?.length" v-if="summaryTopic?.filter((item)=>item?.type==='否定主题')?.length">
          <el-button type="primary" plain size="small" @click="passedThemes">未被采纳主题</el-button>
        </el-badge>
        <!-- <el-button type="primary" plain size="small" @click="passedThemes" >否定主题</el-button> -->
      </div>
      <el-select v-model="currentversion" @change="changeTabView" v-if="false&&viewitems.length>1">
        <el-option
          v-for="(item,index) in viewitems"
          :key="item.id"
          :label="versionLabel(item,index)"
          :value="item.id">
        </el-option>
      </el-select>
    </div>
    <div v-for="(item, index) in datalist" :key="index" class="item-card"
    :class="checkeddata.includes(item['主题'])?'item-card-checked':''">
        <div class="item-theme" @click.stop="toggleData(item['主题'])">
          <div class="base">
            <div class="label-title" >{{item['主题']}}</div>
          </div>
          <div class="toggle" >
            <el-button type="primary" size="small"
            @click.stop="toggleData(item['主题'])">{{(hidethemes.includes(item['主题'])?'展开':'收起')}}</el-button></div>
        </div>
      

        <div class="item-content" v-show="!hidethemes.includes(item['主题'])">
          <template v-for="(value, key) in item" :key="key">
            <div v-if="!['主题','checked'].includes(key)" class="item-property"  @click.stop>
              <div class="label-title able-to-change" >{{ key }}
                
              </div>
              <div class="value-item-property-wrap">
                <div v-for="(val_item, val_index) in value" :key="'val_value'+val_index" class="value-item-property"
                >
                  <div class="single-item">
                    <template
                    v-for="(val_val_item, val_val_key) in val_item" 
                    :key="'val_val_item'+val_val_key" 
                    >
                      <div 
                      v-if="val_val_key!='打分'"
                      :class="[val_val_key==='描述'?'value-value-item-property-full':'']"
                      class="value-value-item-property">
                        <div class="value-value-item-label">{{ val_val_key }}:</div>{{val_val_item}}
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
    </div>
    <h2>已选项目</h2>
    <!-- <pre>{{ checkeddata }}</pre> -->
    <div class="selected-items-container">
      <div v-if="checkeddata.length === 0" class="empty-message">
        无选中项目
      </div>
      <template v-else>
        <div v-for="(item, index) in checkeddata" :key="index" class="selected-item">
          <div class="selected-item-header">
            {{item}}
          </div>
        </div>
      </template>
    </div>
    <el-dialog 
    v-model="chartdialog" 
    title="得分统计" 
    class="import-data-dialog"
    destroy-on-close
    width="70%"
    >
      <diffchart ref="diffrefchart" v-if="chartdialog"></diffchart>
    </el-dialog>
    <el-dialog 
    v-model="passeddia" 
    title="未采纳主题" 
    class="passed-themes-dialog"
    destroy-on-close
    width="70%"
    >
      <passedthemes ref="passedrefthemes"></passedthemes>
    </el-dialog>
    
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router'
import {getPriceViewInfo,updatePriceSessionView,executeTool,submitPriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel,systemsort} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import diffchart from '@/views/competitiveanalysis/diff/diffchart.vue'
import passedthemes from '@/views/competitiveanalysis/diff/passedthemes.vue'
const router = useRouter();
const props = defineProps({
  viewdata: Object,
  reportdata: Object

})
const currentpath = router.currentRoute._value.path;
const $emit=defineEmits();

// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
  if(viewid.value){
    currentversion.value=viewid.value
  }
},{immediate:true})


let hasviewid = getHashParams('viewid')
let summaryTopic=ref([]);
let baseData=ref([
  ])
if (process.env.NODE_ENV !== "production") {
  baseData.value=[{
      "主题": "高端定制体验主题",
      "人有我优": [
        {
          "价值元素": "雪山观景资源",
          "类型": "功能型",
          "描述": "位于玉龙雪山景区海拔3100米的甘海子草甸区，提供独特的雪山景观资源，优于其他竞争对手的地理位置。",
          '我方得分':"",
          '行业得分':"",
          打分:[9,12]
        },
        {
          "价值元素": "高端酒店管理经验",
          "类型": "信任型",
          "描述": "创始人潘小科的高端酒店管理经验，为酒店提供专业的管理和服务保障。",
          '我方得分':"",
    '行业得分':"",
        }
      ],
      "人有我劣": [
        {
          "价值元素": "地理位置便利性",
          "类型": "功能型",
          "描述": "远离丽江古城和束河古镇，出行方便程度不如竞争对手。"
        },
        {
          "价值元素": "设施多样性",
          "类型": "功能型",
          "描述": "设施列表相对简单，如SPA体验可能不如悦榕庄等竞争对手。"
        }
      ],
      "人无我有 - 功能": [
        {
          "价值元素": "私人观星会",
          "类型": "功能型",
          "描述": "利用高海拔和雪山资源，提供专业的天文观星设备和私人观星体验。"
        },
        {
          "价值元素": "定制化摄影课程",
          "类型": "功能型",
          "描述": "结合雪山景观，提供定制化的摄影课程，满足高端游客的摄影需求。"
        }
      ],
      "人无我有 - 体验": [
        {
          "价值元素": "观星体验",
          "类型": "体验型",
          "描述": "独特的观星活动和观星节，增强客户的独特体验感。"
        },
        {
          "价值元素": "定制化摄影体验",
          "类型": "体验型",
          "描述": "提供个性化的摄影指导和服务，让客户在雪山背景下拍摄独特照片。"
        }
      ],
      "人无我有 - 信任": [
        {
          "价值元素": "专家背书",
          "类型": "信任型",
          "描述": "邀请天文专家或摄影大师作为顾问或讲师，提升活动的专业性和权威性。"
        },
        {
          "价值元素": "第三方认证",
          "类型": "信任型",
          "描述": "获得天文或摄影领域的专业认证，增强客户对活动的信任感。"
        }
      ]
    },
    {
      "主题": "健康养生项目主题",
      "人有我优": [
        {
          "价值元素": "雪山景观资源",
          "类型": "功能型",
          "描述": "高海拔和雪山环境为健康养生项目提供了独特的自然条件。"
        },
        {
          "价值元素": "高端酒店管理经验",
          "类型": "信任型",
          "描述": "创始人潘小科的高端酒店管理经验，确保养生项目的专业性和服务质量。"
        }
      ],
      "人有我劣": [
        {
          "价值元素": "地理位置便利性",
          "类型": "功能型",
          "描述": "远离丽江古城和束河古镇，出行方便程度不如竞争对手。"
        },
        {
          "价值元素": "设施多样性",
          "类型": "功能型",
          "描述": "设施列表相对简单，如SPA体验可能不如悦榕庄等竞争对手。"
        }
      ],
      "人无我有 - 功能": [
        {
          "价值元素": "雪山瑜伽",
          "类型": "功能型",
          "描述": "在雪山背景下提供专业的瑜伽课程，结合高海拔环境增强养生效果。"
        },
        {
          "价值元素": "森林浴疗法",
          "类型": "功能型",
          "描述": "利用周边的森林资源，提供森林浴疗法，满足高端游客对身心健康的追求。"
        }
      ],
      "人无我有 - 体验": [
        {
          "价值元素": "雪山瑜伽体验",
          "类型": "体验型",
          "描述": "在雪山和森林环境中进行瑜伽练习，为客户提供独特的养生体验。"
        },
        {
          "价值元素": "森林浴体验",
          "类型": "体验型",
          "描述": "通过森林浴疗法，让客户在自然环境中放松身心，增强健康效果。"
        }
      ],
      "人无我有 - 信任": [
        {
          "价值元素": "专业指导人员",
          "类型": "信任型",
          "描述": "配备专业的瑜伽和森林浴疗法指导人员，确保服务的专业性和安全性。"
        },
        {
          "价值元素": "健康认证",
          "类型": "信任型",
          "描述": "获得健康养生领域的第三方认证，提升客户对项目的信任感。"
        }
      ]
    }]
}
const checkeddata = ref([]);
const datalist = ref([
]);
let loading =ref(false)
let dataid =ref('');
let textval =ref('');
let generateflag=ref(false);

watch(()=>props.viewdata?.summaryTopic,()=>{
  if(props.viewdata?.summaryTopic?.length){
    summaryTopic.value=props.viewdata.summaryTopic;
  }
},{deep:true,immediate:true})



const getData=(ischange)=>{
  generateflag.value=false;
  textval.value="";
  dataid.value='';
  baseData.value=[];
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      currentversion.value=hasviewid
      toGetViewData(hasviewid)
    }
};
const toGetViewData=(viewId)=>{
  loading.value=true;
  baseData.value=[]
  summaryTopic.value=[]
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
      dataid.value=res?.data?.priceSessionVIew?.id
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            if(chartdatas?.differentiated||chartdatas?.summaryTopic){
              baseData.value=chartdatas?.differentiated?chartdatas.differentiated:[]
              summaryTopic.value=chartdatas?.summaryTopic?chartdatas.summaryTopic:[]
              countInitInfo('init')
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
}



const countInitInfo=(init)=>{
  if(init==='init'){
    hidethemes.value=[]
  }
  baseData.value=addScoreKeys(baseData.value);//我方得分和行业得分
  datalist.value=JSON.parse(JSON.stringify(baseData.value));
  checkeddata.value=[];
  
  datalist.value.forEach((item)=>{
     if(init==='init'){
     if(currentpath==='/report'){//总结报告页面全展开
       hidethemes.value=[]
     }else{//默认全部隐藏
       hidethemes.value.push(item['主题'])
     }
      
    }
    if(item.checked&&item['主题']){
      checkeddata.value.push(item['主题'])
    }
    
    for(let key in item){
      if(key!='主题'){
        if(typeof item[key] !='boolean'){
          // 字段排序
          item[key] = item[key].map(item => {
              return {
                  "价值元素": item["价值元素"],
                  "类型": item["类型"],
                  "我方得分": item["我方得分"],
                  "行业得分": item["行业得分"],
                  "描述": item["描述"],
                  "打分": item["打分"]
              };
          });
          item[key]?.forEach((ikitem)=>{
            
            if(ikitem['打分']){
              if(typeof ikitem['打分']==='object'){
                ikitem['我方得分']=ikitem['打分'][0]
                ikitem['行业得分']=ikitem['打分'][1]
              }
            }
          })
        }
      }
    }
  });
  // 
  if(init==='init'&&datalist.value?.length<2){
    //初始化展开第一个或者第一个被选中的值
    // 当主题列表小于4个时才默认展开，否则全部收起来
    let first = datalist.value.find((item)=>item.checked&&item['主题']);
    if(!first){
      first=datalist.value[0]
    }
    const matchedindex = hidethemes.value.findIndex((item)=>item===first['主题']);
    if(matchedindex>=0){
      hidethemes.value.splice(matchedindex,1);
    }
  }
}
const changeTabView=()=>{
    getData('ischange')
}

// 检查某项是否已被选中
const isChecked = (value) => {
  return checkeddata.value.some((item) => item===value);
};
// 添加我方得分和行业得分
const addScoreKeys=(data)=> {
  // 遍历每个主题对象
  data.forEach(topicObj => {
    // 获取所有与"主题"同级的键（排除"主题"本身）
    const keys = Object.keys(topicObj).filter(key => key !== '主题');
    // 遍历每个键对应的数组
    keys.forEach(key => {
      const items = topicObj[key];
      if (Array.isArray(items)) {
        // 遍历数组中的每个对象
        items.forEach(item => {
          // 如果不存在"我方得分"，则添加并设为0
          if (!item.hasOwnProperty('我方得分')) {
            item['我方得分'] = 0;
          }
          // 如果不存在"行业得分"，则添加并设为0
          if (!item.hasOwnProperty('行业得分')) {
            item['行业得分'] = 0;
          }
        });
      }
    });
  });
  
  return data;
};
// countInitInfo()//测试
getData();
// 切换选择状态
const toggleSelection = (value) => {
  const index = checkeddata.value.findIndex((item) => item===value);
  if (index === -1) {
    checkeddata.value=[];
    checkeddata.value.push(value);
  } else {
    // 移除选中项
    checkeddata.value.splice(index, 1);
  }
};
let hidethemes=ref([])
const toggleData=(value)=>{
  const index = hidethemes.value.findIndex((item) => item===value);
  if (index === -1) {
    hidethemes.value.push(value);
  } else {
    // 移除选中项
    hidethemes.value.splice(index, 1);
  }
}

const areArraysContentEqual=(arr1, arr2)=>{
    // 首先检查长度是否相同
    if (arr1.length !== arr2.length) {
        return false;
    }
    
    // 创建一个数组的拷贝以便操作
    const arr2Copy = [...arr2];
    
    // 检查arr1中的每个元素是否都在arr2中
    for (const item of arr1) {
        const index = arr2Copy.indexOf(item);
        if (index === -1) {
            return false;
        }
        arr2Copy.splice(index, 1);
    }
    
    return true;
};
const manageCount=(item)=>{
  for(let key in item){
    if(key!='主题'){
      console.log(item[key])
      if(typeof item[key] !='boolean'){

        item[key]?.forEach((ikitem)=>{
          ikitem['打分']=[ikitem['我方得分'],ikitem['行业得分']]
          delete ikitem['我方得分']
          delete ikitem['行业得分']
        })
      }
      
    }
  }
  return item
};

const preSave=()=>{
  loading.value=true;
  let data=datalist.value.map((item)=>{
    let newitem={...manageCount(item)}
    delete newitem.checked
    if(checkeddata.value.includes(item['主题'])){
      newitem.checked=true
    }
    return newitem
  })
  if(props.viewdata){
    if(props.viewdata?.users?.length){
      let newdata =JSON.parse(JSON.stringify(props.viewdata));
      newdata.differentiated=data;
      // 
      data=newdata;
    }
  }
  console.log('我要保存了',data)

  // return
  updatePriceSessionView({
    entity:{
      id:dataid.value,
      data:JSON.stringify(data)
    }
  }).then((res)=>{
    if(res?.data?.updatePriceSessionView){
      toParent()
    }
  }).catch((err)=>{
    loading.value=false;
    ElMessage.error('发起失败: ' + err)
  })
}
const generateNowtxt=()=>{
  let nowtxt =[];
  if(checkeddata.value?.length){
    checkeddata.value.forEach((item,index)=>{
      nowtxt.push(`${index+1}:${item}`)
    })
  }
  
  return nowtxt.join('')?`请帮我基于新的市场定位"${nowtxt.join('')}"，进行市场分析。`:`请帮我基于新的市场定位，进行市场分析。`
}
const toAutoParent=()=>{
  console.timeEnd('打开图表3S后去父级');
  console.log('自动触发由[差异化]触发啦',{eventname:'autotasktrigger',data:{toolname:'',nowtxt:"差异化",sendnow:'true'}})
  parent.postMessage({eventname:'autotasktrigger',data:{toolname:'',nowtxt:"差异化",sendnow:'true'}});
}
// 
const toParent=async()=>{
  if(checkeddata.value?.length){
    try {
      console.log(parent.parent)
      let nowtxt=generateNowtxt()
      console.log('作为子集发起了',{eventname:'triggerChangeEvent',data:{toolname:'2C市场分析',nowtxt,sendnow:'true'}})
      parent.parent.postMessage({eventname:'triggerChangeEvent',data:{toolname:'2C市场分析',nowtxt,sendnow:'true'}});
    } catch (err) {
      loading.value=false;
      console.error('操作失败，请重试', err)
      ElMessage.error('操作失败，请重试')
    }
  }else{
    loading.value=false;
    ElMessage.warning('请至少勾选一个分析项目')
  }
};
const saveEdit=(index,key,val_index,val_key,newValue)=>{
  baseData.value[index][key][val_index][val_key]=newValue;
  countInitInfo()
}
const insertKeyAfterTarget=(target,newkey,obj,space,newval)=>{
    const entries = Object.entries(obj);
    const index = entries.findIndex(([key]) => key === target);
    
    if(space==-1){//删除当前项，替换新项目
      entries.splice(index , 1,[newkey,newval])
    }else{
      entries.splice(index + space, 0, [newkey,[{
        '价值元素':"",
        '类型':"",
        '描述':"",
        '我方得分':"",
        '行业得分':"",
      }]]);
    }
    const newData = Object.fromEntries(entries);
    console.log(newData)
    return newData;
}
const preKeyHandleAdd=(index,key)=>{
  console.log(index,key,baseData.value[index])
  baseData.value[index]=insertKeyAfterTarget(key,'新增项',baseData.value[index],1)
  // baseData.value[index][key];
  countInitInfo()//
}
const preKeyHandleDel=(index,key)=>{
  ElMessageBox.confirm('确认删除？', '删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(()=>{
    delete baseData.value[index][key];
    countInitInfo()//
  })
}
const preHandleAdd=(index,key,val_index=-1)=>{
  baseData.value[index][key].splice(val_index+1,0,{
    '价值元素':"",
    '类型':"",
    '我方得分':"",
    '行业得分':"",
    '描述':"",
  })
  countInitInfo()//
}
const preHandleDel=(index,key,val_index)=>{
  ElMessageBox.confirm('确认删除？', '删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(()=>{
    baseData.value[index][key].splice(val_index,1)
    countInitInfo()
  })
  
}
const saveEditTheme=(index,newValue)=>{
  baseData.value[index]['主题']=newValue;
  countInitInfo()
}
const saveEditKey=(index,key,newValue)=>{
  baseData.value[index]=insertKeyAfterTarget(key,newValue,baseData.value[index],-1,baseData.value[index][key])
  countInitInfo()
}
// 
let chartdialog=ref(false);
let diffrefchart=ref(null);
const openChart=()=>{
  console.timeEnd('2S后打开图表');
  chartdialog.value=true;
  nextTick(()=>{
    diffrefchart.value?.initChart(datalist.value)
  })
}
window.addEventListener('message', function(event) {
    const message = event.data;
    let {eventname}=message;
    console.log('eventname',eventname)
    
    if(eventname === 'autotasktrigger_fromparent'){
      if(sessionStorage.getItem('autotask')==='1'){
      // 
        console.time('2S后打开图表');
        console.time('打开图表3S后去父级');
        setTimeout(()=>{
          openChart()//2S后打开图表
        },2000)
        setTimeout(()=>{
          toAutoParent()//3S后去父级
        },5000)
        // 20250717 之前
        // if(sessionStorage.getItem('autotask')==='1'){
        //     //如果没有可选，直接去父级触发
        //     // 差异化，选中，触发下一步，不需要updata，不走2C
        //     if(baseData.value?.[0]?.['主题']){
        //       toggleSelection(baseData.value[0]['主题'])
        //       toAutoParent()
        //     }else{
        //       //如果没有可选，直接去父级触发
        //       toAutoParent()
        //     }
        // }
    }
        
      }

});
const showInitDiff=(differentiated,parentViewId)=>{
    dataid.value=parentViewId;

    let chartdatas=differentiated;
    if(chartdatas&&chartdatas.length){
      baseData.value=chartdatas
      countInitInfo('init')
    }
}
let passeddia=ref(false)
let passedrefthemes=ref(null)
let passedlist=ref([])
// 展示否定主题
const passedThemes=()=>{
  passeddia.value=true;
  passedlist.value=summaryTopic.value?.filter((item)=>item?.type==='否定主题');
 
  nextTick(()=>{
    if(passedrefthemes.value){
      passedrefthemes.value.initThemes(passedlist.value)
    }
  })
}


defineExpose({
  showInitDiff
})
watch(() => props.reportdata,(newVal, oldVal) => {
    if(currentpath!='/differentiationreport'){
      if(newVal?.differentiated||newVal?.summaryTopic){
          baseData.value=newVal?.differentiated?newVal.differentiated:[]
          summaryTopic.value=newVal?.summaryTopic?newVal.summaryTopic:[]
          countInitInfo('init')
        }else{
            ElMessage.warning("数据解析失败")
        }
    }
  },
{ deep: true,immediate:true})
</script>

