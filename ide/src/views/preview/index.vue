<template>
    <div class="preview-wrap" v-loading="loading">
        <el-empty v-if="!iframeSrc"  description="暂无案例可显示" />
        <iframe 
            v-show="iframeSrc"
            :src="iframeSrc"
            width="100%" 
            height="100%"
            frameborder="0"></iframe>
     </div>
</template>

<script setup>
import { ref, watch, onMounted,nextTick, inject } from 'vue'
import {caseFiles} from '@/assets/api/api.js'
import { ElMessage, ElMessageBox } from 'element-plus'

import { useStore } from 'vuex'
const store = useStore();

const iframeSrc = ref('');
const loading = ref(false);
const caselist = ref([]);

const getCaseFiles=()=>{
  loading.value=true;
  caseFiles().then((res)=>{
      if(res?.data?.caseFiles){
          let chartdatas=res.data.caseFiles;
          if(chartdatas){
            caselist.value=JSON.parse(JSON.stringify(chartdatas));
            getIframeUrl()
          }else{
            ElMessage.warning("数据解析失败")
          }
      }
  }).catch((err)=>{
    loading.value=false;
    ElMessage.warning("获取内容失败"+err)
  });
};
const getIframeUrl=(url)=>{
    if(!url){
        let matched = caselist.value.find((item)=>item.active&&item.url)
        if(matched){
            url=matched.url;
        }
    }
    let previewurl = store.getters.getpreview_url;
    loading.value=false;
    if(previewurl&&url){
        iframeSrc.value=addOrUpdateUrlParam(previewurl,'url',url)
    }
}
const addOrUpdateUrlParam=(url, searchKey, searchValue)=>{
  const urlObj = new URL(url);
  const params = urlObj.searchParams;
  params.set(searchKey, searchValue);
  urlObj.search = params.toString();
  return urlObj.toString();
}
// 
const getSearchUrl=()=>{
  const urlParams = new URLSearchParams(window.location.search);
  let previewcaseurl = urlParams.get("previewcaseurl");
  if(previewcaseurl){
    getIframeUrl(previewcaseurl)
  }else{
    getCaseFiles()
  }
}
getSearchUrl()

</script>