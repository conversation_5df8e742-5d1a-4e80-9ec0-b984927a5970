<template>
    <!-- 模板部分保持不变 -->
    <div class="stepview-wrap">
        <div class="step-tabs">
            <div 
            v-for="(item, index) in allsteps" 
            :key="item.value+index"
            class="tab-item" 
            @click="changeMenu(item.value+'-'+'1',true)"
            :class="{ active: activestepname == (index+1),
            hasdata:activestepname > (index+1)
             }"
            >
            <div class="tab-item-inner">
                <div class="tii-label"><div class="tii-icon"></div>{{ item.label }}</div>
                <div class="tii-line"></div>
            </div>
            </div>
        </div>
        <div class="top-steps" v-if="false">
            <el-steps
                class="mb-4"
                :space="200"
                :active="activestepname"
                simple
            >
                <el-step 
                :title="item.label"
                v-for="(item,index) in allsteps"
                :key="item.value+index"
                style="cursor:pointer"
                @click="changeMenu(item.value+'-'+'1',true)"></el-step>
            </el-steps>
        </div>
        <!--  -->
        <template v-if="false&&!(activestepname=='1'&&activemenuname=='3')">
            <div class="toggle-menu" v-show="stepmenus[activestepname]?.length"  :class="togglemenu?'hassidemenu':'hidesidemenu'"
            @click="togglemenu=!togglemenu">
                <el-icon style="color:#fff"><Expand  v-if="!togglemenu"/><Fold v-else/></el-icon>
            </div>
        </template>
        
        <div class="step-content">
            <div class="sc-menu sc-side" v-if="togglemenu&&stepmenus[activestepname]?.length">
                <el-menu
                :default-active="activemenuname">
                    <el-menu-item
                    v-for="(item,index) in stepmenus[activestepname]"
                    :key="item.value+index"
                    :index="item.value"
                    @click.native="changeMenu(activestepname+'-'+item.value,true)">
                    <el-icon><document /></el-icon>
                    {{item.label}}
                    </el-menu-item>
                </el-menu>
            </div>
            <div class="sc-content" :class="togglemenu&&stepmenus[activestepname]?.length?'':'no-side'">
                <!--  v-if="activestepname!=3" -->
                <div class="tab-list new-style">
                    <div
                    v-for="(item,index) in sortstepmenutabs"
                    :key="'sorttab'+index"
                    class="tab-item"
                    :class="activetabname === item.viewName ? 'tab-selected' : 'tab-plain'"
                    @click="changeTab(item,true)"
                    >
                    <div class="tab-inner">{{ item.viewName }}</div>
                    </div>
                </div>
                <!--  :class="activestepname==3 ? 'no-tabs' : ''" -->
                <windowiframe v-if="activeurl" :url="activeurl" ref='windowrefiframe'></windowiframe>
                <el-empty v-else description="暂无内容" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, onBeforeUnmount, nextTick, inject, provide } from 'vue';
import { useRoute } from 'vue-router';
import {ElMessage} from 'element-plus'

import {getHashParams,updateUrlHash,systemsort} from '@/utils'
import {getPriceSessionInfo,getFullPriceViewInfo,closeAutoTask} from '@/assets/api/api.js'
import windowiframe from '@/components/windowiframe.vue'
import { set } from 'lodash';
const route = useRoute();
const store=inject('store')

// sessionId
const urlParams = new URLSearchParams(window.location.search);
let sessionId = urlParams.get("sessionId");
if (!sessionId) {
    ElMessage.warning("SessionId 获取失败，请检查");
}
// 
let allsteps = ref([
    {
        value:"1",
        label:"竞争定价法",
        index:2,
    },
     {
        value:"2",
        label:"价值定价法",
        index:2,
    },
     {
        value:"3",
        label:"成本定价法",
        index:2,
    },

    // {
    //     value:"1",
    //     label:"竞争分析",
    //     index:2,
    // },
    // {
    //     value:"2",
    //     label:"2C市场分析",
    //     index:3,
    // },
    // {
    //     value:"3",
    //     label:"成本定价",
    //     index:4,
    // },
    // {
    //     value:"4",
    //     label:"价格弹性",
    //     index:5,
    // },
    // {
    //     value:"5",
    //     label:"价格结构",
    //     index:6,
    // }
]);

let stepmenus = ref({
    // "1":[
    //     {
    //         label:"竞争分析",
    //         value:"1",
    //     },{
    //         label:"VRIO提取",
    //         value:"2",
    //     },{
    //         label:"头脑风暴",
    //         value:"3",
    //     },{
    //         label:"差异化价值",
    //         value:"4",
    //     }
    // ],
    // "4":[
    //     {
    //         label:"单个价格点弹性计算",
    //         value:"1",
    //     },{
    //         label:"单个价格弧弹性计算",
    //         value:"2",
    //     },{
    //         label:"两个产品交叉弹性计算",
    //         value:"3",
    //     }
    // ]
});

// 当前步骤
let activestepname = ref("");
// 当前菜单
let activemenuname = ref("");
// 当前tab
let activetabname = ref("");
let activeurl = ref("");
let allmenutabs = ref({});
let stepmenutabs = ref([]);
let sortstepmenutabs = ref([]);
let hasviewid=ref('');

// 父级监听message事件
// data:{toolname:'2C成本定价',status:'done'|'stop'|'error'}
// const toolarray=["竞争分析提取","VRIO提取","差异化价值提取","2C市场分析","点弹性计算","弧弹性计算","交叉弹性计算","成本定价"];
// const toolarray=["完整演示","VRIO提取","头脑风暴总结","差异化价值提取","提取细分市场信息","财务报表分析","成本定价计算","点弹性计算","弧弹性计算"];
const toolarray=["自动执行","差异化报告","特赞报告","提取细分市场报告","查看财务报表","财务报表分析","成本定价计算","总结报告"];
let nexttoolname = ref('');
let autotask=ref(false);
let windowrefiframe=ref(null)
sessionStorage.setItem('autotask','')
let taskstatus =ref('')
window.addEventListener('message', function(event) {
    const message = event.data;
    let {eventname,data}=message;
    console.log(autotask.value,'====我是来自父级的message',message)
    console.time("监听事件时间")
    console.timeEnd("监听事件时间")
    if (eventname === 'parentToChild') {
            nexttoolname.value='';
            taskstatus.value=data.status;
            if(taskstatus.value=='done'){
                let currenttoolindex = toolarray.findIndex((item)=>item===data.toolname);
                if(currenttoolindex>-1){
                    nexttoolname.value=toolarray[currenttoolindex+1]
                }
                console.log('nexttoolname.value',nexttoolname.value)
                console.log('autotask.value',autotask.value)
                // 
                if(currenttoolindex!=toolarray.length-1){//不是最后一项就继续往下触发
                    if(["特赞报告","提取细分市场报告",'查看财务报表'].includes(nexttoolname.value)){
                        nextTick(()=>{
                            console.log('查看财务报表查看财务报表查看财务报表====',autotask.value)
                            // const iframe = document.getElementById('inneridiframe');
                            // console.log('windowrefiframe.value',iframe)
                            // console.log('windowrefiframe.value',windowrefiframe.value?.autoTaskToChildren)
                            // setTimeout(()=>{
                            //     windowrefiframe.value?.autoTaskToChildren()
                            // },2000)
                            autoTaskToChildren()
                        })
                    }else{
                        preToAutoTask()
                    }
                    
                }
            }else{
                ElMessage.warning(`${data.toolname}执行${data.status==='stop'?'终止':'出错'}，任务已停止`);
                preCloseAutoTask()
            }
    }
    if(eventname === 'autotasktrigger'){
        console.log('=========',autotask.value)
        console.log('自动触发---现在传递到父级了',data)
        autotask.value=true;//为了调试
        // 自动触发，来自‘头脑风暴总结’和‘差异化价值提取’的触发
        autoTaskToParent()
    }
});
// 
const autoTaskToChildren=()=>{
    nextTick(() => {
    const MAX_RETRIES = 10; // 最大重试次数
    const RETRY_INTERVAL = 1000; // 1秒重试一次
    let retryCount = 0;
    const trySendMessage = () => {
        console.timeEnd('trySendMessage');
        const iframe = document.getElementById('inneridiframe');
        if (!iframe || !iframe.contentWindow) {
        console.log('iframe未找到或未加载完成，尝试重新获取...');
        retryCount++;

        if (retryCount >= MAX_RETRIES) {
            console.error(`已达到最大重试次数（${MAX_RETRIES}），停止尝试`);
            return;
        }
        // 1秒后再次尝试
        setTimeout(trySendMessage, RETRY_INTERVAL);
        return;
        }
        // iframe 可用，开始发送消息
        console.log('iframe 已加载，准备发送消息...');
        iframe.contentWindow.postMessage(
        { eventname: 'autotasktrigger_fromparent' },
        "*"
        );
    };
    console.time('trySendMessage');
    setTimeout(()=>{
        trySendMessage();
    },5000)
  });
};
// 向父级发起自动任务
const autoTaskToParent=()=>{
    console.log('autotask.value',autotask.value)
    console.log('nexttoolname.value',nexttoolname.value)
    if(autotask.value&&nexttoolname.value){
        // 
        let nowtxt=nexttoolname.value+"[自动触发]"
        if(nexttoolname.value==='头脑风暴总结'){
            nowtxt="请针对我酒店产品进行新增‘天文主题’项目进行总结[自动触发]"
        }
        console.log('作为子集发起了',{eventname:'triggerChangeEvent',data:{toolname:nexttoolname.value,nowtxt:nowtxt,sendnow:'true'}})
        if(["财务报表分析","成本定价计算"].includes(nexttoolname.value)){//财务报表 + 财务报表分析 太快了，一晃就跳走了。  在自动任务下 也加个 停留5s
            setTimeout(()=>{
                parent.postMessage({
                    eventname:'triggerChangeEvent',
                    data:{
                        toolname:nexttoolname.value,
                        nowtxt:nowtxt,
                        sendnow:'true'
                    }
                });
            },5000)
        }else{
            parent.postMessage({
                eventname:'triggerChangeEvent',
                data:{
                    toolname:nexttoolname.value,
                    nowtxt:nowtxt,
                    sendnow:'true'
                }
            });
        }
        
    }
    
};
const preToAutoTask=()=>{
    if (!sessionId) {
        ElMessage.warning("SessionId 获取失败，请检查");
        return;
    }
    console.time("获取autotask.value时间")
    console.log('自动任务下一步',nexttoolname.value)
    getPriceSessionInfo({
        sessionId,
    }).then((res) => {
        if (res.data?.priceSessionInfo) {
            console.log('自动任务下一步res.data?.priceSessionInfo',res.data?.priceSessionInfo.autoTask)
            autotask.value=res.data.priceSessionInfo.autoTask;
            console.timeEnd("获取autotask.value时间")
            sessionStorage.setItem('autotask',autotask.value?'1':'0')
            if(autotask.value&&nexttoolname.value){
                // 20250704 头脑风暴结果和差异化需要监听子iframe的 autotasktrigger 事件才能触发
                // 所以需要再下一个之前处理
                if(!["特赞报告","提取细分市场报告",'查看财务报表'].includes(nexttoolname.value)){
                    autoTaskToParent()
                }else{
                    // 
                    // console.log('20250704 头脑风暴结果和差异化需要监听子iframe的 autotasktrigger 事件才能触发,如果没有触发请检查')
                }
            }

        }
    }).catch((err) => {
        ElMessage.warning("获取内容失败" + err);
    });
}
const preCloseAutoTask=()=>{
    if (!sessionId) {
        ElMessage.warning("SessionId 获取失败，请检查");
        return;
    }
    closeAutoTask({
        sessionId,
    }).then((res) => {
        if (res.data?.closeAutoTask) {
            console.log('自动任务已关闭')
        }
    }).catch((err) => {
        ElMessage.warning("获取内容失败" + err);
    });
}
//
hasviewid.value=getHashParams('viewid');
watch(() => route.hash, () => {
  hasviewid.value=getHashParams('viewid');
  getViewsInfo();
});

let togglemenu=ref(false)

const getViewsInfo = () => {
    if (!sessionId) {
        ElMessage.warning("SessionId 获取失败，请检查");
        return;
    }
    getPriceSessionInfo({
        sessionId,
    }).then((res) => {
        activestepname.value = "1";
        activemenuname.value = "1";
        if (res.data?.priceSessionInfo) {
            let views = res.data.priceSessionInfo?.views ? JSON.parse(JSON.stringify(res.data.priceSessionInfo.views)) : [];
            // let views =[
            //         {
            //             "id": "5383c859-b555-42f3-96cc-7b04f5995729",
            //             "viewName": "产品差异化价值",
            //             "viewDesc": "分析产品差异化价值",
            //             "createdAt": 1753261713000,
            //             "viewUrl": "https://cloud.ketanyun.cn/hub-price/tezanReportCallBack",
            //             "index": 0,
            //             "step": "1-4",
            //             "__typename": "PriceSessionView"
            //         }]
            // 
            initContentData(JSON.parse(JSON.stringify(views)))
        }
    }).catch((err) => {
        ElMessage.warning("获取内容失败" + err);
    });
};
const initContentData=(views)=>{
    allmenutabs.value = transformData(views);
    let matchedview = "";
    if (hasviewid.value) {
        matchedview = views?.find((item) => item.id === hasviewid.value);
    }
    if (matchedview && matchedview.step) {
        changeMenu(matchedview.step);
    } else {
        let hightlightstep = getHighlightedStep(views);
        changeMenu(hightlightstep);
    }
}
// views 按照要求重新排序
const transformData=(data)=> {
    const result = {};
    // 首先按 step 分组
    data.forEach((item) => {
        if (!result[item.step]) {
            result[item.step] = [];
        }
        let viewGroup = result[item.step].find((group) => group.viewName === item.viewName);
        if (!viewGroup) {
            viewGroup = {
                viewName: item.viewName,
                index:item.index,
                viewitems: []
            };
            result[item.step].push(viewGroup);
        }
        viewGroup.viewitems.push(item);
    });
    // 对每个 step 下的 viewitems 按 createdAt 降序排序
    for (const step in result) {
        result[step].forEach(viewGroup => {
            viewGroup.viewitems.sort((a, b) => b.createdAt - a.createdAt);
        });
    }
    return result;
};



const getHighlightedStep = (views) => {
    if (views.length === 0) return '1-1';
    const maxCreatedAt = Math.max(...views.map(view => view.createdAt));
    const latestViews = views.find(view => view.createdAt === maxCreatedAt);
    return latestViews ? latestViews.step : '1-1';
};
const changeMenu = (steps, pageclick = false) => {
    if (steps.indexOf("-") > 0) {
        activestepname.value = steps.split('-')[0];
        activemenuname.value = steps.split('-')[1];
    } else {
        activestepname.value = steps;
    }
    // 20250704 bot隐藏菜单
    if(activestepname.value=='1'&&activemenuname.value=='3'){
        togglemenu.value=false
    }
    // 20250617 步骤四 重新加载
    if(activestepname.value==4&&pageclick){
        getViewsInfo()
        return
    }
    
    sortstepmenutabs.value = [];
    activetabname.value = '';
    activeurl.value = '';
    if (allmenutabs.value?.[steps]?.length) {
        sortstepmenutabs.value =allmenutabs.value[steps]?.sort((a, b) => a.index - b.index);
        let tabview ='';
        if(!pageclick){//如果不是页面点击，需要定位viewid
            if(hasviewid.value){//
                sortstepmenutabs.value.forEach((item)=>{
                    if((!tabview)&&item?.viewitems?.length){
                        if(item.viewitems.find((vitem)=>vitem.id===hasviewid.value)){
                            tabview=item
                        }
                    }
                })
                
            }
        }
        // // match到当前view路由
        changeTab(tabview);
    }
};

const  addHashParam=(key, value, replace = false)=>{
  const currentUrl = window.location.href;
  const hashIndex = currentUrl.indexOf('#');
  let newHash = '';

  if (hashIndex !== -1) {
    const hash = currentUrl.substring(hashIndex + 1);
    const params = new URLSearchParams(hash);
    if (replace) {
      params.set(key, value);
    } else {
      params.append(key, value);
    }

    newHash = params.toString();
  } else {
    newHash = `${key}=${value}`;
  }
  const newUrl = currentUrl.split('#')[0] + '#' + newHash;
  return newUrl;
}
const matchedCurrentView=(matchedtab)=>{
    let hassetitem="";
    
    if(matchedtab&&matchedtab.viewitems&&matchedtab.viewitems.length){
        sessionStorage.setItem('currenttabviews', JSON.stringify(matchedtab.viewitems));
        if(hasviewid.value){
            hassetitem=matchedtab.viewitems.find((item)=>item.id===hasviewid.value)
        }
        // 
        if(!hassetitem){
            hassetitem=hassetitem?hassetitem:matchedtab.viewitems[0];
        }
    }
    return hassetitem

};
const changeTab = (item,ispageclick) => {
    // 20250617// 20250617 为了兼容价格计算步骤viewid变化问题
    // if(ispageclick&&activestepname.value==4){
    //     // currentviews = JSON.parse(sessionStorage.getItem('currenttabviews'))
    //     // const currentstep=`${activestepname.value}-${activestepname.vaue}`
    //     // let iscurrentstep= currentviews.find((item)=>item.step==currentstep);
    //     // if(!iscurrentstep){//如果不是同一步骤，清空，重新获取，否则从已有的sessionid获取
    //     //     currentviews=[]
    //     // }
    //     getViewsInfo()
    //     return
    // }
    item = item ? item : sortstepmenutabs.value?.[0];
    if (!item) return;
    activetabname.value = item.viewName;
    sessionStorage.setItem('currenttabviews', JSON.stringify([]));
    sessionStorage.setItem('currenttabid', '');
    let hassetitem=matchedCurrentView(item);
    if(hassetitem){
        sessionStorage.setItem('currenttabid', hassetitem.id);
        activeurl.value = updateUrlHash(hassetitem.viewUrl, 'viewid', hassetitem.id);
        // 增加t
        activeurl.value = updateUrlHash(activeurl.value, 't', (new Date().getTime()));
    }
};
getViewsInfo();
</script>