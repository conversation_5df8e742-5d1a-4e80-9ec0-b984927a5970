<template>
  <div class="showoff-index-wrap">
    <div id="impress"
      data-transition-duration="1000"
      data-max-scale="3"
      data-min-scale="0"
      data-perspective="1000"
    >
      <!-- 全景视图 -->
      <!-- <div id="overview" class="step" data-x="0" data-y="0" data-z="0" data-scale="10"></div> -->

      <!-- 步骤1：中心位置 -->
      <div class="step" data-x="0" data-y="0" data-z="0" data-rotate-x="0" data-rotate-y="0">
        <h1>欢迎进入科探智能课程</h1>
        <p>课程+AI 我们将为您呈现新的上课模式</p>
      </div>

      <!-- 步骤2：向右移动 -->
      <div class="step" data-x="2000" data-y="0" data-z="0" data-rotate-y="0">
        <h2>更灵活的交互性</h2>
        <ul>
          <li>场景模拟</li>
          <li>问题预测</li>
          <li>结果分析</li>
        </ul>
      </div>

      <!-- 步骤3：向上移动 -->
      <div class="step" data-x="0" data-y="-2000" data-z="0" data-rotate-x="0">
        <h2>更完善的模型计算</h2>
        <p>开拓思维视野</p>
      </div>

      <!-- 步骤4：深度效果 -->
      <div class="step" data-x="0" data-y="0" data-z="2000" data-scale="2">
        <h2>结果更加灵活</h2>
        <p>头脑风暴级分析结果一次性输出</p>
      </div>

      <!-- 步骤5：右下角 -->
      <div class="step" data-x="2000" data-y="2000" data-z="0" data-rotate-y="45">
        <h2>3D展示效果</h2>
        <p>体验沉浸式教学</p>
      </div>

      <!-- 步骤6：左上角 -->
      <div class="step" data-x="-2000" data-y="-2000" data-z="0" data-rotate-x="45">
        <h2>多维度分析</h2>
        <p>从不同角度理解问题</p>
      </div>
    </div>

    <!-- 右下角导航提示 -->
    <div class="nav-controls">
      <div class="hint">使用键盘方向键 ← → ↑ ↓ 切换</div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import 'impress.js'

onMounted(() => {
  if (typeof window.impress === 'function') {
    window.impress().init()
  }
})
</script>

<style lang="scss" scoped>
$step-colors: (
  #FF9AA2, #FFB7B2, #FFDAC1, #E2F0CB, 
  #B5EAD7, #C7CEEA, #F8B195, #F67280,
  #C06C84, #6C5B7B, #355C7D, #A8E6CE,
  #DCEDC2
);

.showoff-index-wrap {
  overflow: hidden;
  background: linear-gradient(135deg, #1e5799 0%, #3a7bb8 20%, #5ba4d5 40%, #7db9e8 60%, #a8d0f5 80%, #c7e2ff 100%);
  color: #333; /* 文字颜色改为深色，提高可读性 */
  height: 100vh;

  #impress {
    min-height: 100vh;
    position: relative;

    .step {
      transition: opacity 1s;
      border: 1px solid rgba(0,0,0,0.1);
      border-radius: 10px;
      padding: 40px;
    //   width: 1024px;
    //   height: 768px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;

      @for $i from 1 through length($step-colors) {
        &:nth-child(#{$i + 1}) { /* +1 跳过全景视图步骤 */
          background-color: nth($step-colors, $i);
        }
      }

      h1, h2 {
        margin-bottom: 30px;
        color: #333;
      }

      p, li {
        font-size: 24px;
        line-height: 1.6;
      }
    }
  }

  /* 全景视图样式 */
  .impress-enabled {
    position: relative;
  }

  .impress-on-overview .step {
    opacity: 1;
    cursor: pointer;
  }

  /* 提示信息 */
  .hint {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    text-align: center;
    color: white;
    opacity: 0.7;
    font-size: 14px;
    z-index: 100;
    pointer-events: none;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  }
}
</style>