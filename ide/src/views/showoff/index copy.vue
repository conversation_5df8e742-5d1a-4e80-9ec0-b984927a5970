

<template>
  <div id="impress" data-transition-duration="1000">
    <!-- 第一页 - 中心位置 -->
    <div class="step" data-x="0" data-y="0" data-z="0" data-rotate-x="0" data-rotate-y="0">
      <h1>欢迎进入科探智能课程</h1>
      <p>课程+AI 我们将为您呈现新的上课模式</p>
    </div>

    <!-- 第二页 - 向右移动 -->
    <div class="step" data-x="1000" data-y="0" data-z="-100" data-rotate-y="45">
      <h2>更灵活的交互性</h2>
      <ul>
        <li>场景模拟</li>
        <li>问题预测</li>
        <li>结果分析</li>
      </ul>
    </div>

    <!-- 第三页 - 向上移动并旋转 -->
    <div class="step" data-x="0" data-y="-800" data-rotate-x="60">
      <h2>更完善的模型计算</h2>
      <p>开拓思维视野</p>
    </div>

    <!-- 第四页 - 深度效果 -->
    <div class="step" data-x="0" data-y="0" data-z="1000" data-scale="4">
      <h2>结果更加灵活</h2>
      <p>头脑风暴级分析结果一次性输出</p>
    </div>
  </div>
</template>


<script setup>
import { onMounted } from 'vue'
import 'impress.js'

onMounted(() => {
  // 初始化 impress.js
  if (typeof window.impress === 'function') {
    window.impress().init()
  }
})
</script>
<style>
/* 基础样式 */
body {
  font-family: Arial, sans-serif;
  overflow: hidden;
  background: linear-gradient(135deg, #1e5799 0%,#2989d8 50%,#207cca 51%,#7db9e8 100%);
  color: white;
  margin: 0;
  padding: 0;
  height: 100vh;
}

#impress {
  min-height: 100vh;
  position: relative;
}

.step {
  width: 800px;
  padding: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  text-align: center;
  transition: opacity 1s;
  margin: 0 auto;
}

.step h1, .step h2 {
  margin-bottom: 20px;
}

.step ul {
  text-align: left;
  display: inline-block;
}

/* 添加 impress.js 全景视图所需样式 */
.impress-enabled {
  position: relative;
}

.impress-on-overview .step {
  opacity: 1;
  cursor: pointer;
}

/* 提示信息 */
.hint {
  position: fixed;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
  color: white;
  opacity: 0.7;
  font-size: 14px;
  z-index: 100;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 900px) {
  .step {
    width: 90%;
    padding: 20px;
  }
}
</style>