

<template>
<div class="showoff-index-wrap">
    <div id="impress"
    data-transition-duration="1000"
    data-width="1024"
    data-height="768"
    data-max-scale="3"
    data-min-scale="0"
    data-perspective="1000"
    >
    <div id="overview" class="step" data-x="3000" data-y="1500" data-z="0" data-scale="10">
    </div>
    <div id="bored" class="step" data-x="-1000" data-y="-1500">
    </div>
    <div data-x="0" class="step" data-y="-1500">
    </div>
    <div data-x="1000" class="step" data-y="-1500">
    </div>
    <div id="title" class="step" data-x="0" data-y="0" data-scale="4">
    </div>
    <div id="its" class="step" data-x="850" data-y="3000" data-rotate="90" data-scale="5">
    </div>

    <div id="big" class="step" data-x="3500" data-y="2100" data-rotate="180" data-scale="6">
    </div>
    <div id="tiny" class="step" data-x="2825" data-y="2325" data-z="-3000" data-rotate="300" data-scale="1">
    </div>
    <div id="ing" class="step" data-x="3500" data-y="-850" data-z="0" data-rotate="270" data-scale="6">
    </div>
    <div id="imagination" class="step" data-x="6700" data-y="-300" data-scale="6">
    </div>
    <div id="source" class="step" data-x="6300" data-y="2000" data-rotate="20" data-scale="4">
    </div>
    <div id="one-more-thing" class="step" data-x="6000" data-y="4000" data-scale="2">
    </div>
    <div id="its-in-3d" class="step" data-x="6200" data-y="4300" data-z="-100" data-rotate-x="-40" data-rotate-y="10" data-scale="2">
    </div>
    

</div>
  <div id="impress" data-transition-duration="1000" v-if="false">
    <div id="overview" class="step" data-x="3000" data-y="1500" data-z="0" data-scale="10">
    </div>
    <!-- 第一页 - 中心位置 -->
    <div class="step" data-x="0" data-y="0" data-z="0" data-rotate-x="0" data-rotate-y="0">
      <h1>欢迎进入科探智能课程</h1>
      <p>课程+AI 我们将为您呈现新的上课模式</p>
    </div>
    <!-- 第二页 - 向右移动 -->
    <div class="step" data-x="1000" data-y="0" data-z="-100" data-rotate-y="45">
      <h2>更灵活的交互性</h2>
      <ul>
        <li>场景模拟</li>
        <li>问题预测</li>
        <li>结果分析</li>
      </ul>
    </div>
    <!-- 第三页 - 向上移动并旋转 -->
    <div class="step" data-x="0" data-y="-800" data-rotate-x="60">
      <h2>更完善的模型计算</h2>
      <p>开拓思维视野</p>
    </div>

    <!-- 第四页 - 深度效果 -->
    <div class="step" data-x="0" data-y="0" data-z="1000" data-scale="4">
      <h2>结果更加灵活</h2>
      <p>头脑风暴级分析结果一次性输出</p>
    </div>
  </div>

  <!-- 右下角导航按钮 -->
  <div class="nav-controls">
    <div class="hint">使用键盘方向键 ← → ↑ ↓ 切换</div>
  </div>
  </div>
</template>
<script setup>
import { onMounted, ref } from 'vue'
import 'impress.js'

const impressApi = ref(null)
onMounted(() => {
  if (typeof window.impress === 'function') {
    impressApi.value = window.impress()
    impressApi.value.init()
    
    // 监听步骤变化
    // document.addEventListener('impress:stepenter', (event) => {
    //   currentStep.value = parseInt(event.target.getAttribute('data-step'), 10) || 0
    // })
  }
})

</script>


<style lang="scss" scoped>
$step-colors: ( #FF9AA2, // 浅珊瑚粉
#FFB7B2, // 柔和水蜜桃
#FFDAC1, // 奶油杏色
#E2F0CB, // 薄荷绿
#B5EAD7, // 浅海蓝绿
#C7CEEA, // 淡紫罗兰
#F8B195, // 温暖鲑鱼粉
#F67280, // 柔和玫瑰红
#C06C84, // 淡紫红色
#6C5B7B, // 薰衣草紫
#355C7D, // 深海蓝
#A8E6CE, // 冰薄荷
#DCEDC2 // 淡黄绿
);
.showoff-index-wrap {
    overflow: hidden;
    background: linear-gradient( 135deg, #1e5799 0%, #3a7bb8 20%, #5ba4d5 40%, #7db9e8 60%, #a8d0f5 80%, #c7e2ff 100%);
    color: white;
    height: 100vh;
    #impress {
        min-height: 100vh;
        position: relative;
    }
    .step {
        transition: opacity 1s;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px 20px;
        width: 1024px;
        height: 768px;
        @for $i from 1 through length($step-colors) {
            &:nth-child(#{$i}) {
                background-color: nth($step-colors, $i);
            }
        }
    }
    /* 添加 impress.js 全景视图所需样式 */
    .impress-enabled {
        position: relative;
    }
    .impress-on-overview .step {
        opacity: 1;
        cursor: pointer;
    }
    /* 提示信息 */
    .hint {
        position: fixed;
        bottom: 20px;
        left: 0;
        right: 0;
        text-align: center;
        color: white;
        opacity: 0.7;
        font-size: 14px;
        z-index: 100;
        pointer-events: none;
    }
}
</style>