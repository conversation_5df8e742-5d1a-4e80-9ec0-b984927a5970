<template>
  <div class="showoff-index-wrap">
    <div id="impress"
      data-transition-duration="1000"
      data-width="1024"
      data-height="768"
      data-max-scale="3"
      data-min-scale="0"
      data-perspective="1000"
    >
      <!-- 全景视图 -->
      <div id="overview" class="step" data-x="0" data-y="0" data-z="0" data-scale="10"></div>

      <!-- 中心位置 -->
      <!-- <div class="step step-view step-view-1" data-x="0" data-y="0" data-z="0" data-rotate-x="0" data-rotate-y="0"  data-scale="1.5">
        
      </div> -->
      <div class="step step-view step-view-1" data-x="0" data-y="0" data-z="0" data-rotate="90" data-scale="2">
        <h1>欢迎进入科探智能课程</h1>
        <p>课程+AI 我们将为您呈现新的上课模式 <a href="javascript:;" @click="toStepInner('tiny')">进入详情</a></p>
    </div>
       <!-- 右下角 -->
      <div class="step step-view step-view-2" data-x="1800" data-y="-300" data-z="0" data-scale="2">
        <h2>3D展示效果</h2>
        <p>体验沉浸式教学</p>
      </div>

      <!-- 向右移动 -->
      <div class="step step-view  step-view-3" data-x="1000" data-y="-1800" data-z="0" data-scale="2"  data-rotate="180" >
        <h2>更灵活的交互性</h2>
        <ul>
          <li>场景模拟</li>
          <li>问题预测</li>
          <li>结果分析</li>
        </ul>
      </div>

      <!-- 步骤3：向上移动 -->
      <div class="step step-view  step-view-4" data-x="-1600" data-y="-2000" data-z="0" data-scale="3"  >
        <h2>更完善的模型计算</h2>
        <p>开拓思维视野</p>
      </div>

      <!-- 步骤4：深度效果 -->
      <div class="step step-view  step-view-5"  data-x="-1600" data-y="1200" data-z="0" data-scale="1.5"  >
        <h2>结果更加灵活</h2>
        <p>头脑风暴级分析结果一次性输出</p>
      </div>

      <!-- 步骤6：左上角 -->
      <div class="step step-view  step-view-6"  data-x="-2000" data-y="0" data-z="0" data-scale="2"   data-rotate="-180"  >
        <h2>多维度分析</h2>
        <p>从不同角度理解问题</p>
      </div>

      <!-- 新增步骤7：左下角，带旋转动画 -->
      <div class="step step-view  step-view-7"  data-x="100" data-y="2100" data-z="0" data-scale="2"   data-rotate="-180" >
        <h2 class="fade-in">动态数据可视化</h2>
        <p class="slide-up">实时数据流处理与分析</p>
        <div class="pulse-circle"></div>
      </div>

      <!-- 新增步骤8：右上角，带缩放动画 -->
      <div class="step step-view  step-view-8"   data-x="2000" data-y="1700" data-z="0" data-scale="2"   data-rotate="90" >
        <h2 class="zoom-in">智能反馈系统</h2>
        <p class="typewriter">即时学习效果评估与建议</p>
      </div>
      <div id="tiny" class="step  step-view" data-x="0" data-y="0"  data-z="-3000" data-rotate="300" data-scale="1">
            我是内部详情
       </div>
        <template v-if="false">
           
        
      <!-- 新增步骤9：前上方，带3D翻转 -->
      <div class="step step-view" data-x="2000" data-y="-2800" data-z="1000" data-rotate-x="180" data-rotate-y="90">
        <h2 class="flip-in">个性化学习路径</h2>
        <p class="flip-in-delay">AI驱动的自适应学习方案</p>
      </div>

      <!-- 新增步骤10：后下方，带弹性动画 -->
      <div class="step step-view" data-x="1200" data-y="100" data-z="-800" data-rotate-y="90">
        <h2 class="bounce-in">协作学习空间</h2>
        <p class="bounce-in-delay">多用户实时互动与协作</p>
      </div>

      <!-- 新增步骤11：中心上方，带粒子动画 -->
      <div class="step step-view" data-x="-5300" data-y="-2000" data-z="0" data-scale="1.5" data-rotate-y="90">
        <h2 class="particle-text">未来教育体验</h2>
        <p class="wave-text">探索教育与技术的完美融合</p>
        <div class="particles"></div>
      </div>
      </template>
    </div>

    <!-- 右下角导航提示 -->
    <div class="nav-controls">
      <div class="hint">使用键盘方向键 ← → ↑ ↓ 切换</div>
    </div>
    
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import 'impress.js'
onMounted(() => {
  if (typeof window.impress === 'function') {
    window.impress().init()
  }
})
const toStepInner=(id)=>{
     window.location.hash = id;
    // console.log('window.impress().goto',window.impress().goto)
    // console.log('document.getElementById(id)',document.getElementById(id))
    // window.impress().goto(document.getElementById(id),1000); 
}
</script>
