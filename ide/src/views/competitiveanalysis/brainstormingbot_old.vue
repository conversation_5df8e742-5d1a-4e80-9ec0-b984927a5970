<template>
  <div class="differentiation-container brainstorming-bot-container" v-loading="loading">
    <div class="version-change">
      <h2></h2>
      <el-select v-model="currentversion" @change="changeTabView" v-if="false&&viewitems.length>1">
        <el-option
          v-for="(item,index) in viewitems"
          :key="item.id"
          :label="versionLabel(item,index)"
          :value="item.id">
        </el-option>
      </el-select>
    </div>
    <div class="iframe-erea" :class="viewitems.length>1?'multi-version':''" v-if="activeurl">
      <iframe 
            :src="activeurl"
            width="100%" 
            height="100%"
            ref="inneriframe"
            @load="iframeLoaded"
            frameborder="0"></iframe>
    </div>
    <div class="bot-list-wrap" v-else>
      <div class="bl-title">请选择一个智能体</div>
      <div class="bot-list">
        <div class="single-bot-item"
        v-for="(item ,index) in brainstorming_bots"
        :key="item.id+index"
        @click="chooseBot(item)"
        >
          <div class="sinbot-icon" >
              <img class="img-icon"  :src="toSVGDataUrl(fimg.bot1)"/>
          </div>
          <div class="sinbot-des">{{item.name}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch } from 'vue';
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel,toSVGDataUrl} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import bot1 from "@/assets/images/bots/bot1.svg?raw"

let fimg=ref({
  bot1
})
// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})

let activeurl=ref("")
let brainstorming_bots=ref([]);
let hasviewid = getHashParams('viewid')
let baseData=ref({
  uri:"",
  brainstorming_bots:[ {
    "id" : "01JZSWBYKJYP5W80S32D5QRYFY",
    "name" : "(无角色)头脑风暴"
  }, {
    "id" : "01JZW7ZZVR8A624PJRQ0PBQQFB",
    "name" : "单钻模型头脑风暴"
  }, {
    "id" : "01JZW7ZZVR8A624PJRQ0PBQQFB",
    "name" : "双钻模型头脑风暴"
  } ]
})

let loading =ref(false)
let dataid =ref('');

const iframeLoaded = () => {
  console.log('botiframe已经加载')
  loading.value = false
}
// 父级页面中的代码
window.addEventListener('message', function(event) {
  let {eventname,data}=event.data;
  console.log("event.data",event.data)
  if(eventname==="brainstormingbot"){
    if(data.conversation&&activeurl.value){
      let newurl = addOrUpdateUrlParam(activeurl.value,'conversation',data.conversation);
      if(newurl){
        preSave(newurl)
      }
    }else if(data.status=='done'){
      console.log('作为子集发起了',{eventname:'triggerChangeEvent',data:{toolname:"打开头脑风暴总结页面",nowtxt:'',sendnow:'true'}})
      parent.parent.postMessage({
          eventname:'triggerChangeEvent',
          data:{
              toolname:"打开头脑风暴总结页面",
              nowtxt:'',
              sendnow:'true'
          }
      });
    }
  }
});
// 
const addOrUpdateUrlParam=(url, searchKey, searchValue)=>{
  const urlObj = new URL(url);
  const params = urlObj.searchParams;
  params.set(searchKey, searchValue);
  urlObj.search = params.toString();
  return urlObj.toString();
}
const getData=(ischange)=>{
  dataid.value='';
  baseData.value=[];
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      toGetViewData(hasviewid)
    }
};
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
    dataid.value=res?.data?.priceSessionVIew?.id
    if(res?.data?.priceSessionVIew?.data){
        let chartdatas=[]
        try{
            chartdatas=JSON.parse(res.data.priceSessionVIew.data)
        }catch (err){
            ElMessage.warning("数据解析出错")
        }
        console.log('chartdatas',chartdatas)
        if(chartdatas){
          baseData.value=chartdatas
          countInitInfo()
        }else{
            ElMessage.warning("数据解析失败")
        }
    }
  }).catch((err)=>{
    loading.value=false;
      ElMessage.warning("获取内容失败"+err)
  });
}
const countInitInfo=()=>{
  let data=JSON.parse(JSON.stringify(baseData.value));
  if(data?.uri){//如果有uri则直接打开uri
    activeurl.value=data?.uri
  }else if(data?.brainstorming_bots?.length){//如果没有则展示智能体
    brainstorming_bots.value=data.brainstorming_bots
  }
}
const changeTabView=()=>{
    getData('ischange')
}

// countInitInfo()//测试
getData();
const preSave=(uri)=>{
  loading.value=true;
  updatePriceSessionView({
    entity:{
      id:dataid.value,
      data:JSON.stringify({
        uri,
        brainstorming_bots:baseData.value?.brainstorming_bots?.length?(baseData.value.brainstorming_bots):[]
      })
    }
  }).then((res)=>{
    // loading.value=false;
    if(res?.data?.updatePriceSessionView){
      // 暂无需处理
    }
  }).catch((err)=>{
    loading.value=false;
    ElMessage.error('发起失败: ' + err)
  })
};
const chooseBot=(botitem)=>{
  const {id}=botitem;
  activeurl.value=`/copilot-center/airobotsquare?id=${id}`;
}
</script>

