<template>
  <div class="differentiation-container brainstorming-bot-container" v-loading="loading">
    <div class="version-change">
      <h2></h2>
      <el-select v-model="currentversion" @change="changeTabView" v-if="false&&viewitems.length>1">
        <el-option
          v-for="(item,index) in viewitems"
          :key="item.id"
          :label="versionLabel(item,index)"
          :value="item.id">
        </el-option>
      </el-select>
    </div>
    <div class="role-tag-wrap">
      <div class="role-tag-part" v-loading="autoloading">
        <div class="role-part">
          <div class="rp-input">
            <el-input
              placeholder="您可以继续增加岗位"
              ref="inputroleRef"
              v-model="inputrolevalue"
              class="w-20"
              size="large"
              @keyup.enter="handleRoleConfirm"
              @blur="handleRoleConfirm"
              >
              </el-input>
              <el-dropdown split-button type="primary" @click="handleRoleConfirm">
                添加岗位
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click.native.stop="autoGenerate('generateBrainstormingUserType')">自动生成</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
               </el-dropdown>
              <!--<el-button type="primary" @click="handleRoleConfirm">添加岗位</el-button>
              <el-button type="primary" @click="autoGenerate('generateBrainstormingUserType')"></el-button> -->
          </div>
          
              <div class="tag-area">
                <template v-for="(item,index) in userType" :key="item.name+index">
                  <div >
                  <el-popover :width="180" trigger="hover"
                    v-if="item.desc"
                      >
                      <template #reference>
                        <el-tag
                          :disable-transitions="false"
                          size="large"
                          :closable="userType.length>1"
                          :effect="users?.find((ritem)=>ritem.userTypeName===item.name)?'dark':''"
                          @close="handleRoleDelete(item)"
                          @click="toggleToRole(item)"
                          >
                          {{item.name}}
                      </el-tag>
                      </template>
                      <div class="tag-desc">{{item.desc}}</div>
                    </el-popover>
                    <el-tag
                        v-else
                          :disable-transitions="false"
                          size="large"
                          :closable="userType.length>1"
                          :effect="users?.find((ritem)=>ritem.userTypeName===item.name)?'dark':''"
                          @close="handleRoleDelete(item)"
                          @click="toggleToRole(item)"
                          >
                          {{item.name}}
                      </el-tag>
                  </div>
                </template>
                
                
              </div>
          
        </div>
        <div class="tag-part">
          <div class="rp-input">
            <el-input
                    placeholder="您可以继续增加标签"
                    ref="InputRef"
                    v-model="inputValue"
                    class="w-20"
                    size="large"
                    @keyup.enter="handleInputConfirm"
                    @blur="handleInputConfirm"
                    />
                    <el-dropdown split-button type="primary" @click="handleRoleConfirm">
                添加标签
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click.native.stop="autoGenerate('generateBrainstormingTags')">自动生成</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
               </el-dropdown>
                    <!-- <el-button type="primary" @click="handleInputConfirm">添加标签</el-button>
                  <el-button type="primary" @click="autoGenerate('generateBrainstormingTags')">自动生成</el-button> -->

          </div>
          
              <div class="tag-area">
                <template v-for="(item,index) in tags"
                        :key="item.name+index">
                  <div >
                  <el-popover :width="180" trigger="hover"
                    v-if="item.desc"
                      >
                      <template #reference>
                        <el-tag
                        :closable="tags.length>1"
                        :disable-transitions="false"
                        size="large"
                        @close="handleDelete(item)"
                        @click="toggleToTag(item)"
                        >
                        {{item.name}}
                    </el-tag>
                      </template>
                      <div class="tag-desc">{{item.desc}}</div>
                    </el-popover>
                    <el-tag
                        v-else
                        :closable="tags.length>1"
                        :disable-transitions="false"
                        size="large"
                        @close="handleDelete(item)"
                        @click="toggleToTag(item)"
                        >
                        {{item.name}}
                    </el-tag>
                  </div>
                </template>
                 
              </div>
         
        </div>
      </div>
      <div class="role-tag-component">
        <div class="role-tag-title">
          <div>{{users?.length?'':''}}</div>
          <div><el-button type="primary" @click="addNewLinkTwice" v-if="users?.length<6">创建虚拟对话角色</el-button></div>
        </div>
        <div class="role-com-wrap" v-if="users?.length">
          <div class="role-card-item"
            v-for="(item,index) in users"
            :key="item.userTypeName+item.name+index">
            <div class="base-info">
              <div class="r-del" @click="handleComDelete(index)">
              <el-icon ><DeleteFilled /></el-icon>
            </div>
            <div class="r-avater unselectable" >
              <el-icon @click="changeImage(item)"><Refresh /></el-icon>
              <img :src="fimgbase64[item.image]" >
            </div>
            <div class="r-info">
              <div class="ri-item" v-if="false">
                <div class="ritem-label"><span>*</span>姓名</div>
                <div class="ritem-value"><el-input v-model="item.name" placeholder="请为您创建的虚拟角色取名"></el-input> </div>
              </div>
              <div class="ri-item">
                <div class="ritem-label"><span>*</span>岗位</div>
                <div class="ritem-value">
                  <el-select
                    v-model="item.userTypeName"
                    filterable
                    allow-create
                    default-first-option
                    :reserve-keyword="false"
                    @change="(e)=>handleCreate(e,'usertype',item)"
                    placeholder="请选择或创建一个岗位"
                  >
                    <el-option
                      v-for="(ritem,rindex) in userType"
                      :key="ritem.name+rindex"
                      :disabled="disabledUserCount(ritem)"
                      :label="ritem.name"
                      :value="ritem.name"
                    />
                  </el-select>
                </div>
              </div>
              <div class="ri-item">
                <div class="ritem-label"><span>*</span>标签</div>
                <div class="ritem-value">
                  <el-select
                    v-model="item.tagName"
                    filterable
                    multiple
                    allow-create
                    default-first-option
                    :reserve-keyword="false"
                    placeholder="请选择或创建一个标签"
                    @change="(e)=>handleCreate(e,'tag',item)"
                  >
                    <el-option
                      v-for="(ritem,rindex) in tags"
                      :key="ritem.name+rindex"
                      :label="ritem.name"
                      :value="ritem.name"
                    />
                  </el-select>
                </div>
              </div>
            </div>
            </div>
            <div class="base-desc">
              <div class="show-desc" v-if="item.desc&&(item?.userTypeName&&item?.tagName?.length)">
                <el-input v-model="item.desc" placeholder="请输入虚拟对话角色的描述信息" type="textarea" :rows="5"></el-input>
                <!-- {{item.desc}} -->
              </div>
              <div class="show-btn" v-else>
                <el-button type="primary" @click="getUserDecs(item)" :disabled="!(item?.userTypeName&&item?.tagName?.length)" title="请先选择岗位与标签">展示虚拟对话角色描述</el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="role-com-empty-wrap" v-else>
          <el-empty description="暂无虚拟对话角色" :image-size="120"/>
        </div>
      </div>
      <div class="role-tag-tochat">
        <el-button type="primary" @click="preSave">开始对话</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch } from 'vue';
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

import {getPriceViewInfo,updatePriceSessionView,getCopilitGenerate} from '@/assets/api/api.js'
import {getHashParams,versionLabel,toSVGDataUrl,toImageDataUrl,debounceCustom} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import avater1 from '@/assets/images/avater/avater1.svg?raw'
import avater2 from '@/assets/images/avater/avater2.svg?raw'
import avater3 from '@/assets/images/avater/avater3.svg?raw'
import avater4 from '@/assets/images/avater/avater4.svg?raw'
import avater5 from '@/assets/images/avater/avater5.svg?raw'
import avater6 from '@/assets/images/avater/avater6.svg?raw'
import { set } from 'lodash';

const router = useRouter();
const store = useStore();

let fimg=ref({
  avater1,
  avater2,
  avater3,
  avater4,
  avater5,
  avater6,
})
let fimgbase64=ref({})
const togetpngbase64=()=>{
  let alist=['avater1','avater2','avater3','avater4','avater5','avater6'];
  alist.forEach((item,index)=>{
    fimgbase64.value['avater'+(index+1)]=toSVGDataUrl(fimg.value[item])
  })
};
togetpngbase64()

// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})

let hasviewid = getHashParams('viewid')
let baseData=ref({})
let loading =ref(false)
let dataid =ref('');
let userType=ref([
])
let tags=ref([
])
let users = ref([])
// 
const toggleToRole=(item)=>{
  let matchedindx = users.value?.findIndex((ritem)=>ritem.userTypeName===item.name)
  let length=users.value?.length
  if(matchedindx>-1){//删除
    users.value?.splice(matchedindx,1)
  }else{
    if(!(users.value?.length<6)){
      ElMessage.warning("您最多只能创建【六】个虚拟对话角色")
      return
    }
    users.value.push({userTypeName:item.name,image:'avater'+(length+1),name:"",tagName:[]})
  }
};
const toggleToTag=(item)=>{
  let filled = false
  users.value.forEach((uitem)=>{
    if(!filled){
      if(uitem.tagName?.length){
        filled=true
      }else{
        uitem.tagName=[item.name]
      }
    }
  })
};
const addNewLinkTwice=()=>{
  if(users.value?.length<6){
    users.value.push({
      name:"",
      desc:"",
      userTypeName:'',
      tagName:[],
      image:"avater"+(users.value?.length+1)
    })
  }else{
    ElMessage.warning("您最多只能创建【六】个虚拟对话角色")
  }
}
// 
const getData=(ischange)=>{
  dataid.value='';
  baseData.value=[];
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      toGetViewData(hasviewid)
    }
    console.log(currentversion.value)
    console.log(hasviewid)
};
const toGetViewData=(viewId)=>{

  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    // loading.value=false;
    dataid.value=res?.data?.priceSessionVIew?.id
    if(res?.data?.priceSessionVIew?.data){
        let chartdatas=[]
        try{
            chartdatas=JSON.parse(res.data.priceSessionVIew.data)
        }catch (err){
            ElMessage.warning("数据解析出错")
        }
        console.log('chartdatas',chartdatas)
        if(chartdatas){
          baseData.value=chartdatas
          countInitInfo()
        }else{
            ElMessage.warning("数据解析失败")
        }
    }
  }).catch((err)=>{
    loading.value=false;
      ElMessage.warning("获取内容失败"+err)
  });
}
const countInitInfo=()=>{
  let data=JSON.parse(JSON.stringify(baseData.value));
  // 20250815 增加来自chat返回编辑的按钮
  const urlParams= new URLSearchParams(window.location.search);
  const frombotchat=urlParams.get("frombotchat");
  // 
  if((!frombotchat)&&(data?.chatSessionId)){
    const parentUrl = window.parent.location.href;
    const parentUrlObj = new URL(parentUrl);
    let parentSessionId=parentUrlObj.searchParams.get('sessionId')
    const parentViewId = getHashParams('viewid',window.parent.location.hash);
    const id=store.getters.getCopilotBrainstormingId
    if(parentSessionId&&parentViewId&&id){
      let queryrouter={ 
          id,
          parentSessionId,
          // conversation:data?.chatSessionId?data.chatSessionId:"",
          parentViewId
        } 
      queryrouter.conversation=data.chatSessionId
      
      console.log('11111',{ 
        query:queryrouter
      })
      // 暂无需处理
      router.push({ 
        name:'/brainstormingbotchat',
        query: queryrouter
      })
    }
    // 
    return
  }
  loading.value=false
  userType.value=data?.userTypes?data.userTypes:[];
  tags.value=data?.tags?data.tags:[];
  users.value=[]
  if(data?.users?.length){
    data.users.forEach((item)=>{
      let {name,desc,userType,tags,image}=item;
      let newitem={
        name,
        desc,
        image,
        userTypeName:userType?.name,
        tagName:tags.map((titem)=>(titem.name))
      }
      users.value.push(newitem)
    })
  }
}
const changeTabView=()=>{
    getData('ischange')
}

// countInitInfo()//测试
getData();
const urlParams= new URLSearchParams(window.location.search);
const isbackfromchat= urlParams.get('frombotchat')
if(isbackfromchat){
  getData("ischange");
}
const inputrolevalue = ref('')
const inputroleref = ref(null) 
const inputValue = ref('')
const InputRef = ref(null) 
const changeImage=(item)=>{
  const currentNum = parseInt(item.image.replace('avater', ''));
  const nextNum = currentNum % 6 + 1;
  item.image = `avater${nextNum}`;
};
const handleCreate=(val,type,itemdata)=>{
  if(type==='usertype'){
    if (!userType.value?.some(item => item.name === val)) {
      userType.value.push({
        name: val
      });
    }
  }else if(type==='tag'){
    if(typeof val==='string'){
      if (!tags.value?.some(item => item.name === val)) {
        tags.value.push({
          name: val
        });
      }
    }
  }
  // 修改后触发desc获取
  getUserDecs(itemdata)
};
  
const getUserDecs=debounceCustom((itemdata)=>{
  
  let {userTypeName,tagName}=itemdata;
  let userTypeobj= userType.value?.find((item)=>item.name===userTypeName)
  let tagslist=tags.value?.filter((item)=>tagName?.includes(item.name))
  
  if(!(userTypeobj?.name&&tagslist?.length)){
    return
  }
  getCopilitGenerate({
    type:'generateSelfIntroduce',
    data:JSON.stringify({
      userType:userTypeobj,
      tags:tagslist
    })
  }).then((res)=>{
    autoloading.value=false;
    if(res?.data?.generate?.text){
      try{
        let newtype =JSON.parse(res.data.generate.text);
        if(newtype?.name||newtype?.desc){
          itemdata.name=newtype?.name?newtype.name:'';
          itemdata.desc=newtype?.desc?newtype.desc:'';
        }
      }catch(err){
        ElMessage.warning("自动生成获取格式问题"+res.data.generate.text)
      }
    }
  }).catch((err)=>{
    ElMessage.warning("自动生成失败"+err)
    autoloading.value=false;
  })
},500)
const handleComDelete=(index)=>{
  users.value.splice(index, 1);
};
const disabledUserCount=(ritem)=>{
  let flag =false
  if(users.value?.find((uitem)=>uitem.userTypeName===ritem.name)){
    flag=true
  }
  return flag
};
const handleDelete = (item) => {
  const delrole = tags.value.splice(tags.value.findIndex((aitem)=>aitem.name===item.name), 1)[0];
  users.value=users.value.filter((item) => item.tagName !== delrole?.name);
}
const handleInputConfirm = () => {
  if (inputValue.value) {
      if(!(tags.value.find((aitem)=>aitem.name===inputValue.value))){
        tags.value.push({name:inputValue.value})
        inputValue.value = '';
      }else{
          ElMessage.warning("已存在，不可重复添加")
      }
  }
  
};
const handleRoleDelete = (item) => {
  const delrole = userType.value.splice(userType.value.findIndex((aitem)=>aitem.name===item.name), 1)[0];
  users.value=users.value.filter((item) => item.userTypeName !== delrole?.name);
};
const handleRoleConfirm = () => {
  if (inputrolevalue.value) {
      if(!(userType.value.find((aitem)=>aitem.name===inputrolevalue.value))){
        userType.value.push({name:inputrolevalue.value})
        inputrolevalue.value = '';
      }else{
          ElMessage.warning("已存在，不可重复添加")
      }
  }
  
};
const preSave=()=>{
  if(!users.value?.length){
    ElMessage.warning({
      message: '您至少需要创建一个虚拟角色，以完成对话',
      offset: 300,
    })
    return
  }
  let flag =true;
  users.value.forEach((item)=>{
    if(flag){
      if(!(item.userTypeName&&item?.tagName?.length&&item?.desc?.length)){
        flag=false
      }
    }
  })
  if(!flag){
    ElMessage.warning({
      message: "每个虚拟角色的【岗位】、【标签】、【角色描述】均需要创建",
      offset: 300,
    })
    return
  }
  let {differentiated,blackbord,entered,summaryDimension,chatSessionId,summaryTopic}=baseData.value;
  let newusers = [];
  users.value.forEach((itemdata)=>{
    let {userTypeName,tagName,name,desc,image}=itemdata;
    let userTypeobj= userType.value?.find((item)=>item.name===userTypeName)
    let tagslist=tags.value?.filter((item)=>tagName?.includes(item.name))
    let newitem={
      name,
      desc,
      image,
      tags:tagslist.map((item)=>({name:item.name,desc:item.desc})),
      userType:{
        name:userTypeobj.name,
        desc:userTypeobj.desc
      }
    }
    newusers.push(newitem)
  })
  loading.value=true;
  
  updatePriceSessionView({
    entity:{
      id:dataid.value,
      data:JSON.stringify({
        userTypes:userType.value.map((item)=>({name:item.name,desc:item.desc})),
        tags:tags.value.map((item)=>({name:item.name,desc:item.desc})),
        users:newusers,
        blackbord:blackbord?blackbord:[],
        differentiated:differentiated?differentiated:[],
        summaryDimension:summaryDimension?summaryDimension:{},
        chatSessionId:chatSessionId?chatSessionId:"",
        summaryTopic:summaryTopic?summaryTopic:[],
        entered:true
      })
    }
  }).then((res)=>{
    // loading.value=false;
    if(res?.data?.updatePriceSessionView){
      const parentUrl = window.parent.location.href;
      const parentUrlObj = new URL(parentUrl);
      let parentSessionId=parentUrlObj.searchParams.get('sessionId')
      const parentViewId = getHashParams('viewid',window.parent.location.hash);
      const id=store.getters.getCopilotBrainstormingId
      if(parentSessionId&&parentViewId&&id){
        let queryrouter={ 
            id,
            parentSessionId,
            parentViewId
          } 
        if(chatSessionId){
          queryrouter.conversation=chatSessionId
        }else{
          queryrouter.createNew=true
        }
         console.log('2222',{ 
          query: queryrouter
        })
        // 暂无需处理
        router.push({ 
          name:'/brainstormingbotchat',
          query:queryrouter
          // query: { 
          //   // createNew:!(entered||differentiated?.length),
          //   createNew:createNew,
          //   id,
          //   parentSessionId,
          //   conversation:chatSessionId?chatSessionId:"",
          //   parentViewId
          // } 
        })
      }else{
        ElMessage.warning("获取父级信息失败")
        console.log("parentSessionId======",parentSessionId)
        console.log("parentViewId======",parentViewId)
        console.log("id======",id)
      }
    }
  }).catch((err)=>{
    loading.value=false;
    ElMessage.error('发起失败: ' + err)
  })
};

// type= generateBrainstormingUserType ｜ generateBrainstormingTags
let autoloading=ref(false)
const autoGenerate=(type)=>{
  autoloading.value=true
  let data = {}
  if(type==='generateBrainstormingUserType'){
    data={userType:userType.value}
  }else if(type==='generateBrainstormingTags'){
    data={tags:tags.value}
  }
  getCopilitGenerate({
    type,
    data:JSON.stringify(data)
  }).then((res)=>{
    autoloading.value=false;
    if(res?.data?.generate?.text){
      try{
        let newtype =JSON.parse(res.data.generate.text);
        if(newtype){
          if(type==='generateBrainstormingUserType'){
            console.log('newtype',newtype)
            let newusertype = newtype?.userTypes;
            if(newusertype?.length){
              newusertype.forEach((item)=>{
                 if(!userType.value?.find((uitem)=>uitem.name===item.name)){
                  userType.value.push(item)
                 }
              })
            }
          }else if(type==='generateBrainstormingTags'){
            console.log('newtype',newtype)
            let newusertype = newtype?.tags;
            if(newusertype?.length){
              newusertype.forEach((item)=>{
                 if(!tags.value?.find((uitem)=>uitem.name===item.name)){
                  tags.value.push(item)
                 }
              })
            }
          }
        }
      }catch(err){
        ElMessage.warning("自动生成获取格式问题"+res.data.generate.text)
      }
      // userType.value=userType.value.concat()
    }
  }).catch((err)=>{
    ElMessage.warning("自动生成失败"+err)
    autoloading.value=false;
  })
}
</script>

