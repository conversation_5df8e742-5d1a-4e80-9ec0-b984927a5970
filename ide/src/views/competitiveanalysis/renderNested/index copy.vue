<template>
<div>
    <div v-if="isObject(props.value)" class="nested-object">
        <div v-for="(val, key) in props.value" :key="key" class="nested-item">
        <div class="nested-key">{{ key }}:</div>
        <div 
            class="nested-value" 
            :class="{ 'editable': isSimpleValue(val) }"
            @click="isSimpleValue(val) && handleClick(val, key, null)"
        >
            <RenderNestedValue 
            :value="val"
            :path="[...props.path, key]"
            @edit="emit('edit', $event)"
            @add="emit('add', $event)"
            @delete="emit('delete', $event)"
            />
        </div>
        </div>
    </div>
    <ul v-else-if="Array.isArray(props.value)" class="nested-array">
        <li 
        v-for="(item, idx) in props.value"
        :key="idx"
        :class="{ 'editable': isSimpleValue(item) }"
        @click="isSimpleValue(item) && handleClick(item, null, idx)"
        >
        <RenderNestedValue 
            :value="item"
            :path="[...props.path, idx]"
            @edit="emit('edit', $event)"
            @add="emit('add', $event)"
            @delete="emit('delete', $event)"
        />
        <el-icon 
            v-if="isSimpleValue(item)"
            class="add" 
            @click.stop="handleAdd(null, idx)"
        ><CirclePlusFilled /></el-icon>
        <el-icon 
            v-if="isSimpleValue(item)"
            class="del" 
            @click.stop="handleDelete(null, idx)"
        ><RemoveFilled /></el-icon>
        </li>
    </ul>
    <span v-else>{{ props.value }}</span>
</div>
  
</template>
<script>
export default {
  name: 'RenderNestedValue',
}
</script>
<script setup>
const props = defineProps({
  value: {
    type: [String, Number, Boolean, Array, Object],
    required: true
  },
  path: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['edit', 'add', 'delete'])

const isSimpleValue = (value) => {
  return value !== null && typeof value !== 'object' && !Array.isArray(value)
}

const isObject = (value) => {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}

const handleClick = (value, subKey, index) => {
    console.log('12222',value)
  if (isSimpleValue(value)) {
    const path = [...props.path]
    if (subKey !== null) path.push(subKey)
    if (index !== null) path.push(index)
    console.log('12222',path)
    emit('edit', path)
  }
}

const handleAdd = (subKey, index) => {
  const path = [...props.path]
  if (subKey !== null) path.push(subKey)
  if (index !== null) path.push(index)
  emit('add', path)
}

const handleDelete = (subKey, index) => {
  const path = [...props.path]
  if (subKey !== null) path.push(subKey)
  if (index !== null) path.push(index)
  emit('delete', path)
}
</script>