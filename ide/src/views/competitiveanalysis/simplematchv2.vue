<template>
  <div class="carousel-container">
    <!--  hotel-comparison silde-comparison -->
    <div class="comparison-header">
      <h1 class="title">VRIO分析</h1>
    </div>
    
    <div class="carousel-wrapper">
      <el-icon class="nav-button left" :class="isAtStart?'disabled':''" @click="scrollLeft" :disabled="isAtStart"><ArrowLeftBold /></el-icon>
      <el-icon class="nav-button right" :class="isAtEnd?'disabled':''" @click="scrollRight" :disabled="isAtEnd"><ArrowRightBold /></el-icon>
      <div class="carousel" ref="carousel">
        <div 
          v-for="(competitor, competitorIndex) in allothers" 
          :key="'hotel'+competitorIndex" 
          class="comparison-column competitor"
        >
          <div class="hotel-card">
            <div class="comparison-items">
              <div 
                v-for="key in allmatchkeys" 
                :key="key" 
                class="comparison-item"
              >
                <div class="item-title">
                  {{ key}}
                </div>
                <div class="item-value">
                  <div v-if="isObject(competitor[key])">
                    <div 
                      v-for="(subValue, subKey) in competitor[key]" 
                      :key="subKey" 
                      class="nested-item"
                    >
                      <div class="nested-key">{{ subKey }}:</div>
                      <div 
                        class="nested-value"
                        :class="{ 'editable': isSimpleValue(subValue) }"
                        @click="isSimpleValue(subValue) && handleEdit('competitor', competitorIndex, key, subKey, null, subValue)"
                      >
                        <ul v-if="Array.isArray(subValue)">
                          <li 
                            v-for="(item, idx) in subValue" 
                            :key="idx"
                            :class="{ 'editable': isSimpleValue(item) }"
                            @click.stop="isSimpleValue(item) && handleEdit('competitor', competitorIndex, key, subKey, idx, item)"
                          >
                            {{ item }}
                            <el-icon class="add" @click.stop="preHandleAdd('competitor', competitorIndex, key, subKey, idx, item)"><CirclePlusFilled /></el-icon>
                            <el-icon class="del"   @click.stop="preHandleDel('competitor', competitorIndex, key, subKey, idx, item)"><RemoveFilled /></el-icon>
                          </li>
                        </ul>
                        <div v-else>{{ subValue }}</div>
                      </div>
                    </div>
                  </div>
                  <ul v-else-if="Array.isArray(competitor[key])">
                    <li 
                      v-for="(item, index) in competitor[key]" 
                      :key="index"
                      :class="{ 'editable': isSimpleValue(item) }"
                      @click="isSimpleValue(item) && handleEdit('competitor', competitorIndex, key, null, index, item)"
                    >
                      {{ item }}
                      <el-icon class="add" @click.stop="preHandleAdd('competitor', competitorIndex, key, null, index, item)"><CirclePlusFilled /></el-icon >
                      <el-icon class="del"  @click.stop="preHandleDel('competitor', competitorIndex, key, null, index, item)"><RemoveFilled /></el-icon>
                    </li>
                  </ul>
                  <div 
                    v-else
                    class="editable"
                    @click="handleEdit('competitor', competitorIndex, key, null, null, competitor[key])"
                  >
                    {{ competitor[key] }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted,watch, onUnmounted } from 'vue';
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router';
const route = useRoute();
let hashviewid=ref('')
hashviewid.value=getHashParams('viewid');
watch(() => route.hash, () => {
  hashviewid.value=getHashParams('viewid');
  getViewsInfo();
});
const baseData = ref({
  "公司列表": [
    {
      "公司名称": "隐奢逸境（A公司）",
      "VRIO资源列表": [
        "创始人潘小科近20年的高端酒店行业经验及国际品牌管理背景（价值性、稀有性、不可模仿性、组织支持）",
        "独特的'隐奢逸境'品牌理念与'璞修'文化定位（价值性、稀有性、文化不可模仿性）",
        "玉龙雪山3100米海拔稀缺景观资源（地理稀有性、自然价值性）",
        "ROI导向的轻资产运营管理体系（组织流程优化、管理不可模仿性）",
        "业主金茂集团的深度合作关系及零投资接盘能力（资源获取的稀有性）"
      ]
    },
    {
      "公司名称": "Amanvista Resort",
      "VRIO资源列表": [
        "丽江古城狮子山顶全景视角（地理制高点稀缺性）",
        "传统纳西建筑风格与静谧氛围（文化体验的不可复制性）",
        "国际奢华品牌运营标准（组织管理优势）"
      ]
    },
    {
      "公司名称": "Ficus Grove Hotel",
      "VRIO资源列表": [
        "悦榕庄品牌背书与私密度假服务体系（品牌溢价价值）",
        "束河古镇毗邻优势与纳西庭院建筑群（区域协同效应）",
        "高端SPA与养生项目组合（服务差异化）"
      ]
    },
    {
      "公司名称": "Cerulean Lodge",
      "VRIO资源列表": [
        "茶马古道文化主题沉浸式体验（文化IP的独特性）",
        "古城南门核心区位（交通便利性价值）",
        "非物质文化遗产活动策划能力（内容创新性）"
      ]
    },
    {
      "公司名称": "Globemar Hotel",
      "VRIO资源列表": [
        "世界文化遗产地内的建筑融合（文化地标属性）",
        "纳西庭院式建筑集群（规模效应价值）",
        "民族风情与现代舒适度融合设计（产品复合优势）"
      ]
    },
    {
      "公司名称": "Aurea Serenity Lijiang",
      "VRIO资源列表": [
        "直面玉龙雪山主峰的观景设计（视觉冲击价值）",
        "现代极简主义与少数民族元素混搭（设计创新性）",
        "雪山高尔夫球场联动资源（配套协同性）"
      ]
    }
  ]
});

const meinfo = ref([])
const allothers = ref([])
const allmatchkeys = ref([])

const carousel = ref(null);
const scrollPosition = ref(0);
const cardWidth = 320; // 300px宽度 + 20px边距

const isAtStart = computed(() => scrollPosition.value === 0);
const isAtEnd = computed(() => {
  if (!carousel.value) return true;
  return scrollPosition.value >= carousel.value.scrollWidth - carousel.value.clientWidth;
});
// 判断是否为简单值
const isSimpleValue = (value) => {
  return value !== null && !isObject(value) && !Array.isArray(value)
}
// 判断是否为对象
const isObject = (value) => {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}
// 初始化数据
const countInitInfo = () => {
  allothers.value = baseData.value['公司列表']?baseData.value['公司列表']:[]
  let keys = []
  meinfo.value?.forEach(m => keys.push(...Object.keys(m)))
  allothers.value?.forEach(m => keys.push(...Object.keys(m)))
  allmatchkeys.value = [...new Set(keys)]
};
let dataid =ref('')
const getData=()=>{
    dataid.value='';
    baseData.value={};
    if(hashviewid.value){
        getPriceViewInfo({viewId:hashviewid.value}).then((res)=>{
          dataid.value=res?.data?.priceSessionVIew?.id
            if(res?.data?.priceSessionVIew?.data){
                let chartdatas=[]
                try{
                    chartdatas=JSON.parse(res.data.priceSessionVIew.data)
                }catch (err){
                    ElMessage.warning("数据解析出错")
                }
                console.log('chartdataschartdatas',chartdatas)
                if(chartdatas&&chartdatas[0]){
                  baseData.value=chartdatas[0]
                  countInitInfo()
                }else{
                    ElMessage.warning("数据解析失败")
                }
            }
        }).catch((err)=>{
            ElMessage.warning("获取内容失败"+err)
        });
    }
}
// countInitInfo()//测试
getData()

const preHandleAdd=(type, competitorIndex, mainKey, subKey, arrayIndex, value)=>{
  if (type === 'me') {
      if (arrayIndex !== null) {
        // 更新数组项
        if(subKey !== null && subKey !== undefined){
          meinfo.value[competitorIndex][mainKey][subKey].splice(arrayIndex+1,0,'')
          // meinfo.value[competitorIndex][mainKey][subKey][arrayIndex] = newValue
        }else{
          meinfo.value[competitorIndex][mainKey].splice(arrayIndex+1,0,'')
          // meinfo.value[competitorIndex][mainKey][arrayIndex] = newValue
        }
      }
    } else if (type === 'competitor') {
      if (arrayIndex !== null) {
        if(subKey !== null && subKey !== undefined){
           allothers.value[competitorIndex][mainKey][subKey].splice(arrayIndex+1,0,'')
        }else{
          allothers.value[competitorIndex][mainKey].splice(arrayIndex+1,0,'')
        }
      } 
    }
    handleEdit(type, competitorIndex, mainKey, subKey, arrayIndex+1, '','isadded')
     
}
const preHandleDel=(type, competitorIndex, mainKey, subKey, arrayIndex, value)=>{
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
      spliceListItem(type, competitorIndex, mainKey, subKey, arrayIndex, value);
      // 
      toSaveChange()
    }).catch(() => {
      // 取消编辑
    })
    console.log('Updated baseData:', JSON.parse(JSON.stringify(allothers.value)))
}
const spliceListItem=(type, competitorIndex, mainKey, subKey, arrayIndex, value)=>{
  if (type === 'me') {
      if (arrayIndex !== null) {
        // 更新数组项
        if(subKey !== null && subKey !== undefined){
          meinfo.value[competitorIndex][mainKey][subKey].splice(arrayIndex,1)
          // meinfo.value[competitorIndex][mainKey][subKey][arrayIndex] = newValue
        }else{
          meinfo.value[competitorIndex][mainKey].splice(arrayIndex,1)
          // meinfo.value[competitorIndex][mainKey][arrayIndex] = newValue
        }
      }
    } else if (type === 'competitor') {
      if (arrayIndex !== null) {
        if(subKey !== null && subKey !== undefined){
          allothers.value[competitorIndex][mainKey][subKey].splice(arrayIndex,1)
        }else{
          allothers.value[competitorIndex][mainKey].splice(arrayIndex,1)
        }
      } 
    }
}
// 处理编辑点击
const handleEdit = (type, competitorIndex, mainKey, subKey, arrayIndex, value,isadded) => {
  ElMessageBox.prompt('请输入新值:', '编辑', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: value,
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        const newValue = instance.inputValue
        saveEdit(type, competitorIndex, mainKey, subKey, arrayIndex, newValue)
      }
      done()
    }
  }).catch(() => {
    // 取消编辑
    if(isadded=='isadded'){
      spliceListItem(type, competitorIndex, mainKey, subKey, arrayIndex, value)
    }
  })
}
// 保存编辑
const saveEdit = (type, competitorIndex, mainKey, subKey, arrayIndex, newValue) => {
  try {
    // 更新数据
    if (type === 'me') {
      if (arrayIndex !== null) {
        // 更新数组项
        if(subKey !== null && subKey !== undefined){
          meinfo.value[competitorIndex][mainKey][subKey][arrayIndex] = newValue
          // baseData.value['本公司'][mainKey][subKey][arrayIndex] = newValue
        }else{
          meinfo.value[competitorIndex][mainKey][arrayIndex] = newValue
          // baseData.value['本公司'][mainKey][arrayIndex] = newValue
        }
      } else if (subKey !== null && subKey !== undefined) {
        // 更新对象属性
        meinfo.value[competitorIndex][mainKey][subKey] = newValue
        // baseData.value['本公司'][mainKey][subKey] = newValue
      } else {
        // 更新简单属性
        meinfo.value[competitorIndex][mainKey] = newValue
        // baseData.value['本公司'][mainKey] = newValue
      }
    } else if (type === 'competitor') {
      if (arrayIndex !== null) {
        // 更新竞争对手的数组项
        if(subKey !== null && subKey !== undefined){
           allothers.value[competitorIndex][mainKey][subKey][arrayIndex] = newValue
        }else{
          allothers.value[competitorIndex][mainKey][arrayIndex] = newValue
        }
      } else if (subKey !== null && subKey !== undefined) {
        // 更新竞争对手的对象属性
        allothers.value[competitorIndex][mainKey][subKey] = newValue
        // baseData.value['竞争对手'][competitorIndex][mainKey][subKey] = newValue
      } else {
        // 更新竞争对手的简单属性
        allothers.value[competitorIndex][mainKey] = newValue
        // baseData.value['竞争对手'][competitorIndex][mainKey] = newValue
      }
    }
    
    // 输出更新后的baseData到控制台
    console.log('Updated baseData:', JSON.parse(JSON.stringify(allothers.value)))
    
    // ElMessage.success('修改成功')
    toSaveChange()
  } catch (error) {
    ElMessage.error('修改失败: ' + error.message)
  }
};
const toSaveChange=()=>{
  updatePriceSessionView({
    entity:{
      id:dataid.value,
      data:JSON.stringify([{公司列表:allothers.value}])
    }
  }).then((res)=>{
    if(res?.data?.updatePriceSessionView){
      ElMessage.success('修改成功')
    }
  }).catch((err)=>{
    ElMessage.error('修改失败: ' + err)
  })
}


const scrollLeft = () => {
  if (scrollPosition.value > 0) {
    scrollPosition.value = Math.max(0, scrollPosition.value - cardWidth);
    carousel.value.scrollTo({
      left: scrollPosition.value,
      behavior: 'smooth'
    });
  }
};

const scrollRight = () => {
  if (!carousel.value) return;
  const maxScroll = carousel.value.scrollWidth - carousel.value.clientWidth;
  if (scrollPosition.value < maxScroll) {
    scrollPosition.value = Math.min(maxScroll, scrollPosition.value + cardWidth);
    carousel.value.scrollTo({
      left: scrollPosition.value,
      behavior: 'smooth'
    });
  }
};

// 响应式调整
// const handleResize = () => {
//   if (carousel.value) {
//     scrollPosition.value = Math.min(scrollPosition.value, carousel.value.scrollWidth - carousel.value.clientWidth);
//     carousel.value.scrollLeft = scrollPosition.value;
//   }
// };

// onMounted(() => {
//   window.addEventListener('resize', handleResize);
// });

// onUnmounted(() => {
//   window.removeEventListener('resize', handleResize);
// });
</script>

