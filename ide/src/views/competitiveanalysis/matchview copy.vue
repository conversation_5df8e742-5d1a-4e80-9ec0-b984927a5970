<template>
  <div class="carousel-container" 
   v-loading="loading"
    >
    
    <div class="comparison-header">
      <div class="version-change">
        <div>
          <el-select v-model="currentversion" @change="changeTabView"  v-if="false&&viewitems.length>1">
            <el-option
              v-for="(item,index) in viewitems"
              :key="item.id"
              :label="versionLabel(item,index)"
              :value="item.id">
            </el-option>
          </el-select>
        </div>
        <div class="nav-btns-wrap">
          <el-icon class="nav-button left" :class="isAtStart?'disabled':''"  @click="scrollLeft" :disabled="isAtStart"><ArrowLeftBold /></el-icon>
          <el-icon class="nav-button right" :class="isAtEnd&&!isAtStart?'disabled':''"   @click="scrollRight" :disabled="isAtEnd"><ArrowRightBold /></el-icon>
        </div>
      </div>
    </div>
    <div class="carousel-wrapper" >
      <div class="carousel" ref="carousel">
        <div class="comparison-column our-hotel" v-if="meinfo?.[0]">
          <div class="hotel-card">
            <div class="hotel-info">
              <h2 class="hotel-name">{{ meinfo[0].公司名称 }}</h2>
              <div class="price" v-if="false">¥{{ meinfo[0].平均价格 }}</div>
              <template  v-if="(typeof meinfo[0].平均价格 !== 'object')">
                <div class="price">¥{{ meinfo[0].平均价格 }}</div>
              </template>
              <template v-else>
                <div v-for="(price, roomType) in meinfo[0].平均价格" :key="roomType" class="price">
                  {{ roomType }}: ¥{{ price }}
                </div>
              </template>
              <div class="location">{{ meinfo[0].地理位置 }}</div>
            </div>
            <div class="comparison-items">
              <template
              v-for="key in allmatchkeys" >
              <div 
                v-if="!['公司名称'].includes(key)"
                :key="key" 
                class="comparison-item"
              >
                <div class="item-title">
                  {{ key}}
                </div>
                <div class="item-value">
                  <div v-if="isObject(meinfo[0][key])">
                    <div 
                      v-for="(subValue, subKey) in meinfo[0][key]" 
                      :key="subKey" 
                      class="nested-item"
                    >
                      <div class="nested-key">{{ subKey }}:</div>
                      <div 
                        class="nested-value"
                        :class="{ 'editable': isSimpleValue(subValue) }"
                        @click="isSimpleValue(subValue) && handleEdit('me',0, key, subKey, null, subValue)"
                      >
                        <ul v-if="Array.isArray(subValue)">
                          <li 
                            v-for="(item, idx) in subValue" 
                            :key="idx"
                            :class="{ 'editable': isSimpleValue(item) }"
                            @click.stop="isSimpleValue(item) && handleEdit('me', 0,key, subKey, idx, item)"
                          >
                            {{ item }}
                            <el-icon class="add" @click.stop="preHandleAdd('me', 0,key, subKey, idx, item)"><CirclePlusFilled /></el-icon>
                            <el-icon class="del"   @click.stop="preHandleDel('me', 0,key, subKey, idx, item)"><RemoveFilled /></el-icon>
                          </li>
                        </ul>
                        <div v-else>{{ subValue }}</div>
                      </div>
                    </div>
                  </div>
                  <ul v-else-if="Array.isArray(meinfo[0][key])">
                    <li 
                      v-for="(item, index) in meinfo[0][key]" 
                      :key="index"
                      :class="{ 'editable': isSimpleValue(item) }"
                      @click="isSimpleValue(item) && handleEdit('me',0, key, null, index, item)"
                    >
                      {{ item }}
                      <el-icon class="add" @click.stop="preHandleAdd('me',0, key, null, index, item)"><CirclePlusFilled /></el-icon >
                      <el-icon class="del"  @click.stop="preHandleDel('me',0, key, null, index, item)"><RemoveFilled /></el-icon>
                    </li>
                  </ul>
                  <div 
                    v-else
                    class="editable"
                    @click="handleEdit('me',0, key, null, null, meinfo[0][key])"
                  >
                    {{ meinfo[0][key] }}
                  </div>
                </div>
              </div>
              </template>
            </div>
          </div>
        </div>
        <!-- 右侧：竞争对手 -->
        <div class="comparison-column competitor"
          v-for="(competitor, competitorIndex) in allothers"
          :key="'hotel'+competitorIndex"
        >
          <div class="hotel-card">
            <div class="hotel-info">
              <h2 class="hotel-name">{{ competitor.公司名称 }}</h2>
              <div class="price" v-if="false">¥{{ competitor.平均价格 }}</div>
              <template v-if="(typeof competitor.平均价格 !== 'object')">
                <div class="price">¥{{ competitor.平均价格 }}</div>
              </template>
              <template v-else>
                <div v-for="(price, roomType) in competitor.平均价格" :key="roomType" class="price">
                  {{ roomType }}: ¥{{ price }}
                </div>
              </template>
              <div class="location">{{ competitor.地理位置 }}</div>
            </div>
            <div class="comparison-items">
              <template
              v-for="key in allmatchkeys" >
              <div 
                v-if="!['公司名称'].includes(key)"
                :key="key" 
                class="comparison-item"
              >
                <div class="item-title">
                  {{ key}}
                </div>
                <div class="item-value">
                  <div v-if="isObject(competitor[key])">
                    <div 
                      v-for="(subValue, subKey) in competitor[key]" 
                      :key="subKey" 
                      class="nested-item"
                    >
                      <div class="nested-key">{{ subKey }}:</div>
                      <div 
                        class="nested-value"
                        :class="{ 'editable': isSimpleValue(subValue) }"
                        @click="isSimpleValue(subValue) && handleEdit('competitor', competitorIndex, key, subKey, null, subValue)"
                      >
                        <ul v-if="Array.isArray(subValue)">
                          <li 
                            v-for="(item, idx) in subValue" 
                            :key="idx"
                            :class="{ 'editable': isSimpleValue(item) }"
                            @click.stop="isSimpleValue(item) && handleEdit('competitor', competitorIndex, key, subKey, idx, item)"
                          >
                            {{ item }}
                            <el-icon class="add" @click.stop="preHandleAdd('competitor', competitorIndex, key, subKey, idx, item)"><CirclePlusFilled /></el-icon >
                            <el-icon class="del"  @click.stop="preHandleDel('competitor', competitorIndex, key, subKey, idx, item)"><RemoveFilled /></el-icon>
                          </li>
                        </ul>
                        <div v-else>{{ subValue }}</div>
                      </div>
                    </div>
                  </div>
                  <ul v-else-if="Array.isArray(competitor[key])">
                    <li 
                      v-for="(item, index) in competitor[key]" 
                      :key="index"
                      :class="{ 'editable': isSimpleValue(item) }"
                      @click="isSimpleValue(item) && handleEdit('competitor', competitorIndex, key, null, index, item)"
                    >
                      {{ item }}
                      <el-icon class="add" @click.stop="preHandleAdd('competitor', competitorIndex, key, null, index, item)"><CirclePlusFilled /></el-icon >
                      <el-icon class="del"  @click.stop="preHandleDel('competitor', competitorIndex, key, null, index, item)"><RemoveFilled /></el-icon>
                    </li>
                  </ul>
                  <div 
                    v-else
                    class="editable"
                    @click="handleEdit('competitor', competitorIndex, key, null, null, competitor[key])"
                  >
                    {{ competitor[key] }}
                  </div>
                </div>
              </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted,watch } from 'vue'
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
// 父级监听message事件
window.addEventListener('message', function(event) {
  const message = event.data;
  console.log('event.data',event.data)
  if (message.eventname === 'parentToChild') {
    console.log('收到父页面消息:', message);
  }
});
// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})


const changeTabView=()=>{
    getData('ischange')
}
let hasviewid = getHashParams('viewid')
// const baseData = ref({})
const baseData = ref(
    {
        "竞争对手":
        {
            "酒店名称": "丽江金茂璞修雪山酒店",
            "VRIO资源":
            [
                "成功运营的经验",
                "雪山星空资源"
            ],
            "公司名称": "金茂",
            "酒店设施": "未提及",
            "地理位置": "丽江",
            "服务": "未提及",
            "平均价格": "未提及"
        },
        "本公司":{
          "酒店":[{
            "酒店名称": "泸沽湖隐奢逸境酒店",
            "VRIO资源":
            [
                "专业的跨领域团队",
                "深挖项目独特资源，导入匹配的文化主题与体验",
                "整合多方资源，提升运营效率",
                "以数据和ROI为导向，不断调整优化"
            ],
            "公司名称": "隐奢逸境",
            "酒店设施":
            [
                "由三栋废弃别墅改造而成",
                "设计融入摩梭文化与自然元素",
                "每间客房均设有全景露台",
                "大堂落地窗外是四季色彩斑斓的“情人滩”草场",
                "泸沽湖碧波荡漾伸向远方",
                "神圣的格姆女神山矗立眼前",
                "湖面上方常出现宛如瀑布倾泻的云海奇观",
                "夜晚可以仰望浩瀚星空"
            ],
            "地理位置": "四川凉山彝族自治州宁蒗泸沽湖畔两座古老的摩梭村寨之间",
            "服务":
            [
                "提供针对女性旅行者的特色Spa和课程",
                "策划环保、文化活动",
                "营造泸沽湖的品牌形象"
            ],
            "平均价格": "未提及"
        }]

        }
        
    }
)
const carousel = ref(null);
const scrollPosition = ref(0);
const cardWidth = 320; // 300px宽度 + 20px边距

const isAtStart = computed(() => scrollPosition.value === 0);
const isAtEnd = computed(() => {
  if (!carousel.value) return true;
  return scrollPosition.value >= carousel.value.scrollWidth - carousel.value.clientWidth;
});
// 响应式数据
const meinfo = ref([])
const allothers = ref([])
const allmatchkeys = ref([])
const selectedCompetitorIndex = ref(0)
const selectedCompetitor = ref(null)

// 判断是否为简单值
const isSimpleValue = (value) => {
  return value !== null && !isObject(value) && !Array.isArray(value)
}

// 判断是否为对象
const isObject = (value) => {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}

// 初始化数据
const countInitInfo = () => {
  meinfo.value = [baseData.value['本公司']]
  if(baseData.value['竞争对手'].constructor === Array){
    allothers.value = baseData.value['竞争对手']?baseData.value['竞争对手']:[]
  }else if(baseData.value['竞争对手'].constructor === Object){
    allothers.value = baseData.value['竞争对手']?[baseData.value['竞争对手']]:[]
  }
  
  
  let keys = []
  meinfo.value?.forEach(m => keys.push(...Object.keys(m)))
  allothers.value?.forEach(m => keys.push(...Object.keys(m)))
  allmatchkeys.value = [...new Set(keys)]
  
  // 初始化选择第一个竞争对手
  // updateComparison()
}
let dataid =ref('')
let loading =ref(false)
const getData=(ischange)=>{
  dataid.value='';
  baseData.value={}
  if(ischange=='ischange'&&currentversion.value){
    toGetViewData(currentversion.value)
  }else if(hasviewid){
    toGetViewData(hasviewid)
  }
};
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
    dataid.value=res?.data?.priceSessionVIew?.id
      if(res?.data?.priceSessionVIew?.data){
          let chartdatas=[]
          try{
              chartdatas=JSON.parse(res.data.priceSessionVIew.data)
          }catch (err){
              ElMessage.warning("数据解析出错")
          }
          if(chartdatas&&chartdatas[0]){
            baseData.value=chartdatas[0];
            countInitInfo()
          }else{
              ElMessage.warning("数据解析失败")
          }
      }
  }).catch((err)=>{
    loading.value=false;
      ElMessage.warning("获取内容失败"+err)
  });
}
countInitInfo()//测试
// getData()

// 更新选择的竞争对手
const updateComparison = () => {
  selectedCompetitor.value = allothers.value[selectedCompetitorIndex.value]
}
const preHandleAdd=(type, competitorIndex, mainKey, subKey, arrayIndex, value)=>{
  
  if (type === 'me') {
      if (arrayIndex !== null) {
        // 更新数组项
        if(subKey !== null && subKey !== undefined){
          meinfo.value[competitorIndex][mainKey][subKey].splice(arrayIndex+1,0,'')
        }else{
          meinfo.value[competitorIndex][mainKey].splice(arrayIndex+1,0,'')
        }
      }
    } else if (type === 'competitor') {
      if (arrayIndex !== null) {
        if(subKey !== null && subKey !== undefined){
           allothers.value[competitorIndex][mainKey][subKey].splice(arrayIndex+1,0,'');
        }else{
          allothers.value[competitorIndex][mainKey].splice(arrayIndex+1,0,'')
        }
      } 
    }
    handleEdit(type, competitorIndex, mainKey, subKey, arrayIndex+1, '','isadded')
}
const preHandleDel=(type, competitorIndex, mainKey, subKey, arrayIndex, value)=>{
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
      spliceListItem(type, competitorIndex, mainKey, subKey, arrayIndex, value);
      // 
      toSaveChange()
    }).catch(() => {
      // 取消编辑
    })
}
const spliceListItem=(type, competitorIndex, mainKey, subKey, arrayIndex, value)=>{
  if (type === 'me') {
      if (arrayIndex !== null) {
        // 更新数组项
        if(subKey !== null && subKey !== undefined){
          meinfo.value[competitorIndex][mainKey][subKey].splice(arrayIndex,1)
        }else{
          meinfo.value[competitorIndex][mainKey].splice(arrayIndex,1)
        }
      }
    } else if (type === 'competitor') {
      if (arrayIndex !== null) {
        if(subKey !== null && subKey !== undefined){
          allothers.value[competitorIndex][mainKey][subKey].splice(arrayIndex,1)
        }else{
          allothers.value[competitorIndex][mainKey].splice(arrayIndex,1)
        }
      } 
    }
}

// 处理编辑点击
const handleEdit = (type, competitorIndex, mainKey, subKey, arrayIndex, value,isadded) => {
  
  ElMessageBox.prompt('请输入新值:', '编辑', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: value,
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        const newValue = instance.inputValue
        saveEdit(type, competitorIndex, mainKey, subKey, arrayIndex, newValue)
      }
      done()
    }
  }).catch(() => {
    // 取消编辑
    if(isadded=='isadded'){
      spliceListItem(type, competitorIndex, mainKey, subKey, arrayIndex, value)
    }
  })
}

// 保存编辑
const saveEdit = (type, competitorIndex, mainKey, subKey, arrayIndex, newValue) => {
  try {
    // 更新数据
    if (type === 'me') {
      if (arrayIndex !== null) {
        // 更新数组项
        if(subKey !== null){
          meinfo.value[competitorIndex][mainKey][subKey][arrayIndex] = newValue
          baseData.value['本公司'][mainKey][subKey][arrayIndex] = newValue
        }else{
          meinfo.value[competitorIndex][mainKey][arrayIndex] = newValue
          baseData.value['本公司'][mainKey][arrayIndex] = newValue
        }
      } else if (subKey !== null) {
        // 更新对象属性
        meinfo.value[competitorIndex][mainKey][subKey] = newValue
        baseData.value['本公司'][mainKey][subKey] = newValue
      } else {
        // 更新简单属性
        meinfo.value[competitorIndex][mainKey] = newValue
        baseData.value['本公司'][mainKey] = newValue
      }
    } else if (type === 'competitor') {
      if (arrayIndex !== null) {
        // 更新竞争对手的数组项
        if(subKey !== null){
          allothers.value[competitorIndex][mainKey][subKey][arrayIndex] = newValue
          baseData.value['竞争对手'][competitorIndex][mainKey][subKey][arrayIndex] = newValue
        }else{
          allothers.value[competitorIndex][mainKey][arrayIndex] = newValue
          baseData.value['竞争对手'][competitorIndex][mainKey][arrayIndex] = newValue
        }
      } else if (subKey !== null) {
        // 更新竞争对手的对象属性
        allothers.value[competitorIndex][mainKey][subKey] = newValue
        baseData.value['竞争对手'][competitorIndex][mainKey][subKey] = newValue
      } else {
        // 更新竞争对手的简单属性
        allothers.value[competitorIndex][mainKey] = newValue
        baseData.value['竞争对手'][competitorIndex][mainKey] = newValue
      }
    }
    // 输出更新后的baseData到控制台
    // console.log('Updated baseData:', JSON.parse(JSON.stringify(baseData.value)))
    toSaveChange()
    // ElMessage.success('修改成功')
  } catch (error) {
    ElMessage.error('修改失败: ' + error.message)
  }
}
const toSaveChange=()=>{
  console.log('Updated baseData:', JSON.parse(JSON.stringify(baseData.value)))
  updatePriceSessionView({
    entity:{
      id:dataid.value,
      data:JSON.stringify([baseData.value])
    }
  }).then((res)=>{
    if(res?.data?.updatePriceSessionView){
      ElMessage.success('修改成功')
    }
  }).catch((err)=>{
    ElMessage.error('修改失败: ' + err)
  })
};
const scrollLeft = () => {
  if (scrollPosition.value > 0) {
    scrollPosition.value = Math.max(0, scrollPosition.value - cardWidth);
    carousel.value.scrollTo({
      left: scrollPosition.value,
      behavior: 'smooth'
    });
  }
};

const scrollRight = () => {
  if (!carousel.value) return;
  const maxScroll = carousel.value.scrollWidth - carousel.value.clientWidth;
  if (scrollPosition.value < maxScroll) {
    scrollPosition.value = Math.min(maxScroll, scrollPosition.value + cardWidth);
    carousel.value.scrollTo({
      left: scrollPosition.value,
      behavior: 'smooth'
    });
  }
};
</script>