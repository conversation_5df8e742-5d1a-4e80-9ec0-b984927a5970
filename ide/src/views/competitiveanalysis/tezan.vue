<template>
    <div class="tezan-wrap">
        <iframe 
            :src="iframeSrc"
            width="100%" 
            height="100%"
            frameborder="0"></iframe>
     </div>
</template>

<script setup>
import { ref, watch, onMounted,nextTick, inject } from 'vue'

const iframeSrc = ref('https://atypica.musedam.cc/study/jaeQbfuUTytwuj7m/share?replay=1');
// const iframeSrc = ref('http://localhost:8001/hub-price/test');


const toAutoParent=()=>{
  console.log('自动触发由[特赞]触发啦',{eventname:'autotasktrigger',data:{toolname:'',nowtxt:"特赞",sendnow:'true'}})
  console.timeEnd('我在特赞，等一会');
  parent.postMessage({eventname:'autotasktrigger',data:{toolname:'',nowtxt:"特赞",sendnow:'true'}});
}
window.addEventListener('message', function(event) {
    const message = event.data;
    let {eventname}=message;
    console.log('在特赞监听到父级传递的信息了======eventname',eventname)
    
    if(eventname === 'autotasktrigger_fromparent'){
      if(sessionStorage.getItem('autotask')==='1'){
        console.time('我在特赞，等一会');
        setTimeout(()=>{
          toAutoParent()//90S后打开图表
        },10000)
      }
        
     }
    
});
</script>