<template>
  <div class="carousel-container"  v-loading="loading">
    
    <div class="comparison-header">
      <div class="version-change">
        <div>
          <el-select v-model="currentversion" @change="changeTabView"  v-if="false&&viewitems.length>1">
            <el-option
              v-for="(item,index) in viewitems"
              :key="item.id"
              :label="versionLabel(item,index)"
              :value="item.id">
            </el-option>
          </el-select>
        </div>
        <div class="nav-btns-wrap">
          <el-icon class="nav-button left" :class="isAtStart?'disabled':''"  @click="scrollLeft" :disabled="isAtStart"><ArrowLeftBold /></el-icon>
          <el-icon class="nav-button right" :class="isAtEnd&&!isAtStart?'disabled':''"   @click="scrollRight" :disabled="isAtEnd"><ArrowRightBold /></el-icon>
        </div>
      </div>
    </div>
    <div class="carousel-wrapper">
      <div class="carousel" ref="carousel">
        <div 
          v-for="(competitor, competitorIndex) in allothers" 
          :key="'hotel'+competitorIndex" 
          class="comparison-column competitor"
        >
          <div class="hotel-card">
            <div class="comparison-items">
              <div 
                v-for="key in allmatchkeys" 
                :key="key" 
                class="comparison-item"
              >
                <div class="item-title">
                  {{ key}}
                </div>
                <div class="item-value">
                  <RenderNestedValue
                      :value="competitor[key]" 
                      :path="['competitor',competitorIndex, key]"
                      @edit="handleEdit"
                      @add="preHandleAdd"
                      @delete="preHandleDel"
                    />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted,watch, onUnmounted } from 'vue';
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router';
const route = useRoute();
import moment from 'moment'
import RenderNestedValue from './renderNested/index.vue'

// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})


const changeTabView=()=>{
    getData('ischange')
}


let hasviewid=ref('')
hasviewid.value=getHashParams('viewid');
watch(() => route.hash, () => {
  hasviewid.value=getHashParams('viewid');
  getViewsInfo();
});
const baseData = ref({
  "公司列表": [
    {
      "公司名称": {
      "公司名称": "隐奢逸境（A公司）"},
      "VRIO资源列表": [
        {"VRIO资源列表":"121212"},
        "创始人潘小科近20年的高端酒店行业经验及国际品牌管理背景（价值性、稀有性、不可模仿性、组织支持）",
        "独特的'隐奢逸境'品牌理念与'璞修'文化定位（价值性、稀有性、文化不可模仿性）",
        "玉龙雪山3100米海拔稀缺景观资源（地理稀有性、自然价值性）",
        "ROI导向的轻资产运营管理体系（组织流程优化、管理不可模仿性）",
        "业主金茂集团的深度合作关系及零投资接盘能力（资源获取的稀有性）"
      ]
    },
    {
      "公司名称": "Amanvista Resort",
      "VRIO资源列表": [
        "丽江古城狮子山顶全景视角（地理制高点稀缺性）",
        "传统纳西建筑风格与静谧氛围（文化体验的不可复制性）",
        "国际奢华品牌运营标准（组织管理优势）"
      ]
    },
    {
      "公司名称": "Ficus Grove Hotel",
      "VRIO资源列表": [
        "悦榕庄品牌背书与私密度假服务体系（品牌溢价价值）",
        "束河古镇毗邻优势与纳西庭院建筑群（区域协同效应）",
        "高端SPA与养生项目组合（服务差异化）"
      ]
    },
    {
      "公司名称": "Cerulean Lodge",
      "VRIO资源列表": [
        "茶马古道文化主题沉浸式体验（文化IP的独特性）",
        "古城南门核心区位（交通便利性价值）",
        "非物质文化遗产活动策划能力（内容创新性）"
      ]
    },
    {
      "公司名称": "Globemar Hotel",
      "VRIO资源列表": [
        "世界文化遗产地内的建筑融合（文化地标属性）",
        "纳西庭院式建筑集群（规模效应价值）",
        "民族风情与现代舒适度融合设计（产品复合优势）"
      ]
    },
    {
      "公司名称": "Aurea Serenity Lijiang",
      "VRIO资源列表": [
        "直面玉龙雪山主峰的观景设计（视觉冲击价值）",
        "现代极简主义与少数民族元素混搭（设计创新性）",
        "雪山高尔夫球场联动资源（配套协同性）"
      ]
    }
  ]
});

const meinfo = ref([])
const allothers = ref([])
const allmatchkeys = ref([])

const carousel = ref(null);
const scrollPosition = ref(0);
const cardWidth = 320; // 300px宽度 + 20px边距

const isAtStart = computed(() => scrollPosition.value === 0);
const isAtEnd = computed(() => {
  if (!carousel.value) return true;
  return scrollPosition.value >= carousel.value.scrollWidth - carousel.value.clientWidth;
});
// 判断是否为简单值
const isSimpleValue = (value) => {
  return value !== null && !isObject(value) && !Array.isArray(value)
}
// 判断是否为对象
const isObject = (value) => {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}
// 初始化数据
const countInitInfo = () => {
  allothers.value = baseData.value['公司列表']?baseData.value['公司列表']:[]
  let keys = []
  meinfo.value?.forEach(m => keys.push(...Object.keys(m)))
  allothers.value?.forEach(m => keys.push(...Object.keys(m)))
  allmatchkeys.value = [...new Set(keys)]
};
let dataid =ref('')
let loading =ref(false)
const getData=(ischange)=>{
    dataid.value='';
    baseData.value={};
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid.value){
      toGetViewData(hasviewid.value)
    }
}
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
      dataid.value=res?.data?.priceSessionVIew?.id
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            console.log('chartdataschartdatas',chartdatas)
            if(chartdatas&&chartdatas[0]){
              baseData.value=chartdatas[0]
              countInitInfo()
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
}
countInitInfo()//测试
// getData()

const spliceListItem=(type, competitorIndex, mainKey, subKey, arrayIndex, value)=>{
  if (type === 'me') {
      if (arrayIndex !== null) {
        // 更新数组项
        if(subKey !== null && subKey !== undefined){
          meinfo.value[competitorIndex][mainKey][subKey].splice(arrayIndex,1)
          // meinfo.value[competitorIndex][mainKey][subKey][arrayIndex] = newValue
        }else{
          meinfo.value[competitorIndex][mainKey].splice(arrayIndex,1)
          // meinfo.value[competitorIndex][mainKey][arrayIndex] = newValue
        }
      }
    } else if (type === 'competitor') {
      if (arrayIndex !== null) {
        if(subKey !== null && subKey !== undefined){
          allothers.value[competitorIndex][mainKey][subKey].splice(arrayIndex,1)
        }else{
          allothers.value[competitorIndex][mainKey].splice(arrayIndex,1)
        }
      } 
    }
}

// 处理编辑点击
const handleEdit = (path, value = '', isadded) => {
  const [type, ...restPath] = path;
  const target = type === 'me' ? meinfo.value : allothers.value;
  console.log('target',target)
  try {
    // 获取当前值
    let currentValue = restPath.reduce((obj, key) => obj?.[key], target);
    console.log('currentValue',currentValue)
    
    // 处理对象/数组的显示
    const displayValue = typeof currentValue === 'object' 
      ? JSON.stringify(currentValue, null, 2)
      : currentValue;

    ElMessageBox.prompt('请输入新值:', '编辑', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValue: displayValue,
      inputType: typeof currentValue === 'object' ? 'textarea' : 'text',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          let newValue = instance.inputValue;
          
          // 尝试解析JSON字符串
          if (typeof currentValue === 'object') {
            try {
              newValue = JSON.parse(newValue);
            } catch (e) {
              ElMessage.error('请输入有效的JSON格式');
              return;
            }
          }
          
          saveEdit(path, newValue);
        }
        done();
      }
    }).catch(() => {
      if (isadded === 'isadded') {
        preHandleDel(path);
      }
    });
  } catch (error) {
    ElMessage.error('编辑失败: ' + error.message);
  }
};

// 保存编辑
const saveEdit = (path, newValue) => {
  try {
    const [type, ...restPath] = path;
    const target = type === 'me' ? meinfo.value : allothers.value;
    let baseTarget=''
    if(type === 'me'){
      baseTarget=[baseData.value['本公司']]
    }else{
      if(baseData.value['公司列表']?.constructor === Array){
        baseTarget = baseData.value['公司列表']?baseData.value['公司列表']:[]
      }else if(baseData.value['公司列表']?.constructor === Object){
        baseTarget= baseData.value['公司列表']?[baseData.value['公司列表']]:[]
      }
    }
    // // 更新响应式数据
    const parent = restPath.slice(0, -1).reduce((obj, key) => obj[key], target);
    const lastKey = restPath[restPath.length - 1];
    parent[lastKey] = newValue;

    // // 更新baseData
    console.log('estPath.slice(0, -1)',restPath.slice(0, -1))

    const baseParent = restPath.slice(0, -1).reduce((obj, key) => obj[key], baseTarget);
    baseParent[lastKey] = newValue;

    toSaveChange();
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message);
  }
};

// 处理添加
const preHandleAdd = (path) => {
  const [type, ...restPath] = path;
  const target = type === 'me' ? meinfo.value : allothers.value;

  try {
    const parent = restPath.slice(0, -1).reduce((obj, key) => obj[key], target);
    const lastKey = restPath[restPath.length - 1];
    
    if (Array.isArray(parent[lastKey])) {
      parent[lastKey].splice(lastKey + 1, 0, '');
      handleEdit([...path.slice(0, -1), lastKey + 1], '', 'isadded');
    } else {
      ElMessage.warning('只能在数组中添加新项');
    }
  } catch (error) {
    ElMessage.error('添加失败: ' + error.message);
  }
};

// 处理删除
const preHandleDel = (path) => {
  ElMessageBox.confirm('确认删除？', '删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    const [type, ...restPath] = path;
    const target = type === 'me' ? meinfo.value : allothers.value;

    try {
      const parent = restPath.slice(0, -1).reduce((obj, key) => obj[key], target);
      const lastKey = restPath[restPath.length - 1];
      
      if (Array.isArray(parent)) {
        parent.splice(lastKey, 1);
      } else {
        delete parent[lastKey];
      }
      
      toSaveChange();
    } catch (error) {
      ElMessage.error('删除失败: ' + error.message);
    }
  }).catch(() => {});
};
const toSaveChange=()=>{
  console.log('1111',JSON.stringify([{公司列表:allothers.value}]))
  // return
  updatePriceSessionView({
    entity:{
      id:dataid.value,
      data:JSON.stringify([{公司列表:allothers.value}])
    }
  }).then((res)=>{
    if(res?.data?.updatePriceSessionView){
      ElMessage.success('修改成功')
    }
  }).catch((err)=>{
    ElMessage.error('修改失败: ' + err)
  })
}


const scrollLeft = () => {
  if (scrollPosition.value > 0) {
    scrollPosition.value = Math.max(0, scrollPosition.value - cardWidth);
    carousel.value.scrollTo({
      left: scrollPosition.value,
      behavior: 'smooth'
    });
  }
};

const scrollRight = () => {
  if (!carousel.value) return;
  const maxScroll = carousel.value.scrollWidth - carousel.value.clientWidth;
  if (scrollPosition.value < maxScroll) {
    scrollPosition.value = Math.min(maxScroll, scrollPosition.value + cardWidth);
    carousel.value.scrollTo({
      left: scrollPosition.value,
      behavior: 'smooth'
    });
  }
};
const  syncComparisonItemHeights=()=> {
    // 获取所有酒店卡片
    const hotelCards = document.querySelectorAll('.hotel-card');
    if (hotelCards.length < 2) return; // 至少需要两个卡片进行比较
    
    // 获取每个卡片的比较项列表
    const comparisonLists = Array.from(hotelCards).map(card => 
        card.querySelectorAll('.comparison-items .comparison-item')
    );
    
    // 找到最短的比较项列表长度（避免索引越界）
    const minLength = Math.min(...comparisonLists.map(list => list.length));
    
    // 遍历每个索引位置，同步高度
    for (let i = 0; i < minLength; i++) {
        // 获取当前索引下的所有比较项
        const itemsAtSameIndex = comparisonLists.map(list => list[i]);
        
        // 计算这些项中的最大高度
        const heights = itemsAtSameIndex.map(item => {
            // 先移除可能设置的固定高度，以获取真实内容高度
            item.style.height = 'auto';
            return item.offsetHeight;
        });
        
        const maxHeight = Math.max(...heights);
        
        // 将最大高度应用到当前索引的所有比较项
        itemsAtSameIndex.forEach(item => {
            item.style.height = `${maxHeight}px`;
        });
    }
}
onMounted(()=>{
  syncComparisonItemHeights()
})
</script>

