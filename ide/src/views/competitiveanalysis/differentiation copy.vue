<template>
  <div class="differentiation-container" v-loading="loading">
    <div class="version-change">
      <h2>可选项目</h2>
      <el-select v-model="currentversion" @change="changeTabView" v-if="false&&viewitems.length>1">
        <el-option
          v-for="(item,index) in viewitems"
          :key="item.id"
          :label="versionLabel(item,index)"
          :value="item.id">
        </el-option>
      </el-select>
    </div>
    <div v-for="(item, index) in datalist" :key="index" class="item-card">
      <div v-for="(value, key) in item" :key="key" class="item-property">
          <div class="label-title">{{ key }}</div>
          <div v-for="(val_item, val_index) in value" :key="'val_value'+val_index" class="value-item-property">
            <label>
              <input
              type="checkbox"
              :checked="isChecked(val_index,val_item)"
              @change="toggleSelection(val_index,val_item)" 
              />
              <div>
                <div v-for="(val_val_item, val_val_key) in val_item" :key="'val_val_item'+val_val_key" class="value-value-item-property">
                  <strong>{{ val_val_key }}:</strong>{{val_val_item}}
                </div>
              </div>
            </label>
            
          </div>
      </div>
    </div>
    <h2>已选项目</h2>
    <!-- <pre>{{ checkeddata }}</pre> -->
    <div class="selected-items-container">
      <div v-if="checkeddata.length === 0" class="empty-message">
        暂无选中项目
      </div>
      <template v-else>
        <div v-for="(item, index) in checkeddata" :key="index" class="selected-item">
          <div class="selected-item-header">
            <span class="property-key">
              <div v-for="(val_val_item, val_val_key) in item" :key="'check_val_item'+val_val_key" class="value-value-item-property">
                <strong>{{ val_val_key }}:</strong>{{val_val_item}}
              </div>
            </span>
            <button @click="removeItem(index)" class="remove-btn" title="移除">
              ×
            </button>
          </div>
        </div>
      </template>
    </div>
    <div class="differentiation-ope"><el-button type="primary" @click="toParent" :disabled="!(checkeddata.length)">开始2C市场分析</el-button></div>
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch } from 'vue';
import {getPriceViewInfo,generateToCPrompt} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'


// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})


let hasviewid = getHashParams('viewid')
let baseData=ref({})
const checkeddata = ref([]);
const datalist = ref([
]);
let loading =ref(false)
let dataid =ref('');
let textval =ref('');
let generateflag=ref(false);
const getData=(ischange)=>{
  generateflag.value=false;
  textval.value="";
  dataid.value='';
  baseData.value={};
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      toGetViewData(hasviewid)
    }
};
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
      dataid.value=res?.data?.priceSessionVIew?.id
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            if(chartdatas){
              baseData.value=chartdatas
              countInitInfo()
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
}
const countInitInfo=()=>{
  let {data,checkData}=JSON.parse(JSON.stringify(baseData.value));
  datalist.value=data;
  checkeddata.value=checkData;
}
const changeTabView=()=>{
    getData('ischange')
}

// 检查某项是否已被选中
const isChecked = (key,value) => {
  return checkeddata.value.some((item) => {
      const itemKeys = Object.keys(item);
      const valueKeys = Object.keys(value);
      if (itemKeys.length !== valueKeys.length) return false;
      return itemKeys.every((itemkey) =>item[itemkey] === value[itemkey]);
  });
};
// countInitInfo()//测试
getData();
// 切换选择状态
const toggleSelection = (key,value) => {
  const index = checkeddata.value.findIndex((item) => {
      const itemKeys = Object.keys(item);
      const valueKeys = Object.keys(value);
      if (itemKeys.length !== valueKeys.length) return false;
      return itemKeys.every((itemkey) =>item[itemkey] === value[itemkey]);
  });
  if (index === -1) {
    checkeddata.value.push(value);
  } else {
    // 移除选中项
    checkeddata.value.splice(index, 1);
  }
  
};

const removeItem = (index) => {
  checkeddata.value.splice(index, 1);
};
const areArraysContentEqual=(arr1, arr2)=>{
    // 首先检查长度是否相同
    if (arr1.length !== arr2.length) {
        return false;
    }
    
    // 创建一个数组的拷贝以便操作
    const arr2Copy = [...arr2];
    
    // 检查arr1中的每个元素是否都在arr2中
    for (const item of arr1) {
        const index = arr2Copy.indexOf(item);
        if (index === -1) {
            return false;
        }
        arr2Copy.splice(index, 1);
    }
    
    return true;
};

const preSave=()=>{
  generateflag.value=false;
  loading.value=true;
  textval.value="";
  generateToCPrompt({
    entity:{
      id:dataid.value,
      data:JSON.stringify({
        data:datalist.value,
        checkData:checkeddata.value
      })
    }
  }).then((res)=>{
    loading.value=false;
    if(res?.data?.generateToCPrompt){
      generateflag.value=true;
      // ElMessage.success('修改成功')
      // toParent()
      textval.value=res.data.generateToCPrompt?.str?res.data.generateToCPrompt.str:""
    }
  }).catch((err)=>{
    loading.value=false;
    ElMessage.error('发起失败: ' + err)
  })
}
const generateNowtxt=()=>{
  let nowtxt =[];
  if(checkeddata.value?.length){
    checkeddata.value.forEach((item,index)=>{
      nowtxt.push(`${index+1}:${item['描述']}（${item['价值元素']}）`)
    })
  }
  
  return nowtxt.join('')?`请帮我基于新的市场定位"${nowtxt.join('')}"，进行市场分析。`:`请帮我基于新的市场定位，进行市场分析。`
}
// 
const toParent=async()=>{
  if(checkeddata.value?.length){
    try {
      loading.value=false;
      console.log(parent.parent)
      let nowtxt=generateNowtxt()
      console.log('作为子集发起了',{eventname:'triggerChangeEvent',data:{toolname:'2C市场分析',nowtxt,sendnow:'true'}})
      parent.parent.postMessage({eventname:'triggerChangeEvent',data:{toolname:'2C市场分析',nowtxt,sendnow:'true'}});
    } catch (err) {
      loading.value=false;
      console.error('操作失败，请重试', err)
      ElMessage.error('操作失败，请重试')
    }
  }else{
    ElMessage.warning('请至少勾选一个分析项目')
  }
};

</script>

