<template>
  <div
    class="header-contain"
    :class="noBanner && 'noBanner'"
    :style="`--logoHeight: ${logoHeight};${bannerDesigned}`"
  >
    <div class="header-top" :class="{ mini: isMobile }">
      <div class="header-logo" :class="{ hideNow: !(!robotTheme.banner && getTheme) }">
        <img :src="logoFind" alt="" :class="isMobile && 'mini'" />
        <span class="logo-txt" :class="isMobile && 'mini'">
          {{ siteInfo && siteInfo.pageName ? siteInfo.pageName : "" }}
        </span>
      </div>

      <div class="header-login pointer" v-if="oidcUser">
        <el-dropdown @command="handleCommand" trigger="click">
          <span class="el-dropdown-link login-font">
            {{ userName }}
            <i class="el-icon-arrow-down el-icon--right" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <!-- <el-dropdown-item icon="el-icon-user" command="userCenter"
                >个人中心</el-dropdown-item
              > -->
            <el-dropdown-item
              icon="el-icon-place"
              command="logout"
              class="outbtn-item"
              >退出登录</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <div class="header-login pointer" v-else>
        <span class="el-dropdown-link login-font login-btn" @click="pushIndex">
          去登录
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters, mapState } from "vuex";
import { EventBus } from "./js/eventBus.js";

// const suda = require("./img/display/suda/logo.png");
// const usts = require("./img/display/usts/logo.jpg");
// const jssvc = require("./img/display/jssvc/logo.png");
import suda from './img/display/suda/logo.png'
import usts from './img/display/usts/logo.png'
import jssvc from './img/display/jssvc/logo.png'

const ketan = "";

export default {
  name: "myHeader",
  data() {
    return {
      indexSearchKeyword: "",
      getTheme: false,
      styleSet: "",
      robotTheme: {},
      noBanner: false,
      bannerDesigned: "",
    };
  },
  mounted() {},
  created() {
    let self = this;

    EventBus.$on("changeHeader", function (sr) {
      if (!self.getTheme) {
        self.getTheme = true;
        console.log(sr.spec);

        let themes = sr.spec.copilot.themes;

        if (themes) {
          let theme = themes.filter((t) => {
            return (
              (t.screens.includes(self.$userAg()) || t.screens.length === 0) &&
              t.enabled
            );
          });

          if (theme.length) {
            console.log(self.$route.path)
            if (self.$route.path === '/airobot-v2'){
              self.robotTheme = theme[0];
            }
          } else {
            self.noBanner = true;
          }
        }

        if (self.robotTheme && self.robotTheme.banner) {
          self.bannerDesigned =
            "background-image:url(" + self.robotTheme.banner + ");";
        } else {

          if(self.siteInfo.systemBanner){
            self.bannerDesigned = "background-image:url(" + self.siteInfo.systemBanner + ");"
          }else{
            self.noBanner = true;
          }
          
        }
      }
    });
  },
  computed: {
    ...mapGetters("oidcStore", ["oidcUser"]),
    ...mapState(["siteInfo"]),
    sitetheme() {
      return window.Global.SITE_THEME;
    },
    logoHeight() {
      let sitetheme = this.sitetheme;
      let height = "50px";
      switch (sitetheme) {
        case "usts":
          height = "68px";
          break;
        case "jssvc":
          height = "68px";
          break;
        default:
          height = "50px";
          break;
      }
      return height;
    },
    logoFind() {
      let sitetheme = this.sitetheme;
      let logo = null;
      switch (sitetheme) {
        case "suda":
          logo = suda;
          break;
        case "usts":
          logo = usts;
          break;
        case "jssvc":
          logo = jssvc;
          break;
        default:
          logo = this.siteInfo.systemLogo;
          break;
      }
      return logo;
      // if(sitetheme){

      // }else{
      //   return ''
      // }
    },
    userName() {
      // console.log(this.oidcUser, '----------')
      // return this.$store.getters.getUserInfo.name;
      return this.oidcUser.name;
    },
    todoLength() {
      return this.$store.getters.getDataLength["todo"];
    },
    isMobile() {
      var isMobile = false;
      var flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      if (flag != null) {
        isMobile = true;
      }
      return isMobile;
    },
  },
  methods: {
    ...mapActions("oidcStore", ["signOutOidc"]),
    indexToSearch() {
      if (this.indexSearchKeyword) {
        this.$emit("searchText", this.indexSearchKeyword);
        this.indexSearchKeyword = "";
      }
    },
    pushIndex() {
      // this.$router.push({"name": "apps"})
      // sessionStorage.setItem('hasSigned', true)
      // this.$router.push("/workflow/home");
      location.reload();
      // window.open()
    },
    pushUri(val) {
      window.open(val, "_blank");
    },
    handleCommand(command) {
      if (command == "logout") {
        this.signOutOidc();
      }

      if (command == "changeAva") {
        this.avatarEditBox();
      }

      if (command == "userCenter") {
        window.open("https://ehall.xnu.edu.cn/sso/user/", "_blank");
      }
    },
  },
};
</script>
