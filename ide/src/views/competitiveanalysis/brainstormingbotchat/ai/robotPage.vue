<template>
  <div
    class="user_robots"
    :style="setIcons"
    :class="{
      onlyCopilotId: onlyCopilotId,
      openPostIframe: openPostIframe,
      dockLeft: dockIframe === 'left' && openPostIframe,
      showClose: showClose || useFit,
      hiddenChats: showClose && controlChats,
      hasBg: hasBg && !isMobile,
      isMobile: isMobile,
      useToggleBtn: useToggleBtn,
      toggleMobile: toggleMobile,
      isPc: !isMobile,
      useWrongId: useWrongId,
    }"
  >
    <div class="useWrongId-tips" v-if="useWrongId">
      <icon-use name="noid"></icon-use>
      您当前使用的机器人id不在权限范围内,请联系管理员添加权限或检查机器人id是否使用正确。
    </div>
    <div class="sides" v-if="!onlyCopilotId">
      <div class="n1"></div>
      <div class="n2" @click="changePanel(2)" :class="panel === 2 && 'active'">
        对话
      </div>
      <div class="n3">效率</div>
      <div class="n4" @click="changePanel(4)" :class="panel === 4 && 'active'">
        探索
      </div>
      <div class="n5"></div>
      <div class="n6">
        <el-dropdown
          size="mini"
          style="
            position: absolute;
            left: 0px;
            top: 0px;
            width: 100%;
            height: 100%;
          "
        >
          <div style="width: 100%; height: 100%"></div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              class="setbtn-item"
              v-if="$store.getters.getCenterList.includes('system')"
            >
              <el-button
                @click="openSetting"
                size="small"
                type="text"
                icon="el-icon-setting"
                >站点设置</el-button
              >
            </el-dropdown-item>

            <el-dropdown-item class="setbtn-item">
              <el-button
                @click="logOut"
                size="small"
                type="text"
                icon="el-icon-switch-button"
                >退出登录</el-button
              >
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <!-- <el-popover placement="top-start" :title="null" trigger="hover">
          <el-button @click="logOut" size="small">退出登录</el-button>

          
        </el-popover> -->
      </div>
    </div>

    <div
      class="toggleMobileBtn"
      :class="!isNotV2 && 'tp1'"
      @click="
        () => {
          toggleMobile = !toggleMobile;
        }
      "
      v-if="isMobile"
    >
    <el-icon v-if="toggleMobile"><Fold /></el-icon>
      <el-icon v-else><Expand /></el-icon>

    </div>

    <div
      class="select_copilots"
      style="display: none;"
      v-if="false&&panel == 2"
      :class="{
        onlyCopilotId: onlyCopilotId,
        isMobile: isMobile,
        show: toggleMobile && isMobile,
      }"
    >


    <div
            class="pcToggle"
            @click="
              () => {
                toggleMobile = !toggleMobile;
              }
            "
            v-if="useToggleBtn && !isMobile"
          >
            
          <el-tooltip class="item" effect="dark" :content="toggleMobile ? '打开边栏' : '收起边栏'" placement="right-start">
            <el-icon v-if="toggleMobile"><Fold /></el-icon>
            <el-icon v-else><Expand /></el-icon>
          </el-tooltip>
          
          
          </div>


          <div
            class="addNewChat"
            style="text-align: right;
  cursor: pointer;
  color: var(--theme);"
            @click.stop="newChat()"
            v-if="useToggleBtn && !isMobile"
          >
          <el-tooltip class="item" effect="dark" :content="'创建新会话'" placement="right-start">
            <i class="el-icon-plus"></i>
          </el-tooltip>
          </div>


      <div class="search_msg" v-if="!onlyCopilotId">
        <input
          v-model="searchKey"
          placeholder="搜索"
          @focus="
            () => {
              this.searchKey ? (this.isLoadSearch = true) : '';
            }
          "
        />
        <i
          class="el-icon-error"
          v-show="searchKey"
          @click="searchKey = null"
        ></i>
        <a href="#" class="seq-icon"></a>
      </div>

      <div
        class="robot_chooses"
        style="
          line-height: 50px;
          color: #ababab;
          text-align: center;
          font-style: italic;
        "
        v-if="!nowCopilotId && !copilotLoading"
      >
        当前用户暂无可用机器人
      </div>
        
        <div class="robot_chooses" :class="!toggleMobile && 'showOpen'" element-loading-spinner="el-icon-loading"  v-else v-loading="copilotLoading">
        <div class="copilot_selects" v-if="!onlyCopilotId">
          <el-dropdown style="width: 90%" @command="changeCopilotIdCommand">
            <span class="co-tit">
              <i class="el-icon-arrow-down el-icon--right"></i
              >{{ findName(nowCopilotId) }}
            </span>

            <el-dropdown-menu slot="dropdown" class="robot-list-limit">
              <el-dropdown-item
                class="setbtn-item"
                :command="'a_' + idx"
                :key="'a__' + idx"
                v-for="(it, idx) in copilots"
                >{{ getName(it) }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>

          <i @click.stop="newChat()" class="el-icon-plus newchat"></i>
        </div>

        <div class="copilot_selects" v-else>
          <span class="co-tit">
            会话列表
          </span>

          <el-tooltip class="item" effect="dark" :content="'创建新会话'" placement="right-start">
          <i @click.stop="newChat()" class="el-icon-plus newchat"></i>
          </el-tooltip>
        </div>

        <ul v-if="conversationList.length" class="conversation">
          <li
            v-for="(conver,idx) in conversationList"
            :key="conver.name"
            :class="conversationId == conver.name && 'ischat'"
            @click="changeConversation(conver, idx)"
          >
            <span
              class="conver_name"
              v-if="!conver.name || startInputChangeChat !== conver.name"
              :title="conver.text"
              >{{ getPured(conver.text || "新会话") }}

              <i
                class="btn_more"
                :class="
                  !hideChatNow ||
                  (hideChatNow && controlConverBtnsBools[conver.name])
                    ? 'el-icon-caret-bottom'
                    : 'el-icon-caret-right'
                "
                v-if="hideChatNow && conver.name"
                @click.stop="controlConverBtns(conver)"
              ></i>
            </span>
            <el-input
              size="small"
              :ref="'chat' + conver.name"
              v-else
              v-model="conver.text"
            />
            <div
              class="conver_more"
              v-if="
                conver.name &&
                (!hideChatNow ||
                  (hideChatNow && controlConverBtnsBools[conver.name]))
              "
            >
              <i
                :class="
                  !conver.name || startInputChangeChat !== conver.name
                    ? 'el-icon-edit'
                    : 'el-icon-check'
                "
                @click.stop="editConversation(conver)"
              />
              <el-popover
                placement="top-start"
                title="确定要删除当前会话吗？"
                v-model="visiblePop[conver.name]"
              >
                <el-button
                  size="mini"
                  type="primary"
                  @click.stop="delConversation(conver)"
                  >是</el-button
                >
                <el-button size="mini" @click.stop="visiblePop[conver.name] = false"
                  >否</el-button
                >
                <i slot="reference" class="el-icon-delete" />
              </el-popover>
            </div>
          </li>
        </ul>
      </div>
      <h3 v-if="!onlyCopilotId">推荐助手</h3>
      <ul class="robot_recommends" v-if="!onlyCopilotId">
        <li
          :class="'icon' + (idx - 0 + 1)"
          :key="'rbt' + idx"
          v-for="(rbt, idx) in robotRecommend.slice(0, 4)"
          @click="changeCopilot(rbt)"
        >
          {{ rbt.text }}
        </li>
      </ul>
    </div>
    <div class="chatting" v-if="panel == 2" element-loading-spinner="el-icon-loading" v-loading="initSquareInfos !== 2 && !isNotV2">
      
      <div class="mask-close" v-if="toggleMobile && isMobile" @click="
        () => {
          toggleMobile = !toggleMobile;
        }
      "></div>

      <template v-if="conversationList.length && nowCopilotId">
        <!-- <template > -->
          <!-- <span
          v-show="(chat.name === conversationId || (!conversationId && !chat.name))"
          >
          {{ 'robot' + nowCopilotId +
              (chat.uukey ? chat.uukey : chat.name) }}
          </span> -->

          <robot
            :ref="
              'robot' + nowCopilotId + (chat.uukey ? chat.uukey : chat.name)
            "
            :isRight="false"
            :modelParse="modelParse"
            :nowConversationId="conversationId"
            :skillsCategories="robotSkillsCategories"
            :skills="robotSkills"
            :useFit="useFit"
            :useQues="v2sortedQues"
            :useDocs="v2docTypes"
            :useDocNames="docTypeNames"
            :noActionBox="noActionBox"
            :tasksRecommend="tasksRecommend[nowCopilotId]"
            :canUseTaskAndQues="canUseTaskAndQues(nowCopilotId)"
            :isNewChat="chat.name === isNewChat"
            @reNameChat="reNameChat"
            @close="closeUseFit"
            v-for="(chat, idx) in conversationList" :key="
              'robot' + nowCopilotId + (chat.uukey ? chat.uukey : chat.name)
            "
            @toggleChat="toggleChat"
            @updateConversation="updateConversations"
            @updateRobotCenter="updateRobotCenter"
            
            v-show="
              (!conversationId && !chat.name) || chat.name === conversationId
            "
            :useCopilotId="nowCopilotId"
            :useConversation="chat"
          ></robot>
          <!-- && chat.name !== isNewChat  -->
        <!-- </template> -->
      </template>
    </div>
    <div
      class="que_collection"
      v-if="!onlyCopilotId && Object.keys(queVaries).length && panel == 2"
    >
      <div class="ask"></div>
      <div class="title">常用问答</div>
      <div class="selects">
        <i class="el-icon-caret-left" @click="toScrollTab(true)"></i>
        <div
          style="
            overflow-x: scroll;
            width: calc(100% - 36px);
            display: inline-flex;
          "
          id="tabScroll"
        >
          <ul style="white-space: nowrap; height: 50px" id="ulScroll">
            <li
              v-for="(it, ikey, idx) in queVaries"
              :key="it + idx"
              :class="it === chooseVary && 'act'"
              @click="changeQues(it, (idx / queVaries.length).toFixed(2))"
              :id="'que' + idx"
            >
              {{ it }}
            </li>
          </ul>
        </div>
        <i class="el-icon-caret-right" @click="toScrollTab(false)"></i>
      </div>

      <el-collapse v-model="activeNames" accordion>
        <el-collapse-item
          :title="que.question"
          :name="que.name"
          v-for="que in showQues"
          @click="setQue(que)"
          :key="que.name"
        >
          <div v-html="que.answer"></div>
        </el-collapse-item>
      </el-collapse>
      <!-- <ul class="ques">
          <li v-for="que in showQues" @click="setQue(que)" :key="que.id">
            {{ que.question }}
          </li>
        </ul> -->
    </div>

    <div
      class="que_collection"
      v-if="!onlyCopilotId && !Object.keys(queVaries).length && panel == 2"
    >
      <div style="text-align: center; margin-top: 200px">
        <icon-use name='noque' style="display: block; margin: 0px auto" />
        该机器人暂无常用问答
      </div>
    </div>

    <div
      class="postIframe nobd"
      :class="{'isMobile': isMobile, 'hasTool': toolIframe === 'out-normal', 'floatTool': toolIframe === 'out-float'}"
      v-if="openPostIframe && $route.name !== 'airobots'"
    >
      <div class="app-actions" v-if="toolIframe">
        <!-- <i class="el-icon-place" v-if="showSkillTool"  @click="openPopSkillTree"/> -->
        <i class="el-icon-close" @click="closeIframeRight" />
      </div>
      <SkillTree @scrollFindSkill="scrollFindSkill" v-if="showSkillToolBool" :dataVary="robotSkillsCategories" :dataShow="robotSkills"></SkillTree>
      <iframe v-if="openPostIframe === 'iframe'" :src="postIframe" id="postIframeTarget" />
      <ChartTabs v-if="openPostIframe === 'tabs'" :dataBoard="postRightBoard"></ChartTabs>
    </div>

    <div class="select_copilots_cards" v-if="panel == 4">
      <p v-if="!allCopilots.length" style="text-align: center; color: #999">
        <!-- <img
          style="height: 300px; display: block; margin: calc(40vh - 150px) auto"
          src="@/ai/img/norobots.svg"
        /> -->
        <icon-use style="margin: calc(40vh - 150px) auto 20px auto;display:block;" name="norobot"></icon-use>
        您当前暂无可用助手
      </p>
      <ul class="robot_card" :class="allCopilots.length > pagesize && 'haspdTop'" v-else>
        <li
          :class="'icon' + (idx - 0 + 1)"
          :key="'rbt' + idx"
          v-for="(rbt, idx) in allCopilots.slice((current-1)*pagesize,current*pagesize )"
        >
          <!-- .split(/,|，|、/g) -->
          <img :src="rbt.source.spec.copilot.icon || siteRobotIcon || defaultIcon" alt="" />
          <h3>
            {{ rbt.text }}
          </h3>
          <p style="margin-top: 4px">
            <span
              class="aTag"
              v-for="tg in rbt.source.spec.copilot.tags.split(/,|，|、/g)"
              >{{ tg }}</span
            >
          </p>
          <p class="over_line2">
            {{ rbt.source.spec.copilot.description || "暂无简介" }}
          </p>

          <a @click="startCopilot(rbt)" class="startR">
            <i class="el-icon-magic-stick" />
            开始问答</a
          >

          <i title="打开新窗口" style="position: absolute;top:14px;right:14px;color: var(--line1);cursor: pointer;" class="el-icon-top-right" @click="startCopilotPage(rbt)"></i>
        </li>

        <div class="page-cubes" v-if="allCopilots.length > pagesize">
          <div :class="it ===current && 'iscur'" @click="changeCurrent(it)" v-for="it in Math.ceil(allCopilots.length/pagesize)" class="page-cube">
            <!-- {{ it }}{{ current }} -->
          </div>
        </div>
      </ul>
    </div>

    <div class="search_res" :class="{ show: isLoadSearch }">
      <div
        class="search_res_left"
        style="min-height: 180px"
        v-loading="isSearching"
        element-loading-spinner="el-icon-loading"
      >
        <div
          v-if="searchCopilots && searchCopilots.length"
          style="margin-bottom: 12px"
        >
          <h3 class="group-tit">机器人</h3>
          <!-- {{searchRecords}} -->
          <ul>
            <li
              class="ans"
              v-for="(it, idx) in searchCopilots"
              :key="idx + 'iuuu'"
              @click="changeCopilot(it, 'clear')"
            >
              <img
                :src="it.source.spec.copilot.icon || siteRobotIcon || defaultIcon"
                class="icon"
                alt=""
              />
              <h4
                style="line-height: 30px;"
                v-html="highLight(findName(it.name))"
              ></h4>
              <!-- <p>{{ it.num }}条相关聊天记录</p> -->
            </li>
            <!-- <li v-for="it in searchRes" :class="it.sender.kind + ' ans'">
            <p v-html="getMarked(it.text)"></p>
  
            <p class="time">{{ moment(it.sent).format("YYYY/MM/DD") }}</p>
          </li> -->
          </ul>
        </div>

        <div
          v-if="searchRecords && Object.keys(searchRecords).length"
          style="margin-bottom: 12px"
        >
          <h3 class="group-tit">聊天记录</h3>
          <!-- {{searchRecords}} -->
          <ul>
            <li
              class="ans"
              v-for="(it, idx) in searchRecords"
              :key="idx + 'iuuu'"
              :class="searchDisKey === it.copilot.name && 'act'"
              @click="loadSearchRes(it)"
            >
              <img
                :src="it.copilot.source.spec.copilot.icon || siteRobotIcon || defaultIcon"
                class="icon"
                alt=""
              />
              <h4 style="line-height: 30px; margin-bottom: 0px;">{{ findName(idx) }}</h4>
              <p>{{ it.num }}条相关聊天记录</p>
            </li>
          </ul>
        </div>

        <p
          style="line-height: 100px; text-align: center"
          v-if="
            Object.keys(searchRecords).length === 0 &&
            searchCopilots.length === 0
          "
        >
          暂无<span style="color: var(--lineCube2)">{{ searchKey }}</span
          >相关记录
        </p>
      </div>

      <div class="search_res_right" v-show="searchDisplay.length">
        <ul>
          <li
            v-for="it in searchDisplay"
            :key="'sss'+it.sender.kind"
            :class="
              it.sender.kind +
              ' ans' +
              (it.name === searchDisplayChoose.name ? ' act' : '')
            "
          >
            <p v-html="getMarked(it.text)"></p>

            <p class="time">{{ moment(it.sent).format("YYYY/MM/DD") }}</p>
            <p class="showLast" @click="changeRecords(it)">
              显示上下文<i class="el-icon-caret-right"></i>
            </p>
          </li>
        </ul>
      </div>

      <div class="search_res_records" v-show="searchDisplayChooseLastId">
        <chat-scroll ref="searchDisPlayBox"></chat-scroll>
      </div>
    </div>

    <system-config ref="systemRef"></system-config>
  </div>
</template>
<script>
// import "@/views/competitiveanalysis/brainstormingbotchat/ai/css/common.scss";
// import "@/views/competitiveanalysis/brainstormingbotchat/ai/css/other.scss";

import Robot from "./robotVisit.vue";

import _ from "lodash";
import moment from "moment";
import "moment/locale/zh-cn";
moment.locale("zh-cn");

import { marked } from "marked";
import SystemConfig from "./modules/systemConfig.vue";

import { robotCenter } from './modules/robotCenter.js'

const render = new marked.Renderer();
marked.setOptions({
  renderer: render,
  async: false,
  breaks: false,
  extensions: null,
  gfm: true,
  hooks: null,
  pedantic: false,
  silent: false,
  tokenizer: null,
  walkTokens: null,
});

export default {
  mixins: [robotCenter],
  name: 'robotPage',
  created() {
            let siteColor = this.$store.state.siteInfo.siteColor;
            document.body.style =  this.$getColors(siteColor);
          
  },
  computed: {
    v2sortedQues() {
      if(this.$route.name === 'airobot-v2'){
        return this.sortedQues
      }else{
        return []
      }
    },
    v2docTypes() {
      if(this.$route.name === 'airobot-v2'){
        return this.docTypes
      }else{
        return {}
      }

    }
  },
  components: {
    Robot,
    SystemConfig,
  },
};
</script>
