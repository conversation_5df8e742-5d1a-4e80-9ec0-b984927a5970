<template>
  <div class="robot robot_img" v-if="!chatShow && useNowCopilotId !== '--'">
    <div class="animation">
      <img src="./img/normal.png" @click="hideRobot" />
    </div>
  </div>
  <div
    id="robotAuthBox"
    v-else-if="nowCopilotId && parseInt(nowCopilotId) !== 0"
    :style="{ '--widthDefineBox': widthDefineBox + 'px' }"
    :class="useFit && 'useFit'"
  >
    <div
      id="boxBigger"
      v-if="useFit"
      style="
        cursor: col-resize;
        z-index: 88;
        background: transparent;
        position: absolute;
        left: 0px;
        top: 0px;
        width: 8px;
        height: 100%;
      "
    ></div>
    <robot-page
      :useFit="useFit"
      :noActionBox="noActionBox"
      :hideChat="hideChat"
      @close="hideRobot"
      :panel="2"
      :onlyCopilotId="nowCopilotId"
    ></robot-page>
  </div>

  <div class="noIdSvg" v-else>
    <p>该课程尚未创建智能助教~</p>
    <img src="./img/noId.svg" alt="" />
  </div>
</template>
<script>
import Robot from "./robotPage.vue";
import RobotPage from "./robotPage.vue";
export default {
  data() {
    return {
      chatShow: false,
      widthDefineBox: 800,
    };
  },
  mounted() {
    console.log('__refresh___?here?')

    // let historyLength = window.history.length;
    // for(let i =0;i<historyLength;i++){
    //   window.history.pushState(null, null, location.href)
    // }

    //防止页面后退
    // history.pushState(null, null, document.URL);
    // window.addEventListener("popstate", function () {
    //   history.pushState(null, null, document.URL);
    // });
  },
  methods: {
    hideRobot() {
      this.chatShow = !this.chatShow;

      if (this.chatShow) {
        setTimeout(() => {
          let widthDefineBox = localStorage.getItem("widthDefineBox");
          if (widthDefineBox) {
            this.widthDefineBox = widthDefineBox;
          }

          document
            .getElementById("boxBigger")
            .addEventListener("mousedown", function (e) {
              e.preventDefault();
              let startX = e.clientX;
              let stared = true;

              let boxWidth =
                document.getElementById("robotAuthBox").clientWidth;

              let resizeRobotBox = function (e) {
                // if(document.body.className === 'isMoving'){
                let width = e.clientX - startX;

                // console.log(boxWidth, "----boxw");
                let afterW = -width - 5 + boxWidth;

                if (afterW < 720) {
                  afterW = 720;
                }
                // document.getElementById("boxBigger").style.width = -width - 1 + 10 +'px';
                document.getElementById("robotAuthBox").style.width =
                  afterW + "px";

                // }
              };

              // document.body.className = 'isMoving'
              // let startWidth = $box.width();

              document.body.addEventListener("mousemove", resizeRobotBox);

              document.body.addEventListener("mouseup", function (e) {
                let widthDefineBox =
                  document.getElementById("robotAuthBox").clientWidth;
                localStorage.setItem("widthDefineBox", widthDefineBox);
                this.widthDefineBox = widthDefineBox;
                document.body.removeEventListener("mousemove", resizeRobotBox);
              });
            });
        });
      }
    },
    isMobile() {
      var isMobile = false;
      var flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      if (flag != null) {
        isMobile = true;
      }
      return isMobile;
    },
  },
  props: {
    noActionBox: {
      type: Boolean,
      default: () => false
    },
    errorMsg: {
      type: String,
      default: () => '您好，出现未知故障，请刷新页面稍后再试~'
    },
    useForAdmin: {
      type: String,
      default: () => "--",
    },
    useNowCopilotId: {
      type: String,
      default: () => "--",
    },
    hideChat: {
      type: Boolean,
      default: () => false,
    },
    useFit: {
      type: Boolean,
      default: () => false,
    },
  },
  computed: {
    forAdmin() {
      if (this.useForAdmin && this.useForAdmin !== "--") {
        return this.useForAdmin;
      } else {
        return this.$route.query.forAdmin;
      }
    },
    nowCopilotId() {
      if (this.useNowCopilotId && this.useNowCopilotId !== "--") {
        return this.useNowCopilotId;
      } else {
        // return this.$route.query.forAdmin
        return this.$route.query.id;
      }

    },
  },

  components: {
    Robot,
    RobotPage,
  },
};
</script>
