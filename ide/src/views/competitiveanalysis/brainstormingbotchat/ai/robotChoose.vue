<template>
  <div
    class="user_robots square"
    :style="setIcons"
    :class="{
      onlyCopilotId: true,
      showClose: showClose || useFit,
      openPostIframe: openPostIframe,
      dockLeft: dockIframe === 'left' && openPostIframe,
      hiddenChats: showClose && controlChats,
      isMobile: isMobile,
      useToggleBtn: isMobile,
      toggleMobile: toggleMobile,
      useWrongId: useWrongId,
      isPc: !isMobile,
    }"
  >
    <div
      class="toggleMobileBtn"
      :class="!isNotV2 && 'tp1'"
      @click="
        () => {
          toggleMobile = !toggleMobile;
        }
      "
      v-if="isMobile"
    >
      <i :class="toggleMobile ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
    </div>

    <div
      class="select_copilots_display"
      :class="{
        isMobile: isMobile,
        isPc: !isMobile,
        show: toggleMobile,
      }"
    >



    <div
            class="pcToggle"
            @click="
              () => {
                toggleMobile = !toggleMobile;
              }
            "
            v-if="useToggleBtn && !isMobile"
          >

          <img :src="logoFind" class="logoFind" v-if="$route.name === 'airobotsquare-v2'"/>
            
          <el-tooltip class="item" effect="dark" :content="toggleMobile ? '打开边栏' : '收起边栏'" placement="right-start">
      <i :class="toggleMobile ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>

    </el-tooltip>
          
          
          </div>


          <div
            class="addNewChat"
            style="text-align: right;
  cursor: pointer;
  color: var(--theme);"
            @click.stop="newChat()"
            v-if="useToggleBtn && !isMobile && toggleMobile"
          >
          <el-tooltip class="item" effect="dark" :content="'创建新会话'" placement="right-start">
            <i class="el-icon-plus"></i>
          </el-tooltip>
          </div>

      <div
        class="robot_chooses_display"
        style="
          line-height: 50px;
          color: #ababab;
          text-align: center;
          font-style: italic;
        "
        v-if="!nowCopilotId && !copilotLoading && !nowChooseAppVary"
      >
        当前用户暂无可用机器人
      </div>
      <div class="robot_chooses_display" v-else v-loading="copilotLoading" element-loading-spinner="el-icon-loading">
        <div class="copilot_selects_collection">
          <div
            class="copilot_selects"
            :class="nowCopilotId === rbt.name && 'choosed'"
            v-for="(rbt, idx) in robotRecommend"
            @click="changeCopilot(rbt)"
          >
            <!-- {{ rbt }} -->
            
            <span class="co-tit over_line">
              <default-icon
                style="width: 24px; height: 24px;display: inline-block;"
                size="s10"
                :tag="rbt.source.spec.copilot.icon || siteInfo.systemRobotImg || defaultIcon"
              ></default-icon>
              <el-tooltip class="item" effect="dark" :content="findName(rbt.name)" placement="right-start">
                <span class="over_line">{{ findName(rbt.name) }}</span>
              </el-tooltip>
            </span>

            <i @click.stop="newChat()" class="el-icon-plus newchat"></i>
          </div>

          <div
            class="copilot_selects"
            :class="(!nowCopilotId && nowChooseAppVary === vary.text ) && 'choosed'"
            v-for="(vary, idx) in $store.getters.getRobotVary"
            @click="changeAppVary(vary.text)"
          >
            <!-- {{ rbt }} -->
            <span class="co-tit over_line">
              <icon-use style="height: 24px;" name="varydefault" v-if="!vary.icon"/>
              <default-icon
                v-else
                style="width: 24px; height: 24px;display: inline-block;"
                size="s10"
                :tag="vary.icon"
              ></default-icon>

              <el-tooltip class="item" effect="dark" :content="vary.text" placement="right-start">
                <span class="over_line">{{ vary.text }}</span>
              </el-tooltip>
            </span>

            <!-- <i @click.stop="newChat()" class="el-icon-plus newchat"></i> -->
          </div>
        </div>

        <!-- <div> -->
        <!-- <span class="co-tit">
              <i class="el-icon-arrow-down el-icon--right"></i>会话列表
            </span> -->

        <!-- </div> -->

        <!-- <i @click.stop="newChat()" class="el-icon-plus newchat"></i> -->

        <h3 v-if="conversationList.length" class="conversation-tit">
          <span>历史会话</span>
          <span
            :class="!conversationId && 'ischat'"
            class="new-chat"
            @click="newChat"
            v-if="$route.name === 'airobotsquare-v2'"
          >
            <i class="el-icon-chat-dot-round" style="margin-right: 4px"></i>
            <span
              class="conver_name over_line"
              >新会话
            </span>

          </span>
        </h3>
        <ul v-if="conversationList.length" class="conversation">
          <li
            v-for="(conver,idx) in conversationList"
            :key="conver.name"
            :class="{'ischat': conversationId == conver.name, 'hide': !conver.name}"
            @click="changeConversation(conver, idx)"
          >
            <i class="el-icon-chat-dot-round" style="margin-right: 4px"></i>
            <span
              class="conver_name over_line"
              v-if="!conver.name || startInputChangeChat !== conver.name"
              :title="conver.text"
              >{{ conver.text || "新会话" }}
            </span>
            

            <el-input
              style="width: 65%;"
              size="small"
              :ref="'chat' + conver.name"
              v-if="!(!conver.name || startInputChangeChat !== conver.name)"
              v-model="conver.text"
            />

            <i
                class="btn_more"
                :class="
                  !hideChatNow ||
                  (hideChatNow && controlConverBtnsBools[conver.name])
                    ? 'el-icon-caret-bottom'
                    : 'el-icon-caret-right'
                "
                v-if="hideChatNow && conver.name && (startInputChangeChat !== conver.name)"
                @click.stop="controlConverBtns(conver)"
              ></i>

            <div
              class="conver_more"
              v-if="
                conver.name &&
                (!hideChatNow ||
                  (hideChatNow && controlConverBtnsBools[conver.name]))
              "
            >
              <i
                :class="
                  !conver.name || startInputChangeChat !== conver.name
                    ? 'el-icon-edit'
                    : 'el-icon-check'
                "
                @click.stop="editConversation(conver)"
              />


              <i class="el-icon-delete" @click.stop="visiblePop[conver.name] = true;"/>
              
              <el-popover
                placement="top-start"
                title="确定要删除当前会话吗？"
                v-model="visiblePop[conver.name]"
              >
                <el-button
                  size="mini"
                  type="primary"
                  @click.stop="delConversation(conver)"
                  >是</el-button
                >
                <el-button size="mini" @click.stop="visiblePop[conver.name] = false"
                  >否</el-button
                >
                <i v-show="false" class="el-icon-delete" />
              </el-popover>

            </div>
          </li>
        </ul>
      </div>

      <!-- <ul class="robot_recommends">
          <li
            :class="'icon' + (idx - 0 + 1)"
            :key="'rbt' + idx"
            v-for="(rbt, idx) in robotRecommend.slice(0, 4)"
            @click="changeCopilot(rbt)"
          >
            {{ rbt.text }}
          </li>
        </ul> -->
    </div>

    <div class="chatting_display" v-loading="initSquareInfos !== 2" element-loading-spinner="el-icon-loading">

      <div class="mask-close" v-if="toggleMobile && isMobile" @click="
        () => {
          toggleMobile = !toggleMobile;
        }
      ">
      
    </div>
      <div class="robot-project" v-if="!nowCopilotId">
        <!-- {{ project }} -->
        <h3>{{ nowChooseAppVary }}</h3>
        <!-- <ul class="project-tabs">
          <li
            @click="tabIdx = idx"
            v-for="(ittab, idx) in project.paragraphs"
            :class="tabIdx === idx && 'nowIdx'"
          >
            {{ ittab.title }}
          </li>
        </ul> -->
        <ul class="project-apps">
          <li
            v-for="app in filterApps"
            @click="toRobot(app)"
          >
            <div class="icon">
              <!-- <img :src="app.icon" /> -->
              <default-icon
                :tag="app.icon || siteInfo.systemRobotImg || defaultIcon"
              ></default-icon>
            </div>
            <div class="name">{{ app.text }}</div>
            <div
              class="des over_line2"
              v-html="app.description || '暂无简介'"
            ></div>
          </li>
        </ul>
      </div>

      <template v-if="conversationList.length && nowCopilotId">
        <template v-for="(chat, idx) in conversationList" :key="
              'robot' + nowCopilotId + (chat.uukey ? chat.uukey : chat.name)
            ">
          <!-- <span
            v-show="(chat.name === conversationId || (!conversationId && !chat.name))"
            >
            {{ 'robot' + nowCopilotId +
                (chat.uukey ? chat.uukey : chat.name) }}
            </span> -->

          <robot
            :ref="
              'robot' + nowCopilotId + (chat.uukey ? chat.uukey : chat.name)
            "
            :isRight="false"
            :modelParse="modelParse"
            :nowConversationId="conversationId"
            :skillsCategories="robotSkillsCategories"
            :skills="robotSkills"
            :useFit="useFit"
            :useQues="sortedQues"
            :useDocs="docTypes"
            :useDocNames="docTypeNames"
            :noActionBox="true"
            :tasksRecommend="tasksRecommend[nowCopilotId]"
            :canUseTaskAndQues="canUseTaskAndQues(nowCopilotId)"
            :isNewChat="chat.name === isNewChat"
            @resetConversation="getRecentConversations"
            @reNameChat="reNameChat"
            @updateRobotCenter="updateRobotCenter"
            @close="closeUseFit"
            @toggleChat="toggleChat"
            
            v-show="
              chat.name === conversationId || (!conversationId && !chat.name)
            "
            :useCopilotId="nowCopilotId"
            :useConversation="chat"
          >
        </robot>
          <!-- && chat.name !== isNewChat  -->
        </template>
      </template>
    </div>

    <div
      class="postIframe nobd"
      :class="{'isMobile': isMobile, 'hasTool': toolIframe === 'out-normal', 'floatTool': toolIframe === 'out-float'}"
      v-if="openPostIframe && $route.name !== 'airobots'"
    >
      <div class="app-actions" v-if="toolIframe">
        <!-- <i class="el-icon-place" v-if="showSkillTool" @click="openPopSkillTree"/> -->
        <i class="el-icon-close" @click="closeIframeRight" />
      </div>
      <SkillTree @scrollFindSkill="scrollFindSkill" v-if="showSkillToolBool" :dataVary="robotSkillsCategories" :dataShow="robotSkills"></SkillTree>
      <iframe v-if="openPostIframe === 'iframe'" :src="postIframe" id="postIframeTarget" />
      <ChartTabs v-if="openPostIframe === 'tabs'" :dataBoard="postRightBoard"></ChartTabs>
    </div>
    <!-- <div
        class="que_collection"
        v-if="Object.keys(queVaries).length"
      >
        <div class="ask"></div>
        <div class="title">常用问答</div>
        <div class="selects">
          <i class="el-icon-caret-left" @click="toScrollTab(true)"></i>
          <div
            style="
              overflow-x: scroll;
              width: calc(100% - 36px);
              display: inline-flex;
            "
            id="tabScroll"
          >
            <ul style="white-space: nowrap; height: 50px" id="ulScroll">
              <li
                v-for="(it, ikey, idx) in queVaries"
                :key="it + idx"
                :class="it === chooseVary && 'act'"
                @click="changeQues(it, (idx / queVaries.length).toFixed(2))"
                :id="'que' + idx"
              >
                {{ it }}
              </li>
            </ul>
          </div>
          <i class="el-icon-caret-right" @click="toScrollTab(false)"></i>
        </div>
  
        <el-collapse v-model="activeNames" accordion>
          <el-collapse-item
            :title="que.question"
            :name="que.name"
            v-for="que in showQues"
            @click="setQue(que)"
            :key="que.name"
          >
            <div v-html="que.answer"></div>
          </el-collapse-item>
        </el-collapse>
      </div> -->

    <!-- <div
        class="que_collection"
        v-if="!Object.keys(queVaries).length"
      >
        <div style="text-align: center; margin-top: 200px">
          <icon-use name='noque' style="display: block; margin: 0px auto" />
          该机器人暂无常用问答
        </div>
      </div> -->
  </div>
</template>
<script>
import Robot from "./robotVisit";
import chatScroll from "./chatScroll";

import _ from "lodash";
import moment from "moment";
import "moment/locale/zh-cn";
moment.locale("zh-cn");

import { marked } from "marked";
import SystemConfig from "./modules/systemConfig.vue";

import { robotCenter } from "./modules/robotCenter.js";

import { mapActions, mapState } from "vuex";

// const defaultIcon = require("./img/normal.png");
import defaultIcon from './img/normal.png'

const render = new marked.Renderer();
marked.setOptions({
  renderer: render,
  async: false,
  breaks: false,
  extensions: null,
  gfm: true,
  hooks: null,
  pedantic: false,
  silent: false,
  tokenizer: null,
  walkTokens: null,
});

export default {
  mixins: [robotCenter],
  data() {
    return {
      useVaryPage: false,
      tabIdx: 0,
      nowChooseAppVary: null,
    };
  },
  computed: {
    ...mapState("projects", ["project"]),
    logoFind() {
      let sitetheme = this.sitetheme;
      let logo = null;
      switch (sitetheme) {
        case "suda":
          logo = suda;
          break;
        case "usts":
          logo = usts;
          break;
        case "jssvc":
          logo = jssvc;
          break;
        default:
          logo = this.siteInfo.systemLogo;
          break;
      }
      return logo;
      // if(sitetheme){

      // }else{
      //   return ''
      // }
    },
    filterApps() {
        let vary = this.nowChooseAppVary;
        let appList = this.$store.getters.getAppList;
        console.log(appList)
        let filters = appList.filter((t)=>(t.tags || '').includes(vary));
        console.log(filters, '--------')
        return filters.sort((a,b)=>{
          return (b.recommend || 0) - (a.recommend || 0);
        });
    }
  },
  methods: {
    changeAppVary(e){
        this.nowChooseAppVary = e;
        this.nowCopilotId = null;

        this.closeLeft();
    },
    getUrlParam(url, name) {
      if (!url) {
        return "";
      }
      var urlArr = url.split("?");
      if (urlArr.length > 1 && urlArr[1].indexOf(name) > -1) {
        var query = urlArr[1];
        var obj = {};
        var arr = query.split("&");
        for (var i = 0; i < arr.length; i++) {
          arr[i] = arr[i].split("=");
          obj[arr[i][0]] = arr[i][1];
        }
        return obj[name];
      } else {
        return "";
      }
    },
    toRobot(app) {
      if (!app.uri.includes("copilot-center")) {
        if(!app.uri.startsWith("/")){
          window.open(app.uri, "_blank");
        }else{
          window.open(location.origin + app.uri, "_blank");
        }
      } else {
        let id = this.getUrlParam(app.uri, "id");
        console.log(id);
        this.changeCopilotId({ name: id });
        this.newChat();
      }
    },
  },
  components: {
    Robot,
    chatScroll,
    SystemConfig,
  },
};
</script>
