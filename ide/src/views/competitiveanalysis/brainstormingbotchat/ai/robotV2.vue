<template>
  <div
    style="height: 100vh"
    class="isV2"
    v-loading="!getTheme"
    element-loading-spinner="el-icon-loading"
    :style="styleSet"
  >
    <header-common></header-common>
    <robot-auth
      :noActionBox="true"
      :errorMsg="'您好，当前用户过多，请稍后再试'"
    ></robot-auth>
  </div>
</template>
<script>
import robotAuth from "./robotAuth.vue";
import headerCommon from "./headerCommon.vue";
import { EventBus } from "./js/eventBus.js";

export default {
  data() {
    return {
      getTheme: false,
      styleSet: "",
      robotTheme: {},
    };
  },
  components: {
    robotAuth,
    headerCommon,
  },
  created() {
    let self = this;
    EventBus.$on("changeHeader", function (sr) {
      if (!self.getTheme) {
        self.getTheme = true;
        let themes = sr.spec.copilot.themes;

        if (themes) {
          let theme = themes.filter((t) => {
            console.log(t.screens);
            return t.screens.includes(self.$userAg()) || t.screens.length === 0;
          });

          if (theme.length) {
            self.robotTheme = theme[0];
            self.styleSet = self.$getColors(self.robotTheme.color);
          }
        }
      }
    });
  },
};
</script>
