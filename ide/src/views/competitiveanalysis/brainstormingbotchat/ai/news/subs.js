const subs = {
    
    hasBackends: `subscription chat($messages: [ChatMessageInput!],
              $backend: ID,
              $copilot:  ID!,
              $filter: ChatFilter, 
              $conversation: ConversationInput,
              $tool: ToolInput,
              $skill: ID
              ){
              chat(
                messages: $messages,
                  copilot: $copilot,
                  backend: $backend,
                  filter: $filter,
                  conversation: $conversation,
                  tool: $tool,
                  skill: $skill
                ) {
                  conversation {
                    name
                    text
                  }
                  relevantQuestions {
                    name
                    question
                  }
                  
                  choices {
                  actions {
                    kind
                    page {
                      url
                      content {
                        kind
                        template
                      }
                    }
                  }
                  samples {
                    name
                    question
                    answer
                    chunk
                    chunkSourceDocumentUri
                    chunkSourceDocument
                    chunkSourceReference
                    chunkSourceOntology
                    distance
                  }
                  message {
                  text
                  role
                  name
                  userMessage
                  reasoning {
                    duration
                    content
                  }
                }
                }
              }
            }`,

    hasThought: `subscription chat($messages: [ChatMessageInput!],
              $copilot:  ID!,
              $filter: ChatFilter, 
              $conversation: ConversationInput,
              $tool: ToolInput,
              $skill: ID
              ){
              chat(
                messages: $messages,
                  copilot: $copilot,
                  filter: $filter,
                  conversation: $conversation,
                  tool: $tool,
                  skill: $skill
                ) {
                  conversation {
                    name
                    text
                  }
                  relevantQuestions {
                    name
                    question
                  }

                  thoughtChain {
                    items{
                      title
                      content
                      description
                      status
                    }
                  }
                  
                  choices {
                  actions {
                    kind
                    page {
                      url
                      content {
                        kind
                        template
                      }
                    }
                  }
                  samples {
                    name
                    question
                    answer
                    chunk
                    chunkSourceDocumentUri
                    chunkSourceDocument
                    chunkSourceReference
                    chunkSourceOntology
                    distance
                  }
                  message {
                  text
                  role
                  name
                  userMessage
                  reasoning {
                    duration
                    content
                  }
                }
                }
              }
            }`,
            
    hasThoughtAndBackends: `subscription chat($messages: [ChatMessageInput!],
            $backend: ID,
            $copilot:  ID!,
            $filter: ChatFilter, 
            $conversation: ConversationInput,
            $tool: ToolInput,
            $skill: ID
            ){
            chat(
              messages: $messages,
                copilot: $copilot,
                backend: $backend,
                filter: $filter,
                conversation: $conversation,
                tool: $tool,
                skill: $skill
              ) {
                conversation {
                  name
                  text
                }
                relevantQuestions {
                  name
                  question
                }

                thoughtChain {
                  items{
                    title
                    content
                    description
                    status
                  }
                }
                
                choices {
                actions {
                  kind
                  page {
                    url
                    content {
                      kind
                      template
                    }
                  }
                }
                samples {
                  name
                  question
                  answer
                  chunk
                  chunkSourceDocumentUri
                  chunkSourceDocument
                  chunkSourceReference
                  chunkSourceOntology
                  distance
                }
                message {
                text
                role
                name
                userMessage
                reasoning {
                  duration
                  content
                }
              }
              }
            }
          }`,
          
    normal: `subscription chat($messages: [ChatMessageInput!],
              $copilot:  ID!,
              $filter: ChatFilter, 
              $conversation: ConversationInput,
              $tool: ToolInput,
              $skill: ID
              ){
              chat(
                messages: $messages,
                  copilot: $copilot,
                  filter: $filter,
                  conversation: $conversation,
                  tool: $tool,
                  skill: $skill
                ) {
                  conversation {
                    name
                    text
                  }
                  relevantQuestions {
                    name
                    question
                  }

                  choices {
                  actions {
                    kind
                    page {
                      url
                      content {
                        kind
                        template
                      }
                    }
                  }
                  samples {
                    name
                    question
                    answer
                    chunk
                    chunkSourceDocumentUri
                    chunkSourceDocument
                    chunkSourceReference
                    chunkSourceOntology
                    distance
                  }
                  message {
                  text
                  role
                  name
                  userMessage
                  reasoning {
                    duration
                    content
                  }
                }
                }
              }
            }`
}

export default subs