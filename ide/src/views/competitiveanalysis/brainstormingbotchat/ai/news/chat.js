import gql from "graphql-tag";

const apollo = {
    getroleData: gql `query priceSessionVIew($viewId: String!){
    priceSessionVIew(viewId:$viewId){
      id
      data
    }
    }`,
    saveViewData: gql `mutation updatePriceSessionView($entity: PriceSessionViewInput!) {
      updatePriceSessionView(entity: $entity) {
        id
      }
    }`,
    chatRecords: gql `
    query myQuestions(
      $first: Int
      $last: Int
      $before: Cursor
      $after: Cursor
      $copilotId: String
    ) {
      myQuestions(
        where: { copilotID: $copilotId }
        first: $first
        last: $last
        before: $before
        after: $after
        orderBy: { field: CREATED_AT, direction: ASC }
      ) {
        edges {
          node {
            id
            email
            createdByUserFullName
            createdAt
            title
            copilotID
            content
            answerItems {
              id
              aiGenerated
              content
              createdAt
              createdByUserFullName
              isRead
              question {
                content
                __typename
              }
              __typename
            }
            __typename
          }
          cursor
          __typename
        }
        pageInfo {
          hasNextPage
          hasPreviousPage
          startCursor
          endCursor
          __typename
        }
        totalCount
        __typename
      }
    }
  `,

    chatRecordsCheck: gql `query myQuestions($first: Int, $last: Int, $before: Cursor, $after: Cursor) {
  myQuestions(first: $first, last: $last, before: $before, after: $after, orderBy: {field: CREATED_AT, direction: ASC}) {
    edges {
      node {
        id
        content
        copilotID
        answerItems {
          id
          isRead
        }
      }
      cursor
    }
    totalCount
  }
}
`,

    answerRead: gql `mutation updateAnswerReadStatus($id: ID!, $isRead: Boolean!) {
  updateAnswerReadStatus(id: $id, isRead: $isRead) {
    id
    title
    content
    answerItems {
      id
      aiGenerated
      content
      createdAt
      createdByUserFullName
      isRead
      question {
        content
      }
    }
  }
}`,

    chatToUser: gql `
    mutation transferToHumanQuestion($input: CreateHumanQuestionInput!) {
      transferToHumanQuestion(input: $input) {
        content
        copilotID
        createdAt
        createdByUUID
        createdByUserCode
        createdByUserFullName
        createdByUserOpenid
        deletedAt
        departmentCode
        departmentName
        departmentParentCode
        departmentParentName
        email
        feedback {
          comment
          createdByUserFullName
        }
        groupID
        id
        isOpen
        isolated
        questionEvent
        questionStatus
        tel
        title
        updatedAt
      }
    }
  `,

    feedBack: gql `
    mutation feedback(
      $copilot: ID!
      $message: ID!
      $type: Int!
      $reason: String
    ) {
      feedback(
        copilot: $copilot
        message: $message
        type: $type
        reason: $reason
      ) {
        type
        reason
      }
    }
  `,

    messages: gql `
  query messages($copilot: ID!, $first: Int, $keyword: String, $offset: Int, $filter: MessageFilter) {
    messages(copilot: $copilot, first: $first, keyword:$keyword, offset: $offset, filter: $filter) {
      totalCount
      edges {
        node {
          name
          text
          sent
          feedback {
            type
          }
          sender {
            kind
            name
            role
          }
        }
      }
    }
  }
`,


    messagesHasConversation: gql `
query userMessages($copilot: ID!, $first: Int, $keyword: String, $offset: Int, $filter: MessageFilter) {
  userMessages(copilot: $copilot, first: $first, keyword:$keyword, offset: $offset, filter: $filter) {
    totalCount
    edges {
      node {
        name
        text
        sent
        retries {
          text
        }
        feedback {
          type
        }
        skill {
          name
          text
        }
        samples {
          name
          question
          answer
          chunk
          chunkSourceDocumentUri
          chunkSourceDocument
          chunkSourceReference
          chunkSourceOntology
        }
        reasoning {
          duration
          content
        }
        sender {
          kind
          name
          role
        }
      }
    }
  }
}
`,

    messagesHasConversationAndThoughts: gql `
query userMessages($copilot: ID!, $first: Int, $keyword: String, $offset: Int, $filter: MessageFilter) {
  userMessages(copilot: $copilot, first: $first, keyword:$keyword, offset: $offset, filter: $filter) {
    totalCount
    edges {
      node {
        name
        text
        sent
        retries {
          text
        }
        feedback {
          type
        }
        skill {
          name
          text
        }
        samples {
          name
          question
          answer
          chunk
          chunkSourceDocumentUri
          chunkSourceDocument
          chunkSourceReference
          chunkSourceOntology
        }
        thoughtChain {
                    items{
                      title
                      content
                      description
                      status
                    }
                  }
        reasoning {
          duration
          content
        }
        sender {
          kind
          name
          role
        }
      }
    }
  }
}
`,

    // 相似问题
    similarQuestions: gql `query userKnowledges($copilot: ID!, $keyword: String){
  userKnowledges(
    copilot: $copilot,
    keyword: $keyword,
    first: 6
    offset: 0
    order: "similarity desc"
  ) {
    edges {
      node {
        faq {
          name
          question
          distance
        }
      }
    }
    totalCount
  }
}`,


    // 相似任务
    similarTasks: gql `query userTasks($copilot: ID!, $keyword: String){
  userTasks(
    copilot: $copilot,
    keyword: $keyword,
    first: 999
    offset: 0
    order: "similarity desc"
  ) {
    edges {
      node {
        name
        text
        commands
        description
        distance
        recommend
      }
    }
    totalCount
  }
}`,

    robotInfo: gql `
    query copilot($name: ID!) {
      copilot(name: $name) {
        name
        text
        source
        features
      }
    }
  `,

    robots: gql `
    query parts($filter: PartFilter) {
      parts(filter: $filter) {
        name
        text
        description
        tags
        versions {
          name
          sources {
            name
            kind
            content
          }
        }
      }
    }
  `,

    recentCopilots: gql `query userRecentCopilots($filter: CopilotFilter, $first: Int, $keyword: String, $offset: Int) {
    userRecentCopilots(filter: $filter, first: $first, keyword: $keyword, offset: $offset) {
      edges {
        node {
          name
          source
          text
        }
      }
      totalCount
    }
  }`,

    canUseCopilots: gql `query userCopilots($filter: CopilotFilter, $first: Int, $keyword: String, $offset: Int) {
  userCopilots(filter: $filter, first: $first, keyword: $keyword, offset: $offset) {
    edges {
      node {
        name
        source
        text
      }
    }
    totalCount
  }
}`,


    queCategory: gql `query categories($copilot: ID!) {
  categories(copilot: $copilot) {
    name
    filterable
    text
  }
}`,

    // 
    // userDownloadable

    ques: gql `query knowledges($copilot: ID!, $keyword: String, $first: Int, $offset: Int, $filter: KnowledgeFilter) {
  knowledges(
    copilot: $copilot
    keyword: $keyword
    first: $first
    offset: $offset
    filter: $filter
  ) {
    edges {
      node {
        name
        kind
        text
        faq {
          name
          question
          answer
          sort
        }
        doc {
          name
          text
          content
          kind
          uri
          userDownloadable
        }
        updateTime
        createTime
        creator {
          text
          openid
        }
        category {
          name
          text
          sort
        }
      }
    }
    totalCount
  }
}`,


    conversations: gql `query userConversations($copilot: ID!, $keyword: String, $first: Int = 20, $offset: Int = 0, $filter: ConversationFilter) {
  userConversations(copilot: $copilot, keyword: $keyword, first: $first, offset: $offset, filter: $filter) {
    totalCount
    edges {
      node {
        name
        text
        createTime
        updateTime
      }
    }
  }
}
`,

    delConversation: gql `mutation deleteConversation($name: ID!){
  deleteConversation(name: $name){
      name
      text
      createTime
      updateTime
  
  }
}`,


    updateConversation: gql `mutation updateConversation($conversation: ConversationInput!){
  updateConversation(conversation: $conversation){
      name
      text
      createTime
      updateTime
  
  }
}`,

    chatModels: gql `query backends{
  backends{
    name
    text
    description
    isReasoning
    type
    kind
  }
}`,

    clearMessages: gql `mutation clearMessages($conversation: ID){
  clearMessages(conversation: $conversation){
    edges {
      node {
        name
        text
      }
    }
    totalCount
  
  }
}`,

    signs: gql `query JssdkSignature($configName: String!, $url: String!) {
  jssdkSignature(configName: $configName, url: $url) {
    configSignature
    agentConfigSignature
    nonceStr
    url
  }
}
`,

    wxkeys: gql `query workSettings {
  workSettings {
    baseMfaSettings {
      userAgentRegex
      __typename
    }
    corpId
    corpAppSecret
    corpContactSecret
    corpAppAgentId
    corpPrincipalName
    corpServerUri
    corpFollowUrl
    __typename
  }
}`,


    skills: gql `query userSkills($copilot: ID!, $keyword: String, $first: Int, $offset: Int, $filter: TaskFilter, $order: String){
  userSkills(copilot: $copilot, keyword: $keyword, first: $first, offset: $offset, filter: $filter, order: $order){
    totalCount
    edges{
      node {
        name
        text
        description
        multiturn
        prompt
        agent
        task
        categories
      }
    }
  }
}`,

    skillCategories: gql `query skillCategories($copilot: ID!){
  skillCategories(copilot: $copilot){
    name
    text
    description
    sort
  }
}`,

};
export default apollo;