// import apolloProvider from "@/apolloConfig";
import gql from "graphql-tag";

const answerRead = gql`mutation updateAnswerReadStatus($id: ID!, $isRead: Boolean!) {
    updateAnswerReadStatus(id: $id, isRead: $isRead) {
      id
      title
      content
      answerItems {
        id
        aiGenerated
        content
        createdAt
        createdByUserFullName
        isRead
        question {
          content
        }
      }
    }
  }`

export const readImmediate = (variables) => {

  // console.log(variables, '-----读到惹')
    // apolloProvider.clients.helpDesk.mutate({
    //     client: 'helpDesk',
    //     mutation: answerRead,
    //     variables
    // }).then(res => {
    //     // // console.log(res);
    // }).catch(error => {
    //     // // console.log(error);
    // })
}