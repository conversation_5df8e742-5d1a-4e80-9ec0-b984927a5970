import store from "@/store";
import Vue from "vue";
import { readImmediate } from "./read";

let trackCollection = [];
let timer;
function readInterVal(time) {
  clearInterval(timer);
  if (time) {
    if (trackCollection.length > 0) {
      for (let track of trackCollection) {
        readImmediate(track);
      }
    }
    trackCollection = [];
    timer = setTimeout(() => {
      readInterVal(time);
    }, time);
  }
}

readInterVal(3000);

const tracker = (params) => {
  //推入栈中
  trackCollection.push(params);
};

const throttle = (fn, wait = 0) => {
  let timerId;
  let lastInvoke = Number.MIN_SAFE_INTEGER; // 上次调用时间
  return function (...args) {
    // 当前时间
    const currTime = new Date().getTime(); // 距离下次执行的剩余时间
    const remain = Math.max(lastInvoke + wait - currTime, 0); // 更新定时器，确保同一时间只有一个定时器在运行
    clearTimeout(timerId);
    timerId = setTimeout(() => {
      lastInvoke = new Date().getTime();
      fn(...args);
    }, remain);
  };
};

// 判断当前元素是否在可视区域
const isInView = (el) => {
  var rect = el.getBoundingClientRect();

  var elemY = rect.y;

  var elBox = document.getElementsByClassName('contents_robot')[0].getBoundingClientRect();


  // 元素全部出现在视窗
  var isVisible = elBox.y < elemY < (elBox.bottom - 165);

  return isVisible;
};

// 生成随机函数属性名
const createFunName = () => {
  return `track_f_${(Math.random() * 1000000 + "").split(".")[0]}`;
};

// 已绑定的事件处理函数集合
const FunCollection = {};

// 曝光处理函数
const viewFun = throttle((el, binding) => {
  let ans = []
  if(binding.value.data.name && binding.value.data.name.length){
    // // console.log(isInView(el), binding.value.data.name.map(t=>t.content), '----看到惹')
    ans = binding.value.data.name.map(t=>t.isRead)
  }
  if (isInView(el)) {
    // // console.log(ans)
    const occurTime = new Date().getTime();

    const params = getParams(el, binding, occurTime);

    
    if(ans.includes(false) && params.id){
      tracker(params);
    }

    document.getElementById('commentsBoxUser') && document.getElementById('commentsBoxUser').removeEventListener("scroll", FunCollection[el.dataset.viewFun]);
  }
}, 100);

// 埋点事件逻辑
const track = (el, binding, forceRun = false) => {
  const vf = el.dataset.viewFun;

  if (vf && FunCollection[vf]) {
    document.getElementById('commentsBoxUser') && document.getElementById('commentsBoxUser').removeEventListener("scroll", FunCollection[vf]);

    delete FunCollection[vf];
  }

  const fs = createFunName();

  FunCollection[fs] = viewFun.bind(null, el, binding);

  el.dataset.viewFun = fs;

  document.getElementById('commentsBoxUser') && document.getElementById('commentsBoxUser').addEventListener("scroll", FunCollection[fs]);

  FunCollection[fs](el, binding);
};
// 自定义指令
Vue.directive("read", {
  bind: function (el, binding) {
    el.dataset.enterTime = new Date().getTime();

    if (typeof binding.value.t === "undefined" || binding.value.t === "bind") {
      track(el, binding);
    }
  },
  update: function (el, binding) {

    // // console.log(, '---已更改')
    if (binding.value.data.id) {
      track(el, binding);
    }
  },
  unbind: function (el, binding) {
    const vf = el.dataset.viewFun;

    if (vf && FunCollection[vf]) {
      document.getElementById('commentsBoxUser') && document.getElementById('commentsBoxUser').removeEventListener("scroll", FunCollection[vf]);

      delete FunCollection[vf];
    }
  },
});

// 处理参数
const getParams = (el, binding, occurTime, stayTime) => {
  let me = Object.assign({}, store.getters.getUserInfo);
  delete me.positions;
  let params = {
    id: binding.value.data.id, // 事件ID
    isRead: true,
  };

  // // console.log(binding.value.data.id,binding.value.data.name,  '----------')

  return params;
};
