<template>
  <div :class="!forAdmin && (isMobile() ? 'mobilePage' : 'pcPage')" style="height:100vh;width:100vw;">
  <robot
        ref="robot"
        :isRight="false"
        :useCopilotId="nowCopilotId"
        :anonymity="true"
        v-if="nowCopilotId"
        :style="!forAdmin && `max-width: 900px;margin:0px auto;`"
      ></robot>
      <template v-else>
        <div class="noIdSvg">
          <p>该课程尚未创建智能助教~</p>
          <img src="./img/noId.svg" alt="" />
        </div>
      </template>
  </div>
</template>
<script>
import Robot from "./robotVisit";

export default {
  data() {
    return {

    };
  },
  watch: {
  },
  created() {

  },
  mounted() {
    // let historyLength = window.history.length;
    // for(let i =0;i<historyLength;i++){
    //   window.history.pushState(null, null, location.href)
    // }

    //防止页面后退
    history.pushState(null, null, document.URL);
    window.addEventListener("popstate", function () {
      history.pushState(null, null, document.URL);
    });
  },
  methods: {
      isMobile() {
    var isMobile = false;
    var flag = navigator.userAgent.match(
      /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
    );
    if (flag != null) {
      isMobile = true;
    }
    return isMobile

  },
  },
  computed: {
    forAdmin() {
    return this.$route.query.forAdmin
  },
    nowCopilotId(){
      return this.$route.query.id
    }
  },

  components: {
    Robot,
  },
};
</script>

