@-webkit-keyframes breathing {
    0% {
        -webkit-transform: scale(0.9);
        transform: scale(0.9);
    }
    25% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
    60% {
        -webkit-transform: scale(0.9);
        transform: scale(0.9);
    }
    100% {
        -webkit-transform: scale(0.9);
        transform: scale(0.9);
    }
}

.user_robots {
    display: grid;
    width: 100vw;
    height: 100vh;
    overflow-y: auto;
    --lastBox: 340px;
    --pd1: 18%;
    gap: 16px;
    padding: 4px;
    position: relative;
    grid-template-columns: 100px var(--setWidth) 1fr var(--lastBox);
    transition-duration: 0.2s;
    &.square {
        &.toggleMobile {
            grid-template-columns: 40px 1fr;
        }
        &.isMobile {
            .contents_robot .square-infos {
                top: 314px;
                padding: 8px;
            }
            .select_copilots_display {
                position: fixed;
                &.show {
                    left: 0px;
                }
                left: -100%;
                top:0px;
                z-index: 13;
                box-shadow: 0px -2px 4px var(--themelight);
                border-radius: 0px 18px 18px 0px;
            }
            grid-template-columns: 1fr;
        }
    }
    &.onlyCopilotId {
        // grid-template-columns: var(--setWidth) 1fr;
        grid-template-columns: 1fr;
        &.openPostIframe {
            grid-template-columns: var(--setWidth) calc(100% - var(--rightBoxWidth) - var(--setWidth) / 2) calc(var(--rightBoxWidth) - var(--setWidth) / 2);
            &.toggleMobile {
                grid-template-columns: 40px calc(100% - var(--rightBoxWidth) - 20px) calc(var(--rightBoxWidth) - 20px);
            }
        }
        @keyframes fadeIn {
            0% {
                opacity: 0;
            }
            /* 初始透明度 */
            50% {
                opacity: 0;
            }
            /* 中间透明度 */
            100% {
                opacity: 1;
            }
            /* 结束透明度 */
        }
        .pcToggle {
            text-align: right;
            cursor: pointer;
            color: var(--theme);
        }
        &.useToggleBtn {
            // --limit1: 100%;
            // --limit2: 100%;
            &.isPc:not(.hasBg) {
                .select_copilots,
                .select_copilots_display,
                .chatting,
                .chatting_display {
                    border-radius: 0px;
                }
            }
            grid-template-columns: var(--setWidth) 1fr;
            &.openPostIframe {
                grid-template-columns: var(--setWidth) calc(100% - var(--rightBoxWidth) - var(--setWidth) / 2) calc(var(--rightBoxWidth) - var(--setWidth) / 2);
                &.dockLeft {
                    grid-template-columns: calc(var(--rightBoxWidth) - var(--setWidth) / 2) var(--setWidth) calc(100% - var(--rightBoxWidth) - var(--setWidth) / 2);
                    .select_copilots {
                        grid-column: 2 / 3;
                    }
                    .chatting {
                        grid-column: 3 / 4;
                    }
                    .postIframe {
                        grid-column: 1 / 2;
                        grid-row: 1;
                    }
                }
            }
            &.isMobile {
                grid-template-columns: 1fr;
                .chatting,
                .chatting_display {
                    border-radius: 0px;
                }
                .select_copilots,
                .select_copilots_display {
                    border-radius: 0px 18px 18px 0px;
                }
                &.toggleMobile {
                    grid-template-columns: 1fr;
                }
            }
            gap: 0px;
            .showOpen {
                animation: fadeIn 0.4s;
            }
            .addNewChat {
                display: none;
            }
            &.toggleMobile {
                grid-template-columns: 40px 1fr;
                &.openPostIframe {
                    grid-template-columns: 40px calc(100% - var(--rightBoxWidth) - 20px) calc(var(--rightBoxWidth) - 20px);
                    &.dockLeft {
                        grid-template-columns: calc(var(--rightBoxWidth) - 20px) 40px calc(100% - var(--rightBoxWidth) - 20px);
                        .select_copilots {
                            grid-column: 2 / 3;
                        }
                        .chatting {
                            grid-column: 3 / 4;
                        }
                        .postIframe {
                            grid-column: 1 / 2;
                            grid-row: 1;
                        }
                    }
                }
                // .select_copilots {
                //   transform: translateX(-0px);
                // }
                .addNewChat {
                    display: block;
                }
                .robot_chooses {
                    opacity: 0;
                }
            }
            padding: 0px;
        }
        &.isMobile {
            --pd1: 24px;
            .project-apps {
                grid-template-columns: repeat(1, 1fr);
            }
            padding: 0px;
            .chatting {
                min-width: 100vw;
            }
            .action-tabs {
                width: auto;
            }
            .V2-welcome {
                font-size: 18px;
                width: 80%;
                left: 10%;
            }
            .contents_robot .chat_box.robot .txt {
                margin-left: 2px;
                max-width: 98%;
            }
            .contents_robot .chat_box.robot::after {
                background: center center / auto 24px no-repeat var(--robotIcon);
                height: 24px;
                width: 24px;
                top: -4px;
            }
            .contents_robot .chat_box.robot {
                padding-top: 32px;
            }
            .contents_robot .chat_box.person .txt {
                margin-right: 0px;
            }
            .contents_robot .chat_box.person::after {
                display: none;
            }
            .contents_robot .chat_box.person .user_name {
                display: none;
            }
        }
        &.hasBg {
            padding: 8px 10vw;
            background: linear-gradient(180deg, var(--adminBg1), var(--adminBg2));
            gap: 8px;
        }
        &.showClose {
            grid-template-columns: 180px 1fr;
            filter: none;
            gap: 4px;
            padding: 0px;
            &.hiddenChats {
                grid-template-columns: 1fr;
                .select_copilots.onlyCopilotId {
                    display: none;
                }
            }
            .select_copilots .robot_chooses {
                max-height: 100%;
            }
            .select_copilots .robot_chooses .conversation .conver_name {
                width: 100%;
                padding-right: 12px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                position: relative;
            }
            .select_copilots .robot_chooses .conversation .conver_name .btn_more {
                position: absolute;
                right: 0px;
                top: 4px;
            }
            .select_copilots .robot_chooses .conversation .conver_more {
                width: 100%;
            }
            .select_copilots {
                padding: 6px;
            }
            .select_copilots .robot_chooses .conversation {
                margin-left: 8px;
                padding-left: 4px;
            }
            .select_copilots .robot_chooses .conversation li {
                flex-wrap: wrap;
            }
            .co-tit .el-icon-arrow-down {
                display: none;
            }
        }
    }
    &>* {
        transition-duration: 0.6s;
    }
    .setbtn-item.el-dropdown-menu__item:focus,
    .setbtn-item.el-dropdown-menu__item:not(.is-disabled):hover {
        background: var(--themelight);
        color: var(--theme);
    }
    .over_line2 {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }
    .search_res {
        position: absolute;
        top: 78px;
        left: 132px;
        display: none;
        background: transparent;
        opacity: 0;
        z-index: 19;
        &.show {
            opacity: 1;
            display: block;
        }
        .ans {
            position: relative;
            border-bottom: 1px solid #eee;
            // padding: 32px 8px 8px 30px;
            word-break: break-all;
            padding-left: 65px;
            height: 65px;
            padding-top: 5px;
            transition-duration: 0.6s;
            .icon {
                width: 50px;
                height: 50px;
                object-fit: scale-down;
                position: absolute;
                left: 4px;
                top: 6px;
            }
            &>* {
                cursor: default;
            }
            &:hover,
            &.act {
                background-color: #eceef0;
                .showLast {
                    display: block;
                }
            }
            .time {
                position: absolute;
                left: 42px;
                top: 8px;
                background: #eee;
                padding: 1px 4px;
                font-size: 12px;
                border-radius: 3px;
            }
            .showLast {
                position: absolute;
                right: 12px;
                top: 8px;
                padding: 1px 4px;
                font-size: 12px;
                border-radius: 3px;
                display: none;
                cursor: pointer;
                color: var(--theme);
            }
            &.user {
                padding: 32px 12px 12px 45px;
                height: auto;
                background: 5px 5px / 24px auto no-repeat url("../img/svgtopng/user2.png");
                &:hover,
                &.act {
                    background-color: #eceef0;
                }
            }
            &.copilot {
                padding: 32px 12px 12px 45px;
                height: auto;
                background: 5px 5px / 24px auto no-repeat var(--robotImg);
                &:hover,
                &.act {
                    background-color: #eceef0;
                }
            }
        }
    }
    .search_res_left {
        background: #fff;
        float: left;
        width: 310px;
        z-index: 99;
        box-shadow: 0px 0px 12px #d8e8f7;
        border-radius: 18px;
        /*! transition-duration: 0.6s; */
        height: auto;
        max-height: calc(100% - 100px);
        overflow-y: auto;
        padding: 12px;
        transition-duration: 0.6s;
        .group-tit {
            font-size: 15px;
            // border-top: 1px solid #e2ebf4;
            // padding-top: 8px;
        }
    }
    .search_res_right {
        background: #fff;
        width: 510px;
        z-index: 99;
        box-shadow: 0px 0px 12px #d8e8f7;
        border-radius: 18px;
        /*! transition-duration: 0.6s; */
        height: 600px;
        max-height: calc(100% - 100px);
        overflow-y: auto;
        padding: 12px;
        float: left;
        margin-left: 4px;
        // opacity: 0;
        transition-duration: 0.6s;
        // display: none;
        &.show {
            opacity: 1;
            display: block;
        }
    }
    .search_res_records {
        background: #fff;
        width: 450px;
        z-index: 99;
        box-shadow: 0px 0px 12px #d8e8f7;
        border-radius: 18px;
        /*! transition-duration: 0.6s; */
        height: 600px;
        max-height: calc(100% - 100px);
        overflow-y: auto;
        padding: 12px;
        float: left;
        margin-left: 4px;
        // opacity: 0;
        transition-duration: 0.6s;
    }
    // &:has(.que_collection){
    //   --lastBox: 340px;
    // }
    // filter: drop-shadow(var(--dropShadow));
    &> :not(.nobd) {
        border-radius: var(--normalRadius);
        background: #fff;
    }
    .sides {
        background: linear-gradient(to bottom, var(--line3), var(--theme));
        display: grid;
        grid-template-rows: 100px repeat(3, 80px) 1fr repeat(2, 40px) 40px;
        padding: 0px 18px;
        gap: 12px;
        &>* {
            // background: #eee;
        }
        .n1 {
            background: center / 80% auto no-repeat var(--robotImg);
        }
        .n2 {
            background: center 12px / auto calc(100% - 60px) no-repeat url("../img/svgtopng/chat.png");
        }
        .n3 {
            background: center 12px / auto calc(100% - 60px) no-repeat url("../img/svgtopng/chat2.png");
        }
        .n4 {
            background: center 12px / auto calc(100% - 60px) no-repeat url("../img/svgtopng/chat3.png");
        }
        .n2,
        .n3,
        .n4 {
            color: #fff;
            text-align: center;
            padding-top: 48px;
            cursor: pointer;
            position: relative;
            &.active::after {
                background: #ffffff70;
                content: "";
                position: absolute;
                display: block;
                left: calc(50% - 22px);
                top: 0px;
                width: 44px;
                height: 44px;
                border-radius: 12px;
            }
        }
        .n5 {
            grid-row-start: 6;
            grid-row-end: 7;
            border-top: 1px solid #fff;
            background: center 18px / auto calc(100% - 22px) no-repeat url("../img/svgtopng/chat4.png");
        }
        .n6 {
            grid-row-start: 7;
            grid-row-end: 8;
            background: center 18px / auto calc(100% - 22px) no-repeat url("../img/svgtopng/chat5.png");
        }
        .n5,
        .n6 {
            color: #fff;
            text-align: center;
            padding-top: 48px;
            cursor: pointer;
            position: relative;
        }
    }
    .select_copilots_cards {
        grid-area: 2 / 5 / 1 / 2;
        .robot_card {
            grid-template-columns: repeat(4, 1fr);
            display: grid;
            grid-gap: 18px;
            padding: 24px;
            &.haspdTop {
                padding-top: 48px;
            }
            .page-cubes {
                position: absolute;
                right: 48px;
                top: 32px;
                display: flex;
                gap: 12px;
                .page-cube {
                    border-radius: 12px;
                    width: 12px;
                    height: 12px;
                    background: #eee;
                    cursor: pointer;
                    &.iscur,
                    &:hover {
                        background: var(--theme);
                    }
                }
            }
            li {
                border: 1px solid #eee;
                border-radius: 6px;
                height: 210px;
                transition-duration: 0.6s;
                padding: 12px 16px;
                position: relative;
                // cursor: pointer;
                h3 {
                    margin-bottom: 0px;
                }
                img {
                    width: 60px;
                    height: 56px;
                    object-fit: scale-down;
                    padding-bottom: 8px;
                }
                .startR {
                    cursor: pointer;
                    position: absolute;
                    right: 16px;
                    bottom: 8px;
                    color: var(--tagColor2);
                }
                &:hover {
                    box-shadow: 0px 2px 10px var(--tagShadow);
                }
            }
        }
    }
    .toggleMobileBtn {
        position: fixed;
        z-index: 12;
        left: 0px;
        top: 52px;
        font-size: 15px;
        background: #fff;
        border-radius: 0px 12px 12px 0px;
        color: var(--theme);
        padding: 5px;
        box-shadow: 0px 0px 6px var(--lineCube1);
        &.tp1 {
            top: 2px;
        }
        &~.select_copilots {
            border-radius: 0px 18px 18px 0px;
        }
    }
    .select_copilots_display {
        background: var(--themelight2);
        padding: 12px;
        height: 100%;
        overflow: auto;
        width: var(--setWidth);
        .robot_chooses_display {
            height: calc(100% - 60px);
        }
        &.isPc.show {
            width: auto;
            .robot_chooses_display {
                display: none;
            }
        }
        .copilot_selects_collection {
            overflow-y: auto;
            height: 50%;
        }
        .copilot_selects {
            position: relative;
            width: 95%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px;
            padding-left: 18px;
            font-size: 15px;
            font-weight: noraml;
            border-radius: 16px;
            margin: 6px 0px;
            .co-tit {
                cursor: pointer;
                user-select: none;
                display: flex;
                align-items: center;
                gap: 8px;
                width: calc(100% - 20px);
            }
            .newchat {
                display: none;
            }
            &.choosed {
                background: var(--themelight);
                .co-tit {
                    color: var(--theme);
                }
                .newchat {
                    display: block;
                }
            }
            .newchat {
                // position: absolute;
                // right: 0px;
                // top: 50%;
                cursor: pointer;
                width: var(--cube);
                height: var(--cube);
                background: #fff;
                text-align: center;
                line-height: var(--cube);
                border-radius: var(--cube);
                // box-shadow: 0px 0px 5px #78a1fa;
                --cube: 20px;
                font-size: 10px;
                color: var(--theme);
            }
        }
        .conversation-tit {
            font-size: 14px;
            padding-top: 14px;
            padding-bottom: 8px;
            border-top: 1px solid #ededed;
            margin-top: 14px;
            color: #ababab;
        }
        .conversation {
            margin-bottom: 0px;
            padding-left: 8px;
            overflow-y: auto;
            height: calc(45% - 40px);
            li {
                display: flex;
                align-items: center;
                // justify-content: center;
                padding: 6px 0px;
                padding-right: 8px;
                cursor: pointer;
                font-size: 13px;
                color: #bbb;
                &.ischat {
                    span.conver_name,
                    i {
                        color: var(--theme);
                    }
                }
                .conver_name {
                    width: calc(100% - 54px);
                    word-break: break-all;
                }
                .conver_more {
                    white-space: nowrap;
                }
                i {
                    margin-left: 4px;
                }
            }
        }
    }
    .chatting_display {
        height: 100%;
        overflow: hidden;
        h3 {
            font-weight: bold;
        }
        .project-tabs {
            display: flex;
            gap: 12px;
            margin-bottom: 18px;
            li {
                border: 1px solid #eee;
                border-radius: 8px;
                padding: 4px 12px;
                cursor: pointer;
                &.nowIdx,
                &:hover {
                    border-color: var(--theme);
                    background: var(--theme);
                    color: #fff;
                    box-shadow: 0px 0px 8px var(--themelight);
                }
            }
        }
        .project-apps {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 14px;
            li {
                border: 1px solid #eee;
                overflow: hidden;
                border-radius: 12px;
                position: relative;
                padding-left: 110px;
                padding-right: 10px;
                height: 120px;
                cursor: pointer;
                .name {
                    margin-top: 24px;
                }
                .des {
                    font-size: 14px;
                    color: #aaa;
                    margin-top: 4px;
                }
                .icon {
                    width: 60px;
                    height: 60px;
                    border-radius: 60px;
                    position: absolute;
                    top: 30px;
                    left: 18px;
                    background: rgba(0, 0, 0, 0.06);
                    img {
                        object-fit: scale-down;
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
        .robot-project {
            width: 100%;
            padding: 24px 0px;
            height: 100%;
            position: relative;
            h3 {
                padding: 8px var(--pd1);
            }
            .project-apps {
                overflow-y: auto;
                padding: 8px var(--pd1);
                max-height: calc(100% - 42px);
            }
        }
    }
    .select_copilots {
        display: grid;
        grid-template-rows: 50px 1fr 40px 270px;
        &.onlyCopilotId {
            grid-template-rows: 1fr;
            &:has(.pcToggle) {
                grid-template-rows: 30px 1fr;
            }
            background: var(--themelight2);
            &.isMobile {
                position: fixed;
                z-index: 13;
                width: min(60vw, 250px);
                border-right: 1px solid #eee;
                left: max(-60vw, -250px);
                &.show {
                    left: 0px;
                }
            }
        }
        padding: 12px;
        height: 100%;
        overflow: hidden;
        .search_msg {
            display: flex;
            position: relative;
            align-items: center;
            input {
                width: calc(100% - 40px);
                line-height: 36px;
                border: 0px;
                border-radius: 6px;
                text-indent: 1rem;
                background: #eee;
            }
            input:focus-visible {
                outline: none;
            }
            .el-icon-error {
                cursor: pointer;
                position: absolute;
                color: #999;
                font-size: 19px;
                right: 48px;
                top: 16px;
            }
            .seq-icon {
                width: 32px;
                height: 32px;
                display: inline-block;
                border-radius: 18px;
                margin-left: 8px;
                background: center center / auto 16px no-repeat url("../img/svgtopng/search.png"), linear-gradient(to bottom, var(--line3), var(--theme));
            }
        }
        .robot_chooses {
            // padding: 0px 8px;
            overflow-y: auto;
            .copilot_selects {
                position: relative;
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 12px;
                font-size: 15px;
                font-weight: bold;
                background: var(--themelight);
                border-radius: 16px;
                margin: 8px 0px;
                .co-tit {
                    color: var(--theme);
                    cursor: pointer;
                    user-select: none;
                }
                .newchat {
                    // position: absolute;
                    // right: 0px;
                    // top: 50%;
                    cursor: pointer;
                    width: var(--cube);
                    height: var(--cube);
                    background: #fff;
                    text-align: center;
                    line-height: var(--cube);
                    border-radius: var(--cube);
                    // box-shadow: 0px 0px 5px #78a1fa;
                    --cube: 20px;
                    font-size: 10px;
                    color: var(--theme);
                }
            }
            .conversation {
                margin-left: 18px;
                margin-bottom: 0px;
                // &:empty{
                //   margin-bottom:0px;
                // }
                border-left: 2px solid var(--themelight);
                padding-left: 8px;
                li {
                    display: flex;
                    align-items: center;
                    // justify-content: center;
                    padding: 4px 0px;
                    cursor: pointer;
                    &.ischat {
                        span,
                        i {
                            color: var(--theme);
                        }
                    }
                    .conver_name {
                        width: calc(100% - 54px);
                        word-break: break-all;
                    }
                    .conver_more {
                        white-space: nowrap;
                    }
                    i {
                        margin-left: 4px;
                    }
                }
            }
            .robot_choose {
                position: relative;
                &.choosed {
                    background: #e2ecff;
                    color: var(--theme);
                }
                &.hasRecordChange::after {
                    content: "";
                    display: block;
                    width: 5px;
                    height: 5px;
                    border-radius: 5px;
                    background: #ff5500;
                    position: absolute;
                    right: -3px;
                    top: -3px;
                }
                background: #eee;
                color: #bbb;
                line-height: 48px;
                margin: 12px 0px;
                border-radius: 18px;
                text-align: center;
                font-weight: bold;
                font-size: 15px;
                cursor: pointer;
            }
        }
        h3 {
            border-top: 1px solid #eee;
            letter-spacing: 0.1em;
            text-align: center;
            padding-top: 12px;
            font-size: 18px;
        }
        .robot_recommends {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            li {
                height: 130px;
                line-height: 20px;
                font-size: 12px;
                overflow: hidden;
                padding-top: 90px;
                text-align: center;
                color: #5e64ad;
                cursor: pointer;
            }
            .icon1 {
                background: center 12px / auto 80px no-repeat url("../img/svgtopng/roboticon1.png");
            }
            .icon2 {
                background: center 12px / auto 80px no-repeat url("../img/svgtopng/roboticon2.png");
            }
            .icon3 {
                background: center 12px / auto 80px no-repeat url("../img/svgtopng/roboticon3.png");
            }
            .icon4 {
                background: center 12px / auto 80px no-repeat url("../img/svgtopng/roboticon4.png");
            }
        }
    }
    .chatting {
        height: 100%;
        min-width: 420px;
        overflow-y: auto;
    }
    .que_collection {
        display: grid;
        grid-template-rows: 100px 30px 30px;
        gap: 12px;
        padding: 0px 12px;
        .el-collapse-item__header {
            padding-left: 24px;
            position: relative;
            line-height: 24px;
            height: auto;
            padding-top: 12px;
            padding-bottom: 12px;
        }
        .el-collapse {
            width: 96%;
            margin: 0px auto;
            height: 96%;
            overflow: auto;
        }
        .el-collapse-item__arrow {
            margin: 0px;
        }
        .el-collapse-item__header>i:nth-child(1) {
            position: absolute;
            left: 0px;
        }
        .el-collapse-item__header {
            padding-left: 24px;
            position: relative;
        }
        .el-collapse-item__content {
            color: var(--themeDark);
            padding: 0px 12px 12px 12px;
            img {
                width: 100%;
                max-width: 140px;
            }
        }
        .ask {
            width: 80px;
            height: 80px;
            border-radius: 80px;
            background: center center/auto 50% no-repeat url("../img/svgtopng/ask.png"), linear-gradient(to bottom, var(--line3), var(--theme));
            margin-left: calc(50% - 40px);
            margin-top: 24px;
        }
        .title {
            text-align: center;
            line-height: 30px;
            font-size: 18px;
            font-weight: bold;
            letter-spacing: 0.1em;
        }
        .selects {
            height: 40px;
            padding: 0px 4px;
            padding-bottom: 24px;
            overflow: hidden;
            position: relative;
            i {
                cursor: pointer;
                height: 20px;
                width: 16px;
                line-height: 20px;
                background: aliceblue;
            }
            li {
                // float: left;
                display: inline-block;
                padding: 4px 12px;
                cursor: pointer;
                color: #666;
                line-height: 30px;
                transition-duration: 0.6s;
                position: relative;
                &.act {
                    color: var(--theme);
                    &::after {
                        content: "";
                        left: calc(50% - 10px);
                        width: 20px;
                        position: absolute;
                        top: 2.2em;
                        border-radius: 3px;
                        height: 3px;
                        display: block;
                        background: var(--theme);
                    }
                    //   border-bottom: 2px solid
                }
            }
        }
        .ques {
            li {
                position: relative;
                padding: 18px 0px;
                padding-left: 36px;
                line-height: 1.5;
                border-bottom: 1px solid #eee;
                cursor: pointer;
                transition-duration: 0.8s;
                &:hover {
                    background: #efefef;
                }
                &::after {
                    background: var(--theme);
                    content: "";
                    position: absolute;
                    display: block;
                    left: 12px;
                    top: calc(50% - 3px);
                    width: 6px;
                    height: 6px;
                    border-radius: 12px;
                }
            }
        }
    }
    .aTag {
        margin-right: 6px;
        font-size: 11px;
        background-color: var(--tagColor1);
        color: #fff;
        border-radius: 2px;
        display: inline-block;
        cursor: default;
        padding: 2px 6px;
    }
    // from 
    .jssvc .header-contain {
        // background-image: url(./img/display/jssvc/banner.png);
        .header-top {
            background-color: unset;
            background-position: right bottom;
        }
    }
    // 
    .header-contain {
        position: relative;
        background-size: cover;
        background-position: 0px 0px;
        &.noBanner {
            background-color: var(--theme);
        }
        .hideNow {
            visibility: hidden;
        }
        .el-dropdown {
            color: #fff;
        }
        .header-top {
            padding: 6px 24px;
            height: var(--headerTop);
            display: flex;
            &.mini {
                padding: 6px 12px;
            }
            justify-content: space-between;
            align-items: center;
            .header-logo {
                .logo-txt {
                    color: #fff;
                    padding-left: 18px;
                    font-weight: bold;
                    font-size: 18px;
                    &.mini {
                        font-size: 13px;
                    }
                }
                img {
                    height: var(--logoHeight);
                    margin-right: 8px;
                    &.mini {
                        height: 36px;
                    }
                }
            }
            .nav-a {
                margin-left: 24px;
                a {
                    // color: #fff;
                    padding: 0 24px 8px 24px;
                    margin-right: 20px;
                    position: relative;
                    &::after {
                        content: "";
                        display: block;
                        position: absolute;
                        bottom: -8px;
                        left: 50%;
                        width: 0%;
                        height: 3px;
                        border-radius: 3px;
                        background: var(--backgroundThemeColor);
                        transition: all 0.2s ease-in-out;
                    }
                    &.active,
                    &:hover {
                        &::after {
                            width: 30%;
                            left: 35%;
                        }
                    }
                }
            }
            .header-login {
                display: inline-flex;
                align-items: center;
                .warning-txt {
                    color: #fff;
                    position: relative;
                    z-index: 1;
                }
                .el-divider--vertical {
                    margin: 0px 16px;
                }
                .el-badge__content.is-fixed.is-dot {
                    right: 5px;
                    top: 5px;
                    border: none;
                    /*! z-index: ; */
                }
                .login-font {
                    font-size: 13px;
                    // color: #fff;
                    &:hover {
                        // color: var(--otherFontColor);
                    }
                    &.login-btn {
                        border: 1px solid #fff;
                        padding: 5px 16px;
                        height: 40px;
                        line-height: 30px;
                        border-radius: 30px;
                    }
                }
            }
        }
        .nav-contain {
            padding: 15px 0 8px 0;
            .el-input {
                width: 25%;
                .el-input__inner {
                    border: none;
                    border-radius: inherit;
                    height: 30px;
                }
                .el-input__icon {
                    font-size: 18px;
                    font-weight: bold;
                    color: var(--fontColor);
                    line-height: 30px;
                }
            }
        }
    }
    .mobilePage {}
    .pcPage {
        background: linear-gradient(to bottom, var(--tagColor1), var(--themelight));
        margin: 0px auto;
        padding: 18px 0px;
    }
    @media screen and (max-width: 660px) {
        .contents_robot_box {
            .queVary li {
                padding: 2px 3px;
                padding-left: 8px;
                font-size: 11px;
                margin-bottom: 4px;
                margin-right: 4px;
                position: relative;
                &::after {
                    position: absolute;
                    content: "";
                    display: block;
                    left: 2px;
                    background: #999;
                    border-radius: 3px;
                    width: 3px;
                    height: 3px;
                    top: 8px;
                }
                i {
                    // font-size:5px;
                    display: none;
                }
            }
        }
    }
    .useFit {
        background: #fff;
        border-radius: 16px;
        box-shadow: 0px 0px 12px var(--themelight);
        width: var(--widthDefineBox);
        height: calc(100vh - 110px);
        position: fixed;
        right: 18px;
        top: 70px;
    }
    .load-6 .letter {
        animation-name: loadingF;
        animation-duration: var(--time);
        animation-iteration-count: infinite;
        animation-direction: linear;
        float: left;
    }
    @keyframes loadingF {
        0% {
            opacity: 0;
        }
        100% {
            opacity: 1;
        }
    }
    .robot_img {
        position: fixed;
        cursor: pointer;
        img {
            width: 45px;
        }
        right: 28px;
        bottom: 96px;
        z-index: 99;
        width: initial !important;
        .animation {
            position: relative;
            &.redPoint::after {
                content: "";
                position: absolute;
                top: 0px;
                right: 0px;
                background: #ff5500;
                width: 5px;
                height: 5px;
                border-radius: 5px;
                display: block;
            }
            .close {
                position: absolute;
                bottom: 64px;
                right: 8px;
            }
        }
    }
    .btns {
        .btn {
            display: inline-block;
            padding: 0px 12px;
            background: #fff;
            margin-right: 8px;
            margin-bottom: 8px;
            font-size: 12px;
            transition-duration: 0.5s;
            border-radius: 5px;
            &:hover {
                transform: translateY(-3px);
            }
            border: 1px solid #eee;
            cursor: pointer;
        }
    }
    .animation {
        animation: hover 10s cubic-bezier(0.12, 0.9, 0.9, 0.9) infinite;
    }
    @keyframes hover {
        0% {
            transform: translateY(5px);
        }
        20% {
            transform: translateY(15px);
        }
        60% {
            transform: translateY(30px);
        }
        80% {
            transform: translateY(15px);
        }
        100% {
            transform: translateY(5px);
        }
    }
    .isMobile {
        .contents_robot .recommendQues {
            padding-left: 12px;
        }
        .contents_robot_box {
            .openAllTasks {
                left: 8px;
                top: 6px;
            }
            .queVary {
                // padding-top: 36px;
                &.no_pd {
                    padding-top: 8px;
                }
                border-top: 1px solid #ddd;
                li {
                    margin-bottom: 8px;
                }
            }
        }
    }
    .contents_robot_box {
        overflow-y: auto;
        width: 100%;
        height: 100%;
        .queVary {
            background: #f7f7f7;
            min-height: 46px;
            .icon-theme {
                --theme: #666;
                &:hover {
                    // icon-theme
                    --theme: var(--themeRept);
                }
            }
            &.hasVary {
                background: var(--cate);
                padding-right: 12px;
                padding-top: 12px !important;
                &.no_pd {}
            }
            .varyTarget {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding-bottom: 6px;
                span {
                    cursor: default;
                }
                .el-icon-error {
                    color: var(--theme);
                    font-size: 17px;
                }
            }
            padding-bottom: 14px;
            border-radius: 12px 12px 0px 0px;
            padding: 8px 12px;
            padding-right: 100px;
            overflow: hidden;
            margin-bottom: -6px;
            position: relative;
            i.hand {
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
            }
            li {
                float: left;
                border: 1px solid #bbb;
                color: #666;
                padding: 3px 6px;
                border-radius: 6px;
                font-size: 12px;
                background: #fff;
                cursor: pointer;
                margin-right: 8px;
                margin-bottom: 4px;
                &:hover,
                &.active {
                    border: 1px solid var(--theme);
                    color: var(--theme);
                }
            }
        }
        &.isRight {
            position: fixed;
            right: 12px;
            top: 76px;
            width: var(--widthDefine);
            height: calc(100vh - 92px);
            filter: drop-shadow(0px 0px 12px #cddce6);
        }
        z-index: 100 !important;
        --cubeWidth: 70px;
    }
    p.action-box {
        position: absolute;
        width: calc(100% - 0px);
        --cutThree: auto;
        &.cutThree {
            --cutThree: calc(50% - 80px);
        }
        /*! box-sizing: content-box; */
        background: var(--bg);
        top: 0px;
        left: 0px;
        padding: 12px;
        /*! box-shadow: 0px 0px 12px aliceblue; */
        border-bottom: 1px solid #eee;
        .tabs {
            box-shadow: 0px 5px 10px rgba(22, 93, 255, 0.1);
            padding: 0px;
            list-style: none;
            background: #fff;
            display: inline-block;
            border-radius: 50px;
            position: relative;
            height: 37px;
        }
        .tabs a {
            text-decoration: none;
            color: #777;
            text-transform: uppercase;
            padding: 8px 0px;
            display: inline-block;
            position: relative;
            z-index: 1;
            transition-duration: 0.5s;
            width: var(--cubeWidth);
            text-align: center;
            font-size: 14px;
        }
        .tabs a.active {
            color: #fff;
        }
        .tabs a i {
            margin-right: 5px;
        }
        .tabs .selector {
            height: 100%;
            display: inline-block;
            position: absolute;
            left: var(--cubeWidth);
            top: 0px;
            z-index: 1;
            width: var(--cubeWidth);
            border-radius: 50px;
            transition-duration: 0.5s;
            transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
            background: var(--lineCube1);
            background: linear-gradient( 45deg, var(--lineCube1) 0%, var(--lineCube2) 100%);
        }
        .selector.ai {
            left: 0px;
        }
    }
    .taskRecommend {
        &:not(:last-child) {
            margin-bottom: 8px;
        }
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 4px;
        padding: 6px 12px;
        font-size: 13px;
        cursor: pointer;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0px 1px 6px var(--themelight);
        p {
            margin-bottom: 0px;
            &:nth-child(2) {
                color: #999;
            }
        }
    }
    .action-tabs {
        display: flex;
        align-items: center;
        width: calc(50% - 80px);
        flex-direction: row-reverse;
        a.user {
            width: 28px;
            height: 28px;
            display: inline-block;
            cursor: pointer;
            // background: center / 80% auto no-repeat url(./img/user.svg);
            background: center / 80% auto no-repeat url(v-bind(bguser));
            border-radius: 0px 5px 5px 0px;
        }
        a.full {
            width: 28px;
            height: 28px;
            display: inline-block;
            cursor: pointer;
            background: center / 80% auto no-repeat url(v-bind(bgfull));
            // background: center / 80% auto no-repeat url(./img/user.svg);
            border-radius: 0px 5px 5px 0px;
        }
        a.nofull {
            width: 28px;
            height: 28px;
            display: inline-block;
            cursor: pointer;
            // background: center / 80% auto no-repeat url(./img/nofull.svg);
            background: center / 80% auto no-repeat url(v-bind(bgnofull));
            border-radius: 0px 5px 5px 0px;
        }
    }
    .contents_robot {
        height: 100%;
        // overflow-y: auto;
        padding: 4px 14px;
        border-radius: 12px;
        position: relative;
        width: 100%;
        max-width: min(100%, var(--limit1));
        min-width: min(100%, var(--limit2));
        margin: 0px auto;
        background: var(--wholeBg);
        .tip.fixed {
            height: 40px;
            font-size: 12px;
            color: rgb(187, 187, 187);
            text-align: center;
            position: sticky;
            z-index: 20;
            left: 0px;
            width: 100%;
            top: 0px;
            color: var(--theme);
        }
        .chat_box {
            margin: 12px 0px;
            &.robot {
                position: relative;
                text-align: left;
                padding-top: 6px;
                &::after {
                    content: "";
                    position: absolute;
                    background: center center / auto 36px no-repeat var(--robotIcon);
                    left: 2px;
                    top: 0px;
                    height: 36px;
                    width: 36px;
                    border-radius: 36px;
                    box-shadow: 0px 0px 12px #eee;
                    background-color: #fff;
                }
                .txt {
                    padding: 12px;
                    padding-top: 0;
                    background: var(--rbtTxtBg);
                    color: var(--txtColorR);
                    margin-left: 48px;
                    display: inline-block;
                    max-width: calc(100% - 102px);
                    &.size1 {
                        max-width: calc(100% - 132px);
                    }
                    border-radius: 0px 6px 6px 6px;
                    word-break: break-all;
                    text-align: left;
                    line-height: 2;
                    // box-shadow: var(--shadowR);
                    .tip {
                        // border-top: 1px solid #bbb;
                        padding-top: 6px;
                        margin-top: 6px;
                        .tip-0 {
                            color: var(--line2);
                            display: block;
                            font-size: 12px;
                            margin-bottom: 6px;
                        }
                        .tip-1 {
                            display: flex;
                            align-items: center;
                            // justify-content: space-between;
                            // margin-bottom: 6px;
                        }
                    }
                }
            }
            &.help {
                position: relative;
                text-align: left;
                padding-top: 6px;
                // background: 0px 0px / auto 36px no-repeat url(./img/normal3.svg);
                background: 0px 0px / auto 36px no-repeat url(v-bind(bgnormal3));
                .lastQue {
                    display: block;
                    font-size: 12px;
                    border-bottom: 1px solid #bbb;
                    color: #aaa;
                }
                .txt {
                    padding: 10px 12px;
                    background: #f5f8ff;
                    color: #333;
                    margin-left: 48px;
                    border: 1px solid #d7e5ec;
                    display: inline-block;
                    max-width: calc(100% - 102px);
                    border-radius: 0px 6px 6px 6px;
                    word-break: break-all;
                    text-align: left;
                    line-height: 2;
                    box-shadow: var(--shadowH);
                    .tip {
                        border-top: 1px solid #bbb;
                        padding-top: 6px;
                        margin-top: 6px;
                        .tip-0 {
                            color: rgb(153, 153, 153);
                            display: block;
                            font-size: 12px;
                            margin-bottom: 6px;
                        }
                        .tip-1 {
                            display: flex;
                            align-items: center;
                            // justify-content: space-between;
                        }
                    }
                }
                .cor2 {
                    color: #ecccbe;
                    background: #fff8f5;
                    border: none;
                }
            }
            &.person {
                position: relative;
                text-align: right;
                padding-top: 12px;
                .user_name {
                    position: absolute;
                    cursor: default;
                    right: 2px;
                    top: 40px;
                    /*! background: #df8282; */
                    height: 80%;
                    font-size: 11px;
                    color: #999;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    right: 20px;
                    max-width: 4em;
                    transform: translateX(50%);
                }
                &::after {
                    content: "";
                    position: absolute;
                    background: center center / auto 36px no-repeat var(--avatar);
                    right: 2px;
                    top: 0px;
                    height: 36px;
                    width: 36px;
                    border-radius: 36px;
                    box-shadow: 0px 0px 12px #eee;
                    background-color: #fff;
                }
                .txt {
                    padding: 10px 12px;
                    white-space: pre-wrap;
                    word-break: break-word;
                    text-align: left;
                    margin-right: 48px;
                    // background: linear-gradient(to right, var(--line1), var(--line2));
                    background: var(--personTxtBg);
                    color: var(--personTxtColor);
                    // color: rgba(0, 0, 0, 0.9);
                    // var(--txtColorP);
                    display: inline-block;
                    max-width: calc(100% - 102px);
                    border-radius: 6px 0px 6px 6px;
                    // box-shadow: var(--shadowP);
                }
            }
        }
        .action-box {
            display: flex;
            justify-content: space-between;
        }
        a.clear {
            width: 28px;
            height: 28px;
            display: inline-block;
            cursor: pointer;
            // background: center / 80% auto no-repeat url(./img/clear.svg);
            background: center / 80% auto no-repeat url(v-bind(bgclear));
        }
        a.close {
            width: 28px;
            height: 28px;
            display: inline-block;
            cursor: pointer;
            // background: center / 80% auto no-repeat url(./img/close.svg);
            background: center / 80% auto no-repeat url(v-bind(bgclose));
        }
        a.chats {
            width: 26px;
            height: 26px;
            display: inline-block;
            cursor: pointer;
            margin-right: 4px;
            border-radius: 4px;
            // background: center / 66% auto no-repeat url(./img/chatsIcon2.svg);
            background: center / 66% auto no-repeat url(v-bind(bgchatsIcon2));
            &.opened {
                background: center/ 66% auto no-repeat url(v-bind(bgchatsIcon2)), var(--themelight);
                // background: center/ 66% auto no-repeat url(./img/chatsIcon2.svg),var(--themelight);
            }
        }
        .noClick {
            cursor: not-allowed;
            opacity: 0.5;
            &>* {
                pointer-events: none;
            }
        }
        .recommend {
            margin: 18px auto;
            // &::before {
            //   content: "猜您想问";
            //   position: absolute;
            //   left: 28px;
            //   top: 76px;
            // }
            a {
                cursor: pointer;
                color: rgb(244, 170, 84);
            }
            border: 1px solid #d8e7f0;
            position: relative;
            box-shadow: 1px 1px 4px #bbd8eb;
            padding: 10px 12px;
            // padding-left: 144px;
            li {
                margin-bottom: 8px;
                cursor: pointer;
                color: #777;
                &:hover {
                    color: #1e94d7;
                }
            }
        }
        .comments_box {
            height: calc(100% - var(--otherCubeHeight) + var(--taskHeight));
            &.no_que {
                height: calc(100% - 210px);
                &.mob {
                    height: calc(100% - 160px);
                }
            }
            margin: 14px auto;
            margin-top: 50px;
            padding: 0 30px;
            &.noActionBox {
                margin-top: 0px;
                // height: calc(100% - 200px);
                &.no_que {
                    height: calc(100% - 160px);
                    &.mob {
                        height: calc(100% - 110px);
                    }
                }
            }
            overflow-y: auto;
            overflow-x: hidden;
            position: relative;
        }
        .pager {
            text-align: center;
            display: inline-block;
            vertical-align: top;
            margin-left: 6px;
            color: var(--theme);
            cursor: pointer;
            line-height: 1;
            i {
                // display: block;
                font-style: normal;
            }
        }
        .apps_recommend {
            background: #fff;
            padding: 8px;
            h4 {
                color: var(--theme);
                // font-weight: bold;
                font-size: 14px;
            }
            li {
                display: flex;
                justify-content: space-between;
                align-items: center;
                /*! box-shadow: var(--shadowR); */
                border-bottom: 1px dashed #eee;
                cursor: default;
                span:nth-child(1) {
                    width: calc(100% - 170px);
                    font-size: 14px;
                    white-space: nowrap;
                }
                span:nth-child(2) {
                    cursor: pointer;
                    a {
                        display: block;
                        float: left;
                        padding: 6px 12px;
                        border-radius: 6px;
                        line-height: 1;
                        margin-left: 4px;
                        font-size: 12px;
                        &:nth-child(1) {
                            background: var(--theme);
                            color: #fff;
                        }
                        &:nth-child(2) {
                            background: var(--themelight);
                            color: var(--theme);
                        }
                    }
                }
            }
        }
        .recommendQues {
            padding-left: 48px;
            display: block;
            ul {
                background: var(--rbtBg);
                border-radius: 6px;
                padding: 12px;
                box-shadow: var(--shadowR);
                min-width: 45%;
                display: inline-block;
                cursor: pointer;
                color: #555;
                li {
                    line-height: 1.8;
                }
            }
            h3 {
                line-height: 20px;
                // font-weight: bold;
                padding-top: 18px;
                padding-bottom: 0px;
                font-size: 14px;
                color: #555;
            }
        }
        .V2-welcome {
            position: absolute;
            width: 90%;
            left: 5%;
            // top: 100px;
            bottom: calc(50% + 90px);
            transform: translateY(-50%);
            z-index: 6;
            font-weight: bold;
            font-size: 26px;
            text-align: center;
            &.square {
                top: calc(64px + var(--hasHd));
                bottom: unset;
                &.topTrans {
                    top: calc(106px + var(--hasHd));
                }
            }
        }
        .square-infos {
            position: absolute;
            top: calc(348px + var(--hasHd) + var(--welCube));
            // top: 348px;
            width: 90%;
            left: 5%;
            .doc {
                // display: grid;
                // grid-template-columns: repeat(4, 1fr);
                // gap: 12px;
                width: 100%;
                li {
                    border: 1px solid var(--themelight);
                    border-radius: 8px;
                    // height: 90px;
                    padding: 12px;
                    overflow: hidden;
                    float: left;
                    margin-right: 12px;
                    margin-bottom: 12px;
                    cursor: pointer;
                    .tit {
                        user-select: none;
                        .num {
                            border-radius: 10px;
                            min-width: 20px;
                            text-align: center;
                            font-size: 12px;
                            border: 1px solid var(--theme);
                            color: var(--theme);
                            display: inline-block;
                            padding: 0px 4px;
                        }
                    }
                    .des {
                        color: #bbb;
                    }
                }
            }
            .faq {
                display: flex;
                flex-wrap: wrap;
                gap: 12px;
                li {
                    border-radius: 8px;
                    padding: 6px;
                    font-size: 14px;
                    background: var(--themelight2);
                    position: relative;
                    cursor: pointer;
                    padding-left: 30px;
                    &::after {
                        content: "";
                        display: block;
                        width: 8px;
                        height: 8px;
                        border-radius: 8px;
                        background: var(--themelight);
                        position: absolute;
                        left: 10px;
                        top: 12px;
                    }
                }
            }
            .faq-tit,
            .doc-tit {
                font-weight: normal;
            }
            .doc-tit {
                margin-top: 18px;
            }
        }
        .chat_txt {
            position: absolute;
            width: calc(100% - 28px);
            bottom: 14px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.08));
            z-index: 10;
            &.isMobile {
                #queVaryCube ul {
                    overflow: auto;
                    width: auto;
                    white-space: nowrap;
                }
                .queVary li {
                    display: inline-block;
                    float: none;
                }
                #queVaryCube {
                    padding-right: 14px;
                }
                // filter: drop-shadow(0px 0px 4px #eee);
            }
            &.centerNow {
                width: 90%;
                left: 5%;
                bottom: calc(50% - 120px - 100px);
                &.isMobile {
                    width: calc(100% - 28px);
                    left: 14px;
                    bottom: 14px;
                }
                &.square {
                    bottom: unset;
                    top: calc(116px + var(--hasHd) + var(--welCube));
                    &.topTrans {
                        top: 158px;
                    }
                }
            }
            &.denyInput {
                cursor: not-allowed;
                opacity: 0.5;
                input,
                textarea {
                    ime-mode: disabled;
                }
                &::before {
                    content: "";
                    width: 100%;
                    height: 100%;
                    z-index: 9;
                    position: absolute;
                    left: 0px;
                    top: 0px;
                    background: rgba(0, 0, 0, 0.05);
                }
                &>* {
                    pointer-events: none;
                }
            }
            a {
                cursor: pointer;
                border-radius: 10px;
                display: block;
                &:not(.no):hover {
                    box-shadow: 0px 0px 5px #bbb;
                }
                &.no {
                    cursor: not-allowed;
                    opacity: 0.5;
                }
            }
            .textareaBox {
                border: 1px solid #ccc;
                padding: 14px;
                border-radius: 12px;
                background: #fff;
                padding-top: 8px;
                position: relative;
                &.bd1 {
                    border-radius: 12px;
                }
            }
            .tools {
                position: absolute;
                right: 2px;
                bottom: 4px;
                display: flex;
                gap: 6px;
                width: calc(100% - 6px);
                background: #fff;
                // display: flex;
                // justify-content: end;
                // align-items: center;
                gap: 12px;
                height: 40px;
                padding: 6px 12px;
                .txtSend {
                    position: absolute;
                    right: 8px;
                    bottom: 0px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 12px;
                }
                .mdChoose {
                    position: absolute;
                    left: 6px;
                    bottom: 8px;
                    .mdUse {
                        padding: 2px 8px;
                        cursor: pointer;
                        font-size: 12px;
                        border-radius: 8px;
                        line-height: 24px;
                        color: var(--tagColor1);
                        border: 1px solid var(--tagColor1);
                        --theme: var(--tagColor1);
                        float: left;
                        margin-right: 8px;
                        &.deny {
                            cursor: not-allowed;
                            opacity: 0.6;
                            filter: grayscale(0.8);
                        }
                        &.yes {
                            --theme: #fff;
                            background: var(--themeRept);
                            color: #fff;
                            border-color: var(--themeRept);
                        }
                        // background: var(--tagColor1);
                    }
                }
                a {
                    width: 28px;
                    height: 28px;
                }
            }
            a.send {
                background: center / 80% auto no-repeat url(v-bind(bgsend));
                // background: center / 80% auto no-repeat url(./img/send.svg);
            }
            .wave-box {
                position: absolute;
                text-align: center;
                height: 140px;
                width: 98%;
                left: 0px;
                bottom: 8px;
                z-index: 9;
                background: var(--themelight2);
                margin-left: 1%;
                border-radius: 14px;
            }
            .wave-btn {
                z-index: 9;
                line-height: 140px;
                color: var(--adminBg1);
                overflow: hidden;
                cursor: pointer;
            }
            .mic {
                width: 24px;
                height: 24px;
                margin-top: -2px;
            }
            .openAllTasks {
                position: absolute;
                right: 12px;
                cursor: pointer;
                top: 10px;
                color: var(--theme);
                // --theme: var(--theme)
            }
            .question {
                width: 100%;
                &.pcH {
                    height: 120px;
                }
                &.mobileH {
                    min-height: 50px;
                }
                outline: none;
                resize: none;
                border: none;
                padding-bottom: 24px;
                &.hasTask {
                    // padding-top: 24px;
                    pointer-events: none;
                    color: #666;
                }
            }
            .tagTaskChoosed {
                position: absolute;
                left: 6px;
                top: 4px;
                color: var(--theme);
                border: none;
                background: transparent;
                font-size: 14px;
                z-index: 9;
                cursor: pointer;
            }
            .taskTarget {
                background: var(--theme);
                color: #fff;
                padding: 6px;
                border-radius: 6px 6px 0px 0px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                /*! filter: contrast(80%); */
                /*! opacity: 0.6; */
                // position: absolute;
                // bottom: 146px;
                // left: 0px;
                gap: 6px;
                height: 40px;
            }
            .similarQues {
                position: absolute;
                bottom: 0px;
                height: 100px;
                // background: rgba(0,0,0,0.05);
                color: var(--theme);
                z-index: 9;
                overflow: auto;
                box-shadow: 0px 0px 2px var(--theme), 0px 1px 1px var(--theme);
                background: #fff;
                padding: 6px;
                border-radius: 0px 0px 6px 6px;
                width: calc(100% - 90px);
                li {
                    padding: 4px 12px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    &.queChoose {
                        background: var(--themelight);
                    }
                }
            }
            .tasks {
                position: absolute;
                bottom: 148px;
                &.mob {
                    bottom: 98px;
                }
                background: #fff;
                padding: 0px 6px;
                border-radius: 6px 6px 0px 0px;
                &.borderAll {
                    border-radius: 6px;
                }
                width: 100%;
                box-shadow: 0px 0px 2px var(--theme),
                0px 1px 1px var(--theme);
                max-height: 128px;
                overflow-y: auto;
                li {
                    padding: 4px 12px;
                    /*! box-shadow: 0px 0px 4px #fff, 0px 1px 1px var(--theme); */
                    /*! background: #fff; */
                    /*! border-radius: 8px; */
                    cursor: pointer;
                    /*! float: left; */
                    margin-right: 6px;
                    display: flex;
                    &.flexBetween {
                        justify-content: space-between;
                    }
                    &.tabChoose,
                    &:hover {
                        background: var(--themelight);
                    }
                    p {
                        margin-bottom: 0px;
                        line-height: 24px;
                    }
                    p:nth-child(2) {
                        font-size: 12px;
                        color: #999;
                    }
                    /*! border-bottom: 1px solid var(--theme); */
                }
            }
        }
    }
    #formTb {
        min-width: 50%;
        box-shadow: 0px 0px 2px var(--theme);
        margin: 10px;
        border-radius: 6px;
        overflow: hidden;
        th {
            background: var(--cate);
        }
        td,
        th {
            padding: 12px;
            &:not(:last-child) {
                border-right: 1px solid var(--themelight);
            }
        }
    }
    .like-btns {
        display: flex;
        gap: 0px;
        margin-left: 8px;
        .like,
        .unlike {
            height: 18px;
            cursor: pointer;
            overflow: hidden;
        }
        img {
            width: 24px;
            height: 18px;
        }
        svg {
            width: 20px;
            height: 20px;
        }
    }
    .submitReasons {
        .subBtns {
            text-align: center;
            &.noReason {
                .submit {
                    cursor: not-allowed;
                    opacity: 0.6;
                }
            }
            .submit {
                display: inline-block;
                width: 90px;
                text-align: center;
                margin: 0px auto;
                background: linear-gradient(#602eef, #1a8ee3);
                border-radius: 6px;
                line-height: 30px;
                color: #fff;
                cursor: pointer;
                // margin-right: 12px;
            }
            .toUser {
                display: inline-block;
                width: 90px;
                text-align: center;
                margin: 0px auto;
                background: linear-gradient(#ef7f2e, #e3c41a);
                border-radius: 6px;
                line-height: 30px;
                color: #fff;
                cursor: pointer;
            }
        }
        ul {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            text-align: center;
            grid-gap: 12px;
            margin: 12px auto;
            line-height: 2em;
            li {
                width: 100%;
                border: 1px dashed #bbb;
                cursor: pointer;
                &.active {
                    background: #d8efff;
                    border: 1px solid #1a8ee3;
                }
            }
        }
    }
    .con {
        position: absolute;
        left: 0px;
        top: 32px;
        width: 100%;
        height: calc(100% - 32px);
        z-index: 9;
        background: #fff;
    }
    #container {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0px;
        top: 0px;
    }
     ::v-deep.avatar-uploader {
        .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: visible;
            i.el-icon-error {
                position: absolute;
                font-size: 20px;
                right: -10px;
                top: -10px;
                color: #ccc;
                background: #fff;
            }
            &:hover {
                border-color: #409eff;
            }
        }
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 100px;
            height: 100px;
            line-height: 100px;
            text-align: center;
        }
    }
     ::v-deep.el-form-item {
        margin-bottom: 10px;
        .el-form-item__content {
            line-height: 0;
        }
    }
     ::v-deep .descriptions .el-input {
        margin-bottom: 8px;
    }
    .removeBtn {
        font-size: 18px;
        color: rgba(53, 120, 139);
        position: relative;
        cursor: pointer;
        padding: 6px;
        background: #fff;
        box-shadow: 0px 1px 4px 2px #ebebeb;
        border-radius: 3px;
    }
    .mgt1 {
        top: 35px;
        float: right;
    }
    .mgt2 {
        top: 5px;
    }
    .header {
        text-align: center;
        color: white;
        pointer-events: none;
        position: absolute;
        bottom: 0px;
        opacity: 0.5;
        background: linear-gradient( to top, var(--theme) 0%, rgba(0, 172, 193, 0) 100%);
    }
    .logo {
        width: 50px;
        fill: white;
        padding-right: 15px;
        display: inline-block;
        vertical-align: middle;
    }
    .inner-header {
        height: 65vh;
        width: 100%;
        margin: 0;
        padding: 0;
    }
    .flex {
        /*Flexbox for containers*/
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
    }
    .waves {
        position: relative;
        width: 100%;
        height: 15vh;
        margin-bottom: -7px;
        /*Fix for safari gap*/
        min-height: 100px;
        max-height: 150px;
    }
    .content {
        position: relative;
        height: 20vh;
        text-align: center;
        background-color: white;
    }
    /* Animation */
    .parallax>use {
        animation: move-forever 5s cubic-bezier(0.55, 0.5, 0.45, 0.5) infinite;
    }
    .parallax>use:nth-child(1) {
        animation-delay: -0.4s;
        animation-duration: 1.4s;
    }
    .parallax>use:nth-child(2) {
        animation-delay: -0.6s;
        animation-duration: 2s;
    }
    .parallax>use:nth-child(3) {
        animation-delay: -0.8s;
        animation-duration: 2.6s;
    }
    .parallax>use:nth-child(4) {
        animation-delay: -1s;
        animation-duration: 4s;
    }
    @keyframes move-forever {
        0% {
            transform: translate3d(-90px, 0, 0);
        }
        100% {
            transform: translate3d(85px, 0, 0);
        }
    }
    /*Shrinking for mobile*/
    @media (max-width: 768px) {
        .waves {
            height: 40px;
            min-height: 40px;
        }
        .content {
            height: 30vh;
        }
        h1 {
            font-size: 24px;
        }
    }
}

.useFit .user_robots {
    width: 100%;
    height: 100%;
}

.bstopic-drawer-inner-wrap {
    .bst-start {
        text-align: right;
        &.bst-start-first {
            text-align: initial;
            height: 350px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
    }
}