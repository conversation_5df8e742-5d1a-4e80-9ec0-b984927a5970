.custom-btn {
    overflow: hidden;
    border-radius: 6px;
    font-family: 'Lato', sans-serif;
    font-weight: 500;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
    /* box-shadow:4px 4px 4px 0px rgba(0,0,0,.1); */
    outline: none;
    border: 1px solid #bbb;
    --theme: #999;
    user-select: none;
}

.custom-btn .skillName {
    color: var(--theme);
    font-weight: normal;
    font-size: 16px;
}

.btn-15 {
    border: 1px solid var(--theme);
    --theme: var(--themeRept);
    color: #888;
    /* padding: 12px; */
}

.btn-15 .skillName {
    font-size: 20px;
    font-weight: bold;
    color: #222;
}


/* 14 */

.btn-14 {
    background: #fff;
    z-index: 10;
}

.btn-11 {
    --theme: #60c42d;
}

.btn-11 svg {
    overflow: visible;
    margin-right: 4px;
    /* transform: translateY(2px); */
}

.btn-11::after {
    content: "";
    height: 4px;
    display: block;
    left: 0px;
    bottom: 0px;
    width: 100%;
    position: absolute;
    background: var(--theme);
}

.btn-11.nolight {
    filter: grayscale(0.7);
    cursor: not-allowed;
    --theme: #333;
}


/* .btn-11.nolight::after {
    
    
  } */

.scalefade {
    opacity: 0.5;
    transform: scale(100);
}

@keyframes opp {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

.hideNow {
    animation: opp 1s;
}

.btn-7 {
    background: #fff;
    z-index: 10;
    text-align: left !important;
    /* --theme: #333; */
    border: 1px solid var(--theme);
    --theme: var(--themeRept);
    border: 1px solid var(--theme);
}

.btn-7 .skillName {
    font-size: 20px;
    font-weight: bold;
    color: #222;
}

.skillName {
    display: flex;
    align-items: center;
}

.zo-user {
    width: 36px;
    height: 36px;
    display: inline-block;
    background: center / contain no-repeat url(../img/szr/red.png);
    margin-right: 6px;
}

.zo-user.black {
    width: 36px;
    height: 36px;
    display: inline-block;
    background: center / contain no-repeat url(../img/szr/black.png);
    margin-right: 6px;
}