:root {
    .user_robots111 {
        --bg: #eff8ff;
        --theme: #4a7eff;
        --themeDark: #222;
        --themeRept: #4a7eff;
        --themelight: #dfe8ff;
        --themelight3: var(--themelight);
        --personTxtBg: var(--themelight3);
        --personTxtColor: rgba(0, 0, 0, 0.9);
        --rightBoxWidth: 65%;
        --limit1: 100%;
        --limit2: 100%;
        --headerTop: 100px;
        --themelight2: #fff;
        --line1: #7fa8fd;
        --line2: #446fe4;
        --line3: #87aefd;
        --lineCube1: #05abe0;
        --lineCube2: #8200f4;
        --cate: #f5f5ff;
        --txtColorR: rgba(0, 1, 10, 0.94);
        --txtColorP: #fff;
        --wholeBg: #fff;
        --rbtBg: #f9f9f9;
        --rbtTxtBg: rgba(255, 255, 255, 0);
        --shadowR: 0px 0px 8px #ccc;
        --shadowH: 0px 0px 8px #ccc;
        --shadowP: 0px 0px 8px #ccc;
        --dropShadow: 0px 0px 5px #dee7ec;
        --adminBg1: #3f80ef;
        --adminBg2: #e2eafd;
        --codeColor: #4b7efe;
        --codeBgColor: #e2ecff;
        --tagColor1: #8c8aec;
        --tagColor2: #86acf1;
        --tagShadow: #d7c6eb;
        // 与颜色无关的
        --normalRadius: 18px;
        --normalWid: 90%;
        --setWidth: 240px;
        --hasHd: 0px;
    }
}

a {
    text-decoration: none;
}

:root {
    .user_robots {
        color: #333;
        background: #f9f9f9;
        .other_normal {
            font-family: Avenir, Helvetica, Arial, sans-serif;
        }
        .p6 {
            p {
                margin-bottom: 6px;
            }
        }
        // * {
        //     margin: 0px;
        //     padding: 0px;
        //     box-sizing: border-box;
        // }
        p {
            margin-bottom: 4px;
        }
        ul {
            margin: 0px;
            padding: 0px;
            li {
                list-style: none;
            }
        }
    }
}

.user_robots {
     ::v-deep.el-loading-mask>svg>circle {
        stroke: var(--theme);
    }
    .el-icon-loading {
        color: var(--theme) !important;
        font-size: 20px;
    }
    .el-tabs__content {
        overflow: auto;
        height: 100%;
        padding-bottom: 40px;
    }
    .el-tabs__item.is-active,
    .el-tabs__item:hover,
    .el-radio-button__inner:hover {
        color: var(--theme);
    }
    .el-radio-button__orig-radio:checked+.el-radio-button__inner {
        background-color: var(--theme);
        border-color: var(--theme);
    }
    .el-tabs__active-bar {
        background-color: var(--theme);
    }
    &.useWrongId>*:not(.useWrongId-tips) {
        display: none;
    }
    .useWrongId-tips {
        padding: 18px;
        position: fixed;
        left: 50%;
        top: 20vh;
        color: var(--theme);
        transform: translateX(-50%);
        background-color: transparent;
        text-align: center;
    }
    .doc-list-limit {
        max-height: 260px;
        overflow-y: auto;
    }
    .robot-list-limit {
        max-height: 280px;
        overflow-y: auto;
    }
    .contentsSkill {
        padding: 0px 5px;
        background: #fff;
        color: var(--theme);
        display: inline-block;
        margin-right: 8px;
        border-radius: 4px;
    }
    .canvas {
        --bg: #fff;
        --theme: #0072AE;
        --themeRept: #0072AE;
        --themelight: #c5e3f5;
        --themelight2: #fff;
        --personTxtBg: linear-gradient(to right, var(--line1), var(--line2));
        --personTxtColor: #fff;
        --limit1: 100%;
        --limit2: 100%;
        --line1: #0072AE;
        --line2: #0173B2;
        --line3: #27a0e0;
        --lineCube1: #1385c2;
        --lineCube2: #0ca2c7;
        --cate: #eaf4f9;
        --txtColorR: rgba(0, 1, 10, 0.94);
        --txtColorP: #fff;
        --wholeBg: #F5F4F9;
        --rbtBg: #fff;
        --rbtTxtBg: #fff;
        --shadowR: 0px 0px 8px #ccc;
        --shadowH: 0px 0px 8px #ccc;
        --shadowP: 0px 0px 8px #ccc;
        --dropShadow: 0px 0px 5px #dee7ec;
        --adminBg1: #3f80ef;
        --adminBg2: #e2eafd;
        --codeColor: #379ac2;
        --codeBgColor: #e2ecff;
    }
    .suda {
        --bg: #fff;
        --theme: #910a0a;
        --themeRept: #910a0a;
        --themelight: #f3bcbc;
        --themelight2: #ffe9e9bd;
        --line1: #910a0a;
        --line2: #b41616;
        --line3: #b94040;
        --lineCube1: #910a0a;
        --lineCube2: #910a0a;
        --cate: #f5f5ff;
        --txtColorR: rgba(0, 1, 10, 0.94);
        --txtColorP: #fff;
        --wholeBg: #fff;
        --rbtBg: #f9f9f9;
        --shadowR: 0px 0px 4px #ccc;
        --shadowH: 0px 0px 4px #ccc;
        --shadowP: 0px 0px 4px #ccc;
        --dropShadow: 0px 0px 5px transparent;
        --adminBg1: #b94040;
        --adminBg2: #eca18a;
        --codeColor: #7e1515;
        --codeBgColor: #ffe6e2;
        --tagColor1: #f59b80;
        --tagColor2: #f86e64;
        --tagShadow: #ebd5c6;
    }
    .lnvut {
        --headerTop: 56px;
    }
    .usts {
        --bg: #fff;
        --theme: #0068b7;
        --themeRept: #0068b7;
        --themelight: #9fd0f5;
        --themelight2: #f9fbfd;
        --line1: #0068b7;
        --line2: #1077c5;
        --line3: #2a8ad3;
        --lineCube1: #157cca;
        --lineCube2: #0d5891;
        --cate: #f5f5ff;
        --txtColorR: rgba(0, 1, 10, 0.94);
        --txtColorP: #fff;
        --wholeBg: #fff;
        --rbtBg: #f9f9f9;
        --shadowR: 0px 0px 4px #ccc;
        --shadowH: 0px 0px 4px #ccc;
        --shadowP: 0px 0px 4px #ccc;
        --dropShadow: 0px 0px 5px transparent;
        --adminBg1: #0f78c9;
        --adminBg2: #1066a8;
        --codeColor: #1078c7;
        --codeBgColor: #b8daf5;
    }
    .jssvc {
        --bg: #fff;
        --theme: #00ac97;
        --themeRept: #00ac97;
        --themelight: #c9eeea;
        --themelight2: #f1f7f6;
        --line1: #00ac97;
        --line2: #0fc7b1;
        --line3: #00a6ac;
        --lineCube1: #00ac97;
        --lineCube2: #228a7e;
        --cate: #f5f5ff;
        --txtColorR: rgba(0, 1, 10, 0.94);
        --txtColorP: #fff;
        --wholeBg: #fff;
        --rbtBg: #f9f9f9;
        --shadowR: 0px 0px 4px #ccc;
        --shadowH: 0px 0px 4px #ccc;
        --shadowP: 0px 0px 4px #ccc;
        --dropShadow: 0px 0px 5px transparent;
        --adminBg1: #87f3e6;
        --adminBg2: #7fc7be;
        --codeColor: #00ac97;
        --codeBgColor: #aaf8ef;
        --tagColor1: #68e4d3;
        --tagColor2: #4ad6c4;
        --tagShadow: #99f2f8;
    }
    .right-role-cards-drawer,
    .right-role-cards {
        display: block;
        position: fixed;
        right: 0px;
        width: 50px;
        height: 50px;
        top: 25vh;
        gap: 12px;
        text-align: center;
        box-shadow: 0px 0px 6px #ddd;
        border-radius: 50%;
        transition: all 1s;
        padding: 5px;
        overflow: hidden;
        z-index: 29;
        white-space: nowrap;
        background: #fff;
        cursor: pointer;
        //   transform: translateX(calc(50px - 100vw));
        &:hover {
            transform: translateX(calc( 30px - 100%));
            // left:
        }
        .el-avatar {
            background: cornflowerblue;
            font-size: 0.7rem;
        }
        &.right-role-cards-1 {
            top: calc(25vh + 50px + 3px);
            // .el-avatar {
            //     background: rgb(100, 237, 214);
            // }
        }
        &.right-role-cards-2 {
            top: calc(25vh + 100px + 5px);
            // .el-avatar {
            //     background: rgb(14, 100, 50);
            // }
        }
        &.right-role-cards-3 {
            top: calc(25vh + 150px + 7px);
            // .el-avatar {
            //     background: rgb(9, 46, 95);
            // }
        }
        &.right-role-cards-4 {
            top: calc(25vh + 200px + 9px);
            // .el-avatar {
            //     background: rgb(198, 100, 237);
            // }
        }
        &.right-role-cards-5 {
            top: calc(25vh + 250px + 11px);
            // .el-avatar {
            //     background: rgb(237, 189, 100);
            // }
        }
    }
    .right-role-cards-drawer {
        top: 15vh;
        border-radius: 5px 0 0 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        &:hover {
            transform: translateX(calc(0));
        }
        &.copy-bord {
            top: 8vh;
        }
        &.back-to-bot {
            top: 35vh;
            border-radius: 50%;
            background: #bc3935;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            .toggle-drawer {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                >img {
                    width: 100%;
                }
            }
            &.back-to-bot-2 {
                top: calc(35vh + 7vh);
            }
            &.back-to-bot-3 {
                top: calc(35vh + 2*7vh);
            }
            &.back-to-bot-4 {
                top: calc(35vh + 3*7vh);
            }
            &.back-to-bot-5 {
                top: calc(35vh + 4*7vh);
            }
            &.back-to-bot-6 {
                top: calc(35vh + 5*7vh);
            }
        }
    }
    .isV3 {
        --normalRadius: 0px;
        --normalWid: min(800px, 80%);
        --setWidth: 280px;
        --hasHd: 24px;
        --limit1: 1200px;
        --limit2: 960px;
        .thinking-control {
            width: 100%;
            text-align: center;
            background: none;
            color: var(--tagColor2);
            position: relative;
            margin-top: -8px;
            &::after {
                content: "";
                position: absolute;
                left: 2px;
                top: 48%;
                height: 0px;
                width: 100%;
                border-bottom: 1px dashed var(--tagColor2);
            }
            span {
                padding: 0px 12px;
                background: #fff;
                display: inline-block;
                z-index: 9;
                position: relative;
            }
            // &::before{
            //   content: "";
            //   position: absolute;
            //   left: calc(50% - 50px);
            //   top: 0px;
            //   height: 100%;
            //   width: 100px;
            //   background-color: #fff;
            // }
        }
        .user_box {
            position: relative;
            padding-left: 48px;
            line-height: 36px;
            cursor: pointer;
            &::after {
                content: "";
                position: absolute;
                background: center center / auto 36px no-repeat var(--avatar);
                left: 2px;
                top: 0px;
                height: 36px;
                width: 36px;
                border-radius: 36px;
                box-shadow: 0px 0px 12px #eee;
                background-color: #fff;
            }
        }
        .addNewChat {
            --theme: #222;
        }
        .action-header-box {
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: space-between;
            padding: 14px 18px;
            border-bottom: 1px solid #ddd;
            --theme: #222;
            height: 72px;
        }
        .contents_robot {
            height: calc(100% - 78px);
        }
        // --limit1: 1000px;
        // --limit2: 960px;
        .txt.marked pre:has(.white-btn),
        .welStr pre:has(.white-btn) {
            line-height: 90px;
        }
        .txt.marked .white-btn,
        .welStr .white-btn {
            /*! border: 1px solid var(--tagColor1); */
            color: var(--themeDark);
            background: linear-gradient(to left, var(--themelight3), var(--cate));
            padding: 24px 18px;
            padding-right: 54px;
            border: 1px solid #ededed;
            font-size: 17px;
            position: relative;
            &::after {
                content: '';
                display: block;
                width: 36px;
                height: 36px;
                position: absolute;
                right: 12px;
                top: 12px;
                background-image: url(../img/notes.png);
                background-position: center;
                background-size: cover;
            }
        }
        .hide {
            display: none !important;
        }
        div.chat_txt {
            filter: none;
        }
        .new-chat {
            cursor: pointer;
            &.ischat {
                background: #fff;
                color: var(--theme);
                box-shadow: 0px 0px 2px Var(--theme);
                padding: 0px 4px;
            }
        }
        .pcToggle {
            --theme: #222;
            .logoFind {
                max-width: calc(100% - 50px);
                max-height: 40px;
            }
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }
    }
    .stopAnswer {
        color: var(--theme);
        cursor: pointer;
        display: block;
        /*! padding: 12px 0px 12px 48px; */
        z-index: 99;
        position: absolute;
        left: 50%;
        bottom: 50px;
        background: #fff;
        display: block;
        padding: 12px 24px;
        transform: translateX(-50%);
        border-radius: 20px;
        box-shadow: 0px 0px 12px #eee;
    }
    .no {
        cursor: not-allowed;
        opacity: 0.5;
    }
    .mine .el-loading-mask {
        background: var(--wholeBg);
        z-index: 8;
    }
    .icon-one svg {
        width: 28px;
        height: 28px;
        display: inline-block;
        cursor: pointer;
    }
    .icon-one-mini svg {
        width: 24px;
        height: 24px;
        display: inline-block;
    }
    .icon-one-mini:not(.no) svg {
        cursor: pointer;
    }
    .copilot.ans img {
        max-width: 80%;
    }
    /* Chrome、Edge、Safari */
     ::-webkit-scrollbar {
        display: none;
        /* 隐藏滚动条 */
    }
    /* Firefox */
    html {
        scrollbar-width: none;
        /* 隐藏滚动条 */
    }
    /* IE */
    body {
        -ms-overflow-style: none;
        /* 隐藏滚动条 */
    }
    #app div,
    #app ul {
        scrollbar-color: var(--themelight) #f5f5f5;
        scrollbar-width: thin;
        &::-webkit-scrollbar {
            width: 4px;
            height: 20px;
        }
        &::-webkit-scrollbar-thumb {
            // background: -webkit-gradient(linear,left top,left bottom,from(#ff8a00),to(#e52e71));
            background: var(--themelight);
            border-radius: 30px;
        }
        &::-webkit-scrollbar-track {
            background: #f9f9f9;
        }
    }
    .thinking-btn {
        display: inline-block;
        padding: 1px 4px;
        background: var(--themelight);
        border-radius: 4px;
        cursor: pointer;
    }
    .chart-btn {
        display: inline-block;
        padding: 1px 4px;
        background: var(--cate);
        border-radius: 4px !important;
        cursor: pointer;
        color: var(--theme);
        max-width: 200px;
        line-height: 1.5;
        word-wrap: break-word;
        white-space: wrap;
        box-shadow: 0px 0px 2px var(--adminBg1);
        margin: 4px;
    }
    .thinking-control {
        background: linear-gradient(var(--tagColor2), var(--tagColor1));
        color: #fff;
        cursor: pointer;
        border-radius: 8px;
        padding: 0px 8px;
        display: inline-block;
        margin-top: 0px;
    }
    .thinking-steps {
        padding-top: 2px;
        ul {
            li {
                --cube1: 24px;
                padding: 0px 8px;
                padding-left: 32px;
                padding-top: 2px;
                padding-bottom: 18px;
                position: relative;
                line-height: 1.5;
                height: auto;
                overflow: hidden;
                &.step {
                    // :not(:last-child)
                    &.status,
                    &.pendingstatus {}
                    &.successstatus {
                        --theme: green;
                    }
                    &.errorstatus {
                        --theme: red;
                    }
                    @keyframes scaleIn {
                        0% {
                            height: 0%;
                        }
                        100% {
                            height: 100%;
                        }
                    }
                    &::after {
                        content: '';
                        position: absolute;
                        display: block;
                        width: 1px;
                        height: 100%;
                        left: 12px;
                        top: calc(var(--cube1) + 4px);
                        background-color: var(--theme);
                        animation: scaleIn 0.4s;
                    }
                }
                h3 {
                    font-size: 16px;
                    font-weight: normal;
                }
                .contents {
                    color: #999;
                    font-size: 13px;
                }
                .xh {
                    display: block;
                    width: var(--cube1);
                    height: var(--cube1);
                    line-height: var(--cube1);
                    border-radius: var(--cube1);
                    font-style: normal;
                    font-size: 12px;
                    // color: var(--theme);
                    // border: 1px solid var(--theme);
                    color: var(--theme);
                    border: 1px solid var(--theme);
                    background-color: #fff;
                    text-align: center;
                    position: absolute;
                    left: 0px;
                    top: 2px;
                }
            }
        }
    }
    .thinking {
        margin-bottom: 0px;
        color: #555;
        line-height: 1.5;
        display: block;
        padding-bottom: 8px;
        padding-left: 8px;
        margin-top: -2px;
        padding-top: 8px;
        border-left: 1px solid var(--themelight);
    }
    .echarts-tooltip-diy {
        box-shadow: 0px 0px 8px 0px #ededed;
        border-radius: 6px !important;
        text-align: center;
        .arrow.el-icon-top {
            color: #38ffaf;
        }
        .arrow.el-icon-bottom {
            color: #ff3939;
        }
        .tooltip-title {
            font-size: 17px;
            color: #111;
            margin: 0px;
            width: 80px;
        }
        .tooltip-value {
            font-size: 12px;
            margin-top: 5px;
            color: #666;
        }
    }
    .postIframe {
        width: 100%;
        height: 100%;
        box-shadow: 0px -2px 1px #eee;
        overflow: hidden;
        position: relative;
        // width: 99.9%;
        // margin-left: 0.1%;
        border-left: 1px solid #ddd;
        &.hasTool {
            display: grid;
            grid-template-columns: 1fr;
            grid-template-rows: 30px 1fr;
        }
        &.floatTool {
            position: relative;
            .app-actions {
                position: absolute;
                right: 0px;
                top: 0px;
                z-index: 9;
                padding-right: 6px;
            }
        }
        &.isMobile {
            position: fixed;
            height: 100%;
            z-index: 29;
            width: 98vw;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 0px !important;
            overflow: hidden;
            border-left: 1px solid #eee;
            position: relative;
        }
        .app-actions {
            text-align: right;
            padding: 4px 12px;
            background: #fff;
        }
        .el-icon-close {
            // position: absolute;
            // right: 18px;
            // top: 18px;
            font-size: 18px;
            z-index: 9;
            color: var(--adminBg1);
            cursor: pointer;
            border-radius: 4px;
            background-color: var(--themelight);
            height: 20px;
            width: 20px;
        }
        .el-icon-place {
            font-size: 18px;
            height: 20px;
            width: 20px;
            margin-right: 4px;
            z-index: 9;
            color: var(--adminBg1);
            cursor: pointer;
            border-radius: 4px;
            background-color: var(--themelight);
        }
    }
    .isPc .txt.marked {
        table {
            background: #fff;
            width: 100%;
            // overflow-x: auto;
            // overflow-y: auto;
            // max-height: 400px;
            table-layout: fixed;
            // display: block;
            thead {
                background: var(--codeBgColor);
                th {
                    padding: 16px;
                    white-space: break-spaces;
                    font-weight: normal;
                    line-height: 1;
                    font-family: 微软雅黑;
                    color: rgba(0, 0, 0, 0.85);
                }
            }
            tbody {
                tr {
                    &:nth-child(2n) {
                        td {
                            background-color: rgb(248, 248, 248);
                        }
                    }
                    &:hover {
                        td {
                            background-color: var(--cate);
                        }
                    }
                    td {
                        border-bottom: 1px solid rgb(232, 232, 232);
                        white-space: break-spaces;
                        color: rgba(0, 0, 0, 0.95);
                        &:empty {
                            padding: 12px;
                        }
                        // padding: 3px;
                        padding: 16px;
                        font-family: 微软雅黑;
                        background: #fff;
                    }
                }
            }
        }
    }
    .isMobile .txt.marked {
        table {
            background: #fff;
            width: 100%;
            // table-layout: fixed;
            // // display: block;
            thead {
                background: var(--codeBgColor);
                th {
                    padding: 12px 4px;
                    font-weight: normal;
                    line-height: 1;
                    font-family: 微软雅黑;
                    color: rgba(0, 0, 0, 0.85);
                    white-space: nowrap;
                }
            }
            tbody {
                tr {
                    &:nth-child(2n) {
                        td {
                            background-color: rgb(248, 248, 248);
                        }
                    }
                    &:hover {
                        td {
                            background-color: #e6f7ff;
                        }
                    }
                    td {
                        border-bottom: 1px solid rgb(232, 232, 232);
                        color: rgba(0, 0, 0, 0.95);
                        &:empty {
                            padding: 12px 4px;
                        }
                        // padding: 3px;
                        padding: 12px 4px;
                        font-family: 微软雅黑;
                        font-size: 12px;
                        background: #fff;
                    }
                }
            }
        }
    }
    .txt.marked,
    .welStr {
        pre {
            margin-bottom: auto;
            overflow: visible;
        }
        ol {
            margin-left: 16px;
            width: calc(100% - 16px);
        }
        p {
            margin-bottom: 6px;
            &:empty {
                margin: 0px;
            }
        }
        // ---------------------------------智能表格的样式
        .white-btn {
            border: 1px solid var(--tagColor1);
            color: var(--tagColor1);
            padding: 4px 16px;
            font-family: Arial, Helvetica, sans-serif;
            border-radius: 12px;
            font-size: 15px;
            font-weight: normal;
        }
        &.ischatting {
            .white-btn {
                display: none;
            }
        }
        // --------------------------------------------
        code.language-echarts,
        code.language-show-iframe,
        code[class^=language-vue],
        code.language-white-board,
        code.language-tabs,
        code.language-chart-board {
            display: none;
        }
        .thinking-steps .step {
            code.language-echarts,
            code.language-show-iframe,
            code[class^=language-vue],
            code.language-tabs {
                display: block;
            }
        }
        code {
            color: var(--codeColor);
            white-space: break-spaces;
            background: var(--codeBgColor);
            padding: 6px;
            font-size: 12px;
        }
        a,
        .el-icon-link {
            color: var(--lineCube2);
        }
        .links {
            display: block;
            p {
                display: inline-block;
                margin-bottom: 0px;
            }
        }
        img {
            max-width: 80% !important;
            display: block !important;
        }
        .ant-table-small>.ant-table-content>.ant-table-body {
            margin: 0px;
        }
        .ant-table-thead>tr>th {
            background: transparent;
        }
    }
    .txt.marked .btnToUser {
        color: #1793e3;
        display: inline-block;
        padding: 0px 12px;
        background: #d0ecff;
        border: #125b8b;
        font-size: 12px;
        transition-duration: 0.5s;
        border-radius: 5px;
        &:hover {
            transform: translateY(-3px);
        }
        border: 1px solid #eee;
        cursor: pointer;
    }
    .tip-1 i {
        cursor: pointer;
    }
    #myModal {
        position: fixed;
        z-index: 13;
        padding-top: 100px;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgb(0, 0, 0);
        background-color: rgba(0, 0, 0, 0.9);
    }
    #img01 {
        margin: auto;
        display: block;
        width: auto;
    }
    span.close {
        position: fixed;
        top: 15px;
        right: 35px;
        color: #f1f1f1;
        font-size: 40px;
        font-weight: bold;
        transition: 0.3s;
        cursor: pointer;
    }
    span.close:hover,
    span.close:focus {
        color: #bbb;
        text-decoration: none;
    }
    .chat_txt .txtlimit {
        color: #ccc;
        display: block;
        font-size: 12px;
        font-style: normal;
        .exec {
            color: #ff0000;
        }
    }
    /* 通用样式 */
    .outbtn-item.el-dropdown-menu__item:focus,
    .outbtn-item.el-dropdown-menu__item:not(.is-disabled):hover {
        background: var(--themelight);
        color: var(--theme);
    }
    .over_line {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .over_line2 {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }
    .over_line3 {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
    }
    @keyframes opIn {
        0% {
            opacity: 0;
        }
        100% {
            opacity: 1;
        }
    }
    .mask-close {
        background: rgba(0, 0, 0, 0.1);
        display: block;
        width: 100vw;
        height: 100%;
        position: absolute;
        left: 0px;
        top: 0px;
        z-index: 12;
        animation: opIn 0.3s;
    }
    .question.divRef {
        text-indent: var(--tagWidth);
    }
    .question.divRef .divContent:focus-visible {
        border: none;
        outline: none;
    }
    .promp-input {
        border: none;
        display: inline;
        width: auto;
        background: var(--themelight);
        border-radius: 8px;
        padding: 0px 8px;
        color: var(--theme);
        // width: 4em;
        margin: 0px 6px;
    }
    .promp-input::placeholder {
        color: var(--theme);
    }
    .promp-input:focus-visible {
        border: none;
        outline: none;
        box-shadow: 0px 0px 2px var(--theme);
    }
    .skillsContent {
        overflow: hidden;
        li {
            width: 160px;
            border-radius: 12px;
            border: 1px solid var(--themelight);
            padding: 12px;
            cursor: pointer;
            margin-right: 12px;
            float: left;
            .icon {
                display: inline-block;
                vertical-align: top;
            }
            &:hover {
                background-color: var(--themelight);
            }
        }
    }
}

.noIdSvg {
    text-align: center;
    padding-top: 80px;
}

// .textareaBox{
//   .ck.ck-editor__top.ck-reset_all{
//     // display: none;
//   }
//   .ck.ck-reset.ck-editor.ck-rounded-corners{
//     position: realtive;
//     left: -14px;
//     top: -16px;
//     --ck-color-base-background: transparent;
//     --ck-color-base-border: transparent;
//   }
//   .ck.ck-editor__main > .ck-editor__editable{
//     border-color: transparent;
//     height: 120px;
//     box-shadow: none !important;
//     text-indent: var(--tagWidth);
//   }
// }
// // 富文本编辑器的样式修改
// .ck-body-wrapper{
//   display: none;
// }
.user_robots {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: subpixel-antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.user_robots {
    padding: 0px;
    gap: 0px;
    .select_copilots_display {
        padding: 18px;
        background: linear-gradient(to bottom, var(--themelight2), var(--cateGray));
        .robot_chooses_display {
            // width: 90%;
            // margin:0px auto;
        }
        .copilot_selects_collection {
            height: 245px;
        }
        .conversation-tit {
            background: var(--themelight2);
            border-top: none;
            margin-bottom: 0px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
        }
        .conversation li {
            padding: 8px 12px;
            background: #fff;
        }
        .conversation li .conver_name {
            width: calc(100% - 34px);
        }
        .conversation {
            padding-left: 0px;
        }
        .copilot_selects {
            width: 99.5%;
            border-radius: 0px;
            background-color: var(--themelight2);
            padding-top: 12px;
            padding-bottom: 12px;
            padding-left: 12px;
            margin: 0px auto;
            &:not(:last-child) {
                border-bottom: 1px solid #eee;
            }
            &.choosed {
                background: var(--theme);
                .co-tit {
                    color: #fff;
                }
            }
        }
    }
}

.isV2 {
    --limit1: 1200px;
    --limit2: 960px;
    .user_robots.onlyCopilotId {
        height: calc(100vh - var(--headerTop));
        gap: 0px;
        padding: 0px;
        .contents_robot,
        .select_copilots,
        .chatting {
            border-radius: unset;
        }
    }
    .contents_robot .action-box {
        display: none;
    }
}

.robot-inner-base-info {
    .bi-tags {
        margin: 10px 0;
        .el-tag {
            margin-right: 5px;
            margin-bottom: 5px;
        }
    }
    .bi-usertype {
        font-size: 1rem;
        font-weight: bold;
    }
    .bi-desc {
        text-indent: 2rem;
    }
}