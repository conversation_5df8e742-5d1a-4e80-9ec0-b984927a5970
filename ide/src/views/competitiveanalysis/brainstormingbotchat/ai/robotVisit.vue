<template>
  <div class="contents_robot_box" v-loading="isNewChat ? false : copilotRecordLoading"
    element-loading-spinner="el-icon-loading"
    :style="{ '--widthDefine': widthDefine + 'px', '--tagTop': !isMobile() ? (otherCubeHeight - 156) + 'px' : '51px', '--otherCubeHeight': (otherCubeHeight + 'px'), '--welCube': welCubeHeight + 'px', '--taskHeight': (taskHeight + 'px') }"
    :class="isRight && 'isRight'" id="contentsRobotBox">
    <!-- v-loading="copilotLoad" -->
    <div style="position: relative;height: 100%;">
      <div id="bigger" v-if="isRight"
        style="cursor: col-resize;z-index: 88;background: transparent;position:absolute;left:0px;top: 0px;width:8px;height:100%;">
      </div>

      <div class="action-header-box" v-if="copilotInfo && copilotInfo.name && $route.name === 'airobotsquare-v2'">
        <div v-if="copilotInfo && copilotInfo.source && !isMobile()" style="width: calc(50% - 80px);">

          <p style="margin-bottom: 0px;font-size: 17px;color: var(--theme);">{{ copilotInfo.source.spec.copilot.text }}
          </p>
          <p v-if="copilotInfo.source.spec.copilot.description"
            style="font-size: 12px;color: var(--themeRept);line-height: 14px;">
            <span style="font-size:22px;position: relative;top: 3px;">•</span>
            {{ copilotInfo.source.spec.copilot.description }}
          </p>
        </div>

        <span class="action-tabs" :class="isChatting && 'noClick'">


          <!-- <el-dropdown
          size="mini"
          style="
            position: absolute;
            left: 0px;
            top: 0px;
            width: 100%;
            height: 100%;
          "
        >
        <div class="user-name">{{ 'name' }}</div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item class="setbtn-item">
              <el-button
                @click="logOut"
                size="small"
                type="text"
                icon="el-icon-switch-button"
                >退出登录</el-button
              >
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->

          <el-dropdown @command="handleCommand" size="small">
            <div class="user_box" :style="`--avatar:url(${avatar})`">{{ name }}</div>
            
            <template #dropdown>
              <el-dropdown-menu>
              <el-dropdown-item command="point">清除上下文关联</el-dropdown-item>
              <el-dropdown-item command="chatClean"
                v-if="useConversation && useConversation.name">清空当前会话</el-dropdown-item>
              <el-dropdown-item command="all" v-if="anonymity">清空聊天记录</el-dropdown-item>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
            </template>
            
            
            
          </el-dropdown>
          <!-- <el-dropdown @command="handleCommand" size="small" style="height: 24px">
            <icon-use name="clear" class="icon-one-mini" title="重置会话"></icon-use>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="point">清除上下文关联</el-dropdown-item>
              <el-dropdown-item command="chatClean"
                v-if="useConversation && useConversation.name">清空当前会话</el-dropdown-item>
              <el-dropdown-item command="all" v-if="anonymity">清空聊天记录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->

        </span>
      </div>

      <div class="contents_robot">


        <!-- {{ useConversation }}{{ copilotInfo }} -->
        <p class="action-box" v-if="copilotInfo && copilotInfo.name && !noActionBox"
          :class="canUseHelpDesk && 'cutThree'">
          <span v-if="copilotInfo && copilotInfo.source && !isMobile()"
            style="width: var(--cutThree);display: flex;font-size: 16px;color: var(--theme);align-items: center;font-weight: bold;">
            <img v-if="$route.name === 'airobotauth'" :src="robotIcon"
              style="width:30px;height:30px;margin-right: 6px;object-fit: scale-down;" alt="" />
            {{ copilotInfo.source.spec.copilot.text }}
          </span>

        <nav class="tabs" v-if="canUseHelpDesk">
          <div class="selector" :class="isRobot && 'ai'"></div>
          <a href="#" :class="isRobot && 'active'" @click="useUserChatting">AI</a>
          <a href="#" :class="!isRobot && 'active'" @click="useUserChatting">人工</a>
        </nav>


        <span class="action-tabs" :class="isChatting && 'noClick'">
          <!-- <a class="user" title="转人工" @click="useUserChatting"></a> -->

          <a class="close" v-show="isRight || useFit" @click="hideRobot" />
          <a class="close" v-show="showClose" @click="hideRobotPost" />


          <el-dropdown @command="handleCommand" size="small" style="height: 24px">
            <icon-use name="clear" class="icon-one-mini" title="重置会话"></icon-use>
            <template #dropdown>
              <el-dropdown-menu>
              <el-dropdown-item command="point">清除上下文关联</el-dropdown-item>
              <el-dropdown-item command="chatClean"
                v-if="useConversation && useConversation.name">清空当前会话</el-dropdown-item>
              <el-dropdown-item command="all" v-if="anonymity">清空聊天记录</el-dropdown-item>
            </el-dropdown-menu>
            </template>
            
          </el-dropdown>

          <a class="chats" :class="openChated && 'opened'" v-show="showClose" @click="openChatList" />


          <a v-show="isRight" :class="isFull ? 'nofull' : 'full'" :title="isFull ? '窗口模式' : '无遮挡'"
            @click="addBodyClass"></a>
        </span>
        </p>

        <template v-if="hasCopilotId">
          <div class="comments_box"
              @copy="handleCopy" ref="containercopy"
            :class="{ 'no_que': (queVaries.length === 0 && skillsCategories.length === 0), 'noActionBox': noActionBox, 'mob': isMobile() }"
            :id="'commentsBox' + onlyId" v-show="isRobot">
            <div class="mine" style="
                height: 40px;
                font-size: 12px;
                color: #bbb;
                text-align: center;
                line-height: 40px;
              " v-loading="leftChatNums" v-if="hasRecords" element-loading-spinner="el-icon-loading">
              没有更多了~
            </div>

            <!-- v-loading="leftChatNums" -->

            <div class="chat_box robot a11111" :style="`--avatar:url(${avatar});--robotIcon:url(${parseRobotIcon()})`"
              v-if="!commentsIdCollection.length && copilotInfo.source && isNotV2 && (isNewChat || !copilotRecordLoading)">
              <div class="txt welStr" v-if="copilotInfo.source">
                <p style="margin-top: -14px;" v-html="parseWelcome()">
                </p>
                <!-- 你好，我是AI智能问答助手，有什么可以帮到您的吗? -->

                <p v-if="tasksRecommend.length" style="margin:6px 0px;color:var(--theme);font-weight: bold;">您还可以试试：</p>
                <ul v-if="tasksRecommend.length">
                  <li class="taskRecommend" v-for="(task, itdx) in tasksRecommend"
                    :key="'welccc' + itdx + task.commands[0]" @click="useTask(task)">
                    <p>
                      <icon-use name="magic" style="height: 24px;margin-right:6px;float: left;
  margin-top: 1px;"></icon-use>
                      <!-- {{idx}} -->
                      <span>{{ task.commands[0] }}</span>
                    </p>
                    <p>
                      <!-- el-tooltip -->
                      <el-tooltip effect="light" placement="left-end" :content="task.description">
                        
                        <el-icon><InfoFilled /></el-icon>

                      </el-tooltip>
                      <!-- <span></span> -->
                    </p>
                  </li>
                </ul>
              </div>
            </div>
            <template v-for="it in commentsIdCollection" :key="'ttt' + it" >
              <div style="
                  height: 40px;
                  line-height: 40px;
                  font-size: 12px;
                  color: #bbb;
                  text-align: center;
                " v-if="timeStamp[it]">
                {{ timeStamp[it] }}
              </div>

              <div class="chat_box" :style="`--avatar:url(${avatar});--robotIcon:url(${parseRobotIcon(it)})`"
                :class="[it.split('__')[0] == 'p' ? 'person' : 'robot',parseRobotAvatarClass(it)]" :id="it">
                <el-tooltip :content="name" :disabled="name.length <= 3" effect="light" placement="left-end">
                  
                  <div class="user_name" style="text-align: center;" v-show="it.split('__')[0] !== 'r'">{{ name }}</div>

                </el-tooltip>

                <div class="txt marked" :class="{ 'size1': allPager[it], 'ischatting': it === isChattingId }"
                  :id="'txtBox_' + it" v-loading="loading[it]" element-loading-text="正在重新生成答案"
                  element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.3)">

                  <!-- {{comments[it]}}{{commentsTxt[it]}} -->

                  <!-- <MsgContent :isReplaceLinks="true" 
      :loadingMessage="false" 
      :content="commentsTxt[it]" 
      :isFinal="answer_tools.includes(it.split('__')[1])" 
      :isMdExpand="true" 
      :clazzMd="`question-text question-text-mobile`" 
      :styleObj="{ 'max-width': '680px' }" 
      :isPrintAnimate="true" 
      :printCount="1" 
      :hideCursorPoint="true"/> -->
                  <span class="thinking-btn" v-if="reasonTxt[it]" @click="hiddenReason(it)">{{ !comments[it] ? '思考中...'
                    : `思考已完成，耗时${reasonTime[it]}秒` }}
                    <i class="el-icon-arrow-down el-icon--right" v-if="!reasonHidden[it]"></i>
                    <i class="el-icon-arrow-up el-icon--right" v-else></i>
                  </span>
                  <span class="thinking" v-if="reasonTxt[it] && !reasonHidden[it]"
                    v-html="getMarked(reasonTxt[it])"></span>

                  <h4 class="thinking-control" v-if="false&&thoughtTxt[it]" @click="isShowThinking(it)">
                    <!-- <a-icon type="check" v-if="thoughtTxt[it][thoughtTxt[it].length - 1].status === 'success'" /> -->
                    <a-icon type="loading"
                      v-if="!thoughtTxt[it][thoughtTxt[it].length - 1].status || thoughtTxt[it][thoughtTxt[it].length - 1].status === 'pending'" />
                    <!-- <a-icon type="close" v-if="thoughtTxt[it][thoughtTxt[it].length - 1].status === 'error'" /> -->
                    <span>思维链
                      <i class="el-icon-arrow-down el-icon--right" v-if="thinkingShow[it]"></i>
                      <i class="el-icon-arrow-up el-icon--right" v-else></i>
                    </span>

                  </h4>
                  <div class="thinking-steps" v-if="thoughtTxt[it] && thinkingShow[it]">
                    <ul>
                      <li class="step" v-for="(stp, idx) in thoughtTxt[it]" :key="stp.title"
                        :class="stp.status + 'status'">
                        <i class="xh">
                          <a-icon type="check" v-if="stp.status === 'success'" />
                          <a-icon type="loading" v-if="!stp.status || stp.status === 'pending'" />
                          <a-icon type="close" v-if="stp.status === 'error'" />
                        </i>
                        <h3 style="font-weight: normal;">
                          {{ stp.title || (stp.status === 'success' ? '思考完毕' : '思考中') }}

                        </h3>
                        <span class="contents" v-show="$route.query.showThinking">
                          <span style="color: var(--tagColor1);margin-bottom: 2px;display: block;cursor: pointer;"
                            @click="toggleThinkingBtn(it + idx)">{{ !toggleThinking[it + idx] ? '展开详情 >>' : '收起详情 <<'
                            }}</span>
                              <span v-show="toggleThinking[it + idx]" v-html="getMarked(stp.content)"></span>
                          </span>


                      </li>
                    </ul>
                  </div>


                  <span class="contentsSkill" v-if="skillMsg[it] && it.split('__')[0] == 'p'">
                    <icon-use name="skill" style="display: inline-block;height: 16px;transform: translateY(2px);" />
                    {{ skillMsg[it] }}
                  </span>
                  <span class="contentsMarked"
                  @mouseup="handleTextSelection"
                    :class="(it.includes('-') ? 'isChat' : '') + (' robotskill' + skillMsg[it])"
                    :datatime="!it.includes('-') && it" v-if="it.split('__')[0] == 'r'" v-html="comments[it]"></span>
                  <span class="contentsMarked"  v-else v-html="getPured(comments[it], 'from')"
                  @mouseup="handleTextSelection"></span>

                  <!-- {{ answer_recommend }}{{ it }} -->
                  <div class="apps_recommend" v-if="it.split('__')[0] == 'r' && answer_recommend[it.split('__')[1]]">
                    <h4>服务推荐：</h4>
                    <ul>
                      <li v-for="ass in answer_recommend[it.split('__')[1]]" :key="ass">
                        <span>{{ ass.attr.name }}</span>
                        <span>
                          <!-- <a target="_blank" :href="getUri(ass.attr.uri, true)">办事指南</a> -->
                          <a target="_blank" :href="getUri(ass.attr.uri)">{{ ass.attr.action || '立即办理' }}</a>
                        </span>
                      </li>
                    </ul>
                  </div>

                  


                  <div class="tip" v-show="it.split('__')[0] == 'r' &&
                    answer_tools.includes(it.split('__')[1])
                    ">
                    <p class="tip-0" v-if="answer_source[it.split('__')[1]]" v-show="isShowSource">
                      <!-- <i class="el-icon-info" /> -->
                      <i style="font-style: normal;">
                        来源：<p v-for="ass in answer_source[it.split('__')[1]]" :key="ass" style="margin-bottom: 2px;">
                          <icon-use style="display:inline-block;vertical-align: middle;"
                            :name="ass.startsWith('doc_') ? 'doc' : 'qamini'" />
                          {{ ass.slice(4) }}

                            <el-icon title="点此下载" v-if="sourceLinksDownLoad[ass]"
                            style="cursor:pointer; font-size:12px; text-align:center;padding: 1px;border-radius: 4px; border: 1px solid var(--theme); margin-left: 6px;color: var(--theme);"
                            class="el-icon-download" @click="showCards({ doc: { uri: sourceLinksDownLoad[ass] } })"><Download /></el-icon>
                        </p>
                      </i>
                    </p>

                    <!-- <el-button
              size="mini"
              icon="el-icon-edit-outline"
              circle
              title="提交反馈"
            /> -->
                    <div class="tip-1">
                      <span style="display: flex;gap:4px;">
                        <!-- {{}} -->

                        <a class="btnToUser" @click="okuseUserChatting(it)"
                          v-if="canUseHelpDesk && ((findSeq(it, 'clear') && commentsTxt[it] === cantAnswer) || isUnLikeCollection(it))">转人工</a>
                        <el-icon style="color: var(--theme);margin-right:6px;" title="再试一次"
                          v-if="commentsTxt[it] !== cantAnswer && isLastId(it)" @click="findSeq(it)"><RefreshLeft /></el-icon>
                        <el-icon class="el-icon-document-copy" style="color: var(--theme);" circle title="复制"
                        v-if="commentsTxt[it] !== cantAnswer" @click="copyDoc(it)" ><CopyDocument /></el-icon>


                      </span>

                      <!-- <el-button
                    size="mini"
                    @click="findSeq(it)"
                  >
                    重新生成
                  </el-button>

                  <el-button
                    size="mini"
                    @click="findSeq(it, 'clear')"
                  >
                    重置会话
                  </el-button> -->

                      <feed-back @newComment="newComment" @toUser="useUserChatting" @setStatus="setLikeStatus"
                        :itId="it" :idNow="idStorage[it]" :status="likeStatus[it]" :copilotId="copilotId" />

                      <!-- {{idStorage[it]}} -->

                    </div>
                  </div>
                </div>

                <div class="pager" v-if="allPager[it]">
                  <i class="el-icon-caret-left" @click="changeAnswer(it, false)" />
                  <i>{{ currentPager[it] }}</i>
                  <i>/</i>
                  <i>{{ allPager[it].length }}</i>
                  <i class="el-icon-caret-right" @click="changeAnswer(it, true)" />
                </div>

                <!-- {{lastQueRecommend}} -->
                <div class="recommendQues" v-if="lastQueRecommend[it]">
                  <h3>您可能还想问：</h3>
                  <ul>
                    <li v-for="(que, idx) in lastQueRecommend[it]" :key="'rrrque' + idx + it"
                      @click="useTheQue(que, true)">
                      <!-- <i class="el-icon-question" /> -->
                      <icon-use style="height: 13px;width: 17px;padding-top: 2px;float: left;" name="que"></icon-use>
                      {{ que.question }}
                    </li>
                  </ul>
                </div>

              </div>
            </template>

            <div class="chat_box robot a22222" :style="`--avatar:url(${avatar});--robotIcon:url(${parseRobotIcon()})`"
              v-if="loadingNew">
              <div class="txt">
                <div class="load-wrapp">
                  <div class="load-6">
                    <div class="letter-holder" style="--time: 3s">
                      <div v-for="(it, ik) in '正在思考中请稍候......'.split('')" :class="`l-${ik + 1} letter`" :key="it + ik">
                        {{ it }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="chat_box robot a33333" v-if="showOther.show"
              :style="`--avatar:url(${avatar});--robotIcon:url(${parseRobotIcon()})`">
              <div class="txt">
                {{ showOther.txt }}
                <p style="font-size:12px;border-top:1px solid #999;line-height:30px;">
                  消息框于3秒后自动关闭
                </p>
              </div>
            </div>

            <p :id="'boxBottom' + onlyId"></p>
          </div>

          <div class="comments_box"
            :class="{ 'no_que': (queVaries.length === 0 && skillsCategories.length === 0), 'mob': isMobile() }"
            id="commentsBoxUser" v-show="!isRobot">
            <!--  -->
            <div class="tip fixed">
              人工正在为您服务~
            </div>

            <div class="mine" style="
                height: 40px;
                font-size: 12px;
                color: #bbb;
                text-align: center;
              " v-loading="leftUserChatNums" element-loading-spinner="el-icon-loading">

              没有更多了~
            </div>
            <!-- v-loading="getUserChatRecordsLoading" -->
            <div style="
                height: 40px;
                font-size: 12px;
                color: #bbb;
                text-align: center;
              ">



            </div>






            <template v-for="(it, idx) in commentsUserIds" :key="'uuu' + idx" >
              <div style="
              height: 40px;
              line-height: 40px;
              font-size: 12px;
              color: #bbb;
              text-align: center;
            " v-if="timeStamp[it]">
                {{ timeStamp[it] }}
              </div>

              <div class="chat_box" :name="name" :style="`--avatar:url(${avatar})`"
                :class="commentsUser[it].type == 'user' ? 'person' : 'help'" :id="commentsUser[it].id"
                v-read="{ data: { id: commentsUser[it].id, type: commentsUser[it].type, name: commentsUser[it].answerItems } }">
                <el-tooltip :content="name" :disabled="name.length <= 3" effect="light" placement="left-end">
                  
                  <div class="user_name" v-show="commentsUser[it].type !== 'help'">{{ name }}</div>

                </el-tooltip>

                <div class="txt marked" :class="commentsUser[it].point == 'dealing' && 'cor2'">
                  <span v-if="commentsUser[it].type == 'help'" class="lastQue">
                    {{ commentsUser[it].question.content }}
                    <!-- {{commentsUser[it]}} -->
                  </span>
                  <template v-if="commentsUser[it].answerItems && commentsUser[it].type == 'help'">
                    <span v-for="(anw, ia) in commentsUser[it].answerItems" :key="'anw' + ia">
                      <span>{{ anw.content }}</span>
                      <p style="color: #bbb;">{{ moment(anw.createdAt).format('YYYY-MM-DD HH:mm') }}&nbsp;{{
                        anw.createdByUserFullName }}</p>
                    </span>


                  </template>
                  <span v-else v-html="commentsUser[it].content"></span>
                </div>
              </div>
            </template>

            <p :id="'boxBottomUser' + onlyId"></p>
          </div>

          <i v-if="isChatting && !loadingNew" class="el-icon-video-pause stopAnswer" @click="toStopClient">停止生成</i>

          <h3 class="V2-welcome welStr"
            :class="{ 'square': isSquare, 'topTrans': !(queVaries.length || tasksRecommend.length) }"
            v-if="!isNotV2 && !commentsIdCollection.length && copilotInfo.source && (isNewChat || !copilotRecordLoading) && (nowConversationId == useConversation.name)">
            <span v-html="parseWelcome()"></span>
          </h3>

          <div class="square-infos"
            v-if="isSquare && !isNotV2 && !commentsIdCollection.length && copilotInfo.source && (isNewChat || !copilotRecordLoading)">
            <h3 class="faq-tit" v-if="useQues.length">
              <i class="el-icon-question" style="color: var(--theme);"></i>
              常用问答
            </h3>
            <ul class="faq" v-if="useQues.length">
              <li v-for="it in useQues" @click="nowTxt = it.question">
                {{ it.question }}
              </li>
            </ul>
            <!-- <p style="color: #bbb;" v-else>当前暂无常用问题</p> -->


            <h3 class="doc-tit" v-if="Object.keys(useDocs).length">
              <i class="el-icon-document" style="color: var(--theme);"></i>

              我已经学习的本地知识：
            </h3>
            <ul class="doc" v-if="Object.keys(useDocs).length">

              <el-dropdown v-for="(it, val) in useDocNames" size="small" :key="it" :trigger="'hover'">

                <li>
                  <div class="tit">
                    <i class="el-icon-s-flag" style="color: var(--theme);"></i>
                    {{ it }}
                    <div class="num">{{ useDocs[it].length }}</div>
                  </div>

                  <!-- <div class="des">{{ '服务简介' }}</div> -->

                  <!-- {{ it }} -->
                </li>

                <template #dropdown>
                  <el-dropdown-menu class="doc-list-limit">
                  <el-dropdown-item v-for="(it, idx) in useDocs[it]" :key="'model' + idx" class="outbtn-item"
                    :command="JSON.stringify(it)">
                    <div>
                      <span>{{
                        it.doc.text
                      }}</span>
                      <!-- <a :href="it.doc.uri" :download="it.doc.text" type="file"> -->
                        <el-icon title="点此下载"
                        style="text-align:center;padding: 2px;border-radius: 6px; border: 1px solid var(--theme); margin-left: 6px;color: var(--theme);"
                        class="el-icon-download" @click="showCards(JSON.stringify(it))"
                        v-if="it.doc.userDownloadable" ><Download /></el-icon>
                      
                      <!-- </a> -->
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
                  
                </template>
                
              </el-dropdown>

              

            </ul>
            <!-- <p style="color: #bbb;" v-else>当前暂无已学习的知识</p> -->


          </div>

          <div class="chat_txt"
            :class="{ 'denyInput': isChatting, 'centerNow': !isNotV2 && !commentsIdCollection.length && (isNewChat || !copilotRecordLoading), 'square': isSquare, 'topTrans': !(queVaries.length || nowSkillsAll.length), 'isMobile': isMobile() }">
            <div v-if="setStartRecord" class="wave-box">
              <div class="wave-btn">{{ startRecord ? '正在录音中...' : '按住 说话' }}</div>
              <Wave v-if="startRecord"></Wave>
              <div @click.stop="setStartRecordNow(false)"
                style="color: var(--theme);background:var(--themelight);border-radius: 24px;padding: 4px;position: absolute;right: 0px;bottom: 0px;z-index: 19;">

                <icon-use style="height: 24px;" name="keyboard" />
              </div>

              <div @touchstart.stop="startMic()" @touchend.stop="startMic('stop')" style="color: var(--theme);background:transparent;position: absolute;left: 0px;bottom: 0px;width:100%;height:100%;z-index: 19;
              clip-path: polygon(0% 0, 100% 0%, 100% calc(100% - 38px), calc(100% - 38px) calc(100% - 38px), calc(100% - 38px) 100%, 0 100%);
              ">
                <!-- 遮罩 -->
              </div>


            </div>

            <div>
              <!-- <div v-if="nowTask.name" class="taskTarget">
                <span>
                  <icon-use name="magic" style="--theme: #fff;float: left;
  margin-top: 1px;height: 20px;
margin-right: 2px;"></icon-use>
                  {{ nowTask.commands[0] }}
                  <i class="el-icon-more" style="cursor: pointer;" v-if="nowTask.commands[0] === '智能任务'"
                    @click.stop="toggleAllTasksList"></i>

                </span>

                <i class="el-icon-error" @click="clearTaskAll" />
              </div> -->

              <!-- tasksPop -->



              <!-- {{ (nowTxt || clickedMagic) && !nowTask.name && ((!similarLoading && tasksAndQuesPopFilter.length)) && !stopPopSimilarQue }} -->
              <!-- (nowTxt || clickedMagic) -->
              <ul style="z-index:8;" class="tasks"
                :class="{ 'mob': isMobile(), 'borderAll': !(queVaries.length || tasksRecommend.length) }"
                v-if="showTasksTips">
                <!-- v-loading="similarLoading" -->
                <!-- similarLoading ||  -->
                <p style="height:100px;" v-if="!tasksAndQuesPopFilter.length"></p>
                <!-- {{ skills }} -->
                <li @click="useTask(task, idx)" v-for="(task, idx) in tasksAndQuesPopFilter" :id="'tk' + idx"
                  :key="'nott' + idx" :class="{ 'tabChoose': idx === nowTaskIdx, 'flexBetween': task.commands }">

                  <template v-if="task.commands">
                    <p class="task-command">
                      <icon-use name="magic" style="display: inline-block;
  height: 20px;
margin-right: 2px;"></icon-use>
                      <!-- {{idx}} -->
                      <span class="over_line" v-html="getNowTxtHighLight(task.commands[0])"></span>
                      
                    </p>
                    <p>
                      <el-tooltip effect="light" placement="left-end">
                        <div style="max-width: 200px;" slot="content" v-html="getNowTxtHighLight(task.description)"></div>
                        <i class="el-icon-info"></i>

                      </el-tooltip>
                      <!-- <span ></span> -->
                    </p>
                  </template>
                  <template v-else-if="task.question">
                    <icon-use name="qa" style="display: inline-block;height: 15px;margin-top: 3px;"></icon-use>
                    <span class="over_line" v-html="getNowTxtHighLight(task.question)"></span>
                  </template>
                  <template v-else>
                    <icon-use name="skill" style="display: inline-block;height: 15px;margin-top: 3px;"></icon-use>
                    <span class="over_line" v-html="getNowTxtHighLight(task.text)"></span>
                    <el-tag type="success" 
                      class="task-user-tag"
                      v-for="(titem,tindex) in task.tags"
                      :key="titem.name+tindex">{{titem.name}}</el-tag>
                  </template>
                </li>


              </ul>
            </div>


            <div class="close" @click="clearTask" v-if="tasksAndQuesPopFilter.length && showTasksTips"
              :style="{ bottom: computedBottom }" style="position: absolute;
right: -6px;
z-index: 10;
  color: var(--theme);
  font-size: 16px;
  cursor: pointer;"><i class="el-icon-error" /></div>

            <!-- <div > -->
            <ul class="similarQues" v-if="similarQueShow && !stopPopSimilarQue && !this.nowTask.name && !closeQues"
              element-loading-spinner="el-icon-loading" v-loading="similarLoading">
              <li :id="'que' + idx" @click="useTheQue(que)" v-for="(que, idx) in similarQues" :key="'quee' + idx"
                :class="idx === nowTaskIdx && 'queChoose'">

                <icon-use name="qa" style="display: inline-block;
  height: 15px;
  margin-top: 3px;"></icon-use>
                {{ que.question }}

              </li>

            </ul>

            <i @click="clearTipQues" v-if="similarQueShow && !stopPopSimilarQue" class="el-icon-error" style="
  position: absolute;
  right: -98px;
  top: 85px;
  z-index: 11;
  font-size: 18px;
  cursor: pointer;
  color: var(--theme);" />

            <!-- </div> -->

            <!-- 有技能就使用技能 -->
            <div class="queVary" id="queVaryCube" :class="{ 'hasVary': queVary, 'no_pd': !tasksRecommend.length }"
              v-if="false&&(nowSkillsAll.length || queVaries.length)">

              <div v-if="queVary" class="varyTarget">
                <span>
                  <el-icon><Place /></el-icon>
                  {{ findTxt }}
                </span>

                <el-icon class="el-icon-error" @click="toSetVary(queVary)"><CircleCloseFilled /></el-icon>
                
              </div>
              <ul v-if="!queVary">
                <li v-for="it in queVaries" @click="toSetVary(it.name)" :class="it.name === queVary && 'active'">
                  <el-icon><Place /></el-icon>
                  {{ it.text }}
                </li>

                <el-popover :width="180*skillsFilterVary(it.name).length+20" :data-num="skillsFilterVary(it.name).length" v-show="skillsFilterVary(it.name) && skillsFilterVary(it.name).length" v-for="it in skillsCategories" size="small" :key="it.text" v-model="cates[it.name]"
                  placement="top-start" :trigger="'click'">

                  

                  <template #reference>
                    <li>
                      <el-icon><Operation /></el-icon>{{ it.text }}
                    </li>

                  </template>


                  <ul class="skillsContent">


                    <li @click="useSkill(skill, it.name)" v-for="(skill, idx) in skillsFilterVary(it.name)"
                      :key="it + idx">
                      <p style="color: var(--theme);font-size: 14px;">
                        <icon-use class="icon" name="skill2" />
                        {{ skill.text }}
                      </p>
                      <p style="font-size: 12px;" class="over_line2">{{ skill.description || '暂无简介' }}</p>
                    </li>

                  </ul>


                </el-popover>

                <i @click="toSetTree" class="hand" v-if="welcomeStr.includes('showSkillTool')">
                  <icon-use name="mind"
                    style="padding:0px 4px;line-height: 16px;border-radius:4px; padding-top: 2px;display:inline-block; background: var(--themelight);pointer-events: none;" />
                </i>

              </ul>
            </div>
            <!-- <div class="queVary" id="queVaryCube" :class="{ 'hasVary': queVary, 'no_pd': !tasksRecommend.length }"
              v-else-if="(queVaries.length || tasksRecommend.length ) && isRobot">
              <div v-if="queVary" class="varyTarget">
                <span>
                  <i class="el-icon-place" />
                  {{ findTxt }}
                </span>

                <i class="el-icon-error" @click="toSetVary(queVary)" />
              </div>
              <ul v-if="!queVary">
                <li v-for="it in queVaries" @click="toSetVary(it.name)" :class="it.name === queVary && 'active'">
                  <i class="el-icon-place" />
                  {{ it.text }}
                </li>

                <li class="icon-theme" v-for="(task, idx) in tasksRecommend" :key="'wecc' + idx + task.commands[0]"
                  @click="useTask(task)">
                  <icon-use name="magic" style="height: 12px;margin-right:6px;float: left;
  margin-top: 1px;"></icon-use>
                  <span>{{ task.commands[0] }}</span>

                  <el-tooltip effect="light" placement="left-end" style="margin-left:4px;">
                    <div style="max-width: 200px;" slot="content">
                      {{ task.description }}
                    </div>
                    <i class="el-icon-info"></i>

                  </el-tooltip>
                </li>
              </ul>

            </div> -->

            <!-- <div class="openAllTasks" @click="toggleAllTasksShow" v-show="!nowTask.name && !queVary"
              v-if="canUseTaskAndQues && nowTaskIds && nowTaskIds.length">
              <icon-use name="magic" style="
  height: 20px;
  margin-right: 2px;
  float: left;
  margin-top: 1px;"></icon-use>智能任务
              <i class="el-icon-more" @click.stop="toggleAllTasksList"></i>
            </div> -->

            <div class="textareaBox" :style="{ '--tagWidth': computedIndient }"
              :class="!(nowSkillsAll.length || queVaries.length) && 'bd1'">

              <!-- <textarea id="markdown-input" v-show="false" ref="area2">

                ### 通讯录：

```tabs
[{"title":"表格1","tableData":[{"key":"1","name":"Johnsss","age":32,"address":"New York No. 1 Lake Park","tags":["nice","developer"]},{"key":"2","name":"Jimsss","age":42,"address":"London No. 1 Lake Park","tags":["loser"]},{"key":"3","name":"Joesss","age":32,"address":"Sidney No. 1 Lake Park","tags":["cool","teacher"]}],"columns":[{"title":"Name","dataIndex":"name","key":"name"},{"title":"Age","dataIndex":"age","key":"age"},{"title":"Address","dataIndex":"address","key":"address"},{"title":"Tags","key":"tags","dataIndex":"tags"}]},{"title":"表格2","tableData":[{"key":"1","name":"John Brown","age":32,"address":"New York No. 1 Lake Park","tags":["nice","developer"]},{"key":"2","name":"Jim Green","age":42,"address":"London No. 1 Lake Park","tags":["loser"]},{"key":"3","name":"Joe Black","age":32,"address":"Sidney No. 1 Lake Park","tags":["cool","teacher"]}],"columns":[{"title":"Name","dataIndex":"name","key":"name"},{"title":"Age","dataIndex":"age","key":"age"},{"title":"Address","dataIndex":"address","key":"address"},{"title":"Tags","key":"tags","dataIndex":"tags"}]}]
```
  
    </textarea> -->
              <!-- closable -->
              <!-- size="small" -->

              <el-tooltip class="item" effect="dark" :content="'点击退出技能'" v-if="nowSkill && nowSkill.text"
                placement="top">
                <!-- <i class="el-icon-plus"></i> -->
                <el-tag @click="clearTaskAndSkillChoosed" class="tagTaskChoosed">
                  <!-- {{taskChoosed.commands[0]}} -->
                  {{ nowSkill.text }}
                </el-tag>
              </el-tooltip>


              <div class="question divRef" :class="!isMobile() ? 'pcH' : 'mobileH'" v-if="useEditor">
                <span class="divContent" @keydown="toSendQue" v-html="nowTxt" contenteditable="true"></span>
              </div>

              <textarea class="question" :class="!isMobile() ? 'pcH' : 'mobileH'" v-model="nowTxt" ref="questionRef"
                v-else :style="`text-indent:${computedIndient};`" @keydown="toSendQue" placeholder="请输入您要咨询的问题...">
    </textarea>
              <div class="tools">
                <div class="mdChoose" v-show="modelUse">
                  <el-dropdown v-if="modelChooses && modelChooses.length" @command="changeModel" size="small"
                    :trigger="isMobile() ? 'click' : 'hover'" style="height: 24px; float: left;">
                    <div class="mdUse">
                      <icon-use name="model" style="height: 13px;margin-right:4px;float: left;
  margin-top: 4px;" />
                      {{ modelUseTxt }}

                      <i class="el-icon-caret-bottom"></i>
                    </div>
                <template #dropdown>

                    <el-dropdown-menu>
                      <el-dropdown-item v-for="(it, idx) in modelChooses" :key="'model' + idx"
                        :command="it.name + '***' + it.text">{{
                          it.text
                        }}</el-dropdown-item>
                    </el-dropdown-menu>

                  </template>
                  </el-dropdown>

                  <!-- <div class="mdUse deny" :class="modelParse[modelUse] && modelParse[modelUse].isReasoning && 'canuse'">
                    <icon-use name="deep"  style="height: 13px;margin-right:4px;float: left;
  margin-top: 4px;"/>
                    深度思考
                  </div> -->


                </div>

                <div class="txtSend">
                  <i class="txtlimit"><span :class="{ 'exec': nowTxt && nowTxt.length > 6000 }">{{ nowTxt ?
                    nowTxt.length
                    :
                    0 }}</span>
                    / 6000</i>
                  <span @click="toSendQueNow(null, null)">
                    <icon-use class="icon-one-mini" name="send" title="发送问题" :class="{ no: !nowTxt }" />
                  </span>

                  <div id="talkButton" v-if="siteInfo.openWXSDK && isMobile()" @click="setStartRecordNow(true)"
                    class="mic">
                    <icon-use name="mic"></icon-use>
                  </div>


                </div>

              </div>
            </div>
          </div>
        </template>
        <!-- <el-button size="mini" type="text" @click="hideRobot">收起>></el-button> -->
        <template v-else>
          <div class="noIdSvg">
            <p>该课程尚未创建智能助教~</p>
            <img src="./img/noId.svg" alt="" />
          </div>
        </template>
      </div>
    </div>

    <div id="myModal" class="modal" v-show="modalShow">
      <span class="close" @click="triggerModal">&times;</span>
      <img class="modal-content" id="img01" :src="imgTarget">
    </div>

    <el-dialog :title="null" :visible.sync="popVisible" width="850px" center :append-to-body="true">
      <div v-html="popContent"></div>
    </el-dialog>

    <tabs-table v-for="(tbb, idx) in tabsTable" v-show="false" :key="'etabs' + idx" class="etabs"
      :id="'etabs_' + tbb.id" :dataParse="tbb.dataJsonParse">
    </tabs-table>
    <div class="right-role-cards-drawer" @click="toggleBStopic">
      <div class="toggle-drawer">
        {{!bstopic?'收敛':''}}
      </div>
    </div>
    <div class="right-role-cards-drawer copy-bord" @click="toggleCopyedtopic">
      <div class="toggle-drawer">
        {{!copytopic?'黑板':''}}
      </div>
    </div>
     <div class="right-role-cards-drawer back-to-bot"
     :class="'back-to-bot-'+roleData?.length" @click="backToBot">
          <div class="toggle-drawer">
            <el-tooltip
              class="box-item"
              effect="light"
              content="修改虚拟对话角色"
              placement="left"
            >
              <img :src="avaterimgs.rollback"/>
            </el-tooltip>
          </div>
        </div>
    
    <el-drawer
      v-model="bstopic"
      :with-header="false"
      size="80%"
      title="头脑风暴收敛"
      class="bstopic-drawer-wrap"
    >
    <div class="bstopic-drawer-outter-wrap">
      <div class="bstopic-drawer-inner-wrap" v-if="!bstopicloading">
        <template v-if="differentiated?.length">
          <div class="bst-start">
            <el-button type="primary" @click="toStartTotal('restart')">重新收敛</el-button>
          </div>
        </template>
        <template v-else>
          <div class="bst-start bst-start-first">
            <div class="bst-start-inner">
              <div class="inner-text-area">
                <div class="ita-label">收敛维度：</div>
                <div class="ita-classify">
            <div class="bst-textarea">
              <span>外部：</span>
              <el-input v-model="viewdata.summaryDimension['外部']" placeholder="例如：市场因素、市场需求等" type="textarea" :rows="2"></el-input>
            </div>
            <div class="bst-textarea">
              <span>内部：</span>
              <el-input v-model="viewdata.summaryDimension['内部']" placeholder="例如：时间成本、技术成本等" type="textarea" :rows="2"></el-input>
            </div>
            <div class="bst-textarea">
              <span>竞争：</span>
              <el-input v-model="viewdata.summaryDimension['竞争']" placeholder="例如：差异化、优劣势等" type="textarea" :rows="2"></el-input>
            </div>
                </div>
              </div>
              
              <div class="bst-btns">
                <el-button type="primary" @click="toStartTotal('start')">基于头脑风暴聊天记录收敛</el-button>
                <el-button type="primary" @click="toStartTotal('blackbord')">基于黑板收敛</el-button>
              </div>
            </div>
          </div>
        </template>
        <differentiation ref="differentiationref" :viewdata="viewdata" v-show="(differentiated?.length)" @changedifferentiated="changedifferentiated"></differentiation>
      </div>
      <ailoading v-if="bstopicloading"></ailoading>
    </div>
      
    </el-drawer>
    <el-drawer
      v-model="copytopic"
      :with-header="false"
      size="80%"
      title="黑板"
      class="copytopic-drawer-wrap"
    >
      <div class="copytopic-drawer-inner-wrap">
        <copytopic ref="copyreftopic" @delCopy="delCopy" @editCopy="editCopy"></copytopic>
      </div>
    </el-drawer>
    <div class="right-role-cards"
        :class="'right-role-cards-'+itindex"
        v-for="(it,itindex) in roleData"
        :key ="it.name+itindex">
          <el-popover :width="350" placement="left" trigger="hover">
              <template #reference>
                <el-avatar 
                @click.native="useRole(it)" 
                class="avater" 
                :src="avaterimgs[it.image]"
                @error="avatererrimgs[it.image]=true"
              >
                {{ !avatererrimgs[it.image] ? it.name : '' }}
              </el-avatar>
                <el-avatar  v-if="false" @click.native="useRole(it)" class="avater" :src="avaterimgs[it.image]" />
              </template>
            <div class="robot-inner-base-info">
                <div class="bi-usertype">
                  {{ it.name }} / {{ it.userType.name }}
                </div>
                <div class="bi-tags">
                  <el-tag v-for="(tg,tgindex) in it.tags" :key="tg.name+tgindex">{{ tg.name }}</el-tag>
                </div>
                <div class="bi-desc">
                  {{it.desc}}
                </div>
              </div>
            </el-popover>
        </div>
   
    <el-dialog
      v-model="dialogrestart"
      title="重新收敛"
      width="500"
    >
     <div class="bst-start-inner">
        <div class="inner-text-area">
          <div class="ita-label">收敛维度：</div>
          <div class="ita-classify">
            <div class="bst-textarea">
              <span>外部：</span>
              <el-input v-model="viewdata.summaryDimension['外部']" placeholder="例如：市场因素、市场需求等" type="textarea" :rows="2"></el-input>
            </div>
            <div class="bst-textarea">
              <span>内部：</span>
              <el-input v-model="viewdata.summaryDimension['内部']" placeholder="例如：时间成本、技术成本等" type="textarea" :rows="2"></el-input>
            </div>
            <div class="bst-textarea">
              <span>竞争：</span>
              <el-input v-model="viewdata.summaryDimension['竞争']" placeholder="例如：差异化、优劣势等" type="textarea" :rows="2"></el-input>
            </div>
          </div>
        </div>
        <div class="bst-btns">
          <el-button type="primary" @click="toStartTotal('start')">基于头脑风暴聊天记录收敛</el-button>
          <el-button type="primary" @click="toStartTotal('blackbord')">基于黑板收敛</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { createClient } from "graphql-ws";
import Wave from "./modules/wave.vue";
import FeedBack from "./modules/feedback.vue";
import apolloProvider from "@/assets/js/apolloclient.js";
import queryStr from "./news/chat.js";


import subs from "./news/subs.js";
import avater1 from '@/assets/images/avater/avater1.svg?raw'
import avater2 from '@/assets/images/avater/avater2.svg?raw'
import avater3 from '@/assets/images/avater/avater3.svg?raw'
import avater4 from '@/assets/images/avater/avater4.svg?raw'
import avater5 from '@/assets/images/avater/avater5.svg?raw'
import avater6 from '@/assets/images/avater/avater6.svg?raw'
import rollback from '@/assets/images/rollback.svg?raw'
import ding from '@/assets/images/ding.svg?raw'
import {toSVGDataUrl} from '@/utils'
import differentiation from '@/views/competitiveanalysis/differentiation.vue'
import copytopic from '@/views/competitiveanalysis/copytopic.vue'

import gql from "graphql-tag";




// const json2yaml = require("js-yaml");
import yaml from "js-yaml";
import { mapState } from "vuex";
import { saveAs } from "file-saver";

import { EventBus } from "./js/eventBus.js";

import * as ww from "@wecom/jssdk";

import { v4 as uuidv4 } from "uuid";

// const defaultIcon = require("./img/normal.png");
// const usernormal = require("./img/usernormal.svg");
import defaultIcon from "./img/normal.png";
import usernormal from "./img/usernormal.svg";


// console.log(defaultIcon, "-----------");

// import bguser from "./img/user.svg";
// import bgfull from "./img/full.svg";
// import bgnofull from "./img/nofull.svg";
// import bgclear from "./img/clear.svg";
// import bgclose from "./img/close.svg";
// import bgchatsIcon2 from "./img/chatsIcon2.svg";
// import bgsend from "./img/send.svg";
// import bgnormal3 from "./img/normal3.svg";

import moment from "moment";
import "moment/locale/zh-cn";
moment.locale("zh-cn");

import _ from "lodash";

import { marked } from "marked";

import ailoading from '@/components/ailoading.vue'




let setFunc = (obj, key, value) => {
  obj[key] = value;
};

const render = new marked.Renderer();
marked.setOptions({
  renderer: render,
  async: false,
  breaks: false,
  extensions: null,
  gfm: true,
  hooks: null,
  pedantic: false,
  silent: false,
  tokenizer: null,
  walkTokens: null,
});

let timer = null;

import * as echarts from "echarts";
import {
  TitleComponent,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
} from "echarts/components";
import { LineChart } from "echarts/charts";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
echarts.use([
  TitleComponent,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);

import TabsTable from "./modules/tabsTable.vue";
import DOMPurify from "dompurify";

import { robotMdInit } from "./modules/robotMdInit.js";

// import { computed, ref,inject,onMounted, watch } from 'vue';

export default {
  mixins: [robotMdInit],
  props: {
    // 机器人技能
    skillsCategories: {
      type: Array,
      default: () => [],
    },
    // skills: {
    //   type: Array,
    //   default: () => [],
    // },
    // 判断当前会话是否正在被使用
    nowConversationId: {
      type: String,
      default: () => "",
    },
    //匿名模式
    anonymity: {
      type: Boolean,
      default: () => false,
    },
    modelParse: {
      type: Object,
      default: () => {},
    },
    canUseTaskAndQues: {
      type: Boolean,
      default: () => true,
    },
    isNewChat: {
      type: Boolean,
      default: () => false,
    },
    noActionBox: {
      type: Boolean,
      default: () => false,
    },
    errorMsg: {
      type: String,
      default: () => "您好，出现未知故障，请刷新页面稍后再试~",
    },
    useQues: {
      type: Array,
      default: () => [],
    },
    useDocs: {
      type: Object,
      default: () => {},
    },
    useDocNames: {
      type: Array,
      default: () => [],
    },
    isRight: {
      type: Boolean,
      default: () => true,
    },
    useCopilotId: {
      type: String,
      default: () => "",
    },
    useConversation: {
      type: Object,
      default: () => {},
    },
    useFit: {
      type: Boolean,
      default: () => false,
    },
    tasksRecommend: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      parentSessionId: '',
      // parentSessionId: '01K08ZVPJK5NPXQW06MFEP790E',
      roleData: [],
      // 是否属于lke模型
      isLkeModel: false,
      //输入法和shift
      isIMEComposing: false,
      isShiftKey: false,
      //-------------
      modalShow: false,
      imgTarget: "",
      copilotInfo: {},
      copilotLoad: false,
      // nowTxt: '',
      nowTxt: "",
      client: null,
      clientMsg: null,
      loading: {},
      loadingNew: false,
      chatShow: false,
      startRecord: false,
      setStartRecord: false,
      showMsg: true,
      isChatting: false,
      isChattingId: null,

      hasCopilotId: true,
      totalCount: 0,
      copilotRecordLoading: true,

      recommends: [
        "登录企业邮箱的初始密码是什么?",
        "历年录取分数是多少?",
        "学生社团有哪些?",
      ],

      showOther: {
        show: false,
        txt: null,
      },

      answer_tools: [],
      idStorage: {},
      likeStatus: {},
      answer_link: {},
      answer_source: {},
      //下载链接
      sourceLinksDownLoad: {},
      answer_recommend: {},
      timeStamp: {},
      chattTimeStamp: false,

      leftChatNums: false,
      //有聊天记录，默认没有
      hasRecords: false,

      breakPoint: null,

      pageSize: 20,
      pageNum: -1,

      cantAnswer: "",
      showCantAnswer: false,

      comments: {},
      commentsTxt: {},
      commentsIdCollection: [],
      commentsfindIds: {},

      //踩和赞
      isUnLikeIds: [],

      //转人工---------------
      commentsUser: {},
      commentsUserIds: [],
      leftUserChatNums: true,
      getUserChatRecordsLoading: true,
      firstLoading: 1,
      userAnswerCollect: {},
      hasAnswerChanged: false,
      myQueStartCursor: null,
      uChatTotalCount: 0,

      pageResolved: {},

      //-----------------------

      widthDefine: 400,
      otherCubeHeight: 250,
      welCubeHeight: 0,

      taskHeight: 0,

      isRobot: true,
      isFull: false,

      // 知识分类
      queVaries: [],
      queVary: null,

      //最近一条问答的时间
      lastTime: 0,
      //所有问答的时间
      lastTimeCollection: {},

      //多轮会话
      conversationId: null,
      conversationText: null,
      //暂停会话
      subs: null,

      // 再试一次的pager
      queNameToId: {},
      currentPager: {},
      allPager: {},

      //任务and问题
      tasksAndQuesPopFilter: [],
      tasksPopShow: false,
      tasksPop: [],
      allTasksShow: false,
      clickedMagic: false,
      tasksPopFilter: [],
      nowTask: {},
      nowSkill: {},
      //为了避免skill被清空之后的消息无法传递
      lastSkill: {},
      nowSkillsAll: [],
      skillMsg: {},
      robotSkillMsg: {},
      robotUsedSkillMsg: {},
      taskChoosed: {},
      nowTaskIds: [],
      nowTasksAll: [],
      firstSendTask: false,
      taskActions: {},
      popVisible: false,
      popContent: "",
      nowTaskIdx: 0,

      similarQues: [],
      similarQueShow: false,
      similarLoading: false,
      stopPopSimilarQue: false,
      closeQues: false,

      tabsTable: [],

      // 技能
      skills: [],
      skillTarget: {},
      skillTargetTxt: '发散',

      //会话显示按钮的高亮
      openChated: false,

      // 问答推荐
      lastQueRecommend: {},

      // 思考过程
      reasonTxt: {},
      reasonTime: {},
      reasonHidden: {},

      // 思维链
      thinkingShow: {},
      thoughtTxt: {},
      toggleThinking: {},

      // 模型切换和选择
      modelChooses: [],
      modelUse: "",
      modelUseTxt: "",
      isDeep: false,

      // 企业微信sdk
      signs: {
        config: "",
        agent: "",
        time: new Date().getTime(),
      },
      sdkMsg: false,

      corpId: "",
      corpAppAgentId: "",

      // 您可能还想问
      maybeAsk: false,

      // 富文本编辑器
      editorConfig: {
        allowedContent: true,
      },
      cates: {},
      useEditor: false,
      avaterimgs:{
        avater1:"",
        avater2:"",
        avater3:"",
        avater4:"",
        avater5:"",
        avater6:"",
        ding:"",
      },
      bstopic:false,
      avatererrimgs:{
        avater1:false,
        avater2:false,
        avater3:false,
        avater4:false,
        avater5:false,
        avater6:false,
      },
      differentiated:"",
      bstopicloading:false,
      copytopic:false,
      blackbord:[
      ],
      viewdata:{
        summaryDimension:{}
      },//viewid获取的viewdata
      currentTooltip:"",
      dialogrestart:false,
      tooltipDebounce:'',


      // 后添加的
      position: {},
      userInfo: {},
    };
  },
  computed: {
    ...mapState(["siteInfo", "postIframeData"]),
    skillsVaries() {
      let arr = (this.nowSkillsAll || []).map((t) => t.category || []).flat(2);
      return arr;
    },
    computedBottom() {
      let i =
        this.tasksAndQuesPopFilter.length >= 4
          ? 4
          : this.tasksAndQuesPopFilter.length;
      let hii = this.isMobile() ? 86 : 136;
      return hii + 32 * i + "px";
      // ['-16px','-48px','-82px', '-110px'][i-1];
      // 4*32=128
      // '-110px' : '-'+(tasksAndQuesPopFilter.length*32+20)+'px'
    },
    queVaryCube() {
      return [...this.queVaries, ...this.tasksRecommend, ...this.nowTaskIds];
    },
    isSquare() {
      if (
        this.siteInfo.hiddenQuesAndDocs &&
        this.$route.name === "airobot-v2"
      ) {
        return false;
      } else {
        return (
          (this.useQues && this.useQues.length) ||
          (this.useDocs && Object.keys(this.useDocs).length)
        );
      }
    },
    isNotV2() {
      if (
        this.$route.query.theme === "canvas" ||
        this.$route.name === "airobotauth" ||
        this.$route.name === "airobots"
      ) {
        return true;
      } else if (
        this.$route.name === "airobot-v2" ||
        window.Global.SITE_THEME ||
        (this.siteInfo && this.siteInfo.systemLogo)
      ) {
        return false;
      } else {
        return this.$route.name !== "airobotsuda";
      }
    },
    computedIndient() {
      let realLen = 0;

      if (this.nowSkill.text) {
        // this.taskChoosed && this.taskChoosed.commands && this.taskChoosed.commands.length) {
        let name = this.nowSkill.text;
        //汉字
        let len1 = name
          .toUpperCase()
          .split("")
          .filter((t) => t.charCodeAt() > 90 || t.charCodeAt() < 65);
        // 字母是半格
        let len2 = name.length - len1.length;

        realLen = len1.length + len2 * 0.6 + 1;
      }
      return realLen + "em";
    },
    showTasksTips() {
      // return
      let bool =
        (this.nowTxt || this.clickedMagic) &&
        !this.similarLoading &&
        this.tasksAndQuesPopFilter.length &&
        !this.stopPopSimilarQue;
      // console.log(this.nowTask)
      if (!this.tasksPopShow) {
        return false;
      } else if (this.firstSendTask) {
        return false;
      } else if (this.useEditor && this.nowTxt.includes("<input")) {
        return false;
      } else {
        return bool;
      }
      // return bool;
    },
    onlyId() {
      return (
        this.copilotId +
        (this.useConversation && this.useConversation.name
          ? this.useConversation.name
          : "newid")
      );
    },
    welcomeStr() {
      if (
        this.copilotInfo.source &&
        this.copilotInfo.source.spec.copilot.welcome
      ) {
        return this.copilotInfo.source.spec.copilot.welcome;
      } else {
        return "你好，我是AI智能问答助手，有什么可以帮到您的吗?";
      }
    },
    avatar() {
      if (
        this.userInfo &&
        this.userInfo.avatar &&
        this.userInfo.avatar.avatarUrl &&
        !this.anonymity
      ) {
        return this.userInfo.avatar.avatarUrl;
      } else if (this.siteInfo.systemUserImg) {
        return this.siteInfo.systemUserImg;
      } else {
        return usernormal;
      }
    },
    name() {
      return this.anonymity
        ? "匿名"
        : this.userInfo && this.userInfo.name
        ? this.userInfo.name
        : "未知用户";
      // '欢迎来到 MDN 学习区。本系列文章意在为 Web 开发的纯新手提供编写网站的基本技巧，而非意在让你从“新手”变成“专家”，但能让你从“新手”变得“熟练”。从那之后，你就能开始以你自己的方式学习 MDN 的其余部分，和其他需要大量前置知识的中高级资源。从零开始学习 Web 开发极具挑战性，该教程将为你提供详细的资料，手把手帮助你轻松愉快地学习。无论你是正在学习 Web 开发的学生（自学或参与课程）、寻找材料的老师、编程爱好者，抑或是仅想进一步了解 Web 技术的工作原理，我们都希望你能感到宾至如归。';
    },
    isShowSource() {
      if (
        this.$route.query.showSource !== null &&
        this.$route.query.showSource !== undefined
      ) {
        if (this.$route.query.showSource == "false") {
          return false;
        } else {
          return true;
        }
      } else {
        return this.siteInfo.showSource;
      }
    },
    showClose() {
      return this.$route.query.showClose;
    },
    nowTime() {
      return new Date().getTime();
    },
    robotIcon() {
      if (this.copilotInfo && this.copilotInfo.source.spec.copilot.icon) {
        return this.copilotInfo.source.spec.copilot.icon;
      }

      if (this.siteInfo.systemRobotImg) {
        return this.siteInfo.systemRobotImg;
      }
      return defaultIcon;
    },
    findTxt() {
      return this.queVaries.find((t) => t.name === this.queVary).text;
    },
    canUseHelpDesk() {
      if (
        this.copilotInfo.source &&
        this.copilotInfo.source.spec.copilot.helpdesk
      ) {
        let help = this.copilotInfo.source.spec.copilot.helpdesk;
        // console.log(help)
        if (this.anonymity) {
          return false;
        } else {
          return help.enabled;
        }
      } else {
        return false;
      }
    },
    copilotId() {
      if (!this.isRight) {
        return this.useCopilotId;
      } else {
        return window.Global.COPILOT_ID;
      }
    },
    token() {
      if (
        this.$store &&
        this.$store.state &&
        this.$store.state.oidcStore &&
        this.$store.state.oidcStore.access_token
      ) {
        return this.$store.state.oidcStore.access_token;
      } else if (sessionStorage.getItem("oidcStore")) {
        let oidcPassed = JSON.parse(sessionStorage.getItem("oidcStore"));
        return oidcPassed["access_token"];
      } else {
        return null;
      }
      // return "fe5577389807b6d83cd735640cf2e61e";
    },
    openid() {
      if (
        this.$store &&
        this.$store.getters &&
        this.$store.getters.getUserInfo &&
        this.$store.getters.getUserInfo.openid &&
        !this.anonymity
      ) {
        return this.$store.getters.getUserInfo.openid;
      } else {
        let thisOpenid = localStorage.getItem("thisOpenid");
        if (thisOpenid) {
          return thisOpenid;
        } else {
          let openid = this.initNewId();
          localStorage.setItem("thisOpenid", openid);
          return openid;
        }
      }
      // return "fe5577389807b6d83cd735640cf2e61e";
    },
  },
  components: {
    Wave,
    FeedBack,
    TabsTable,
    differentiation,
    copytopic,
    ailoading,
  },
  created() {
    this.tooltipDebounce="";
    this.getWxKeys();
    
  },
  mounted() {
    let self = this;

    //   setTimeout(()=>{
    //   this.$store.commit('updatePostIframeData', {toolname:'完整演示',nowtxt:'[auto]',sendnow:'true'});
    // }, 10000)
    self.parentSessionId = this.$route.query.parentSessionId;

    // setInterval(() => {
    //   self.setSign();
    // }, 360000);

    self.getLoginInfo();

    setTimeout(() => {
      document.getElementById("commentsBox" + this.onlyId) &&
        document
          .getElementById("commentsBox" + this.onlyId)
          .addEventListener("scroll", this.handleScroll);
    });

    // 监听开始输入法编辑
    document.addEventListener("compositionstart", function (event) {
      self.isIMEComposing = true;
    });

    // 监听输入法编辑结束
    document.addEventListener("compositionend", function (event) {
      self.isIMEComposing = false;
    });

    document.addEventListener("keyup", function (event) {
      if (event.key === "Shift") {
        self.isShiftKey = false;
      }
    });
    document.addEventListener("keydown", function (event) {
      // console.log(event.key === "Shift", self.isShift, "-----");
      if (event.key === "Shift") {
        self.isShiftKey = true;
      }

      let len = self.similarQueShow
        ? self.similarQues.length
        : self.tasksAndQuesPopFilter.length;
      if (event.key === "ArrowUp") {
        // 在这里编写响应上键按下的逻辑
        let nowTaskIdx = self.nowTaskIdx - 1;
        if (nowTaskIdx < 0) {
          self.nowTaskIdx = len - 1;
        } else {
          self.nowTaskIdx = nowTaskIdx;
        }
      } else if (event.key === "ArrowDown") {
        // 下键被按下
        // self.nowTaskIdx = 0;
        let nowTaskIdx = self.nowTaskIdx + 1;
        if (nowTaskIdx > len - 1) {
          self.nowTaskIdx = 0;
        } else {
          self.nowTaskIdx = nowTaskIdx;
        }
        // 在这里编写响应下键按下的逻辑
      } else if (event.key === "Enter") {
        // if (self.similarQueShow && self.nowTaskIdx !== -1) {
        //   self.nowTxt = self.similarQues[self.nowTaskIdx].question;
        //   self.stopPopSimilarQue = true;
        //   self.similarQueShow = false;
        // } else {
        //   if (
        //     self.nowTxt.startsWith("/") &&
        //     self.tasksAndQuesPopFilter.length &&
        //     !self.firstSendTask
        //   ) {
        //     self.useTask(self.tasksAndQuesPopFilter[self.nowTaskIdx], self.nowTaskIdx);
        //   }

        // }

        if (
          self.tasksAndQuesPopFilter.length &&
          !self.stopPopSimilarQue &&
          self.nowTaskIdx !== -1
        ) {
          self.useTask(
            self.tasksAndQuesPopFilter[self.nowTaskIdx],
            self.nowTaskIdx
          );
        }

        // 在这里编写响应下键按下的逻辑
      }
    });
    // 
    this.toGetRoleData();
    if(!this.avaterimgs.avater1){
      this.getAvaterImg()
    }
    
  },
  watch: {
    //skillmsg及时传到外面点亮技能树
    robotSkillMsg: {
      handler(val, oldVal) {
        // this.$store.commit("changeSkillTree", val);
      },
      deep: true,
    },
    robotUsedSkillMsg: {
      handler(val, oldVal) {
        // console.log(val, '--------=====');
        // this.$store.commit("changeSkillUsedTree", val);
      },
      deep: true,
    },
    nowConversationId: {
      handler(val, oldVal) {
        // console.log(val, '--------')
        if (val !== oldVal) {
          if (
            val === this.useConversation.name &&
            Object.values(this.robotUsedSkillMsg).length
          ) {
            // console.log(this.robotSkillMsg, Object.values(this.robotUsedSkillMsg), '---------传递了吧')
            // this.$store.commit("changeSkillTree", this.robotSkillMsg);
            // this.$store.commit("changeSkillUsedTree", this.robotUsedSkillMsg);
          }

          if (!val) {
            // this.initWhiteBoard(".welStr .language-white-board");
          }

          if (this.pageNum == -1 && !this.commentsIdCollection.length) {
            this.getChatRecords();
          }
        }
      },
      deep: true,
    },
    postIframeData: {
      handler(val, oldVal) {
        console.log(val, oldVal, "-888---------");
        if (
          val !== oldVal &&
          this.nowConversationId == this.useConversation.name
        ) {
          if (val.taskname) {
            let fTask = this.nowTasksAll.filter((t) => t.text == val.taskname);
            if (fTask.length) {
              this.useTask(fTask[0], val.nowtxt);
            }
          }

          if (val.toolname) {
            let fTask = this.nowSkillsAll.filter((t) => t.text == val.toolname);
            console.log(fTask, "----daozheer");

            if (fTask.length) {
              this.useTask(fTask[0], val.nowtxt);
            }
          }

          if (val.nowtxt) {
            this.parseNowTxt(val.nowtxt);
          }

          if (val.sendnow !== "false" && val.sendnow) {
            this.toSendQueNow(null, null);
          }
        }
      },
      deep: true,
    },
    nowTaskIdx: {
      handler(val, oldVal) {
        // console.log(val, '-----------')
        setTimeout(() => {
          let idx = val;
          const element1 = document.getElementById("tk" + idx);
          const element2 = document.getElementById("que" + idx);

          // console.log(element1, element2, '-------')
          if (element1) {
            element1.scrollIntoView({
              block: val > oldVal ? "end" : "start",
            });
          }

          if (element2) {
            element2.scrollIntoView({
              block: val > oldVal ? "end" : "start",
            });
          }
        }, 10);
      },
      immediate: true,
      deep: true,
    },
    corpId: {
      handler(val, oldVal) {
        if (val) {
          this.setSign();
        }
      },
      immediate: true,
      deep: true,
    },
    nowTask: {
      handler(val, oldVal) {
        if (val.name && this.isMobile()) {
          // this.taskHeight = 30;
        } else {
          this.taskHeight = 0;
        }
      },
      deep: true,
    },
    queVaryCube: {
      handler(val, oldVal) {
        if (val.length) {
          this.$nextTick(() => {
            this.initBoxHeight();
          });
        } else {
          // this.other
        }
      },
      deep: true,
      // immediate: true
    },
    nowTxt: {
      handler(val, oldVal) {
        if (this.canUseTaskAndQues) {
          // 防止回车键干扰
          let realOldVal = String(oldVal).replaceAll("\n", "");
          let realVal = String(val).replaceAll("\n", "");

          // console.log(this.firstSendTask,val, beforeVal, '----刚刚选中？')
          if (val && realVal !== realOldVal) {
            this.tasksAndQuesPopFilter = [];
            // this.similarLoading = true;
            this.tasksPopShow = true;
            this.nowTaskIdx = -1;

            //防止选中之后突然弹出选项
            if (!this.firstSendTask) {
              this.similarLoading = true;
              this.getSimilars();
            } else {
              this.firstSendTask = false;
            }

            // console.log(this.firstSendTask, "-----------chufa");
          } else {
            this.tasksAndQuesPopFilter = [];
            this.similarQueShow = false;
            this.stopPopSimilarQue = false;
            this.firstSendTask = false;
          }

          // if (val.startsWith("/")) {
          //   this.tasksPopShow = true;
          //   this.nowTaskIdx = 0;
          //   this.tasksPopFilter = [];
          //   this.similarLoading = true;
          //   this.getSimilarTasks();
          // } else if (val && !this.closeQues) {
          //   this.similarLoading = true;
          //   this.similarQueShow = true;
          //   this.getSimilarQues();
          // } else if (!val) {

          //   this.similarQues = []
          //   this.tasksPopFilter = []
          //   this.similarQueShow = false;
          //   this.stopPopSimilarQue = false;

          //   if (!this.nowTask.name) {
          //     this.tasksPopShow = false;

          //   }

          // }
        }
      },
    },
    isChatting: {
      handler(val) {
        if (!val && this.$refs) {
          this.$refs["questionRef"] ? this.$refs["questionRef"].focus() : "";
          this.firstSendTask = false;
        }
      },
      // immediate: true
    },
    isRight: {
      handler(val) {
        if (!val) {
          this.chatShow = true;
          this.chatToBottom();
        }
      },
      immediate: true,
    },
    userAnswerCollect: {
      handler(val, oldVal) {
        if (val && oldVal && Object.keys(oldVal).length) {
          //在有值的情况下，新的传进来的值已经对不上旧的值
          let changed = false;
          for (let it in val) {
            if (val[it] >= 0 && oldVal[it] >= 0) {
              //已经记录的题目，答案长度更改了吗
              if (val[it] !== oldVal[it]) {
                changed = true;
              }
            }
          }

          this.hasAnswerChanged = changed;
        }
      },
    },
    isRobot: {
      handler(val) {
        if (val) {
          if (this.chatShow) {
            setTimeout(() => {
              document.getElementById("commentsBox" + this.onlyId) &&
                document
                  .getElementById("commentsBox" + this.onlyId)
                  .addEventListener("scroll", this.handleScroll);
            });
          }
          this.resetTimer(true);
        } else {
          if (this.chatShow) {
            setTimeout(() => {
              document.getElementById("commentsBoxUser") &&
                document
                  .getElementById("commentsBoxUser")
                  .addEventListener("scroll", this.handleScrollUser);
            });
          }
        }
      },
      immediate: true,
    },
    clientMsg: {
      handler(clientMsg) {
        // clientMsg
      },
    },
    welcomeStr: {
      handler(val, oldVal) {
        if (val) {
          // this.initWhiteBoard(".welStr .language-white-board");
        }
      },
      immediate: true,
      deep: true,
    },
    copilotId: {
      handler(val, oldVal) {
        if (!this.copilotId) {
        } else if (String(val) === "0") {
          //默认一个不请求详情的错误机器人值
          this.hasCopilotId = false;
        } else {
          // if (val !== oldVal) {
          //数据复原
          this.initCopilotsCase();

          this.scrollCheck("all");
          //请求机器人详情
          this.getRobotInfo();

          this.getChatRecords();
          this.initVaries();

          this.$nextTick(() => {
            // // console.log(this, document.getElementById("commentsBox"))
            document.getElementById("commentsBox" + this.onlyId) &&
              document
                .getElementById("commentsBox" + this.onlyId)
                .addEventListener("scroll", this.handleScroll);
          });
          // }
        }
      },
      immediate: true,
      deep: true,
    },

    token: {
      handler(val, oldVal) {
        if (val !== oldVal) {
          if (this.anonymity) {
            const openid = this.openid;
            this.client = createClient({
              url: `${window.Global.WSS_ORIGIN}${window.Global.COPILOT_URL}?openid=${openid}&access_token=${val}`,
            });
          } else {
            this.client = createClient({
              url:
                `${window.Global.WSS_ORIGIN}${window.Global.COPILOT_URL}?access_token=` +
                val,
            });

            if (this.canUseTaskAndQues) {
              this.getAllTasksList();
              this.getAllSkillsList();
            }
          }
        } else {
        }
      },
      immediate: true,
      deep: true,
    },
  },
  beforeDestroy() {
    this.removeSelectionTooltip();
    if (this.tooltipDebounce) {
      clearTimeout(this.tooltipDebounce);
    }
  },
  methods: {
    moment,
    toggleBStopic(){
      this.bstopicloading=false;
      this.bstopic=!this.bstopic;
      if(this.bstopic){
        this.$nextTick(()=>{
          const urlParams= new URLSearchParams(window.location.search);
          const parentViewId=urlParams.get("parentViewId");
          if(parentViewId&&(this.differentiated?.length)){
            this.$refs.differentiationref.showInitDiff(this.differentiated,parentViewId)
          }
        })
      }
    },
    // 收藏夹/黑板
    toggleCopyedtopic(){
      this.copytopic=!this.copytopic;
      this.$nextTick(()=>{
        if(this.copytopic){
          const urlParams= new URLSearchParams(window.location.search);
          let parentViewId = urlParams.get("parentViewId");
          if(parentViewId){
            this.$refs.copyreftopic.initCopyTopic(parentViewId,this.blackbord)
          }
        }
      })
    },
    //20250815 从对话页面返回到智能体编辑页面 
    // 待测试
    backToBot(){
      // 暂无需处理
      this.$router.push({ 
        path:'/brainstormingbot',
        query: {
          frombotchat:true
        }
      })
    },
    handleTextSelection(event) {
      // 移除之前可能存在的提示
      this.removeSelectionTooltip();
      
      const selection = window.getSelection();
      // 检查是否有选中内容且选中文本不为空
      if (!selection || selection.isCollapsed || !selection.toString().trim()) {
        return;
      }
      
      // 检查是否选中了我们的内容
      let target = event.target;
      let currentavater = this.getRobotIcon(event.target.closest('.chat_box'))
      while (target !== this.$refs.containercopy) {
        if (target.classList && target.classList.contains('contentsMarked')) {
          const selectedText = selection.toString();
          if (selectedText.trim()) {
            this.showSelectionTooltip(selection, selectedText, currentavater);
          }
          // this.showSelectionTooltip(selection,selectedText,currentavater);
          break;
        }
        target = target.parentNode;
      }
    },
    
    showSelectionTooltip(selection,selectedText,currentavater) {
      // 防抖处理，避免频繁操作
      if (this.tooltipDebounce) {
        clearTimeout(this.tooltipDebounce);
      }
      this.tooltipDebounce = setTimeout(()=>{

      
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      // 创建提示元素
      const tooltip = document.createElement('div');
      tooltip.className = 'selection-tooltip';
      tooltip.innerHTML = `<image class="ding" src="${this.avaterimgs.ding}">`; // 
      
      // 定位提示到选中区域的右上角
      tooltip.style.top = `${rect.top + window.scrollY - 30}px`;
      tooltip.style.left = `${rect.right + window.scrollX - 12}px`;
      
      // 点击提示复制文本
      tooltip.addEventListener('click', (e) => {
        e.stopPropagation();
        if(!selectedText){
          return
        }
        this.$message({
          type:"success",
          message:"已经为您复制到黑板"
        });
        this.preSaveCopyToBlackbord(selectedText,currentavater)
        this.removeSelectionTooltip();
      });
      
      document.body.appendChild(tooltip);
      this.currentTooltip = tooltip;
      
      // 点击其他地方时移除提示
      document.addEventListener('click', this.handleClickOutside);
      // 监听选区变化
      document.addEventListener('selectionchange', this.handleSelectionChange);

      },200)
    },
    handleSelectionChange() {
      const selection = window.getSelection();
      if (!selection || selection.isCollapsed || !selection.toString().trim()) {
        this.removeSelectionTooltip();
      }
    },
    removeSelectionTooltip() {
      if (this.currentTooltip) {
        this.currentTooltip.remove();
        this.currentTooltip = null;
        document.removeEventListener('click', this.handleClickOutside);
        document.removeEventListener('selectionchange', this.handleSelectionChange);
      }
    },
    
    handleClickOutside(event) {
      if (this.currentTooltip && !this.currentTooltip.contains(event.target)) {
        // 检查点击是否在选中的文本上
        const selection = window.getSelection();
        if (!selection.containsNode(event.target, true)) {
          this.removeSelectionTooltip();
        }
      }
    },
    getRobotIcon(chatBox) {
      console.log('chatBox',chatBox)
      const classList = Array.from(chatBox.classList);
      // 筛选出avater1到avater6的类名
      const avatarClasses = classList.filter(className => {
        return /^avater[1-6]$/.test(className);
      });

      return avatarClasses?.[0]?avatarClasses[0]:'';
    },
    // 复制内容到黑板
    handleCopy(event) {
      let currentavater = this.getRobotIcon(event.target.closest('.chat_box'))
      // 检查事件目标或其祖先是否有我们需要的class
      let target = event.target;
      while (target !== this.$refs.containercopy) {
        if (target.classList && target.classList.contains('contentsMarked')) {
          console.log('复制了具有 copyable-content 类的内容:', window.getSelection().toString());
          if(!window.getSelection().toString()){
            return
          }
          this.$message({
            type:"success",
            message:"已经为您复制到黑板"
          });
          this.preSaveCopyToBlackbord(window.getSelection().toString(),currentavater)
          break;
        }
        target = target.parentNode;
      }
    },
    preSaveCopyToBlackbord(txt,avatar){
      if(txt){
        this.toGetRoleData(()=>{
          if(!(this.viewdata?.blackbord)){
            this.viewdata.blackbord=[]
          }
          this.viewdata.blackbord.splice(0,0,{txt,avatar});
          this.toSaveViewData(this.viewdata,()=>{
            this.blackbord=this.viewdata.blackbord;
          })
        })
      }
    },
    toSaveViewData(viewdata,cb){
      const urlParams= new URLSearchParams(window.location.search);
      let parentViewId = urlParams.get("parentViewId");
      // 
      if(parentViewId){
        apolloProvider.clients.priceclient.mutate({
          mutation: queryStr.saveViewData,
          variables: {
            entity:{
              id:parentViewId,
              data:JSON.stringify(viewdata)
            }
          },
          client: "priceclient",
          fetchPolicy: "no-cache",
        }).then((res) => {
          if(res?.data?.updatePriceSessionView?.id){
            this.$refs?.copyreftopic.stopLoading()
            cb()
          }
        }).catch((e) => {});
      }
    },
    // 来自黑板的删除
    delCopy(delindex){
      let {blackbord,tags,userTypes,users,differentiated,summaryDimension,chatSessionId,summaryTopic}=this.viewdata
      let newblackbord=JSON.parse(JSON.stringify(blackbord))
      newblackbord.splice(delindex,1);
      let viewdata={
        tags,userTypes,users,differentiated,
        blackbord:newblackbord,summaryDimension,chatSessionId,summaryTopic,
      }
      this.toSaveViewData(viewdata,()=>{
        this.blackbord.splice(delindex,1)
      })
    },
    // 来自黑板的编辑
    editCopy(editindex,newitem,isnew){
      let {blackbord,tags,userTypes,users,differentiated,summaryDimension,chatSessionId,summaryTopic}=this.viewdata
      let newblackbord=JSON.parse(JSON.stringify(blackbord))
      // 
      if(isnew){
        newblackbord.splice(editindex,0,newitem);
      }else{
        newblackbord.splice(editindex,1,newitem);
      }
      let viewdata={
        blackbord:newblackbord,
        tags,userTypes,users,differentiated,
        summaryDimension,chatSessionId,summaryTopic,
      }
      this.toSaveViewData(viewdata,()=>{
        this.blackbord.splice(delindex,1)
      })
    },

    changedifferentiated(data){
      this.bstopicloading=false;
      console.log('changedifferentiated',data)
      if(!['done','then'].includes(data)){
        this.differentiated=data
      }
    },
    toStartTotal(type){
      this.dialogrestart=false;
      // 测试
      // this.bstopicloading=true;
      // return
      if(type==='start'){
        this.bstopicloading=true;
        this.$nextTick(()=>{
          this.getChatRecords(null,true,(resData,sessionId)=>{
            console.log('我是会话',resData)
            let hischat = resData.map((item)=>({role:item?.node?.sender?.role,text:item.node.text}))
            console.log('hischat',hischat)
            const urlParams= new URLSearchParams(window.location.search);
            const parentSessionId=urlParams.get("parentSessionId");
            const parentViewId=urlParams.get("parentViewId");
            console.log('parentViewId',parentViewId)
            console.log('parentSessionId',parentSessionId)
            console.log('sessionId',sessionId)
            console.log('parentViewId&&parentSessionId&&sessionId',parentViewId&&parentSessionId&&sessionId)
            if(parentViewId&&parentSessionId&&(this.differentiated||sessionId)){
              this.$refs.differentiationref.fromRobotVisit(this.differentiated,parentSessionId,parentViewId,sessionId,hischat,this.viewdata.summaryDimension)
            }
          })
          // 获取历史对话
        })
      }else if(type==='restart'){
        this.dialogrestart=true;
      }else if(type==='blackbord'){
        this.bstopicloading=true;
        let hischat = this.blackbord
        let sessionId=this.useConversation.name;
        const urlParams= new URLSearchParams(window.location.search);
        const parentSessionId=urlParams.get("parentSessionId");
        const parentViewId=urlParams.get("parentViewId");
        console.log('parentViewId',parentViewId)
        console.log('parentSessionId',parentSessionId)
        console.log('sessionId',sessionId)
        console.log('parentViewId&&parentSessionId&&sessionId',parentViewId&&parentSessionId&&sessionId)
        if(parentViewId&&parentSessionId&&(this.differentiated||sessionId)){
          this.$refs.differentiationref.fromRobotVisit(this.differentiated,parentSessionId,parentViewId,sessionId,hischat,this.viewdata.summaryDimension)
        }
      }
    },
    parseRobotAvatarClass(e){
      if(!e){
        return ''
      }else if(!e.includes('-')){
        let idx = this.commentsIdCollection.indexOf(e);
        let pStrId = this.commentsIdCollection[idx-1];
        let rstr = this.skillMsg[pStrId];
        let data = this.roleData;
        if(rstr){
          for(let dd of data){
            if(dd.userType.name == rstr){
              return dd.image
            }
          }
        }else{
          return ''
        }
      }else{
        let rstr = this.skillMsg[e];
        let data = this.roleData;
        if(rstr){
          for(let dd of data){
            if(dd.userType.name == rstr){
              return dd.image
            }
          }
        }else{
          return ''
        }
      }
    },
    parseRobotIcon(e){
      if(!e){
        return this.robotIcon
      }else if(!e.includes('-')){
        let idx = this.commentsIdCollection.indexOf(e);
        let pStrId = this.commentsIdCollection[idx-1];
        let rstr = this.skillMsg[pStrId];
        let data = this.roleData;
        let avatars = this.avaterimgs;
        if(rstr){
          for(let dd of data){
            if(dd.userType.name == rstr){
              return avatars[dd.image]
            }
          }

        }else{
          return this.robotIcon
        }

      }else{
        let rstr = this.skillMsg[e];
        let data = this.roleData;
        let avatars = this.avaterimgs;

        if(rstr){
          for(let dd of data){
          if(dd.userType.name == rstr){
            return avatars[dd.image]
          }
        }

        }else{
        return this.robotIcon
        }
      }
    },
    getAvaterImg(){
      this.avaterimgs.avater1=toSVGDataUrl(avater1)
      this.avaterimgs.avater2=toSVGDataUrl(avater2)
      this.avaterimgs.avater3=toSVGDataUrl(avater3)
      this.avaterimgs.avater4=toSVGDataUrl(avater4)
      this.avaterimgs.avater5=toSVGDataUrl(avater5)
      this.avaterimgs.avater6=toSVGDataUrl(avater6)
      this.avaterimgs.rollback=toSVGDataUrl(rollback)
      this.avaterimgs.ding=toSVGDataUrl(ding)
      
    },
    useRole(role){
      let obj = Object.assign({}, this.skillTarget);
      this.useTask(Object.assign(obj, {
		      "text": role.userType.name,
        }))

    },
    getLoginInfo(){
            apolloProvider.clients.iam.query({
                client: 'iam',
                query: gql`query me {
                    me{
                        id
                        name
                        username
                        cardNo
                        phone
                        email
                        openid
                        avatar {
                          avatarUrl
                        }
                    }
                }`,
                fetchPolicy: "no-cache",
            }).then(res => {
                this.userInfo = res.data.me;
                this.position = res.data.me;
            }).catch(error => {
                console.log(error);
            })

    },
    toSetTree() {
      this.getChatRecords(null, true);
      this.$emit("updateRobotCenter", { key: "openPostIframe", val: "iframe" });
      if (!this.isMobile()) {
        this.$emit("updateRobotCenter", { key: "toggleMobile", val: true });
      }
      this.$emit("updateRobotCenter", { key: "toolIframe", val: "out-normal" });
      this.$emit("updateRobotCenter", { key: "showSkillTool", val: true });
      this.$emit("updateRobotCenter", { key: "showSkillToolBool", val: true });
      this.$nextTick(() => {
        this.initBoxHeight();
      });
    },
    logOut() {
      this.$store.dispatch("oidcStore/signOutOidc");
    },
    sendParentMsg(type, data) {
      // 发送消息给父窗口
      const message = {
        eventname: type, // 消息类型标识
        data,
      };

      // 关键代码：发送消息
      window.parent.postMessage(message, "*");
    },
    sendParentMessage(value) {
      // 发送消息给父窗口
      this.sendParentMsg("customPost", { value, timestamp: Date.now() });
    },
    sendPostIframStatus(status) {
      setTimeout(() => {
        console.log("-----------333", status, this.lastSkill.text);

        if (location.href.includes("sendMsg=true")) {
          this.sendParentMsg("brainstormingbot", {
            toolname: this.lastSkill.text,
            status,
          });
        } else {
          const iframeTarget = document.getElementById("postIframeTarget");
          if (iframeTarget) {
            // 向子页面发送消息
            iframeTarget.contentWindow.postMessage(
              {
                eventname: "parentToChild",
                data: { toolname: this.lastSkill.text, status },
              },
              "*"
            );
          }
        }
      }, 240);
    },
    skillsFilterVary(it) {
      return this.nowSkillsAll.filter((t) => (t.categories || "").includes(it));
    },
    initBoxHeight() {
      let queVaryCubeH = document.getElementsByClassName("chat_txt");
      let welCube = document.querySelectorAll(".V2-welcome.welStr");

      if (welCube && welCube[0] && welCube[0].offsetHeight > 32) {
        this.welCubeHeight = welCube[0].offsetHeight - 32;
      }
      if (queVaryCubeH.length) {
        let realHeight = 0;
        for (let a of queVaryCubeH) {
          if (a.offsetHeight !== 0) {
            realHeight = a.offsetHeight;
          }
        }
        // 一行的时候是46
        let heightQue = realHeight;
        // let initHeight = this.isMobile() ? 154 : 204;
        let h11 = 50;
        if (this.noActionBox) {
          h11 = 0;
        }

        // console.log(heightQue, h11, '----------高度问题')

        this.otherCubeHeight = heightQue + h11 + 14;
      }
    },
    toggleThinkingBtn(str) {
      if (this.toggleThinking[str]) {
        this.toggleThinking[str] = false;
      } else {
        setFunc(this.toggleThinking, str, true);
      }
    },
    getWxKeys() {
      apolloProvider.clients.iam
        .query({
          query: queryStr.wxkeys,
          variables: {},
          client: "iam",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.corpId = res.data.workSettings.corpId;
          this.corpAppAgentId = res.data.workSettings.corpAppAgentId;
        })
        .catch((e) => {});
    },
    setSign() {
      let self = this;
      if (!self.corpId) {
        if (self.siteInfo.openWXSDK && !self.sdkMsg) {
          self.sdkMsg = true;
          self.$message.info("当前企业暂无配置企业微信");
        }
      } else {
        this.getSignatureWx().then((res) => {
          ww.register({
            corpId: self.corpId,
            agentId: self.corpAppAgentId,
            jsApiList: [
              "selectExternalContact",
              "getContext",
              "startRecord",
              "stopRecord",
              "translateVoice",
            ],
            getConfigSignature() {
              // return ww.getSignature('HoagFKDcsGMVCIY2vOjf9k7iEXyBUEuR74W91oy0aDHLl2VYqE-gBGas51NHpDXEl_eckOJ4IrAgmv4Qz85ccQ');
              return ww.getSignature(res.config);
            },
            getAgentConfigSignature() {
              return ww.getSignature(res.agent);
              // return ww.getSignature(res.agent);
            },
          });
        });
      }
    },
    getSignatureWx() {
      return new Promise((resolve, reject) => {
        let time = new Date().getTime();

        if (this.signs.config && this.signs.time - time < 360000) {
          resolve(this.signs);
        } else {
          apolloProvider.clients.iam
            .query({
              query: queryStr.signs,
              variables: {
                configName: window.Global.SSO_KEY,
                url: location.href.split("#")[0],
              },
              client: "iam",
              fetchPolicy: "no-cache",
            })
            .then((res) => {
              let signs = res.data.jssdkSignature;

              this.signs.config = signs.configSignature;
              this.signs.agent = signs.agentConfigSignature;
              this.signs.time = new Date().getTime();

              resolve(this.signs);
            })
            .catch((e) => {});
        }
      });
    },
    showCards(e) {
      let record = typeof e === "object" ? e : JSON.parse(e);

      if (record.doc.uri) {
        window.open(record.doc.uri);
      } else {
        if (record.doc.content) {
          const text = record.doc.content;
          const kind = record.doc.kind;
          const dataBlob = new Blob([text], { type: "text/plain" });
          let name = record.doc.text;
          var testmsg = name.substring(name.lastIndexOf(".") + 1);
          if (testmsg) {
            name += "." + (kind ? kind : "txt");
          }
          saveAs(dataBlob, name);
        }
      }
    },
    getUrlParam(url, name) {
      if (!url) {
        return "";
      }
      var urlArr = url.split("?");
      if (urlArr.length > 1 && urlArr[1].indexOf(name) > -1) {
        var query = urlArr[1];
        var obj = {};
        var arr = query.split("&");
        for (var i = 0; i < arr.length; i++) {
          arr[i] = arr[i].split("=");
          obj[arr[i][0]] = arr[i][1];
        }
        return obj[name];
      } else {
        return "";
      }
    },
    getPuredRobotTxt(str) {
      // const result = HtmlPurifier.purify(content);
      // return result;
      // if(str.indexOf('<') !== -1){
      //   console.log(str, '--------------', str.indexOf('<') !== -1, '====',this.getMarked(str))
      // }
      // return str
      // .indexOf('```') > -1 ? str : str.replaceAll("<", "&lt;").replaceAll(">", "&gt;")
      // .replace(
      //   /[<>]/g,
      //   (tag) =>
      //     ({
      //       "<": "&lt;",
      //       ">": "&gt;",
      //     }[tag])
      // );

      return str.replace(
        /[&<>"']/g,
        (tag) =>
          ({
            "&": "&amp;",
            "<": "&lt;",
            ">": "&gt;",
            '"': "&quot;",
            "'": "&#39;",
          }[tag])
      );
      return str;
    },
    getPured(html, target) {
      //防注入式攻击

      const doc = DOMPurify.sanitize(html);
      let txt = this.getPuredRobotTxt(doc || "");
      let txtB = this.getParsed(txt);
      if(target == 'to'){
        return this.parseRole(txtB, true)
      }else if(target == 'from'){
        return this.parseRole(txtB, false);
      }else{
        return txtB;
      }
    },
    parseRole(str, bool) {
      // let data = this.roleData;
      if(bool){
        return `##岗位名称：${this.nowSkill.text};父级会话id：${this.parentSessionId}##${str}`
      }else{
        const input = str;
        let res = this.parseRoleArr(input);
        return res[1] || input;
      }

    },
    parseRoleArr(str) {
        const input = str;
        const regex = /##岗位名称：([^;]+);父级会话id：([^#]+)##(.+)/;
        const match = input.match(regex);

        console.log(match, '--------')
        if (match) {
          //技能名称
          const cfo = match[1];        // CFO
          const sessionId = match[2];  // 12344444
          const statement = match[3];  // 我想要测试一下螺丝刀性能
          return [cfo, statement];
        }else{
          return ['', '']
        }
      

    },
    getParsed(str) {
      return str
        .replaceAll("&amp;lt;", "&lt;")
        .replaceAll("&amp;gt;", "&gt;")
        .replaceAll("&amp;quot;", " &quot;")
        .replaceAll("&amp;#39;", " &#39;");
    },
    hiddenReason(it) {
      setFunc(this.reasonHidden, it, this.reasonHidden[it] ? false : true);
    },
    isShowThinking(it) {
      setFunc(this.thinkingShow, it, this.thinkingShow[it] ? false : true);
    },
    getUri(uri, preview) {
      if (!preview) {
        return uri;
      } else {
        if (uri.includes("?")) {
          return uri + "&preview=true";
        } else {
          return uri + "?preview=true";
        }
      }
    },
    getAllSkillsList() {
      return new Promise((resolve, reject) => {
        if (this.nowSkillsAll.length && this.nowSkillIds.length) {
          resolve(true);
        } else {
          this.getSimilarSkills(true).then((res) => {
            // console.log(res.flat(2))
            let dat = res;
            this.nowSkillsAll = dat;
            resolve(true);
          });
        }
      });
    },
    getAllTasksList() {
      return new Promise((resolve, reject) => {
        if (this.nowTasksAll.length && this.nowTaskIds.length) {
          resolve(true);
        } else {
          this.getSimilarTasks(true).then((res) => {
            // console.log(res.flat(2))
            let dat = res.sort(function (a, b) {
              return b.distance - a.distance;
            });
            this.nowTasksAll = dat;
            this.nowTaskIds = dat.map((t) => t.name);

            resolve(true);
          });
        }
      });
    },
    toggleAllTasksList() {
      this.clickedMagic = true;
      this.firstSendTask = false;
      this.tasksPopShow = true;
      this.getAllTasksList().then((res) => {
        this.nowTaskIdx = -1;
        this.tasksAndQuesPopFilter = this.nowTasksAll;
      });

      // console.log((this.nowTxt || this.clickedMagic),!this.similarLoading,
      //   this.tasksAndQuesPopFilter.length,
      //   !this.stopPopSimilarQue)
    },
    toggleAllTasksShow() {
      this.clickedMagic = false;
      this.firstSendTask = false;
      this.getAllTasksList();
      // this.clickedMagic = true;
      this.nowTask = {
        commands: ["智能任务"],
        names: [],
        name: "allTask",
      };
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.commands[0].indexOf(queryString.toLowerCase()) === 0;
      };
    },
    getNowTxtHighLight(txt) {
      let txtSplit = txt.split("");
      let highSplit = this.nowTxt.split("");
      txtSplit = txtSplit.map((t) => {
        if (highSplit.includes(t)) {
          return `<span style="color: var(--theme);">${t}</span>`;
        } else {
          return t;
        }
      });
      return txtSplit.join("");
    },
    useTheQue(que, et) {
      let self = this;
      // if(self.similarQueShow && self.nowTaskIdx !== -1){
      self.nowTxt = que.question;
      self.stopPopSimilarQue = true;
      self.tasksAndQuesPopFilter = [];
      self.$refs["questionRef"].focus();

      if (et) {
        self.maybeAsk = true;
      }

      // }
    },
    clearTipQues() {
      this.stopPopSimilarQue = true;
      this.nowTaskIdx = -1;
      this.$refs["questionRef"] ? this.$refs["questionRef"].focus() : "";
    },
    taskSearch(queryString, cb) {
      var restaurants = this.tasksPop;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    clearTaskAll() {
      this.nowTask = {};
      this.nowTaskIdx = -1;
      this.clickedMagic = false;
      this.firstSendTask = false;
      this.tasksPopShow = false;
      this.taskChoosed = {};
    },
    clearTask() {
      // this.nowTask = {};
      // this.nowTaskIdx = -1;
      this.clickedMagic = false;
      this.firstSendTask = false;
      this.tasksPopShow = false;
      // this.taskChoosed = {};
    },
    clearTaskChoosed() {
      this.taskChoosed = {};
    },
    clearTaskAndSkillChoosed() {
      this.taskChoosed = {};
      this.nowSkill = {};
      this.nowTxt = "";
      this.useEditor = false;
    },
    clearTxt() {
      this.nowTxt = "";
    },
    useSkill(tk, name) {
      setFunc(this.cates, name, false);
      this.clearTxt();
      this.clearTaskChoosed();
      this.useTask(tk);
    },
    parseWelcome() {
      const parser = new DOMParser();
      let str = parser.parseFromString(
        "<h3>" + this.welcomeStr + "</h3>",
        "application/xml"
      );
      let arrStr = [];
      let nodesShow = str.all[0].childNodes;
      for (let nod of nodesShow) {
        let val =
          nod.tagName === "markdown"
            ? this.getMarked(nod.textContent)
            : nod.tagName
            ? nod.outerHTML
            : nod.textContent;
        arrStr.push(val);
      }

      // console.log('解析过')
      return arrStr.join("");
    },
    parseNowTxt(txt) {
      let text = txt;
      const regex = /\{([^{}]+)\}/g;
      const matches = text.match(regex);

      if (matches && matches.length) {
        this.useEditor = true;
        for (let aput of matches) {
          text = text.replaceAll(
            aput,
            `<input type="text" style="width:${
              aput.slice(1, -1).length + 1
            }em;" class="promp-input" placeholder="${aput.slice(1, -1)}" />`
          );
        }
      } else {
        this.useEditor = false;
      }

      this.nowTxt = text;
    },
    useTask(tk, stopPostNowTxt) {
      if (tk.commands) {
        // this.nowTaskIdx = -1;

        // this.clickedMagic = true;
        // this.nowTask.name = tk.name
        // }
        // this.nowTask = tk;
        this.toggleAllTasksShow();
        this.firstSendTask = false;
        this.taskChoosed = tk;
        // .commands[0];

        // console.log(this.$refs, this.nowTaskIdx)
        this.$refs["questionRef"] ? this.$refs["questionRef"].focus() : "";
      } else if (tk.question) {
        this.useTheQue(tk);
      } else {
        this.nowSkill = tk;

        if (
          tk.prompt &&
          (!stopPostNowTxt || typeof stopPostNowTxt === "number")
        ) {
          this.parseNowTxt(tk.prompt);
        }

        if (!tk.prompt) {
          this.useEditor = false;
        }

        if (tk.task) {
          let fTask = this.nowTasksAll.filter((t) => t.name == tk.task);

          if (fTask.length) {
            this.useTask(fTask[0]);
          }
        }
      }

      this.allTasksShow = false;

      setTimeout(() => {
        this.nowTxt = this.nowTxt.replace("@", "").replaceAll("\n", "");
      }, 100);
    },
    getSimilars: _.debounce(function () {
      const callback=()=>{
        this.tasksAndQuesPopFilter = this.roleData.map((t)=>{
            let obj = Object.assign({}, this.skillTarget);
            return Object.assign(obj, {
              "text": t.userType.name,
              tags:t.tags
            })
          });
        this.similarLoading = false;
      }
      if(this.roleData?.length){
        callback()
      }else{
        this.toGetRoleData(()=>{
          callback()
        })
      }
      

      
    }, 500),
    toGetRoleData:function (cb) {
      const urlParams= new URLSearchParams(window.location.search);
      let parentViewId = urlParams.get("parentViewId");
      apolloProvider.clients.priceclient.query({
          query: queryStr.getroleData,
          variables: {
           viewId:parentViewId
          },
          client: "priceclient",
          fetchPolicy: "no-cache",
        }).then((res) => {
          if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            this.viewdata=chartdatas;
            if(chartdatas.differentiated){
              this.differentiated=chartdatas.differentiated
            }
            if(chartdatas.blackbord){
              this.blackbord=chartdatas.blackbord
            }
            if(!chartdatas.summaryDimension){
              this.viewdata.summaryDimension={}
            }else if(typeof chartdatas.summaryDimension==='string'){
              try{
                this.viewdata.summaryDimension=JSON.parse(chartdatas.summaryDimension)
              }catch(e){
                this.viewdata.summaryDimension={}
              }
            }else if(chartdatas.summaryDimension?.constructor?.name==='Array'){//如果为数组，则
              this.viewdata.summaryDimension={}
            }
            if(chartdatas.users){
              this.roleData=chartdatas.users;
              if(cb){
                cb()
              }
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
        }).catch((e) => {});
    },
    getSimilarQues: function () {
      return new Promise((resolve, reject) => {
        let keyword = this.nowTxt.replaceAll("\n", "");
        // let keyword = this.nowTxt.startsWith('@') ? this.nowTxt.slice(1) : this.nowTxt;
        if (
          keyword &&
          keyword.length >= 1 &&
          !this.nowTxt.startsWith("@") &&
          this.nowTask.name !== "allTask"
        ) {
          apolloProvider.clients.copilotAuth
            .query({
              query: queryStr.similarQuestions,
              variables: {
                copilot: this.copilotId,
                keyword,
              },
              client: "copilotAuth",
              fetchPolicy: "no-cache",
            })
            .then((res) => {
              this.similarQues = res.data.userKnowledges.edges.map(
                (t) => t.node.faq
              );

              resolve(this.similarQues);
              this.similarLoading = false;
              // this.nowTaskIdx = -1;
            })
            .finally((e) => {
              this.similarLoading = false;
            });
        } else {
          resolve([]);
        }
      });
    },
    // , 600),
    getSimilarSkills: function (e) {
      return new Promise((resolve, reject) => {
        let keyword = this.nowTxt.replaceAll("\n", "");
        keyword = keyword.startsWith("@") ? keyword.slice(1) : keyword;
        // if(keyword && keyword.length >= 1){
        if (e) {
          //请求所有ids
          keyword = "";
        }
        apolloProvider.clients.copilotBus
          .query({
            query: queryStr.skills,
            variables: {
              copilot: this.copilotId,
              offset: 0,
              first: 999,
              keyword,
            },
            client: "copilotBus",
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            let data = res.data.userSkills.edges.map((t) => t.node);
            this.skills = data;

            this.skillTarget = data.filter(t=>t.text === this.skillTargetTxt)[0];

            // console.log(this.skillTarget, '----------')
            resolve(data);
          });
      });
    },
    getSimilarTasks: function (e) {
      return new Promise((resolve, reject) => {
        let keyword = this.nowTxt.replaceAll("\n", "");
        keyword = keyword.startsWith("@") ? keyword.slice(1) : keyword;
        // if(keyword && keyword.length >= 1){
        if (e) {
          //请求所有ids
          keyword = "";
        }
        apolloProvider.clients.copilotAuth
          .query({
            query: queryStr.similarTasks,
            variables: {
              copilot: this.copilotId,
              keyword,
            },
            client: "copilotAuth",
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            this.tasksPopFilter = res.data.userTasks.edges.map((t) => t.node);

            resolve(this.tasksPopFilter);
            this.similarLoading = false;
          });

        // }else{
        //   resolve([])
        // }
      });
    },
    // 600),

    fitNewChat() {
      //关于保持新会话窗口不出现闪动
      this.initCopilotsCase();
      this.getChatRecords();
    },
    changeAnswer(it, e) {
      if (this.isChatting || this.loading[it]) {
        this.$message.info("正在生成内容~请稍后~");
        return false;
      }
      let allLen = this.allPager[it].length;

      if (e && this.currentPager[it] !== allLen) {
        this.currentPager[it] = this.currentPager[it] + 1;
      } else if (!e && this.currentPager[it] !== 1) {
        this.currentPager[it] = this.currentPager[it] - 1;
      }
      let idx = this.currentPager[it] - 1;
      if (this.allPager[it][idx]) {
        this.comments[it] = this.getMarked(this.allPager[it][idx].text);
        this.commentsTxt[it] = this.allPager[it][idx].text;
      }

      let len = this.commentsIdCollection.length;
      let lastId = this.commentsIdCollection[len - 1];
      if (lastId === it) {
        this.chatToBottom();
      }
    },
    checkReNameChat: _.debounce(function (node) {
      if (!this.useConversation || !this.useConversation.name) {
        this.$emit("reNameChat", node);
      }
    }, 200),
    isMobile() {
      var isMobile = false;
      var flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      if (flag != null) {
        isMobile = true;
      }
      return isMobile;
    },
    toSetVary(e) {
      if (this.queVary == e) {
        this.queVary = null;
      } else {
        this.queVary = e;
      }
    },
    initVaries() {
      let id = this.copilotId;
      apolloProvider.clients.copilotBus
        .query({
          query: queryStr.queCategory,
          variables: {
            copilot: id,
          },
          client: "copilotBus",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.queVaries = res.data.categories.filter((t) => t.filterable);
        });
    },
    isUnLikeCollection(it) {
      // includes
      // // console.log(it)
      // // let ikey = it.split("__")[1];
      // let bool1 = this.likeStatus[it] == 0

      // // console.log(this.isUnLikeIds.includes(it), this.isChattingRecord(it));
      return this.isUnLikeIds.includes(it) && this.isChattingRecord(it);
    },
    setLikeStatus(id, bool) {
      let idx = this.isUnLikeIds.indexOf(id);
      if (bool == 0) {
        if (idx === -1) {
          this.isUnLikeIds.push(id);
        }
      } else {
        if (idx !== -1) {
          this.isUnLikeIds.splice(idx, 1);
        }
      }
    },
    isChattingRecord(it) {
      let itKey = it.split("__")[1];
      let ids = this.commentsIdCollection;
      //聊天记录和正在生成的记录的区别就是id一样还是不一样
      if (ids.includes("r__" + itKey) && ids.includes("p__" + itKey)) {
        return true;
      } else {
        return false;
      }
    },
    isLastId(it) {
      if (this.commentsIdCollection.length >= 2) {
        let len = this.commentsIdCollection.length;
        // console.log(this.commentsIdCollection, it, '----')
        let lastIdU = this.commentsIdCollection[len - 2];
        let lastId = this.commentsIdCollection[len - 1];
        if (lastId === it) {
          // return
          // if (lastId.split("__")[1] !== lastIdU.split("__")[1]) {
          if (lastId.indexOf("-") === -1) {
            return false;
          } else {
            return true;
          }
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    addBodyClass() {
      sessionStorage.setItem("copilotId", this.copilotId);

      window.open(
        location.origin + this.$router.resolve("airobots").href,
        // this.$router.resolve("airobots").href,
        "_blank"
      );

      // this.isFull = !this.isFull;
      // document.body.className ? document.body.className = '' : document.body.className = 'miniable'
    },
    getGenerate(e) {
      let msg = this.getMessagesPrompt();
      this.client.subscribe(
        {
          query: `subscription generate($prompt: String!,
              $copilot:  ID!, $data: String!){
              generate(
                prompt: $prompt,
                copilot: $copilot,
                data: $data
                ) {
                  conversation {
                    name
                    text
                  }
                  choices {
                  message {
                  text
                  role
                }
                }
              }
            }`,
          variables: {
            prompt: "2cd28170-ca3c-40e4-9207-9031b9a3a6ed",
            copilot: this.copilotId,
            data: msg,
          },
        },
        {
          next: (data) => {
            let nowTxt = data.data.generate.choices[0].message.text;

            this.nowTxt = nowTxt;
            // .split("核心问题：")[1];
            // // console.log(nowTxt, '-----')
          },
          error: (error) => console.error(error),
          complete: () => console.log("Completed"),
        }
      );
    },
    getRobotInfo() {
      this.copilotLoad = true;
      apolloProvider.clients.copilotAuth
        .query({
          query: queryStr.robotInfo,
          variables: {
            name: this.copilotId,
          },
          client: "copilotAuth",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let newData = Object.assign({}, res.data);
          this.copilotInfo = newData.copilot;

          if (typeof newData.copilot.source == "string") {
            // this.copilotInfo.source = json2yaml.load(newData.copilot.source);
            this.copilotInfo.source = yaml.load(newData.copilot.source);
          }

          // this.$store.commit('changerobotInfo', this.copilotInfo)

          // EventBus.$emit("changeHeader", this.copilotInfo.source);

          this.copilotLoad = false;

          // console.log(this.copilotInfo.source, '-----source')

          this.tasksPop = this.copilotInfo.source.spec.copilot.tasks;

          let modelChooses =
            this.copilotInfo.source.spec.copilot.backends.filter(
              (t) => t.kind == "llm"
            );

          let modelAllChooses = this.copilotInfo.source.spec.copilot.backends;

          // console.log(JSON.stringify(modelAllChooses), modelAllChooses[0] , modelAllChooses[0].text , modelAllChooses[0].text.startsWith('lke'), '----------')

          if (modelChooses && modelChooses.length > 1) {
            this.modelChooses = modelChooses;
            this.modelUse = modelChooses[0].name;
            this.modelUseTxt = modelChooses[0].text;
          }

          if (
            modelAllChooses[0] &&
            modelAllChooses[0].text &&
            modelAllChooses[0].text.startsWith("lke")
          ) {
            this.isLkeModel = true;
          }

          if (!this.siteInfo.systemName) {
            document.title = this.copilotInfo.source.spec.copilot.text;
          }
          this.cantAnswer =
            this.copilotInfo.source.spec.copilot.knowledge.reply.defaultValue;
        })
        .catch((e) => {
          console.log(e, "-----------");
          // if (JSON.stringify(e).includes("copilot")) {
          this.hasCopilotId = false;
          // }
        });
    },
    setQue(it) {
      this.nowTxt = it;
    },

    okuseUserChatting(it) {
      this.getGenerate();
      this.useUserChatting();
    },
    useUserChatting() {
      this.isRobot = !this.isRobot;
      if (!this.isRobot) {
        this.chatToBottomUser();
        this.resetTimer(null);
      } else {
        this.resetTimer(true);
        this.chatToBottom(5);
      }
    },
    initNewId() {
      return new Array(8)
        .fill(null)
        .map((t) => Math.random().toString().slice(3, 6))
        .join("-");
    },

    changeModel(e) {
      console.log(e, "---------");
      let eArr = e.split("***");
      this.modelUse = eArr[0];
      this.modelUseTxt = eArr[1];
      // if(this.hasRecords){
      //   this.newComment('已为您切换到'+eArr[1]);
      // }

      if (this.modelUseTxt.startsWith("lke")) {
        this.isLkeModel = true;
      } else {
        this.isLkeModel = false;
      }
    },
    getOrigin(href) {
      try {
        let robotUrlObj = new URL(href);
        return robotUrlObj.origin;
      } catch (e) {
        return location.origin;
      }
    },
    clearLkeComments() {
      return new Promise((resolve, reject) => {
        if (this.isLkeModel) {
          let originL = this.getOrigin(window.Global.COPILOT_URL);
          this.$axios
            .post(
              originL +
                "/copilot-api/invoke?access_token=" +
                this.token +
                "&copilotId=" +
                this.copilotId,
              {
                regionId: 1,
                serviceType: "lke",
                cmd: "ResetSession",
                data: {
                  SessionId: this.useConversation.name,
                },
              }
            )
            .then((e) => {
              resolve(e.newSessionId);
            })
            .catch((e) => {
              reject();
            });
        } else {
          resolve(true);
        }
      });
    },
    handleCommand(e) {
      if (e == "point") {
        this.clearLkeComments()
          .then((res) => {
            if (this.isLkeModel) {
              this.$emit("updateConversation", res);
            }
            this.clearComments();
          })
          .catch((e) => {
            this.newComment(this.errorMsg);
          });
      }

      if (e == "logout") {
        this.logOut();
      }

      if (e == "all") {
        let openid = this.initNewId();
        localStorage.setItem("thisOpenid", openid);
        this.initCopilotsCase();
      }

      if (e == "chatClean") {
        this.cleanConversation();
      }
    },
    cleanConversation() {
      // clearMessages
      apolloProvider.clients.copilotAuth
        .mutate({
          mutation: queryStr.clearMessages,
          variables: {
            conversation: this.useConversation.name,
          },
          client: "copilotAuth",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.$message.success("成功清空会话~");
          this.initCopilotsCase();

          if (this.isLkeModel) {
            this.clearLkeComments().then((res) => {
              this.$emit("updateConversation", res);
            });
          }
        });
    },
    initCopilotsCase() {
      this.pageNum = -1;
      this.answer_tools = [];
      this.idStorage = {};
      this.likeStatus = {};
      this.answer_source = {};
      this.answer_link = {};
      this.timeStamp = {};

      this.skillMsg = {};
      this.robotSkillMsg = {};

      this.leftChatNums = false;
      //有聊天记录，默认没有
      this.hasRecords = false;

      this.hasCopilotId = true;

      this.comments = {};
      this.commentsTxt = {};
      this.commentsIdCollection = [];

      this.totalCount = 0;

      this.commentsUser = {};
      this.commentsUserIds = [];
      this.leftUserChatNums = true;
      this.getUserChatRecordsLoading = true;
      this.firstLoading = 1;
      this.userAnswerCollect = {};
      this.hasAnswerChanged = false;
      this.myQueStartCursor = null;
      this.uChatTotalCount = 0;
      this.isRobot = true;
    },
    clearComments() {
      let len = this.commentsIdCollection.length;
      this.breakPoint = this.commentsIdCollection[len - 1];
      this.$message.success("已清除上下文关联，您可以重新提问~");
      if (this.isLkeModel) {
        this.$emit("updateConversation");
      }
    },
    getChatRecords(LastRecordId, seqAll,cb) {
      console.log('111')
      if (
        (this.pageNum * 20 < this.totalCount &&
          ((this.useConversation &&
            this.useConversation.name &&
            this.useConversation.name == this.nowConversationId) ||
            this.anonymity)) ||
        seqAll
      ) {
        console.log("请求了000");

        let offset = this.pageSize * this.pageNum;
        let first = 20;

        if (seqAll) {
          offset = 0;
          first = 9999;
        }

        let openid = this.openid;
        this.pageNum = this.pageNum + 1;
        // this.copilotRecordLoading = true;
        let nowStrWs = "messagesHasConversation";
        let variables = {
          copilot: this.copilotId,
          first,
          offset,
          filter: {
            conversation: {
              eq: this.useConversation ? this.useConversation.name : null,
            },
          },
        };

        let clientUse = "copilotAuth";

        if (this.anonymity) {
          variables.filter = { openid: { eq: openid } };
          // variables.userId = this.openid;
          clientUse = "copilot";
        }

        // else{
        if (this.siteInfo.openThoughtSteps) {
          nowStrWs = "messagesHasConversationAndThoughts";
        }
        // }

        apolloProvider.clients[clientUse]
          .query({
            query: queryStr[nowStrWs],
            variables,
            // userId: this.openid,
            client: clientUse,
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            let ids = [];
            let msgkey = "userMessages";
            // this.anonymity ?
            // 'messages' :
            // let LastRecordId = this.commentsIdCollection[0]
            this.totalCount = res.data[msgkey].totalCount;

            if (this.totalCount == 0) {
              this.hasRecords = false;
            }
            //聊天记录是倒的
            let resData = res.data[msgkey].edges.reverse();
            // 20250717
            if(cb){
              cb(resData,this.useConversation.name)
              return
            }
            if (resData.length) {
              this.hasRecords = true;
              this.leftChatNums = true;

              resData.length == this.totalCount
                ? (this.leftChatNums = false)
                : "";

              let lastTime = 0;

              for (let t of resData) {
                // ids.indexOf
                // this.commentsIdCollection.indexOf(idStr) === -1 && this.commentsIdCollection.push(idStr);

                let im = [
                  t.node.sender.role,
                  t.node.text,
                  t.node.name,
                  t.node.sent,
                  t.node.retries,
                  t.node.samples,
                  t.node.reasoning,
                  t.node.thoughtChain,
                  t.node.skill,
                ];

                let idKey = im[0] == "user" ? "p__" + im[2] : "r__" + im[2];
                if (im[3] - lastTime > 1000 * 60 * 30 && im[0] == "user") {
                  this.timeStamp[idKey] = moment(im[3]).format(
                    "YYYY-MM-DD HH:mm"
                  );
                  lastTime = im[3];
                }

                //  t..nodeRelatedRecordId || t.RecordId,

                if(im[0] == "user"){
                  const imPured = im[1];
                  let res = this.parseRoleArr(imPured);
                  // return res[1] || input;
                  setFunc(
                  this.comments,
                    "p__" + im[2],
                    (res[1] || imPured)
                  );
                }else{
                  setFunc(
                  this.comments,
                    "r__" + im[2],
                    this.getMarked(im[1])
                  );
                }
                

                setFunc(
                  this.commentsTxt,
                  im[0] == "user" ? "p__" + im[2] : "r__" + im[2],
                  im[1]
                );

                //记录所有聊天记录的时间
                setFunc(
                  this.lastTimeCollection,
                  im[0] == "user" ? "p__" + im[2] : "r__" + im[2],
                  im[3]
                );

                let idStr = im[0] == "user" ? "p__" + im[2] : "r__" + im[2];

                this.commentsIdCollection.indexOf(idStr) === -1 &&
                  ids.push(idStr);

                if (im[0] !== "user") {
                  this.answer_tools.push(im[2].toString());
                  setFunc(
                    this.likeStatus,
                    idStr,
                    t.node.feedback ? t.node.feedback.type : null
                  );
                  setFunc(this.idStorage, "r__" + im[2], im[2]);
                }

                // feedbackType

                // this.commentsIdsLastNext.indexOf(idStr) === -1 &&
                //   ids.push(idStr);

                if (im[4] && im[4].length) {
                  //记录所有重试的数据
                  let allAns = [t.node].concat(im[4]);
                  setFunc(
                    this.allPager,
                    im[0] == "user" ? "p__" + im[2] : "r__" + im[2],
                    allAns
                  );

                  setFunc(
                    this.currentPager,
                    im[0] == "user" ? "p__" + im[2] : "r__" + im[2],
                    1
                  );
                }

                // 记录所有来源和相关链接
                if (im[5] && im[5].length) {
                  // t.node.samples
                  this.showSourceAndLinks(im[2], im[5]);
                }

                if (im[6] && im[6].content) {
                  setFunc(
                    this.reasonTxt,
                    im[0] == "user" ? "p__" + im[2] : "r__" + im[2],
                    im[6].content
                  );
                  setFunc(
                    this.reasonTime,
                    im[0] == "user" ? "p__" + im[2] : "r__" + im[2],
                    parseFloat(im[6].duration / 1000).toFixed(0)
                  );
                }

                //思维链
                if (
                  im[7] &&
                  im[7].items &&
                  im[7].items.length &&
                  im[0] !== "user"
                ) {
                  setFunc(this.thoughtTxt, "r__" + im[2], im[7].items);
                }

                // 技能单独回显
                if (im[8] && im[8].text) {

                  if (im[0] !== "user") {
                    setFunc(this.robotSkillMsg, "r__" + im[2], im[8].text);
                    if (im[1].includes("white-board")) {
                      setFunc(
                        this.robotUsedSkillMsg,
                        "r__" + im[2],
                        im[8].text
                      );
                    }
                  }else{
                    
                    const imPured = im[1];
                    let res = this.parseRoleArr(imPured);

                    // console.log(res, '---resmmmm----')
                    setFunc(
                      this.skillMsg,
                      "p__" + im[2],
                      (res[0] || im[8].text)
                    );
                  }
                }
              }

              this.lastTime = lastTime;

              this.commentsIdCollection = [
                ...ids,
                ...this.commentsIdCollection,
              ];

              setTimeout(() => {
                this.initOtherMd();
              }, 30);

              LastRecordId ? this.chatToId(LastRecordId) : this.chatToBottom();
            } else {
              this.leftChatNums = false;
              // if (!LastRecordId) {
              // 无条件请求下，没有聊天记录则做一个标记

              // }
            }

            this.copilotRecordLoading = false;
            this.transformMath();
            this.scalePic();
          })
          .catch((e) => {
            this.bstopicloading=false;
          });
      }
    },
    findSeq(ikey, action) {
      let id = ikey.split("__")[1];
      let que = "p__" + id;
      if (action && action == "clear") {
        return this.comments[que];
      } else {
        let name = this.queNameToId[ikey];
        let thisRobotId = null;
        // console.log(name, this.comments[que], que, this.queNameToId)
        for (const [ikey, ivalue] of Object.entries(this.queNameToId)) {
          if (ivalue === name) {
            thisRobotId = ikey;
          }
        }
        // console.log(thisRobotId, this.commentsTxt)
        if (!this.allPager[thisRobotId]) {
          //   this.currentPager[thisRobotId] = this.currentPager[thisRobotId] + 1
          //   this.allPager[thisRobotId].push({text: this.commentsTxt[thisRobotId]})
          // }else{
          setFunc(this.currentPager, thisRobotId, 1);
          setFunc(this.allPager, thisRobotId, [
            { text: this.commentsTxt[thisRobotId] },
          ]);
        }

        this.toSendQueNow(name, this.comments[que], thisRobotId);
      }
    },
    copyDoc(ikey, action) {
      // console.log(this.commentsTxt, ikey)
      navigator.clipboard.writeText(this.commentsTxt[ikey]);
      this.$message.success("复制成功~");
    },
    triggerModal() {
      this.modalShow = !this.modalShow;
      document.body.style.overflow = this.modalShow ? "hidden" : "";
    },
    newComment(msg) {
      // let id = this.getId();
      // setFunc(this.comments, "r__" + id, msg);
      // this.commentsIdCollection.push("r__" + id);
      this.showOther.show = true;
      this.showOther.txt = msg;
      setTimeout(() => {
        this.showOther.show = false;
        this.showOther.txt = null;
      }, 3000);

      this.chatToBottom(4);
    },
    showRelevantQues: function (id, ques) {
      // console.log(id, ques, '-----------')
      if (Array.isArray(ques) && ques.length) {
        setFunc(this.lastQueRecommend, "r__" + id, ques);
      }
    },
    showToolTip: _.debounce(function (id, source) {
      this.scalePic();
      this.initOtherMd();

      if (this.taskActions["r__" + id] && this.taskActions["r__" + id].length) {
        // this.comments["r__" + id] =
        let popsIframe = [];
        // for(let actOne of this.taskActions["r__" + id]){
        //   if(actOne.kind == 'window'){
        //     popsIframe.push(`<iframe src=${actOne.page.url} style="width: 400px;height:500px;" />`)
        //   }
        // }

        // popVisible
        let actOne = this.taskActions["r__" + id][0];

        if (actOne.kind == "window") {
          this.comments["r__" + id] = "为您打开新页面";
          setTimeout(() => {
            window.open(actOne.page.url, "_blank");
          }, 100);
        } else if (actOne.kind == "popup") {
          this.popContent = `<iframe src=${actOne.page.url} style="width: 800px;height:500px;" />`;
          this.popVisible = true;
        }

        // popsIframe.join('');
      }
      this.lastTime = new Date().getTime();
      setFunc(this.lastTimeCollection, "r__" + id, new Date().getTime());

      this.showSourceAndLinks(id, source);

      this.chatToBottom(3);
    }, 100),

    showSourceAndLinks(id, source) {
      if (source && source.length) {
        let sourceCollect = [];
        let sourceLinksDownLoad = {};
        let linkCollect = [];
        let appsCollect = [];
        let ids = [];
        for (let sr of source) {
          if (sr.chunkSourceDocument) {
            sourceCollect.push("doc_" + sr.chunkSourceDocument);
            if (sr.chunkSourceDocumentUri) {
              sourceLinksDownLoad["doc_" + sr.chunkSourceDocument] =
                sr.chunkSourceDocumentUri;
            }
          }
          if (sr.question) {
            sourceCollect.push("que_" + sr.question);
          }
          if (
            sr.chunkSourceOntology &&
            sr.chunkSourceOntology.length &&
            sr.chunkSourceOntology.includes("id")
          ) {
            const app = JSON.parse(sr.chunkSourceOntology);
            app.attr = JSON.parse(app.attr);
            if (!ids.includes(app.id)) {
              ids.push(app.id);
              appsCollect.push(app);
            }
          }
          linkCollect.push(this.getMarked(sr.chunkSourceReference));
        }

        sourceCollect = Array.from(new Set(sourceCollect));
        linkCollect = Array.from(new Set(linkCollect));

        // appsCollect = Array.from(new Set(appsCollect));

        sourceCollect = sourceCollect.filter((t) => t);
        linkCollect = linkCollect.filter((t) => t);

        if (sourceCollect.length) {
          setFunc(this.answer_source, id, sourceCollect);
        }

        this.sourceLinksDownLoad = sourceLinksDownLoad;

        if (linkCollect.length) {
          setFunc(this.answer_link, id, linkCollect);
        }

        if (appsCollect.length) {
          setFunc(this.answer_recommend, id, appsCollect);
        }
      }
    },
    closeRecord: _.debounce(function () {
      this.startRecord = false;
    }, 1400),
    setStartRecordNow(e) {
      this.setStartRecord = e;
    },
    // startMic(e) {
    //   if(e !== 'stop'){
    //     this.startRecord = true;
    //   }else{
    //     this.startRecord = false;
    //   }
    // },
    startMic(e) {
      // // 调用 register 后可以立刻调用其他 JS 接口
      let self = this;
      if (self.signs.config) {
        if (self.startRecord || e === "stop") {
          self.startRecord = false;
          self.setStartRecordNow(false);
          ww.stopRecord({
            success: function (res) {
              ww.translateVoice({
                localId: res.localId, // 需要识别的音频的本地Id，由录音相关接口获得，音频时长不能超过60秒
                isShowProgressTips: 1, // 默认为1，显示进度提示
                success: function (res) {
                  self.nowTxt = self.nowTxt + res.translateResult; // 语音识别的结果
                },
              });
            },
            fail(res) {
              if (self.siteInfo.openWXSDK) {
                self.$message.info(JSON.stringify(res));
              }
            },
          });
        } else {
          ww.startRecord();
          self.startRecord = true;
        }

        ww.onVoiceRecordEnd(function (res) {
          self.startRecord = false;
          self.setStartRecordNow(false);
          ww.translateVoice({
            localId: res.localId, // 需要识别的音频的本地Id，由录音相关接口获得，音频时长不能超过60秒
            isShowProgressTips: 1, // 默认为1，显示进度提示
            success: function (res) {
              self.nowTxt = self.nowTxt + res.translateResult; // 语音识别的结果
            },
          });
        });
      } else {
        this.$message.info("签名未拿到，不能使用录音功能~~");
      }
    },
    startMicOld() {
      const self = this;
      const SpeechRecognition =
        window.SpeechRecognition || window.webkitSpeechRecognition;

      if (!SpeechRecognition) {
        this.$message.warning("当前浏览器不支持Web Speech Api");
      } else {
        self.startRecord = true;

        const recognition = new SpeechRecognition();
        recognition.lang = "zh-CN";
        recognition.interimResults = true;

        recognition.start();

        const id = this.getId();

        recognition.onresult = function (e) {
          let last = e.results.length - 1;
          let text = e.results[last][0].transcript;

          console.log("Confidence: ", text);

          self.nowTxt = text;
          self.closeRecord();

          // self.timeStamp = (new Date())

          console.log(recognition.continuous);
          // e.results[0][0].confidence);

          // We will use the Socket.IO here later…
        };

        recognition.onerror = function (event) {
          self.closeRecord();
        };
      }
    },
    openChatList() {
      // 打开会话显示
      this.openChated = !this.openChated;
      this.$emit("toggleChat");
    },
    hideRobotPost() {
      window.parent.closeRobotIframe();
    },
    hideRobot() {
      this.chatShow = !this.chatShow;

      if (this.hasAnswerChanged) {
        //有小红点，直接切到人工
        this.isRobot = false;
        this.hasAnswerChanged = false;
        this.chatToBottomUser();
      }

      if (this.chatShow && this.isRobot) {
        this.chatToBottom(2);
        setTimeout(() => {
          document
            .getElementById("commentsBox" + this.onlyId)
            .addEventListener("scroll", this.handleScroll);

          let widthDefine = localStorage.getItem("widthDefine");
          if (widthDefine) {
            this.widthDefine = widthDefine;

            // const target = document.getElementById("contentsRobotBox")
            // if(target){
            // target.style.width = widthDefine + 'px'
            // }
          }

          document
            .getElementById("bigger")
            .addEventListener("mousedown", function (e) {
              e.preventDefault();
              let startX = e.clientX;
              let stared = true;

              let boxWidth =
                document.getElementById("contentsRobotBox").clientWidth;

              let resizeRobotBox = function (e) {
                // if(document.body.className === 'isMoving'){
                let width = e.clientX - startX;

                // console.log(boxWidth, "----boxw");
                let afterW = -width - 5 + boxWidth;

                if (afterW < 400) {
                  afterW = 400;
                }
                // document.getElementById("bigger").style.width = -width - 1 + 10 +'px';
                document.getElementById("contentsRobotBox").style.width =
                  afterW + "px";

                // }
              };

              // document.body.className = 'isMoving'
              // let startWidth = $box.width();

              document.body.addEventListener("mousemove", resizeRobotBox);

              document.body.addEventListener("mouseup", function (e) {
                // // console.log('-----?')
                // document.getElementById('bigger').remoEventListener('mousedown')
                let widthDefine =
                  document.getElementById("contentsRobotBox").clientWidth;
                localStorage.setItem("widthDefine", widthDefine);
                this.widthDefine = widthDefine;
                document.body.removeEventListener("mousemove", resizeRobotBox);
                // document.body.className = ""
              });
            });
        });
      } else {
        document
          .getElementById("commentsBox" + this.onlyId)
          .removeEventListener("scroll", this.handleScroll);
      }

      if (this.useFit) {
        this.$emit("close", true);
      }
    },
    handleScrollUser: _.debounce(function () {
      let scrollTop = document.getElementById("commentsBoxUser").scrollTop;
      // // console.log(scrollTop, "---------users滚动向上");
      if (scrollTop < 20 && this.leftUserChatNums) {
        let id = this.commentsUserIds[0];
        this.getUserChatRecords(id.split("__")[1]);
      }
    }, 500),
    handleScroll: _.debounce(function () {
      let scrollTop = document.getElementById(
        "commentsBox" + this.onlyId
      ).scrollTop;
      // console.log(scrollTop, "---------滚动向上");
      if (scrollTop < 20 && !this.isNewChat) {
        let id = this.commentsIdCollection[0];
        // if (this.useConversation.name) {
        this.getChatRecords(id);
        // }
        // // console.log(this.commentsIdCollection[0].split("p__")[1], "-----");
      }
    }, 500),
    getId() {
      return uuidv4();
    },
    chatToId(id) {
      setTimeout(() => {
        const element = document.getElementById(id);
        if (element) {
          element.scrollIntoView();
        } else {
          // this.chatToBottom(1);
        }
      }, 10);
    },
    chatToBottom(e) {
      if (this.commentsIdCollection.length) {
        //只有在有聊天记录的时候才滚动
        // console.log(e, '-------------')
        setTimeout(() => {
          const element = document.getElementById("boxBottom" + this.onlyId);
          element
            ? element.scrollIntoView({
                behavior: "smooth",
                block: "nearest", // 仅滚动到最近的可视位置
                inline: "nearest",
              })
            : "";
        }, 600);
      }
    },
    chatToBottomUser() {
      setTimeout(() => {
        const element = document.getElementById("boxBottomUser" + this.onlyId);
        element
          ? element.scrollIntoView({
              behavior: "smooth",
              block: "nearest", // 仅滚动到最近的可视位置
              inline: "nearest",
            })
          : "";
      }, 600);
    },
    linkifyStr: function (str) {
      let isWay = this.isMobile() ? "_self" : "_blank";
      return str.replaceAll("href=", "target=" + isWay + " href=");
    },
    prefixLink: function (str) {
      let text = str.replaceAll("“", "**").replaceAll("”", "**");
      // const urlRegex = /(?<!\]\()(https?:\/\/[^\s\)\]]+)(?!\))/g;
      // const urlRegex =
      // const urlRegex = /(?<!\]\()https:\/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:\/[a-zA-Z0-9._~:/?#\[\]@!$&'()*+,;=-]*)?(?!\))/g;
      // const urlRegex =
      const urlRegex =
        /(?<!\]\()(https?:\/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:\/[a-zA-Z0-9._~:/?#\[\]@!$&'()+,;=-]*)?)(?!\))/g;
      let matchArr = text.match(urlRegex);

      if (
        Array.isArray(
          matchArr &&
            !str.includes("show-iframe") &&
            !str.includes("white-board")
        )
      ) {
        for (let noteStr of matchArr) {
          if (noteStr.indexOf("]") === -1 && noteStr.indexOf(")") === -1) {
            text = text.replace(noteStr, ` [${noteStr}](${noteStr}) `);
          }
        }
      }

      // while ((match = urlRegex.exec(text)) !== null) {

      //   // && urlRegex.exec(text).join(',').indexOf(']') === -1 && urlRegex.exec(text).join(',').indexOf(')') === -1
      //   // console.log("Detected URL:", match[0]);
      //   text = text.replace(match[0], ` [${match[0]}](${match[0]}) `);
      // }

      return text;
    },
    getPuredRobotTxt(str) {
      return str.replace(
        /[&<>"']/g,
        (tag) =>
          ({
            "&": "&amp;",
            "<": "&lt;",
            ">": "&gt;",
            '"': "&quot;",
            "'": "&#39;",
          }[tag])
      );
    },
    getMarked(txt) {
      // 纯净化回复代码
      // let nTxt = DOMPurify.sanitize(txt || "");
      // let pTxt = this.getPuredRobotTxt(nTxt || "");

      // let txt = this.$refs['area2'].value;
      let aCut = this.prefixLink(txt || "");
      // let aCut = md.render(txt || "")
      let bCut = marked(aCut || "");
      // let bCut = md.render(aCut || "");

      // let bCut = aCut
      let cCut = DOMPurify.sanitize(bCut);

      let dCut = this.linkifyStr(cCut);
      // .replaceAll("[", "$$").replaceAll("]", "$$");
      return dCut;
    },
    getMarked2(txt) {
      // return marked(txt || "")
      let aCut = txt
        .replaceAll("）", ")")
        .replaceAll("（", "[点击打开](")
        .replaceAll("“", '"')
        .replaceAll("”", '"');

      let asHref =
        /^(?:(http|https|ftp):\/\/)?((|[\w-]+\.)+[a-z0-9]+)(?:(\/[^/?#]+)*)?(\?[^#]+)?(#.+)?$/gi;
      // let reas = aCut.match(as)

      // let as = /^(?:(http|https|ftp):\/\/)?((|[\w-]+\.)+[a-z0-9]+)(?:(\/[^/?#]+)*)?(\?[^#]+)?(#.+)?$/i;

      // // console.log(aCut, reas, '---------------')

      let pattern = /\((.*?)\)/gi;
      let result = aCut.match(pattern);
      // // console.log()

      // // console.log(aCut)
      // let res = []

      // // console.log(txtRes)
      // for(let aCut of txtRes){
      let aHtml = marked(aCut || "");

      let aReg = /<a.*?(?:>|\/>)/gi;
      let arr = aHtml.match(aReg);
      let hrefs = [];

      //匹配href属性
      let hrefReg = /href=[\'\"]?([^\'\"]*)[\'\"]?/i;
      if (arr && arr.length) {
        let href_s = arr.map((item) => item.match(hrefReg));
        hrefs = hrefs.concat(href_s);
      }

      // // console.log(hrefs, result, "----+");
      if (result && result.length) {
        for (let im in hrefs) {
          let href = hrefs[im][0];

          // console.log(href, "-------href", hrefs[im][1]);

          if (!hrefs[im][1].startsWith("%")) {
            aHtml = aHtml.replace(href, `target="_blank"  ${href}`);
            if (result[im]) {
              aHtml = aHtml.replace("点击打开", result[im]);
            }
          } else {
            aHtml = aHtml.replace(href, `href="#"`);
            aHtml = aHtml.replace("点击打开", result[im]);
          }

          // let clearHref = href.toString().split("）")[0];
          // let before = href.startsWith(`href=www`) ?
          // aHtml = aHtml.replace(href, `target="_blank"  ${href}`);
          // .replace("点击打开", '('+href.split('href=')[1]+')');
          // if (result[im]) {
          // aHtml = aHtml.replace("点击打开", result[im]);
          // }
          // aHtml = aHtml.replace("<a", `<a target="_blank"`);
        }
      }

      // res.push(aHtml)
      // }

      return aHtml;

      // return res.join("")
      // let txtRes = txt.replaceAll('（', '**').replaceAll('）', '**')
      // txtRes = marked(txtRes || "");
      // let after_str = txtRes;

      // if (txtRes && txtRes.length) {
      //   let aReg = /<a.*?(?:>|\/>)/gi;
      //   //匹配href属性
      //   let hrefReg = /href=[\'\"]?([^\'\"]*)[\'\"]?/i;
      //   let arr = txtRes.match(aReg);
      //   let hrefs = [];

      //   if (arr && arr.length) {
      //     let href_s = arr.map((item) => item.match(hrefReg));
      //     hrefs = hrefs.concat(href_s);
      //   }

      //   // console.log(hrefs)

      //   for (let im of hrefs) {
      //     let href = im[1];

      //     let clearHref = href.toString().split("）")[0];
      //     after_str = after_str.replace(href, clearHref);
      //     after_str = after_str.replace("<a", `<a target="_blank"`);
      //   }

      //   return after_str;
      // }

      // return after_str;
    },
    getUnicode(str) {
      let result = "";
      for (let i = 0; i < str.length; i++) {
        let charCode = str.charCodeAt(i).toString(16);
        result += "\\u" + charCode;
      }
      return result;
    },
    getMessages() {
      let commentsTxt = Object.assign({}, this.commentsTxt);
      let allIds = Object.assign([], this.commentsIdCollection);

      let nowTime = new Date().getTime();
      let limitMinutes = this.copilotInfo.source.spec.copilot.session.timeout;

      // this.lastTimeCollection
      // console.log(commentsTxt, allIds, "-----ceshi");

      // if (
      //   passedMinites <
      // ) {
      let endSet = this.copilotInfo.source
        ? this.copilotInfo.source.spec.copilot.session.context.backtrack
        : 5;
      let range = endSet * 2;

      let end = allIds.length;
      let start = end - range;
      if (this.breakPoint) {
        let brk = allIds.indexOf(this.breakPoint) + 1;
        if (brk > start) {
          start = brk;
        }
      }
      // end - start > 10 ? start + 10 : end;

      let ids = this.commentsIdCollection.slice(start, end);
      let idsMap = ids
        .filter((t) => {
          let passedMinites =
            (nowTime - this.lastTimeCollection[t]) / 60 / 1000;
          // console.log(parseInt(passedMinites)+'分钟前'+passedMinites+','+parseInt(limitMinutes))
          return parseInt(passedMinites) < parseInt(limitMinutes);
        })
        .map((t) => {
          return {
            text: commentsTxt[t],
            role: t.startsWith("p__") ? "user" : "assistant",
          };
        });

      // console.log(idsMap, "-----messages上下文");

      return idsMap;
      // } else {
      //   return [];
      // }
    },
    getMessagesPrompt() {
      let commentsTxt = Object.assign({}, this.commentsTxt);
      let allIds = Object.assign([], this.commentsIdCollection);

      //console.log(this.copilotInfo.source.spec.copilot.session.context.backtrack, '?');

      let endSet = this.copilotInfo.source
        ? this.copilotInfo.source.spec.copilot.session.context.backtrack
        : 5;
      let range = endSet * 2;

      let end = allIds.length;
      let start = end - range;
      // end - start > 10 ? start + 10 : end;

      let ids = this.commentsIdCollection.slice(start, end);
      let idsMap = ids.map((t) => {
        return {
          text: commentsTxt[t],
          role: t.startsWith("p__") ? "user" : "assistant",
        };
      });

      let msgBox = [];
      for (let a of idsMap) {
        msgBox.push(a.role + "：" + a.text);
      }

      let str = JSON.stringify({ messages: msgBox.join("\n") });

      //console.log(str)
      return str;
    },
    toStopClient() {
      this.client.dispose();
      this.newComment("已暂停回答~此次对话将不会出现在聊天记录中~");
      this.sendPostIframStatus("stop");

      if (this.firstSendTask) {
        this.nowTask = {};
        this.tasksPopShow = false;
        this.nowTaskIdx = -1;
        this.tasksPopFilter = [];
        this.similarLoading = false;
        this.firstSendTask = false;
      }
      // }
      // id: this.subscriptionId,
    },
    cleanTxt(e) {
      if (this.useEditor) {
        let nodes = document.getElementsByClassName("divContent")[0].childNodes;
        let txt = "";
        nodes.forEach((it, idx) => {
          txt = txt + (it.data || it.value);
        });
        // console.log(e, nodes.map(t=>t.value), '------')
        return txt;
      } else {
        return e;
      }
    },
    toSendQueNow(name, str, thisRobotId) {
      let a = this.cleanTxt(str ? str : this.nowTxt);
      let aOrigin = a;

      if (this.nowSkill && this.nowSkill.text && !a) {
        a = this.nowSkill.text;
      }

      // a && a.length < 6000
      if (a && a.length < 6000) {
        // a = a.replaceAll("\n", "");
        let messages = this.getMessages();

        // console.log("上下文", messages);

        if (a) {
          setTimeout(() => {
            this.nowTxt = "";
            this.useEditor = false;

            if (this.nowSkill) {
              this.lastSkill = Object.assign({}, this.nowSkill);
              // 正在使用技能
              if (!this.nowSkill.multiturn) {
                // this.clearTaskChoosed();
                this.nowSkill = {};
              }
            }

            this.$refs["questionRef"] ? this.$refs["questionRef"].blur() : "";
          }, 100);

          if (this.isRobot) {
            const id = this.getId();

            let loadingid = id;
            if (thisRobotId) {
              loadingid = thisRobotId;
            }

            // console.log(name)
            if (!name) {
              //重试一次的问题不需要出现在右边

              setFunc(this.comments, "p__" + id, a);
              setFunc(this.commentsTxt, "p__" + id, a);
              this.commentsIdCollection.push("p__" + id);

              this.loadingNew = true;
            } else {
              setFunc(this.loading, loadingid, true);
            }

            //判断要不要重新打一个时间戳，在聊天记录和当前记录之间
            if (!this.chattTimeStamp) {
              this.chattTimeStamp = true;
              setFunc(
                this.timeStamp,
                "p__" + id,
                moment(new Date()).format("YYYY-MM-DD HH:mm")
              );
            }

            //记录所有聊天记录的时间，便于上下文超时判断
            setFunc(this.lastTimeCollection, "p__" + id, new Date().getTime());
            // // console.log(, itt, this.commentsIdCollection, this.timeStamp[itt])

            this.chatToBottom(6);

            this.isChatting = true;

            let userId = this.openid;
            let self = this;
            // $userId:  ID!
            // userId: $userId
            let conversation =
              this.useConversation && this.useConversation.name
                ? {
                    name: this.useConversation.name,
                  }
                : {};
            let messagesCombined = messages.concat([
              { text: this.getPured(a, 'to'), role: "user" },
            ]);

            console.log(this.getPured(a, 'to'), '---------传到后端的')

            // if (this.nowTask && this.nowTask.name && this.nowTask.name !== 'allTask') {
            //   messagesCombined = messages.concat([
            //     { text: a, role: "user", tasks: [this.nowTask.name] },
            //   ]);
            // }else

            // console.log(messagesCombined, '发送了？？')

            // 重试一次
            if (name) {
              messagesCombined = messages.concat([
                { text: a, role: "user", name: name },
              ]);
            }
            let variables = {
              messages: messagesCombined,
              // messages: [{ text: a, role: "user" }],
              filter: {},
              copilot: this.copilotId,
              conversation,
              // this.useConversation || this.getId()
            };

            if (this.modelChooses && this.modelChooses.length) {
              variables.backend = this.modelUse;
            }
            // ConversationInput

            if (this.queVary) {
              variables.filter = { category: { in: this.queVary } };
            }

            if (this.anonymity) {
              delete variables.conversation;
            }

            if (this.nowSkill && this.nowSkill.text) {
              variables.skill = this.nowSkill.name;

              setFunc(this.skillMsg, "p__" + id, this.nowSkill.text);

              setFunc(this.skillMsg, "r__" + id, this.nowSkill.text);

              if (this.nowSkill.task) {
                variables.tool = { tasks: [this.nowSkill.task] };
              }
            }


            

            // if (this.maybeAsk) {
            //   this.maybeAsk = false;
            //   variables.tool = {
            //     tasks: [],
            //     choice:"none",
            //   }
            // }else{
            //   variables.tool = {
            //     tasks: [],
            //     choice:"auto",
            //   }
            // }

            // $conversation: string,
            // conversation: $conversation

            // $tool: ToolInput,
            // tool: $tool,

            this.hasRecords = true;

            this.client.subscribe(
              {
                query: variables.backend
                  ? self.siteInfo.openThoughtSteps
                    ? subs.hasThoughtAndBackends
                    : subs.hasBackends
                  : self.siteInfo.openThoughtSteps
                  ? subs.hasThought
                  : subs.normal,
                variables,
              },
              {
                next: (event) => {
                  let robotId = "r__" + id;

                  if (
                    self.siteInfo.openThoughtSteps &&
                    event.data.chat.thoughtChain &&
                    event.data.chat.thoughtChain.items
                  ) {
                    setFunc(
                      this.thoughtTxt,
                      robotId,
                      event.data.chat.thoughtChain.items
                    );
                    this.loadingNew = false;
                  }

                  if (event.data.chat.choices) {
                    let txt = event.data.chat.choices[0].message.text;
                    txt = this.getMarked(txt || "");
                    if (thisRobotId) {
                      robotId = thisRobotId;
                      setFunc(this.loading, loadingid, false);
                    } else {
                      this.loadingNew = false;
                    }

                    setFunc(this.comments, robotId, txt);

                    if (txt.includes("white-board")) {
                      setFunc(
                        this.robotUsedSkillMsg,
                        "r__" + id,
                        this.lastSkill.text
                      );
                    }
                    setFunc(
                      this.commentsTxt,
                      robotId,
                      event.data.chat.choices[0].message.text
                    );

                    if (event.data.chat.choices[0].message.reasoning) {
                      let reason = event.data.chat.choices[0].message.reasoning;
                      setFunc(this.reasonTxt, robotId, reason.content);
                      setFunc(
                        this.reasonTime,
                        robotId,
                        parseFloat(reason.duration / 1000).toFixed(0)
                      );

                      // if(!this.reasonTime[robotId]){
                      //   setFunc(this.reasonTime, robotId, [(new Date()).getTime()])
                      // }else{
                      //   this.reasonTime[robotId].push((new Date()).getTime())
                      // }
                    }

                    //记录此时的name，为了再试一次
                    if (event.data.chat.choices[0].message.userMessage) {
                      setFunc(
                        this.queNameToId,
                        robotId,
                        event.data.chat.choices[0].message.userMessage
                      );
                    }

                    // console.log(event.data.chat.choices[0].message.userMessage, '----usermessage')

                    this.isChattingId = robotId;
                    setFunc(
                      this.idStorage,
                      robotId,
                      event.data.chat.choices[0].message.name
                    );

                    this.commentsIdCollection.indexOf(robotId) === -1 &&
                      this.commentsIdCollection.push(robotId);

                    // if(!this.nowTask.name){
                    this.showToolTip(id, event.data.chat.choices[0].samples);

                    this.showRelevantQues(
                      id,
                      event.data.chat.relevantQuestions
                    );
                    // }

                    setFunc(
                      this.taskActions,
                      robotId,
                      event.data.chat.choices[0].actions
                    );
                    //检查是否是新会话；
                    this.checkReNameChat(event.data.chat.conversation);

                    this.chatToBottom(7);

                    this.transformMath();
                  }
                  // 20250822 
                  //如果viewdata里没有chatSessionId
                  if(event?.data?.chat?.conversation?.name&&!this.viewdata?.chatSessionId){
                    this.viewdata.chatSessionId=event.data.chat.conversation.name;//避免多次进入保存
                    let {blackbord,tags,userTypes,users,differentiated,summaryDimension,chatSessionId,summaryTopic}=this.viewdata
                    
                    let viewdata={
                      blackbord,
                      tags,userTypes,users,differentiated,
                      summaryDimension,chatSessionId,summaryTopic,
                    }
                    this.toSaveViewData(viewdata,()=>{
                      // 
                      console.log('chatSessionId已保存=====',chatSessionId)
                    })
                  }
                  // this.todoMsg = data.data.chat.choices[0].message.text;
                },
                error: (error) => {
                  this.sendPostIframStatus("error");
                  // console.error(error)
                  this.isChatting = false;
                  if (name) {
                    setFunc(this.loading, loadingid, false);
                  } else {
                    this.loadingNew = false;
                  }
                  this.isChattingId = null;

                  this.newComment(this.errorMsg);
                },
                complete: () => {
                  this.answer_tools.push(id);
                  setFunc(this.robotSkillMsg, "r__" + id, this.lastSkill.text);

                  this.sendPostIframStatus("done");

                  if (name) {
                    this.allPager[thisRobotId].push({
                      text: this.commentsTxt[thisRobotId],
                    });
                    this.currentPager[thisRobotId] =
                      this.allPager[thisRobotId].length;
                  }
                  this.isChatting = false;
                  this.isChattingId = null;
                },
              }
            );
          } else {
            this.toAsk(a);
          }
        }
      } else {
        a && this.$message.warning("当前问题最长不超过6000字符~");
      }
    },
    scrollFindSkill(skill) {
      console.log(skill, "-----------");
      const element = document.getElementsByClassName("robotskill" + skill);
      if (element.length) {
        element[0].scrollIntoView({
          behavior: "smooth",
          block: "nearest", // 仅滚动到最近的可视位置
          inline: "nearest",
        });
        // const element2 = document.getElementsByClassName('skill'+skill+'robot');
        // console.log(element2[0])
        let childNodes = element[0].childNodes;
        let hasWhite = false;
        for (let cn of childNodes) {
          if (cn.tagName === "PRE") {
            if (cn.children[1].className === "white-btn") {
              hasWhite = true;
              this.postIframesNow({ target: cn.children[1] });
              this.$emit("updateRobotCenter", {
                key: "showSkillTool",
                val: false,
              });
              // this.$emit('updateRobotCenter', {key: 'showSkillTool', val: true})
              this.$emit("updateRobotCenter", {
                key: "showSkillToolBool",
                val: false,
              });
            }
          }
        }

        if (!hasWhite) {
          // this.$message.info('当前技能暂无生成的报告')
          //this.$emit('updateRobotCenter', {key: 'showSkillTool', val: false})
          // this.$emit('updateRobotCenter', {key: 'showSkillToolBool', val: false})
          //this.$emit('updateRobotCenter', {key: 'openPostIframe', val: false})
        }
        // this.$emit('update', {})
        // this.$emit('updateRobotCenter', {key: '', val:'iframe'})

        // .click;
      }
      // console.log(skill)
    },
    toSendQue(e) {
      // console.log(this.nowTaskIdx, '--------')
      if (e.keyCode === 8) {
        //退格键
        let editorNodes = true;
        if (this.useEditor) {
          let child =
            document.getElementsByClassName("divContent")[0].childNodes.length;
          editorNodes = child != 0;

          if (child == 0) {
            this.nowTxt = "";
            this.useEditor = false;
          }
        }
        if (!this.nowTxt || !editorNodes) {
          this.taskChoosed = {};
          this.nowSkill = {};
        }
      } else if (e.key === "Enter" && !this.isIMEComposing) {
        // (!this.firstSendTask && !this.stopPopSimilarQue && !this.nowTask.name) &&
        if (
          e.isComposing ||
          e.keyCode === 229 ||
          (this.tasksAndQuesPopFilter.length && this.nowTaskIdx !== -1)
        ) {
          // && this.nowTxt.startsWith("/") && this.tasksPopFilter.length
          console.log(
            "正在使用中文输入法输入或正在使用指令选中或者，正在填充相似问题~"
          );
        } else if (this.isShiftKey) {
          console.log("正在使用shift~");
        } else {
          // console.log(this.isShiftKey)
          this.toSendQueNow(null, null);
        }
      }
    },
    toAsk(a) {
      this.isChatting = true;
      apolloProvider.clients.helpDesk
        .mutate({
          mutation: queryStr.chatToUser,
          variables: {
            input: {
              content: a,
              copilotId: this.copilotId,
            },
          },
          client: "helpDesk",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.isChatting = false;
          // // console.log(res, '------------res')
          let idStr = res.data.transferToHumanQuestion.id;

          setFunc(
            this.commentsUser,
            "p__" + idStr,
            Object.assign(
              {
                type: "user",
              },
              res.data.transferToHumanQuestion
            )
          );

          setFunc(
            this.commentsUser,
            "h__" + idStr,
            Object.assign(
              {
                type: "help",
                point: "dealing",
                content: "人工正在处理中，请耐心等候...",
              },
              { question: res.data.transferToHumanQuestion }
            )
          );

          this.commentsUserIds.push("p__" + idStr);
          this.commentsUserIds.push("h__" + idStr);

          // if(this.firstLoading <= 2){
          this.chatToBottomUser();
          // this.firstLoading++
          // }
        })
        .catch((e) => {});
    },
    getUserChatRecords(lastRecordId) {
      if (this.canUseHelpDesk && !this.isRobot) {
        //有人工再请求人工聊天记录
        // let pkey = lastRecordId ? lastRecordId : "firstPage";

        // if (!this.pageResolved[pkey]) {

        if (this.firstLoading == 1) {
          this.getUserChatRecordsLoading = true;
          this.firstLoading++;
        }

        let lastNum =
          this.uChatTotalCount * 2 === this.commentsUserIds.length &&
          this.uChatTotalCount > 0
            ? this.uChatTotalCount
            : 15;
        // : this.uChatTotalCount;

        // // console.log(this.uChatTotalCount, this.commentsUserIds.length)

        apolloProvider.clients.helpDesk
          .query({
            query: queryStr.chatRecords,
            variables: {
              after: null,
              before:
                this.uChatTotalCount * 2 === this.commentsUserIds.length
                  ? null
                  : this.myQueStartCursor,
              last: lastNum,
              copilotId: this.copilotId,
            },
            client: "helpDesk",
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            if (this.leftUserChatNums !== false) {
              //没有就不再转了
              this.leftUserChatNums =
                res.data.myQuestions.pageInfo.hasPreviousPage;
              this.myQueStartCursor = res.data.myQuestions.pageInfo.startCursor;
            }

            this.uChatTotalCount = res.data.myQuestions.totalCount;

            let commentsUserIds = [];
            let handleData = res.data.myQuestions.edges;
            let userAnswerCollect = {};

            for (let { node } of handleData) {
              userAnswerCollect[node.id] = node.answerItems.length;

              this.commentsUserIds.indexOf("p__" + node.id) === -1 &&
                commentsUserIds.push("p__" + node.id);
              setFunc(
                this.commentsUser,
                "p__" + node.id,
                Object.assign(
                  {
                    type: "user",
                  },
                  node
                )
              );

              if (node.answerItems && node.answerItems.length) {
                this.commentsUserIds.indexOf("h__" + node.id) === -1 &&
                  commentsUserIds.push("h__" + node.id);
                setFunc(
                  this.commentsUser,
                  "h__" + node.id,
                  Object.assign(
                    {
                      type: "help",
                    },
                    { answerItems: node.answerItems, question: node }
                  )
                );
              } else {
                this.commentsUserIds.indexOf("h__" + node.id) === -1 &&
                  commentsUserIds.push("h__" + node.id);
                setFunc(
                  this.commentsUser,
                  "h__" + node.id,
                  Object.assign(
                    {
                      type: "help",
                      point: "dealing",
                      content: "人工正在处理中，请耐心等候...",
                    },
                    { question: node }
                  )
                );
              }
            }

            this.commentsUserIds = [
              ...commentsUserIds,
              ...this.commentsUserIds,
            ];

            this.chatToId(lastRecordId);

            this.userAnswerCollect = userAnswerCollect;

            // // console.log(commentsUser, '-----------uuuu')
            this.getUserChatRecordsLoading = false;

            if (this.firstLoading <= 2) {
              this.chatToBottomUser();
              this.firstLoading++;
            }

            this.resetTimer();

            // // console.log(res, commentsUser, '55-----')
          })
          .catch((e) => {});
        // }
      }
    },
    resetTimer(real) {
      clearTimeout(timer);
      timer = null;

      if (!real) {
        timer = setTimeout(() => {
          // if(!this.isRobot){
          this.getUserChatRecords();
          // }
          // // console.log(new Date().getTime(), timer)
        }, 10000);
      }
    },
    scrollCheck() {
      this.getUserChatRecords();
      // this.resetTimer();
    },
  },
};
</script>
