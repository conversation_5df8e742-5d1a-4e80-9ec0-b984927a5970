// const json2yaml = require("js-yaml");

import yaml from 'js-yaml';
import queryStr from "../news/chat";
let timer = null;
import _ from "lodash";
import moment from "moment";
import "moment/locale/zh-cn";
moment.locale("zh-cn");
import { mapState } from "vuex";

import apolloProvider from "@/assets/js/apolloclient.js";


// const defaultIcon = require("../img/normal.png");
import defaultIcon from '../img/normal.png'
import ChartTabs from "./chartTabs.vue";
import { marked } from "marked";

import SkillTree from "./skillTree.vue";

const render = new marked.Renderer();
marked.setOptions({
    renderer: render,
    async: false,
    breaks: false,
    extensions: null,
    gfm: true,
    hooks: null,
    pedantic: false,
    silent: false,
    tokenizer: null,
    walkTokens: null,
});


let setFunc = (obj, key, value) => {
    obj[key] = value;
  }

export const robotCenter = {
    components: {
        ChartTabs,
        SkillTree
    },
    data() {
        return {
            initSquareInfos: 0,
            searchKey: "",
            nowCopilotId: "",
            useWrongId: false,
            nowCopilotIds: [],
            chooseVary: "",

            // 技能
            robotSkills: [],
            robotSkillsCategories: [],

            queVaries: {},
            showQues: [],
            allQues: [],
            sortedQues: [],
            docTypes: {},
            //排序之后的文档
            docTypeNames: [],
            docCategoriesSort: {},
            value: 0,
            activeNames: "",
            userRecordsCheck: {},
            copilotLoading: true,
            robotRecommend: [],

            // 机器人分页
            current: 1,
            pagesize: 12,
            allCopilots: [],
            copilots: [],
            searchRecords: {},
            searchCopilots: [],
            searchDisplay: [],
            searchDisKey: null,

            // 第三层
            searchDisplayChooseLastId: null,
            searchDisplayChoose: {},
            changeTime: 0,
            defaultIcon,

            isLoadSearch: false,
            isSearching: false,

            //会话列表
            conversationList: [],
            conversationId: "",
            conversation: {},
            conversationIdx: 0,

            toggleMobile: false,
            //会话更名
            startInputChangeChat: "",
            visiblePop: {},

            //小窗模式下的会话按钮显示控制
            controlConverBtnsBools: {},

            //避免新会话更新之后界面的闪烁
            isNewChat: null,
            uukeyCollection: [],

            controlChats: true,

            // 任务推荐：欢迎语之下，区分机器人
            tasksRecommend: {},

            modelParse: {},

            // 第三列面板
            openPostIframe: '',
            postIframe: '',
            postTargetRef: null,

            dockIframe: 'right',
            widIframe: '65%',
            toolIframe: "",
            showSkillTool: false,
            showSkillToolBool: false,

            // 第三列面板：组件之一：tab
            postRightBoard: null
        };
    },
    mounted() {
        this.getChatModels();
        // if (this.$route.query.useToggleBtn && this.$route.name === 'airobotauth') {
            this.toggleMobile = true;
            this.useToggleBtn = true;
        // }

        // let rbtTarget = sessionStorage.getItem("aiStart");
        // if (rbtTarget) {
        //   let rbt = JSON.parse(rbtTarget);
        //   this.changeCopilot(rbt);
        // }
        // , JSON.stringify(rbt))
    },
    watch: {
        nowCopilotId: {
            handler(val) {
                if (val) {
                    this.getRecentConversations(val);

                    if (this.nowCopilotIds.includes(val)) {
                        this.targetToBottom();
                    } else {
                        this.nowCopilotIds.push(val);
                    }

                    // 假如开了任务，则请求所有任务放在欢迎语下面
                    if (this.canUseTaskAndQues) {
                        this.getSimilarTasks().then((res) => {
                            console.log(res);
                            setFunc(this.tasksRecommend, this.nowCopilotId, res);
                        });
                    }
                }
            },
            deep: true,
            immediate: true,
        },
        conversationId: {
            handler(val, oldVal) {
                if (location.href.includes('sendMsg=true') && oldVal && val) {
                    this.sendParentMsg('brainstormingbot', { conversation: val, isnew: false });
                }
                // if(val){
                this.targetToBottom();
                // }
            },
            immediate: true,
        },
        searchKey: {
            handler(val) {
                if (val) {
                    this.isLoadSearch = true;
                    this.isSearching = true;

                    this.searchDisplay = [];

                    this.searchRecords = {};

                    this.searchDisplayChooseLastId = null;
                    this.searchNow();

                    this.searchCopilots = this.allCopilots.filter((t) =>
                        JSON.stringify(t).includes(val)
                    );
                } else {
                    this.isLoadSearch = false;
                    this.isSearching = false;

                    this.searchDisplay = [];

                    this.searchRecords = {};
                }
            },
            // immediate: true,
            deep: true,
        },
        token: {
            handler(val) {
                if (val) {
        console.log(val, '__refresh___?here?')

                    Promise.all([this.getAllRobots(), this.getRecentRobots()])
                        .then((res) => {
                            this.copilotLoading = false;

                            if (this.$route.query.id) {
                                this.nowCopilotId = this.$route.query.id;
                            } else if (!this.nowCopilotId) {
                                let id = sessionStorage.getItem("nowCopilotId");
                                let nowid = this.onlyCopilotId;
                                if (nowid) {
                                    this.nowCopilotId = nowid;
                                } else if (id) {
                                    this.nowCopilotId = id;
                                } else {}
                            }

                            // 所有可用机器人的赋值取值操作
                            let robotsAll = res[0];
                            let robotsAllIds = res[0].map((t) => t.name);
                            let robotsRecent = res[1].filter((t) =>
                                robotsAllIds.includes(t.name)
                            );

                            this.allCopilots = robotsAll;

                            let robotRecommend = robotsAll
                                .filter((t) => {
                                    let source = t.source;

                                    return (
                                        source.spec.copilot.recommend &&
                                        source.spec.copilot.recommend - 1 > 0
                                    );
                                })
                                .sort((a, b) => {
                                    let aSource = a.source;
                                    let bSource = b.source;
                                    return (
                                        bSource.spec.copilot.recommend -
                                        aSource.spec.copilot.recommend
                                    );
                                });

                            this.robotRecommend = robotRecommend;

                            if (this.$route.path.includes('square') && this.robotRecommend.length) {
                                this.nowCopilotId = this.robotRecommend[0].name;
                            }

                            // 有路由id的时候，一般是单机器人，判断在不在权限内
                            if (this.$route.query.id) {
                                if (!robotsAllIds.includes(this.nowCopilotId)) {
                                    this.useWrongId = true;
                                }
                            } else if (this.nowCopilotId) {
                                let idTarget = this.nowCopilotId;
                                let ids = robotsRecent.map((t) => t.name);
                                let robots = [
                                    ...robotsRecent,
                                    ...robotsAll.filter((t) => !ids.includes(t.name)),
                                ];
                                let dataArr = Object.assign([], robots).filter(
                                    (a) => a.name == idTarget
                                );
                                let filterArr = Object.assign([], robots).filter(
                                    (a) => a.name !== idTarget
                                );

                                this.copilots = dataArr.concat(filterArr);
                            } else {
                                // 没有指定id的时候 ------------------------------------------------

                                // 访问过且在权限内，使用最近使用的机器人列表
                                if (robotsRecent.length && !this.nowCopilotId) {
                                    this.nowCopilotId = robotsRecent[0].name;
                                    this.copilots = robotsRecent;
                                }

                                // 如果之前没有访问过智能体，使用可使用的机器人第一个作为第一个访问的选择
                                if (robotsAll.length && !this.nowCopilotId) {
                                    this.nowCopilotId = robotsAll[0].name;
                                    this.copilots = robotsAll.slice(0, 1);
                                }

                                //  ------------------------------------------------
                            }

                            this.initQues();
                        })
                        .catch((e) => {
                            // console.log(e, "----9990");
                        });
                    this.startCheckUserRecordsRead();
                }
            },
            immediate: true,
            deep: true,
        },
    },
    created() {},
    computed: {
        ...mapState(["position", "userInfo", "siteInfo"]),
        setIcons() {
            let str = [];
            if (this.siteInfo.systemRobotImg) {
                str.push(`--robotImg:url(${this.siteInfo.systemRobotImg})`)
            } else {
                str.push(`--robotImg:url(${defaultIcon})`)
            }

            if (this.siteInfo.systemUserImg) {
                str.push(`--userImg:url(${this.siteInfo.systemUserImg})`)
            }

            if (this.widIframe) {
                if (this.dockIframe !== 'right') {
                    str.push(`--rightBoxWidth:${this.widIframe}`)
                } else {
                    let num = this.widIframe;
                    let realCalcWidth = (100 - Number(num.replaceAll('%', ''))) + "%"
                    str.push(`--rightBoxWidth:${realCalcWidth}}`)
                }
            }

            return str.join(";")
        },
        siteRobotIcon() {
            return this.siteInfo.systemRobotImg;
        },
        isNotV2() {
            if (
                this.$route.query.theme === "canvas" ||
                this.$route.name === "airobotauth"
            ) {
                return true;
            } else if (
                this.$route.name === "airobot-v2" ||
                window.Global.SITE_THEME ||
                (this.siteInfo && this.siteInfo.systemLogo)
            ) {
                return false;
            } else {
                return this.$route.name !== "airobotsuda";
            }
        },
        useToggleBtn() {
            return !this.isMobile && (["airobot-v2", "airobotauth", "airobotsquare", "airobotsquare-v2"].includes(this.$route.name));
        },
        isMobile() {
            var isMobile = false;
            var flag = navigator.userAgent.match(
                /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
            );
            if (flag != null) {
                isMobile = true;
            }

            if (this.$route.query.useMobile) {
                isMobile = true;
            }
            return isMobile;
        },
        token() {
            if (
                this.$store &&
                this.$store.state &&
                this.$store.state.oidcStore &&
                this.$store.state.oidcStore.access_token
            ) {
                return this.$store.state.oidcStore.access_token;
            } else if (sessionStorage.getItem("oidcStore")) {
                let oidcPassed = JSON.parse(sessionStorage.getItem("oidcStore"));
                return oidcPassed["access_token"];
            } else {
                return null;
            }
            // return "fe5577389807b6d83cd735640cf2e61e";
        },
        showClose() {
            return this.$route.query.showClose;
        },
        hideChatNow() {
            return this.hideChat || this.showClose;
        },
        hasBg() {
            return this.$route.query.hasBg;
        },
        createNew() {
            return this.$route.query.createNew;
        },
    },
    props: [
        "panel",
        "onlyCopilotId",
        "useFit",
        "noActionBox",
        "errorMsg",
        "hideChat",
        "useWheel",
        "useSquare"
    ],
    methods: {
        moment,
        getPured(input){
            const regex = /##岗位名称：([^;]+);父级会话id：(\d+)##(.+)/;
            const match = input.match(regex);

            if (match) {
                //技能名称
                const cfo = match[1];        // CFO
                const sessionId = match[2];  // 12344444
                const statement = match[3];  // 我想要测试一下螺丝刀性能
                
                console.log('岗位名称:', cfo,'会话ID:', sessionId, '语句:', statement);
                return statement;
            }else{
                return input
            }
        },
        //站点设置
        openSetting() {
            console.log(this.$refs.systemRef);
            this.$refs.systemRef.openForm();
        },

        sendParentMsg(type, data) {
            // 发送消息给父窗口
            const message = {
                eventname: type, // 消息类型标识
                data
            };

            // 关键代码：发送消息
            window.parent.postMessage(
                message,
                '*'
            );
        },

        // -----------------------
        updateRobotCenter({ key, val }) {
            this[key] = val;
        },
        openPopSkillTree() {
            this.showSkillToolBool = !this.showSkillToolBool;
        },

        scrollFindSkill(skill) {
            if (this.conversationId) {
                let robotRef = "robot" + this.nowCopilotId + this.conversationId;
                this.$refs[robotRef][0].scrollFindSkill(skill);
            } else {

            }

        },

        // 重置第三列面板
        closeIframeRight() {
            this.openPostIframe = false;
            this.postIframe = null;

            this.showSkillToolBool = false;
            this.showSkillTool = false;

            if (!this.isMobile) {
                this.toggleMobile = false;
            }

            for (let refTar in this.$refs) {
                if (this.$refs[refTar] && this.$refs[refTar][0]) {

                    this.$nextTick(() => {
                        if (this.$refs[refTar][0]) {
                            this.$refs[refTar][0].initBoxHeight();
                        }
                    })
                }
                // console.log(refTar, '--------refs')
            }

        },
        closeUseFit() {
            if (this.useFit) {
                this.$emit("close", true);
            }
        },
        canUseTaskAndQues(name) {
            let rbts = this.allCopilots
                .concat(this.copilots)
                .filter((t) => t.name == name);
            let source = {};
            if (rbts[0]) {
                source = rbts[0].source;
            }

            if (
                source &&
                source.spec &&
                source.spec.copilot &&
                source.spec.copilot.completion
            ) {
                let knowledgeEnable = source.spec.copilot.completion.knowledge.enabled;
                let TaskEnable = source.spec.copilot.completion.task.enabled;

                // console.log(knowledgeEnable, TaskEnable)
                return knowledgeEnable && TaskEnable;
            } else {
                return false;
            }
        },
        controlConverBtns(node) {
            // console.log(ikey)
            let ikey = node.name;
            setFunc(
                this.controlConverBtnsBools,
                ikey, !this.controlConverBtnsBools[ikey]
            );
        },
        getChatModels() {
            apolloProvider.clients.copilotAuth
                .query({
                    query: queryStr.chatModels,
                    variables: {},
                    client: "copilotAuth",
                    fetchPolicy: "no-cache",
                })
                .then((res) => {
                    let data = res.data.backends.filter((t) => t.kind === "llm");
                    let modelParse = {};
                    for (let model of data) {
                        modelParse[model.name] = model;
                    }
                    this.modelParse = modelParse;
                });
        },
        getSimilarTasks: function() {
            return new Promise((resolve, reject) => {
                let keyword = "";
                // this.nowTxt.startsWith('/') ? this.nowTxt.slice(1) : this.nowTxt;
                // if(keyword && keyword.length >= 1){
                apolloProvider.clients.copilotAuth
                    .query({
                        query: queryStr.similarTasks,
                        variables: {
                            copilot: this.nowCopilotId,
                            keyword,
                        },
                        client: "copilotAuth",
                        fetchPolicy: "no-cache",
                    })
                    .then((res) => {
                        let data = res.data.userTasks.edges
                            .map((t) => t.node)
                            .filter((t) => t.recommend > 0);
                        data.sort(function(a, b) {
                            return b.recommend - a.recommend;
                        });
                        resolve(data);
                    });
            });
        },
        toggleChat() {
            this.controlChats = !this.controlChats;
        },
        logOut() {
            this.$store.dispatch("oidcStore/signOutOidc");
        },
        newChat(e) {
            this.isNewChat = null;

            console.log(
                this.conversationId === null, !e,
                this.conversationList.length
            );
            if (this.conversationId === null && !e && this.conversationList.length) {
                if (!this.$route.path.includes('square')) {
                    this.$message.info("当前已是新会话~~");
                }
            } else {
                this.conversationId = null;

                if (
                    (this.conversationList.length && this.conversationList[0].name) ||
                    !this.conversationList.length
                ) {
                    //避免反复创建新会话
                    let uukey = Math.random().toString().slice(4, 20);
                    this.uukeyCollection.push(uukey);
                    this.conversation = { text: "新会话", name: null, uukey };
                    this.conversationList = [this.conversation].concat(
                        this.conversationList
                    );
                }
            }

            // this.changeConversation(this.conversation);
        },
        changeConversation(node, iik) {
            if (node.uukey) {
                // isnewchat
                let robotRef =
                    "robot" + this.nowCopilotId + (node.uukey ? node.uukey : node.name);
                // "'robot' + nowCopilotId + (chat.uukey ? chat.uukey : chat.name)"
                console.log(this.$refs, robotRef);
                this.$refs[robotRef][0].fitNewChat();
            }
            this.conversation = node;
            this.conversationId = node.name;

            if (iik !== null && iik !== undefined) {
                this.conversationIdx = iik;
            }

            this.closeLeft();

            this.closeIframeRight();
        },
        closeLeft() {
            if (this.isMobile && this.toggleMobile) {
                this.toggleMobile = false;
            }
        },
        clearConversation(node) {
            return new Promise((resolve, reject) => {
                apolloProvider.clients.copilotAuth
                    .mutate({
                        mutation: queryStr.clearMessages,
                        variables: {
                            conversation: node.name,
                        },
                        client: "copilotAuth",
                        fetchPolicy: "no-cache",
                    })
                    .then((res) => {
                        resolve("true");
                    })
                    .catch((e) => {
                        reject();
                    });
            });
        },
        delConversation(node) {
            if (node.name) {
                this.visiblePop[node.name] = false;
                this.clearConversation(node).then((res) => {
                    apolloProvider.clients.copilotAuth
                        .query({
                            query: queryStr.delConversation,
                            variables: {
                                name: node.name,
                            },
                            client: "copilotAuth",
                            fetchPolicy: "no-cache",
                        })
                        .then((res) => {
                            this.$message.success("删除成功~");
                            this.getRecentConversations(this.nowCopilotId);
                        });
                });
            }
        },
        reNameChat(ch) {
            // 寻找新对话并及时赋值
            this.conversationId = ch.name;
            this.isNewChat = ch.name;

            if (location.href.includes('sendMsg=true')) {
                this.sendParentMsg('brainstormingbot', { conversation: ch.name, isnew: true });
            }

            this.conversationList = this.conversationList.map((item, idx) => {
                if (!item.name) {
                    return Object.assign(item, ch);
                } else {
                    return item;
                }
            });

            // 如果是嵌入式的窗口则需要通知parent新会话已经确立
            if (this.showClose) {
                window.parent.newChatSet();
            }

            // let targetNode = .
        },
        editConversation(node) {
            if (this.startInputChangeChat !== node.name) {
                this.startInputChangeChat = node.name;
            } else {
                node.name &&
                    apolloProvider.clients.copilotAuth
                    .mutate({
                        mutation: queryStr.updateConversation,
                        variables: {
                            conversation: {
                                name: node.name,
                                text: node.text,
                            },
                        },
                        client: "copilotAuth",
                        fetchPolicy: "no-cache",
                    })
                    .then((res) => {
                        this.$message.success("编辑成功~");
                        // this.startI
                        this.startInputChangeChat = "-----";
                    });
            }
        },
        targetToBottom() {
            let ukey = "robot" + this.nowCopilotId + (this.conversationId || "newid");
            setTimeout(() => {
                // console.log(this.$refs[ukey][0]);
                let uDom = this.$refs[ukey];

                if (uDom && uDom[0]) {
                    uDom[0].chatToBottom();
                }
            }, 150);
        },
        changePanel(e) {
            if (e !== this.panel) {
                switch (e) {
                    case 2:
                        this.$router.push({ name: "airobots" });
                        break;
                    case 4:
                        this.$router.push({ name: "aiexplore" });
                        break;
                }
            }
        },
        toScrollTab(e) {
            let rtl = document.getElementById("tabScroll");

            let value = this.value;
            let s_left = rtl.scrollLeft;
            let c_w = rtl.clientWidth;
            let r_w = rtl.scrollWidth;

            // console.log(s_left, c_w, r_w, value, '-------')

            if (e && s_left !== 0) {
                value = value - 100;
                rtl.scroll({
                    left: value,
                    behavior: "smooth",
                });
            }

            if (!e && s_left + c_w < r_w) {
                value = value + 100;
                rtl.scroll({
                    left: value,
                    behavior: "smooth",
                });
            }

            this.value = value > r_w - c_w ? r_w - c_w : value < 0 ? 0 : value;
        },
        changeRecords(it) {
            console.log(it, this.searchDisKey, this.$refs)
            this.searchDisplayChooseLastId = this.searchDisKey;
            this.searchDisplayChoose = it;


            console.log(this.searchDisplayChooseLastId, this.searchDisplayChoose)

            this.$refs["searchDisPlayBox"].touch(
                this.searchDisplayChooseLastId,
                this.searchDisplayChoose
            );
        },
        loadSearchRes(it) {
            this.searchDisKey = it.copilot.name;
            this.searchDisplay = it.msgs;

            this.searchDisplayChooseLastId = null;
        },
        findName(name) {
            let rbts = this.allCopilots
                .concat(this.copilots)
                .filter((t) => t.name == name);
            if (rbts[0]) {
                let source = rbts[0].source;
                return source.spec.copilot.text;
            } else {
                return "";
            }
        },
        searchNow: _.debounce(function() {
            let keyW = this.searchKey;

            if (keyW.length) {
                this.isSearching = true;
                Promise.all(
                    this.copilots.map((rbt) => {
                        return apolloProvider.clients.copilotAuth
                            .query({
                                query: queryStr.messagesHasConversation,
                                variables: {
                                    copilot: rbt.name,
                                    keyword: keyW,
                                },
                                client: "copilotAuth",
                                fetchPolicy: "no-cache",
                            })
                            .then((res) => {
                                let msgs = res.data.userMessages.edges.map((t) => t.node);
                                if (msgs.length) {
                                    this.searchRecords[rbt.name] = {
                                        num: res.data.userMessages.totalCount,
                                        copilot: rbt,
                                        msgs,
                                    };
                                }
                            });
                        // .catch((e) => {

                        // });
                    })
                ).then((res) => {
                    this.isSearching = false;
                });
            } else {
                this.isSearching = false;
                this.isLoadSearch = false;
            }
        }, 1000),
        linkifyStr: function(str) {
            return str.replaceAll("href=", 'target="_blank" href=');
        },
        prefixLink: function(str) {
            let text = str.replaceAll("“", "**").replaceAll("”", "**");
            // const urlRegex = /(?<!\]\()(https?:\/\/[^\s\)\]]+)(?!\))/g;
            // const urlRegex =
            // const urlRegex = /(?<!\]\()https:\/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:\/[a-zA-Z0-9._~:/?#\[\]@!$&'()*+,;=-]*)?(?!\))/g;
            // const urlRegex =
            const urlRegex =
                /(?<!\]\()(https?:\/\/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:\/[a-zA-Z0-9._~:/?#\[\]@!$&'()+,;=-]*)?)(?!\))/g;
            let matchArr = text.match(urlRegex);

            if (Array.isArray(matchArr)) {
                for (let noteStr of matchArr) {
                    if (noteStr.indexOf("]") === -1 && noteStr.indexOf(")") === -1) {
                        text = text.replace(noteStr, ` [${noteStr}](${noteStr}) `);
                    }
                }
            }

            // while ((match = urlRegex.exec(text)) !== null) {

            //   // && urlRegex.exec(text).join(',').indexOf(']') === -1 && urlRegex.exec(text).join(',').indexOf(')') === -1
            //   // console.log("Detected URL:", match[0]);
            //   text = text.replace(match[0], ` [${match[0]}](${match[0]}) `);
            // }

            return text;
        },
        highLight(str) {
            let keyW = this.searchKey;
            if (keyW) {
                return str.replaceAll(
                    keyW,
                    `<span style='color:var(--lineCube2);'>${keyW}</span>`
                );
            } else {
                return str;
            }
        },
        getMarked(txt) {
            // let aCut = md.render(txt || "");
            let aCut = this.prefixLink(txt || "");
            let bCut = marked(aCut || "");
            // let bCut = aCut
            let cCut = this.linkifyStr(bCut);
            let dCut = this.highLight(cCut);

            return dCut;
            // return aCut;
        },
        copilotUserRecordUnread(it) {
            // // console.log(it)
            let id = it.name;
            let s = this.userRecordsCheck[id];
            if (!s) {
                return false;
            } else {
                let vals = Object.values(s);
                if (vals.length) {
                    //有未读的情况
                    return vals;
                } else {
                    return false;
                }
            }
        },
        resetTimer(real) {
            clearTimeout(timer);
            timer = null;
            timer = setTimeout(() => {
                this.startCheckUserRecordsRead();
            }, 45000);
        },
        startCheckUserRecordsRead() {
            if (this.siteInfo.openManualReply) {
                apolloProvider.clients.helpDesk
                    .query({
                        query: queryStr.chatRecordsCheck,
                        variables: {
                            after: null,
                            before: null,
                            last: 9999,
                        },
                        client: "helpDesk",
                        fetchPolicy: "no-cache",
                    })
                    .then((res) => {
                        //
                        let dataU = res.data.myQuestions.edges.map((t) => t.node);
                        let objRes = {};
                        for (let it of dataU) {
                            if (it.answerItems && it.answerItems.length) {
                                for (let ans of it.answerItems) {
                                    if (!ans.isRead) {
                                        if (!objRes[it.copilotID]) {
                                            objRes[it.copilotID] = {};
                                        }
                                        objRes[it.copilotID][ans.id] = it.content;
                                        // ans.isRead
                                    }
                                }
                            }
                        }

                        // // console.log(objRes, "-----");

                        this.userRecordsCheck = objRes;

                        this.resetTimer();
                    });
            }
        },
        setQue(it) {
            // this.$refs["robot"] ? this.$refs["robot"].setQue(it) : "";
        },
        getAllRobots() {
            return new Promise((resolve, reject) => {
                apolloProvider.clients.copilotAuth
                    .query({
                        query: queryStr.canUseCopilots,
                        variables: {
                            offset: 0,
                            first: 9999
                        },
                        client: "copilotAuth",
                        fetchPolicy: "no-cache",
                    })
                    .then((res) => {
                        if (res.data.userCopilots.edges.length) {
                            let robots = res.data.userCopilots.edges.map((t) => {
                                let node = t.node;
                                // node.source = json2yaml.load(node.source);
                                node.source = yaml.load(node.source);
                                return node;
                            });

                            resolve(robots);
                        } else {
                            resolve([]);
                        }
                    })
                    .catch((e) => {
                        reject(false);
                        if (JSON.stringify(e).includes("copilot")) {
                            this.hasCopilotId = false;
                        }
                    });
            });
        },
        updateConversations(name) {
            // console.log()
            this.getRecentConversations(null, name)
        },
        getRecentConversations(copilotId, iik) {
            this.conversationList = [];
            let copilot = copilotId || this.nowCopilotId;
            apolloProvider.clients.copilotAuth
                .query({
                    query: queryStr.conversations,
                    variables: {
                        first: 6,
                        offset: 0,
                        copilot,
                    },
                    client: "copilotAuth",
                    fetchPolicy: "no-cache",
                })
                .then((res) => {
                    this.conversationList = res.data.userConversations.edges
                        .map((t) => t.node)
                        .sort((a, b) => {
                            return b.updateTime - a.updateTime
                        })

                    if (this.conversationList.length && !this.createNew && !this.$route.path.includes('square')) {
                        let ikquery = this.$route.query.conversation;
                        if ((iik && iik !== true) || ikquery) {
                            let tar = ikquery || iik;
                            this.conversation = this.conversationList.filter(t => t.name === tar)[0];
                            this.conversationId = tar;
                        } else {
                            this.conversation = this.conversationList[0];
                            this.conversationId = this.conversationList[0].name;
                        }

                    } else {
                        this.newChat(true);
                    }


                })
                .catch((e) => {})
                .finally((e) => {});
        },
        getRecentRobots() {
            return new Promise((resolve, reject) => {
                apolloProvider.clients.copilotAuth
                    .query({
                        query: queryStr.recentCopilots,
                        variables: {},
                        client: "copilotAuth",
                        fetchPolicy: "no-cache",
                    })
                    .then((res) => {
                        if (res.data.userRecentCopilots.edges.length) {
                            let robots = res.data.userRecentCopilots.edges.map((t) => {
                                let node = t.node;
                                // node.source = json2yaml.load(node.source);
                                node.source = yaml.load(node.source);
                                return node;
                            });

                            resolve(robots);
                        } else {
                            resolve([]);
                        }
                    })
                    .catch((e) => {
                        reject(false);
                        if (JSON.stringify(e).includes("copilot")) {
                            this.hasCopilotId = false;
                        }
                    })
                    .finally((e) => {});
            });
        },
        async changeQues(vary, num) {
            this.chooseVary = vary;
            let width = document.getElementById("tabScroll").scrollWidth;
            let val = num * width;
            // this.value > val && val =

            this.value = val;
            // console.log(val, num, width)
            document.getElementById("tabScroll").scroll({
                left: val,
                behavior: "smooth",
            });
            // .scrollIntoView(true)
            await this.initQuesChildren();
        },
        getName(it) {
            return it.text;
        },
        initQuesChildren() {
            this.showQues = this.allQues.filter(
                (t) => t.origin.category && t.origin.category.text === this.chooseVary
            );
        },
        initDocuments() {
            let id = this.nowCopilotId;

            apolloProvider.clients.copilotBus
                .query({
                    query: queryStr.ques,
                    variables: {
                        copilot: id,
                        filter: {
                            kind: {
                                eq: "doc",
                            },
                            status: { in: ["6"] },
                            enabled: {
                                eq: true,
                            },
                        },
                        first: 9999,
                        keyword: "",
                        offset: 0,
                    },
                    client: "copilotBus",
                    fetchPolicy: "no-cache",
                })
                .then((res) => {
                    let docTypes = {}
                    let docs = res.data.knowledges.edges.map((t) => t.node)

                    let docCategoriesSort = {};
                    for (let doc of docs) {
                        // console.log(doc.category.text)
                        if (doc.category && doc.category.text) {
                            if (!docTypes[doc.category.text]) {
                                docTypes[doc.category.text] = [doc]
                            } else {
                                docTypes[doc.category.text].push(doc)
                            }

                            docCategoriesSort[doc.category.text] = doc.category.sort

                        }
                    }
                    this.docTypes = docTypes;
                    this.docCategoriesSort = docCategoriesSort;

                    this.docTypeNames = Object.keys(docTypes).sort((a, b) => {
                        let aNum = docCategoriesSort[a] !== null ? docCategoriesSort[a] : Infinity;
                        let bNum = docCategoriesSort[b] !== null ? docCategoriesSort[b] : Infinity;
                        return aNum - bNum;
                    });
                    this.initSquareInfos++;
                });

        },
        initSkillCategories() {
            let id = this.nowCopilotId;

            apolloProvider.clients.copilotBus
                .query({
                    query: queryStr.skillCategories,
                    variables: {
                        copilot: id,
                        keyword: "",
                    },
                    client: "copilotBus",
                    fetchPolicy: "no-cache",
                })
                .then((res) => {
                    this.robotSkillsCategories = res.data.skillCategories;
                });
        },
        initSkills() {
            let id = this.nowCopilotId;
            apolloProvider.clients.copilotBus
                .query({
                    query: queryStr.skills,
                    variables: {
                        copilot: id,
                        keyword: "",
                    },
                    client: "copilotBus",
                    fetchPolicy: "no-cache",
                })
                .then((res) => {
                    let data = res.data.userSkills.edges.map(t => t.node);
                    this.robotSkills = data;
                });
        },
        initQues() {
            this.initDocuments();
            this.initSkills();
            this.initSkillCategories();

            this.initSquareInfos = 0;

            let id = this.nowCopilotId;

            apolloProvider.clients.copilotBus
                .query({
                    query: queryStr.ques,
                    variables: {
                        copilot: id,
                        filter: {
                            kind: {
                                eq: "faq",
                            },
                            common: {
                                eq: true,
                            },
                            enabled: {
                                eq: true,
                            },
                        },
                        first: 9999,
                        keyword: "",
                        offset: 0,
                    },
                    client: "copilotBus",
                    fetchPolicy: "no-cache",
                })
                .then((res) => {
                    this.initSquareInfos++;
                    let queVaries = {};

                    this.allQues = res.data.knowledges.edges.map((t) => {
                        let b = t.node.faq;
                        b.answer = this.getMarked(b.answer);
                        if (t.node.category) {
                            // category	Object
                            queVaries[t.node.category.name] = t.node.category.text;

                            if (!this.chooseVary) {
                                this.chooseVary = t.node.category.text;
                            }
                        }
                        return Object.assign({ origin: t.node }, b);
                    });

                    this.sortedQues = this.allQues.sort((a, b) => {
                        return (a.sort || Infinity) - (b.sort || Infinity)
                    })

                    this.queVaries = queVaries;
                    this.initQuesChildren();
                });
        },
        startCopilot(rbt) {
            sessionStorage.setItem("nowCopilotId", rbt.name);
            this.$router.push({ name: "airobots" });
        },
        changeCurrent(i) {
            this.current = i;
        },
        startCopilotPage(rbt) {
            window.open(location.origin + '/copilot-center/airobotauth?createNew=true&id=' + rbt.name)
        },
        changeCopilot(rbt, e) {
            let findC = this.copilots.filter((t) => t.name == rbt.name);
            if (findC.length == 0) {
                this.copilots.unshift(rbt);
            }
            this.changeCopilotId(rbt);

            if (e) {
                this.isLoadSearch = false;
            }
        },
        changeCopilotIdCommand(e) {
            console.log(e, "----");
            let idx = parseInt(e.split("a_")[1]);

            this.changeCopilot(this.copilots[idx]);
        },
        changeCopilotId(it) {
            const source = it.name;
            this.nowCopilotId = source;
            this.initQues();

            if (this.panel == 2) {
                this.setQue("");
            }

            this.closeLeft();
            this.closeIframeRight();

            // this.newChat()
        },
    },
}