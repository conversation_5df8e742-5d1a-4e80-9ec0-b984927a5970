<template>
  <div class="con">
    <div id="container" v-if="refresh" :class="hideNow && 'hideNow'"></div>
  </div>
</template>
<script>
import {
  Graph,
  treeToGraphData,
  CanvasEvent,
  Circle,
  ExtensionCategory,
  NodeEvent,
  register,
} from "@antv/g6";

import { EventBus } from "../js/eventBus.js";
import { mapState } from "vuex";

export default {
  props: ["dataVary", "dataShow"],
  data() {
    return {
      hideNow: false,
      refresh: true,
    }
  },
  computed: {
    ...mapState(['skillTree','useRobotInfo', 'skillTreeUsed'])
  },
  methods: {
    async initContainerTree(names) {
      // console.log(this.useRobotInfo)
      function isLeafNode(d) {
      return !d.children || d.children.length === 0;
    }


    let self = this;

    const nodes2 = this.dataShow.map((t) => {
      t.id = t.name;
      t.status = "overload";
      t.ip = "bala";
      t.parent = t.categories;
      t.color = "pink";
      t.isLeaf = true;
      t.btnn = 14;
      return t;
    });
    const combinedSkills = this.dataVary.map((t) => {
      t.id = t.name;
      t.status = "overload";
      t.ucolor = "black";
      t.btnn = 7;
      t.children = nodes2.filter((o) => {
        return o.parent[0] === t.name;
      });
      return t;
    });
    const data = Object.assign(this.useRobotInfo, {
      id: this.useRobotInfo.name,
      children: combinedSkills,
      ucolor: "red",
      btnn: 15,
    });

    const graph = new Graph({
      container: "container",
      data: treeToGraphData(data),
      behaviors: [
        "drag-canvas",
        "zoom-canvas",
        "drag-element",
        "collapse-expand",
      ],
      node: {
        style: {
          labelText: (d) => d.id,
          labelPlacement: (d) => (isLeafNode(d) ? "right" : "left"),
          labelBackground: true,
          ports: [{ placement: "right" }, { placement: "left" }],
        },
        animation: {
          enter: false,
        },
      },
      node: {
        type: "html",
        style: {
          innerHTML: (d) => {
            let cls = "nolight";

            // console.log(names, '------')
            if (names && names.length && names.includes(d.text)) {
              cls = "light";
            }
            if (d.ip) {
              return `
<div 
class="custom-btn btn-11 ${cls}"
style="width:340px;padding: 14px;font-size:13px;height: auto;"
>
  <div>
    <div style="font-weight: bold;display:flex;align-items:center;">
        <svg t="1748251184683" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5525" width="16" height="16" class="icon"><path d="M466 819.2c-11 0-18.4 0-25.8-3.8-25.8-11.5-40.5-38.4-33.1-69.1l25.8-153.6h-62.6c-22.1 0-44.2-11.5-55.2-34.6s-11-46.1 3.7-69.1l187.7-261.2c11-11.5 25.8-19.2 40.5-23h7.4c33.1 0 58.8 30.7 58.8 65.3v15.4l-22.1 130.6h62.6c22.1 0 44.2 11.5 55.2 34.6s11 46.1-3.7 65.3L517.5 796.2c-14.7 15.3-33.1 23-51.5 23z m84.6-553L362.9 523.5v7.7c0 3.8 3.7 3.8 3.7 3.8h95.7c7.4 0 14.7 3.8 22.1 11.5 7.4 7.7 7.4 15.4 7.4 23l-29.5 188.3c0 3.8 0 3.8 3.7 7.7h3.7l3.7-3.8 184-280.3c3.7-3.8 0-7.7 0-7.7 0-3.8-3.7-3.8-3.7-3.8h-99.4c-7.4 0-14.7-3.8-18.4-11.5-7.4-7.7-7.4-15.4-7.4-23l29.4-165.1v-3.8c0-3.8-3.7-3.8-3.7-3.8l-3.6 3.5c3.7-3.8 0-3.8 0 0z" fill="var(--theme)" p-id="5526"></path><path d="M512 1024C229.2 1024 0 794.8 0 512S229.2 0 512 0s512 229.2 512 512-229.2 512-512 512z m0-73.9c242 0 438.1-196.1 438.1-438.1S754 73.9 512 73.9 73.9 270 73.9 512 270 950.1 512 950.1z" fill="var(--theme)" p-id="5527"></path></svg>
        <span class="skillName" title="${d.text}">${d.text}</span>
    </div>
    <p style="font-size:12px;color: #999;">${d.description}</p>
  </div>
</div>`;
            } else {
              return `
<div 
style="width:${
                d.text.length + 8
              }em;padding: 12px;text-align:center;"
  class="custom-btn btn-${d.btnn}"
>
  <div>
    <div class="skillName">
      <i class="zo-user ${d.ucolor}"></i>
      ${d.text}
    </div>
    <p style="font-size:12px;color: #999;">${d.source ? d.source.spec.copilot.description : d.description}</p>
  </div>
</div>`;
            }
          },
        },
      },
      // edge: {
      //   type: "quadratic",
      //   style: {
      //     labelBackground: true,
      //     endArrow: true,
      //     badge: true,
      //     stroke: "#F6BD16",
      //     badgeFontFamily: "iconfont",
      //     badgeBackgroundWidth: 12,
      //     badgeBackgroundHeight: 12,
      //   },
      // },
      offset: [1000,10000],
      edge: {
        type: 'cubic-horizontal',
        style: {
          stroke: "#CCC"
        },
      },
      autoFit: {
        type: "view",
        options: {
          direction: 'y' 
          // | 'y' | 'both';
        }

      },
      // padding: [16,16,66,16],

      layout: {
        type: 'indented',
        direction: 'LR',
        dropCap: false,
        // begin: [20, 20], 
        indent: 350,
        getHeight: () => 90,
        preLayout: true,
        // type: 'radial',
        // type: "compact-box",
        // direction: "LR",
        // // type: 'mindmap',
        // // direction: 'H',
        // // preLayout: false,
        // getHeight: function getHeight() {
        //   return 92;
        // },
        // getWidth: function getWidth() {
        //   return 172;
        // },
        // getVGap: function getVGap() {
        //   return 22;
        // },
        // getHGap: function getHGap() {
        //   return 120;
        // },
      },
    });

    graph.render();

    setTimeout(async ()=>{
      await graph.zoomBy(2, {
        duration: 400,
        easing: 'ease-in-out',
      });

      graph.translateTo([-200, 400], {
        duration: 400,
        easing: 'ease-in-out',
      });

   

    // await graph.focusElement(this.useRobotInfo.name, {
    //     duration: 300,
    //     easing: 'ease-in-out',
    // })

    }, 1000)

      
   


//     const [width, height] = graph.getSize();
// console.log('画布宽度:', width);
// console.log('画布高度:', height);

//     await graph.focusElement(this.useRobotInfo.name, {
//   duration: 400,
//   easing: 'ease-in-out',
// });
//     graph.fitView({
//   when: 'overflow',
//   direction: 'y',
// });

// graph.translateTo([-4000, -4000], {
//   duration: 1000,
//   easing: 'ease-in-out',
// });

// graph.fitView({ padding: [50, 50, 50, 50] }); 
// graph.on('afterlayout', () => {
//   const root = graph.findById('root');
//   const bbox = root.getBBox();
//   graph.translate(20 - bbox.minX, 20 - bbox.minY);
// });

// graph.translateBy([-5000, -5000], {
//   duration: 5000,
// });

    graph.on(NodeEvent.CLICK, async(event) => {
      const { target, originalTarget } = event;
      // target,
      // graph.setElementState(target.id, 'selected');

      let str = originalTarget.config.style.innerHTML;
      let titReg = /<span class="skillName".*?(?:>|\/>)/gi;
      let arr = str.match(titReg);
      // console.log(arr);
      if (arr.length) {
        let title = arr[0].split("title=")[1];
        let titleReal = title.replaceAll(">", "").replaceAll('"', "");

        if(!str.includes('nolight')){
          // await graph.focusElement(target.id, {
          //   duration: 300,
          //   easing: 'ease-in-out',
          // })
          // console.log(Object.values(self.skillTreeUsed), titleReal, '=====================')
          if(Object.values(self.skillTreeUsed).includes(titleReal)){
            self.hideNow = true;
            await graph.zoomBy(1.5, {
            duration: 500,
            easing: 'ease-in-out',
          });

          this.$emit("scrollFindSkill", titleReal);

          }else{
          this.$emit("scrollFindSkill", titleReal);

          }
          

        }
       


      }
    });

    }
  },
  watch: {
    skillTree: {
      handler(val, oldVal){

        console.log(val, '--------changed')
        this.refresh = false;
        setTimeout(()=>{
          this.refresh = true;
        });
        setTimeout(()=>{
          // this.refresh = true;
          this.initContainerTree(Object.values(val))
        }, 100);

        
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    let self = this;
    // self.initContainerTree();

    // EventBus.$on("changeSkillTree", function(val){
    //   console.log(val)
    //   self.initContainerTree();
    // });
    
    // const graph = new Graph({
    //   container: 'container',
    //   data: {
    //     nodes: [
    //     {color: 'red',text: "智能定价课程", description: "欢迎使用智能定价课程", id: "01JTMDGAFK6FSAPWVCKTRRV0H5"},
    //       ...this.dataVary.map(t=>{
    //         t.id = t.name;
    //         t.status = 'overload';
    //         t.ip = 'bala';
    //         t.color = 'green'
    //         return t;
    //       }),

    //       ...this.dataShow.map(t=>{
    //         t.id = t.name;
    //         t.status = 'overload';
    //         t.ip = 'bala';
    //         t.parent = t.categories;
    //         t.color = 'pink'
    //         t.isLeaf = true;
    //         return t;
    //       }),

    //     ],
    //     edges: [
    //     ...this.dataVary.map(t=>{
    //       return {
    //           source: "01JTMDGAFK6FSAPWVCKTRRV0H5",
    //           target: t.name
    //         }
    //       }),
    //     ...this.dataShow.map(t=>{
    //         return {
    //           source: t.categories[0],
    //           target: t.name
    //         }
    //       })
    //     ],
    //   },
    //   node: {
    //     type: 'html',
    //     style: {
    //       innerHTML: (d) => {

    //         return `
    // <div
    //   style="
    //     width: ${d.text.length+2}em;
    //     height: 30px;
    //     border-radius: 12px;
    //     background: ${d.color};
    //     border: 1px solid ${d.color};
    //     color: #fff;
    //     user-select: none;
    //     display: flex;
    //     padding: 4px;
    //     font-size: 10px;
    //     text-align: center;
    //     "
    // >
    //   <div style="display: flex;flex-direction: column;flex: 1;">
    //     <div style="font-weight: bold;">
    //       ${d.text}
    //     </div>
    //   </div>
    // </div>`;
    //       },
    //     },
    //   },
    //   edge: {
    //     type: 'quadratic',
    //     style: {
    //       labelBackground: true,
    //       endArrow: true,
    //       badge: true,
    //       stroke: '#F6BD16',
    //       badgeFontFamily: 'iconfont',
    //       badgeBackgroundWidth: 12,
    //       badgeBackgroundHeight: 12,
    //     },
    //   },
    //   autoFit: {
    //   type: 'view',
    // },
    // padding: [16,16,16,16],
    //   layout: {
    //     type: 'radial',
    //     unitRadius: 220,
    //     direction: 'LR',
    //     linkDistance: 200,
    //   },
    //   behaviors: ['drag-element', 'zoom-canvas', 'drag-canvas'],
    // });

    // graph.render();
  },
};
</script>
