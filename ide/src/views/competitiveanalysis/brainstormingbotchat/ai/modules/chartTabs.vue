<template>
  <div style="background: #fff; padding: 14px; height: 100%">
    <!-- {{dataBoard.data}} -->
    <h2 style="color: var(--theme); font-weight: bold">
      {{ dataBoard.rephrasedQuestion }}
    </h2>
    <el-tabs
      style="height: calc(100% - 40px)"
      v-model="activeName"
      @tab-click="handleClick"
    >
      <el-tab-pane label="图表" name="first" v-if="needChart">
        <el-radio-group v-model="chartVary" size="mini">
          <el-radio-button label="pie">饼图</el-radio-button>
          <el-radio-button label="bar">柱状图</el-radio-button>
          <el-radio-button label="line">折线图</el-radio-button>
        </el-radio-group>
        <div
          v-if="refreshChart"
          id="formChart1"
          style="width: 70%; height: 450px; margin-left: 15%; margin-top: 16px"
        ></div>
        <!-- <div id="formChart2" style="width: 50%; height: 200px;float:left;"></div> -->
      </el-tab-pane>
      <el-tab-pane label="原始数据" name="second">
        <div v-if="dataBoard.data.length === 1">
          <!-- {{ dataBoard.data[0].data }}{{ numberKey }} -->
          <h3 style="font-size: 26px; color: var(--theme); text-align: center">
            {{ dataBoard.data[0].data[numberKey[0]] }}
          </h3>
          <p style="text-align: center; font-size: 18px">{{ numberKey[0] }}</p>
        </div>

        <p v-if="dataBoard.data.length === 0">暂无数据</p>

        <table id="formTb" v-if="dataBoard.data.length > 1">
          <thead>
            <tr>
              <!-- <th scope="col">Person</th> -->
              <th :key="it" v-for="it in Object.keys(dataBoard.data[0].data)">
                {{ it }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr :key="it" v-for="it in dataBoard.data">
              <!-- <th scope="row">Chris</th> -->
              <td v-for="kk in Object.keys(dataBoard.data[0].data)">
                {{ it.data[kk] }}
              </td>
            </tr>
          </tbody>
        </table>

        <!-- {{ dataBoard }} -->
      </el-tab-pane>
      <el-tab-pane label="SQL语句" name="third">
        <p
          style="font-size: 18px; padding: 12px; height: 90%; overflow: auto"
          v-html="dataBoard.sql.replaceAll('\n', '<br/>')"
        >
        </p>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import * as echarts from "echarts";
import {
  TitleComponent,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
} from "echarts/components";
import { LineChart } from "echarts/charts";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
echarts.use([
  TitleComponent,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);

export default {
  data() {
    return {
      activeName: "first",
      chartVary: "pie",
      numberKey: [],
      otherKey: [],
      refreshChart: true,
    };
  },
  props: ["dataBoard"],
  computed: {
    needChart() {
      return this.dataBoard.data.length > 1 && this.numberKey.length !== 0;
    },
  },
  mounted() {
    this.parseKeys();
    if (this.needChart) {
      this.activeName = "first";
      this.chartVary = "pie";
      setTimeout(() => {
        this.initOneEchart("formChart1", this.dataBoard, "pie");
      }, 200);
    } else {
      this.activeName = "second";
    }
    // this.initOneEchart('formChart2', this.dataBoard, 'bar')
  },
  watch: {
    dataBoard: {
      handler(val, oldVal) {
        this.parseKeys();
        if (this.needChart) {
          this.activeName = "first";
          this.chartVary = "pie";
          this.refreshChart = false;
          setTimeout(() => {
            this.refreshChart = true;
          }, 10);
          setTimeout(() => {
            this.initOneEchart("formChart1", this.dataBoard, "pie");
          }, 200);
        } else {
          this.activeName = "second";
        }
      },
      deep: true,
    },
    chartVary: {
      handler(val, oldVal) {
        if (val !== oldVal) {
          this.refreshChart = false;
          setTimeout(() => {
            this.refreshChart = true;
          }, 10);
          setTimeout(() => {
            // this.refreshChart = true;
            this.initOneEchart("formChart1", this.dataBoard, val);
          }, 200);
        }
      },
    },
  },
  methods: {
    parseKeys() {
      if (this.dataBoard.data.length) {
        let vals = this.dataBoard.data.map((t) => t.data);
        let valsType = Object.values(vals[0]);
        let hds = Object.keys(vals[0]);

        let numberKey = [];
        let otherKey = [];
        for (let vl in valsType) {
          if (typeof valsType[vl] === "number") {
            numberKey.push(hds[vl]);
          } else {
            otherKey.push(hds[vl]);
          }
        }

        this.numberKey = numberKey;
        this.otherKey = otherKey;
      }
    },
    // 图表
    initOneEchart(id, dataA, vary) {
      let dataJsonParse = this.getChartData(dataA, vary);
      echarts.init(document.getElementById(id)).setOption(dataJsonParse);
    },
    getChartData(dataA, vary) {
      let vals = dataA.data.map((t) => t.data);

      let numberKey = this.numberKey;
      let otherKey = this.otherKey;

      let numberKeyT = numberKey[0];

      let dataB = vals.map((t, idx) => {
        let t_str = "";
        for (let okey of otherKey) {
          t_str = t_str + " " + t[okey];
        }
        return { value: t[numberKeyT], name: t_str };
      });

      console.log(dataB, vals, numberKey[0], "-------");

      let colorsAll = sessionStorage.getItem("colorAll").split(";");

      let themelight = colorsAll
        .filter((t) => t.includes("--themelight:"))[0]
        .split(":")[1];
      let theme = colorsAll
        .filter((t) => t.includes("--theme:"))[0]
        .split(":")[1];

      if (vary === "bar") {
        return {
          xAxis: {
            type: "category",
            data: dataB.map((t) => t.name),
          },
          yAxis: {
            type: "value",
          },
          tooltip: {
            trigger: "item",
          },
          series: [
            {
              data: dataB.map((t) => t.value),
              type: "bar",
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: themelight },
                  { offset: 1, color: theme },
                ]),
              },
            },
          ],
        };
      } else if (vary === "line") {
        return {
          xAxis: {
            data: dataB.map((t) => t.name),
          },
          yAxis: {
            type: "value",
          },
          tooltip: {
            trigger: "axis",
          },
          series: [
            {
              data: dataB.map((t) => t.value),
              color: [theme],
              type: "line",
            },
          ],
        };
      } else {
        return {
          title: {
            text: "",
            left: "center",
          },
          tooltip: {
            trigger: "item",
          },
          legend: {
            orient: "vertical",
            left: "left",
          },
          series: [
            {
              name: dataA.rephrasedQuestion,
              type: "pie",
              radius: "50%",
              data: dataB,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
            },
          ],
        };
      }
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
  },
};
</script>
