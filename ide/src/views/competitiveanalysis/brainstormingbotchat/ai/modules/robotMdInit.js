
import * as echarts from "echarts";
import {
  TitleComponent,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
} from "echarts/components";
import { LineChart } from "echarts/charts";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
echarts.use([
  TitleComponent,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);

import _ from "lodash";

import moment from "moment";
import "moment/locale/zh-cn";
moment.locale("zh-cn");


let setFunc = (obj, key, value) => {
  obj[key] = value;
}

export const robotMdInit = {
    data() {
      return {
        chartBoardDataSet: {}
      }
    },
    methods: {
        addHashParam(key, value, replace, nhref) {
          const currentUrl = nhref;
          const hashIndex = currentUrl.indexOf('#');
          let newHash = '';
        
          if (hashIndex !== -1) {
            const hash = currentUrl.substring(hashIndex + 1);
            const params = new URLSearchParams(hash);
            if (replace) {
              params.set(key, value);
            } else {
              params.append(key, value);
            }
        
            newHash = params.toString();
          } else {
            newHash = `${key}=${value}`;
          }
          const newUrl = currentUrl.split('#')[0] + '#' + newHash;
          return newUrl;
        },
        addHashTimeIframe(ourl, checkOnce){
          let url = "";
          if(this.isChatting || checkOnce){
            url = this.addHashParam('time', '1000000', false, ourl)
          }else{
            url = this.addHashParam('time', (new Date()).getTime(), false, ourl)
          }
          return url;
        },
        postRightBoardNow(e) {
          let btn = e.target;
          let id = btn.attributes.targetId;
          let dataSet = this.chartBoardDataSet[id];

          console.log(dataSet, '---------')

          this.$emit('updateRobotCenter', {key: 'openPostIframe', val:'tabs'})
          this.$emit('updateRobotCenter', {key: 'postRightBoard', val:dataSet})
          this.$emit('updateRobotCenter', {key: 'toolIframe', val:"out-float"})

          if(!this.isMobile()){
            this.$emit('updateRobotCenter', {key: 'toggleMobile', val:true})
          }

          this.$nextTick(()=>{
            this.initBoxHeight();
          })

        },
        postIframesNow(e){
          let btn = e.target;
          let url = btn.attributes.targetUrl;
          let dock = btn.attributes.targetPosition;
          let wid = btn.attributes.targetWidth;
          let tool = btn.attributes.targetTool;
          let checkOnce = btn.attributes.checkOnce;
          let showSkillTool = btn.attributes.showSkillTool;

          // console.log(e, '----已经被点击')

          this.$emit('updateRobotCenter', {key: 'openPostIframe', val:'iframe'})

          if(!this.isMobile()){
            this.$emit('updateRobotCenter', {key: 'toggleMobile', val:true})
          }

          if(dock){
            this.$emit('updateRobotCenter', {key: 'dockIframe', val:dock})
          }

          if(wid){
            this.$emit('updateRobotCenter', {key: 'widIframe', val:wid})
          }


          if(tool === 'in'){
            this.$emit('updateRobotCenter', {key: 'toolIframe', val:""})
          }else{
            this.$emit('updateRobotCenter', {key: 'toolIframe', val: (tool || "out-normal")})
          }

          this.$emit('updateRobotCenter', {key: 'showSkillToolBool', val: false})

          
          
          let urlAddHashTime = this.addHashTimeIframe(url, checkOnce);
          this.$emit('updateRobotCenter', {key: 'postIframe', val:urlAddHashTime})

          this.$nextTick(()=>{
            this.initBoxHeight();
          })
        },
        transformMath:_.throttle(function () {
          let answers = document.querySelectorAll(
            ".chat_box.robot .contentsMarked"
          );
          answers.forEach((it, id) => {
            // if(!it.className.includes('formula')){
            // it.className = it.className + ' formula';
            // if (!window.Global.SITE_THEME) {

            if(it.innerHTML.includes('公式')){
              this.$formula(it);
            }
            // }
            // }
          });
        }, 500),
        scalePic() {
          let self = this;
          setTimeout(() => {
            var imgs = document.querySelectorAll(".txt.marked .contentsMarked img");
    
            imgs.forEach((it, id) => {
    
              if (it.className !== "hasBinded") {
                it.addEventListener("click", function (e) {
                  self.imgTarget = e.target.src;
                  self.triggerModal();
                });
    
                it.className = "hasBinded";
              }
            });

          }, 600);
        },
        initOtherMd() {
            this.initEchart();
            this.initIframe();
            this.initEchartVisit();
            this.initTabs2();
            this.initTabs();
            this.initWhiteBoard(".chat_box.robot .contentsMarked .language-white-board");
            this.initChartBoard();
            this.sendMessage();
          },
          sendMessage() {
            let self = this;
            let whiteBoards = document.querySelectorAll('.chat_box.robot .contentsMarked.isChat .language-send-message');
            whiteBoards.forEach((it, id) => {
              if(it.className.indexOf('pred') === -1){
                it.className = it.className + ' pred';
                let dataT = JSON.parse(it.innerText);
                let le = document.createElement('a');
                le.innerText = dataT.title || '加载中...';
                self.sendParentMessage(dataT.data);
                let whiteP = it.parentElement;
                whiteP.appendChild(le)
              }
              
            });
          },
          initChartBoard() {
            let self = this;
            let chartBoards = document.querySelectorAll(".chat_box.robot .contentsMarked .language-chart-board");
            chartBoards.forEach((it, id) => {
              if(it.className.indexOf('pred') === -1){
                it.className = it.className + ' pred';
                
                let dataT = JSON.parse(it.innerText);

              let le = document.createElement('a');
              le.innerText = dataT.rephrasedQuestion || '点此打开';
              le.className = 'chart-btn';

              let id = this.getId();
              le.attributes.targetId = id;

              setFunc(this.chartBoardDataSet, id, dataT)

              // 新增属性
              le.addEventListener('click', self.postRightBoardNow);

              if(it.parentElement.parentElement.className.includes('isChat')){
                self.postRightBoardNow({target: { attributes: {
                  targetId: id
                }}})
              }

              let chartP = it.parentElement;
                chartP.appendChild(le)
              }
              
            });
          },
          initWhiteBoard(clName) {
            let self = this;
            let whiteBoards = document.querySelectorAll(clName);
            whiteBoards.forEach((it, id) => {
              if(it.className.indexOf('pred') === -1){
                it.className = it.className + ' pred';
                
                let dataT = JSON.parse(it.innerText);

              let le = document.createElement('a');
              le.innerText = dataT.title || '点此打开';
              le.className = 'white-btn';
              le.attributes.targetUrl = dataT.url;

              let attrs = it.parentElement.parentElement.attributes;
              if(attrs.datatime && attrs.datatime.value){
                let tt = moment(this.lastTimeCollection[attrs.datatime.value]).format('YYYY-MM-DD HH:mm');
                le.attributes.dataTime = tt;
              }

              // 新增属性
              le.attributes.targetPosition = dataT.dock;
              le.attributes.targetWidth = dataT.width;
              le.attributes.targetTool = dataT.toolbar;
              le.attributes.showSkillTool = dataT.showSkillTool;

              le.addEventListener('click', self.postIframesNow);

              if(dataT.openAlways || (it.parentElement.parentElement.className.includes('isChat') && dataT.instantOpen)){
                self.postIframesNow({target: { attributes: {
                  targetUrl: dataT.url,
                  targetPosition: dataT.dock,
                  targetWidth: dataT.width,
                  targetTool: dataT.toolbar,
                  showSkillTool: dataT.showSkillTool,
                  checkOnce: true
                }}})

                le.attributes.dataTime = moment().format('YYYY-MM-DD HH:mm');
                // it.parentElement.parentElement.attributes['data-time']
              }

              let whiteP = it.parentElement;
                whiteP.appendChild(le)
              }
              
            });
          },
          initTabs2() {
            // const data = document.querySelectorAll('.contentsMarked code.language-echarts')
            const data = document.querySelectorAll(".contentsMarked code.language-vue-tabs");
      
            for (let tabData of data) {
              let tabDataJson = tabData.innerText;
              let id = "etab_" + this.getId();
      
              // console.log(tabDataJson, '--444-----')
              let tabDiv = document.createElement("div");
              tabDiv.id = id;
              tabDiv.style.width = "400px";
              tabDiv.style.height = "400px";
      
              // 获取父节点的引用
              const parentDiv = tabData.parentNode;
      
              parentDiv.insertBefore(tabDiv, tabData);
              tabData.remove();
      
              let dataJsonIdx = tabDataJson.indexOf("{");
      
              try {
                let dataJsonParse = JSON.parse(
                  tabDataJson
                    .slice(dataJsonIdx)
                    .replaceAll(" ", "")
                    .replaceAll("`", "")
                );
      
                // console.log(dataJsonParse, '--------ppp')
                this.tabsTable.push({ id, dataJsonParse });
      
                setTimeout(() => {
                  const tabsTable = document.getElementById("etabs_" + id);
                  // console.log(tabsTable, '-----')
                  tabsTable.style.display = "block";
                  parentDiv.insertBefore(tabsTable, tabDiv);
                  tabDiv.remove();
                  // document.getElementById(id) = document.getElementById('etabs_'+id)
                });
                // console.log(, '解析出来')
                // etabs.init(document.getElementById(id)).setOption(dataJsonParse)
              } catch (e) {
                console.log("解析失败");
              }
      
              // }
            }
          },
          initTabs() {
            // const data = document.querySelectorAll('.contentsMarked code.language-echarts')
            const data = document.querySelectorAll(".contentsMarked code.language-tabs");
            // const data1 = document.querySelectorAll('.contentsMarked code.language-vue-tabs')
      
            for (let tabData of data) {
              let tabDataJson = tabData.innerText;
              let id = "etab_" + this.getId();
              let tabDiv = document.createElement("div");
              tabDiv.id = id;
              tabDiv.style.width = "400px";
              tabDiv.style.height = "400px";
      
              // 获取父节点的引用
              const parentDiv = tabData.parentNode;
      
              parentDiv.insertBefore(tabDiv, tabData);
              tabData.remove();
      
              let dataJsonIdx = tabDataJson.indexOf("[");
              // console.log(dataJsonIdx, tabDataJson.slice(dataJsonIdx), '-------')
      
              // try{
              let dataJsonParse = JSON.parse(
                tabDataJson.slice(dataJsonIdx).replaceAll(" ", "").replaceAll("`", "")
              );
      
              this.tabsTable.push({ id, dataJsonParse });
      
              setTimeout(() => {
                const tabsTable = document.getElementById("etabs_" + id);
                // console.log(tabsTable, '-----')
                tabsTable.style.display = "block";
                parentDiv.insertBefore(tabsTable, tabDiv);
                tabDiv.remove();
                // document.getElementById(id) = document.getElementById('etabs_'+id)
              });
              // console.log(, '解析出来')
              // etabs.init(document.getElementById(id)).setOption(dataJsonParse)
      
              // }catch(e){
              //   console.log('解析失败')
              // }
      
              // }
            }
          },
          initIframe() {
            const data = document.querySelectorAll(".contentsMarked code.language-show-iframe");
      
            // .filter((t)=>t.innerText.startsWith('echarts { '))
            for (let chartData of data) {
              let chartDataJson = chartData.innerText;
              // console.log(chartData.innerText,chartDataJson.includes('echarts {') , '-----')
      
              // if(chartDataJson.includes('echarts {')){
              let dataJsonIdx = chartDataJson.indexOf("{");
              // try{
              let dataJsonParse = JSON.parse(
                chartDataJson
                  .slice(dataJsonIdx)
                  .replaceAll("[", "")
                  .replaceAll("]", "")
                  .replaceAll(")", "")
                  .replaceAll("(", "")
                  .replaceAll(" ", "")
              );
      
              console.log(dataJsonParse);
      
              let id = "iframe_" + this.getId();
              let chartDiv = document.createElement("iframe");
      
              let setHeight = this.getUrlParam(dataJsonParse.url, "singlechartheigh");
              chartDiv.id = id;
              chartDiv.style.width = "min(440px, calc(100vw - 70px))";
              chartDiv.style.height = setHeight || "320px";
              chartDiv.style.border = "none";
              chartDiv.src = dataJsonParse.url;
              chartDiv.allowTransparency = "true";
      
              // 获取父节点的引用
              const parentDiv = chartData.parentNode;
      
              parentDiv.insertBefore(chartDiv, chartData);
              chartData.remove();
            }
          },
          initEchart() {
            const data = document.querySelectorAll(".contentsMarked code.language-echarts");
      
            // .filter((t)=>t.innerText.startsWith('echarts { '))

            // console.log(data, '-----------data')
            for (let chartData of data) {
              let chartDataJson = chartData.innerText;
              // console.log(chartData.innerText,chartDataJson.includes('echarts {') , '-----')
      
              // if(chartDataJson.includes('echarts {')){
      
              let id = "echart_" + this.getId();
              let chartDiv = document.createElement("div");
              chartDiv.id = id;
              chartDiv.style.width = "min(440px, calc(100vw - 70px))";
              chartDiv.style.height = "400px";
      
              // 获取父节点的引用
              const parentDiv = chartData.parentNode;
      
              parentDiv.insertBefore(chartDiv, chartData);
              chartData.remove();
      
              let dataJsonIdx = chartDataJson.indexOf("{");
              // try{
              let dataJsonParse = JSON.parse(
                chartDataJson
                  .slice(dataJsonIdx)
                  .replaceAll(" ", "")
                  .replaceAll("`", "")
              );
      
              // console.log(dataJsonParse)
              echarts.init(document.getElementById(id)).setOption(dataJsonParse);
      
            }
          },
          initEchartVisit() {
            const data = document.querySelectorAll(".contentsMarked code.language-vue-visitCount");
      
            // .filter((t)=>t.innerText.startsWith('echarts { '))
            for (let chartData of data) {
              let chartDataJson = chartData.innerText;
              // console.log(chartData.innerText,chartDataJson.includes('echarts {') , '-----')
      
              // if(chartDataJson.includes('echarts {')){
      
              let id = "echart_" + this.getId();
              let chartDiv = document.createElement("div");
              chartDiv.id = id;
              chartDiv.style.width = "min(440px, calc(100vw - 70px))";
              chartDiv.style.height = "300px";
              chartDiv.style.marginTop = "12px";
      
              // 获取父节点的引用
              const parentDiv = chartData.parentNode;
      
              parentDiv.insertBefore(chartDiv, chartData);
              chartData.remove();
      
              let dataJsonIdx = chartDataJson.indexOf("{");
              // console.log(dataJsonIdx, chartDataJson.slice(dataJsonIdx), '-------')
      
              // try{
              let dataJsonParse = JSON.parse(
                chartDataJson.replaceAll(" ", "").replaceAll("`", "")
              );
      
              let nowData = dataJsonParse.map((t) => Number(t.value));
              // const showIcon = nowData.reduce((prev, cur) => {
              //   if (prev.length) {
              //     let before = prev[prev.length - 1];
              //     if (before == cur) {
              //       prev[prev.length - 1] = undefined;
              //     } else {
              //       before - cur > 0
              //         ? (prev[prev.length - 1] = false)
              //         : (prev[prev.length - 1] = true);
              //     }
      
              //   }
              //   prev.push(cur);
              //   return prev;
              // }, []);
      
              const options = {
                title: {
                  text: "最近一个月流程访问统计",
                },
                xAxis: {
                  type: "category",
                  data: dataJsonParse.map((t) => {
                    let dat = t.label.split("-");
                    return dat[1] + "." + dat[2];
                  }),
                },
                grid: {
                  left: "20",
                  containLabel: true,
                  right: "20",
                  top: "55",
                  bottom: "5",
                },
                tooltip: {
                  trigger: "axis",
                  // trigger: 'item',
                  formatter(params, e) {
                    const [firstElement] = params;
                    // const icon = showIcon[firstElement.dataIndex];
                    // let strIcon = '';
                    // if (icon !== undefined) {
                    //   strIcon = `<i class="arrow ${icon ? "el-icon-top" : "el-icon-bottom"
                    //     }"></i>`;
                    // }
                    const month =
                      dataJsonParse[firstElement.dataIndex].label.split("-");
                    return `<div>
                  <div class="tooltip-title">${firstElement.data
                      }<i class="el-icon-thumb"></i></div>
                  <div class="tooltip-value">${month[0] + "年" + month[1] + "月" + month[2] + "日"
                      }</div>
                </div>`;
                  },
                  className: "echarts-tooltip-diy",
                },
                yAxis: {
                  type: "value",
                },
                series: [
                  {
                    data: nowData,
                    type: "line",
                    smooth: false,
                    showSymbol: false,
                    color: ["#045cff"],
                    areaStyle: {
                      normal: {
                        color: {
                          type: "linear",
                          x: 0,
                          y: 0,
                          x2: 0,
                          y2: 1,
                          colorStops: [
                            { offset: 0, color: "rgba(40, 128, 255,1)" },
                            { offset: 0.6, color: "rgba(40, 128, 255,0.1)" },
                            { offset: 1, color: "rgba(255,255, 255,0)" },
                          ],
                        },
                      },
                    },
                  },
                ],
              };
              echarts.init(document.getElementById(id)).setOption(options);
      
              // }catch(e){
              //   console.log('解析失败')
              // }
      
              // }
            }
          },
    }
}
