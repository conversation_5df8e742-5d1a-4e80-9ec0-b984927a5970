<template>
  <div class="like-btns">
    <div class="like" @click="like" title="满意">
      <!-- <img :src="toSVGDataUrl(likeempty)" v-if="!isLike" />
      <img :src="toSVGDataUrl(likeicon)" v-if="isLike" /> -->
      <!-- <img src="../img/like-empty.svg" v-if="!isLike" />
      <img src="../img/like.svg" v-if="isLike" /> -->
    </div>
    <div
      class="unlike"
      style="transform: rotateX(180deg)"
      @click="unlike"
      title="不满意"
    >
      <el-popover placement="right" width="240" v-model="visible" trigger="manual">
        您的反馈将帮助我们进步~
        <div class="submitReasons">
          <ul>
            <li
              :class="choosedReason.includes(it) && 'active'"
              @click="chooseReason(it)"
              v-for="it in reasons"
              :key="'ree' + it"
            >
              {{ it }}
            </li>
            <!-- <li>转人工</li> -->
          </ul>
          <p>
            <el-input
              type="text"
              size="small"
              style="margin: 12px 0px"
              v-model="inputReason"
              placeholder="其他"
            />
          </p>
          <p class="subBtns" :class="!reasonStr.length && 'noReason'">
            <a class="submit" @click="submitReason">提交</a>
            <!-- <a class="toUser" @click="toUser">转人工</a> -->
          </p>
        </div>

        <template #reference>
          <!-- <img src="../img/like-un-empty.svg" v-if="!isUnLike" />
          <img src="../img/like-un.svg" v-if="isUnLike" /> -->
          <!-- <img :src="toSVGDataUrl(likeunempty)" v-if="!isUnLike" />
          <img :src="toSVGDataUrl(likeun)" v-if="isUnLike" /> -->
        </template>
        
        <!-- <el-button >click 激活</el-button> -->
      </el-popover>
    </div>
  </div>
</template>
<script>
import queryStr from "../news/chat";
import likeempty from "../img/like-empty.svg";
import likeicon from "../img/like.svg";
import likeunempty from "../img/like-un-empty.svg";
import likeun from "../img/like-un.svg";
import {toSVGDataUrl} from '@/utils'

export default {
  data() {
    return {
      likeempty,
      likeicon,
      likeunempty,
      likeun,
      toSVGDataUrl,
      isLike: false,
      isUnLike: false,
      maked: false,
      message: {
        1: "谢谢您的点赞~",
        0: "反馈已收到~",
        2: "已取消反馈"
      },
      reasons: ["格式问题", "逻辑问题", "有害信息", "事实错误", "没有帮助", "答非所问"],
      visible: false,
      choosedReason: [],
      inputReason: null,
      sayed: [],
    };
    // type 0:不满意 1:满意
  },
  mounted() {
    let self = this;
    document.body.addEventListener("click",function(e){
      // // console.log(self.visible, e.target)
      if(self.visible && e.target.className !== 'el-popover__reference'){
        self.visible = false
      }
      // this.visible == false
    }) 
  },
  props: {
    itId: {
      type: String,
      default: () => "",
    },
    idNow: {
      type: String,
      default: () => "",
    },
    copilotId: {
      type: String,
      default: () => "",
    },
    status: {
      type: Number,
      default: () => 23,
    },
  },
  watch: {
    status: {
      handler(val) {
        // if (val !== 0) {
        if (val === 1) {
          this.isLike = true;
          this.isUnLike = false;
        }

        if (val === 0) {
          this.isUnLike = true;
          this.isLike = false;
        }
        // }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    reasonStr() {
      let reasonStr = Object.assign([], this.choosedReason)
      if(this.inputReason){
        reasonStr.push(this.inputReason)
      }
      return reasonStr.join(",")
    }
  },
  methods: {
    like() {
      if(!this.isLike){
        // console.log(!this.isLike, !this.isUnLike, !this.maked, this.idNow);
      // if (!this.isLike && !this.isUnLike && !this.maked && this.idNow) {
      this.isLike = true;
      this.isUnLike = false;
      this.actionLike(1);
      this.maked = true;
      // }

      }else{
        
        this.isLike = false;
        this.actionLike(2);

      }
      

      // // console.log(this.idNow, "----一系列操作");
    },
    submitReason() {
      this.visible = false;
      if (this.reasonStr.length) {
        this.actionLike(0, this.reasonStr);
      }
    },
    toUser() {
      this.$emit('toUser')
    },
    unlike() {
      if(!this.isUnLike){
        this.visible = true;
      // if (!this.isUnLike && !this.isUnLike && !this.maked && this.idNow) {
      this.isLike = false;
      this.isUnLike = true;
      this.actionLike(0);
      this.maked = true;
      // }

      }else{
        this.isUnLike = false;
        this.actionLike(2);
      }
      

      // // console.log(this.idNow, "----一系列操作");
    },
    chooseReason(it) {
      let idx = this.choosedReason.indexOf(it);
      if (idx === -1) {
        this.choosedReason.push(it);
      } else {
        this.choosedReason.splice(idx, 1);
      }
    },
    actionLike(t, reason) {
      apolloProvider.clients.copilot
        .mutate({
          mutation: queryStr.feedBack,
          variables: {
            copilot: this.copilotId,
            message: this.idNow,
            type: t,
            reason: reason ? reason : null,
          },
          client: "copilot",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          // if(this.sayed.indexOf(t) == -1){
            this.$emit("newComment", this.message[t]);
            // this.sayed.push(t);

            // if(t === 0){
            this.$emit("setStatus", this.itId, t)
            // }
          // }
        })
        .catch((e) => {});
      // }
    },
  },
};
</script>
