<template>
  <el-dialog
    title="站点配置"
    :visible.sync="userDrawerVisible"
    append-to-body
    width="800px"
    top="5vh"
  >
    <el-form
      :model="siteInfoForm"
      label-width="200px"
      style="width: 90%; margin: 0px auto"
      label-position="right"
      element-loading-spinner="el-icon-loading"
      v-loading="configFormLoading"
    >
      <el-form-item label="站点名称：" :style="{ width: '450px' }">
        <el-input
          v-model="siteInfoForm.systemName"
          size="small"
          placeholder="请输入..."
          clearable
        />
      </el-form-item>
      <el-form-item label="站点favico：">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :http-request="handleSystemSuccess"
        >
          <el-image
            style="width: 100px; height: 100px"
            v-if="siteInfoForm.systemLogoImageUrl"
            :src="siteInfoForm.systemLogoImageUrl"
            :fit="'scale-down'"
          ></el-image>
          <!-- <img  class="avatar"> -->
          <i v-else class="el-icon-plus avatar-uploader-icon" />
          <i v-if="siteInfoForm.systemLogoImageUrl" class="el-icon-error" @click.stop="()=>{siteInfoForm.systemLogoImageUrl = '';}"/>

        </el-upload>
      </el-form-item>
      <el-form-item label="站点主题色：">
        <el-color-picker v-model="siteInfoForm.siteColor"></el-color-picker>
        
      </el-form-item>
      <el-form-item label="站点Logo：">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :http-request="handleLogoSuccess"
        >
          <el-image
            style="width: 100px; height: 100px"
            v-if="siteInfoForm.systemLogo"
            :src="siteInfoForm.systemLogo"
            :fit="'scale-down'"
          ></el-image>
          <!-- <img  class="avatar"> -->
          <i v-else class="el-icon-plus avatar-uploader-icon" />
          <i v-if="siteInfoForm.systemLogo" class="el-icon-error" @click.stop="()=>{siteInfoForm.systemLogo = '';}"/>
        </el-upload>
      </el-form-item>
      <el-form-item label="V2版banner：">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :http-request="handleBannerSuccess"
        >
          <el-image
            style="width: 100px; height: 100px"
            v-if="siteInfoForm.systemBanner"
            :src="siteInfoForm.systemBanner"
            :fit="'scale-down'"
          ></el-image>
          <!-- <img  class="avatar"> -->
          <i v-else class="el-icon-plus avatar-uploader-icon" />
          <i v-if="siteInfoForm.systemBanner" class="el-icon-error" @click.stop="()=>{siteInfoForm.systemBanner = '';}" />
        </el-upload>

      </el-form-item>

      <el-form-item label="默认机器人图标：">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :http-request="handleSystemSuccessRobot"
        >
          <el-image
            style="width: 100px; height: 100px"
            v-if="siteInfoForm.systemRobotImg"
            :src="siteInfoForm.systemRobotImg"
            :fit="'scale-down'"
          ></el-image>
          <!-- <img  class="avatar"> -->
          <i v-else class="el-icon-plus avatar-uploader-icon" />
          <i v-if="siteInfoForm.systemRobotImg" class="el-icon-error" @click.stop="()=>{siteInfoForm.systemRobotImg = '';}"/>

        </el-upload>
      </el-form-item>

      <el-form-item label="默认用户头像：">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :http-request="handleSystemSuccessUser"
        >
          <el-image
            style="width: 100px; height: 100px"
            v-if="siteInfoForm.systemUserImg"
            :src="siteInfoForm.systemUserImg"
            :fit="'scale-down'"
          ></el-image>
          <!-- <img  class="avatar"> -->
          <i v-else class="el-icon-plus avatar-uploader-icon" />
          <i v-if="siteInfoForm.systemUserImg" class="el-icon-error" @click.stop="()=>{siteInfoForm.systemUserImg = '';}"/>

        </el-upload>
      </el-form-item>


      <el-form-item label="V2版名称：" :style="{ width: '450px' }">
        <el-input
          v-model="siteInfoForm.pageName"
          size="small"
          placeholder="请输入..."
          clearable
        />
      </el-form-item>

      <el-form-item label="是否已部署人工回复：" :style="{ width: '450px' }">
        <el-switch
          style="margin-top:12px;"
          v-model="siteInfoForm.openManualReply"
          size="small"
        />
      </el-form-item>

      <el-form-item label="V2页面隐藏知识库：" :style="{ width: '450px' }">
        <el-switch
          style="margin-top:12px;"
          v-model="siteInfoForm.hiddenQuesAndDocs"
          size="small"
        />
      </el-form-item>

      <el-form-item label="是否在回答中开启思维链：" :style="{ width: '450px' }">
        <el-switch
          style="margin-top:12px;"
          v-model="siteInfoForm.openThoughtSteps"
          size="small"
        />
      </el-form-item>

      <el-form-item label="是否显示命中知识库：" :style="{ width: '450px' }">
        <el-switch
          style="margin-top:12px;"
          v-model="siteInfoForm.showSource"
          size="small"
        />
      </el-form-item>

      

      <el-form-item label="是否已部署企业微信SDK：" :style="{ width: '450px' }">
        <el-switch
          style="margin-top:12px;"
          v-model="siteInfoForm.openWXSDK"
          size="small"
        />
      </el-form-item>

      

      <el-form-item label="默认跳转路由：" :style="{ width: '450px' }">
        <el-input
          v-model="siteInfoForm.redirectUrl"
          size="small"
          placeholder="请输入相对路径，默认是aiexplore"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="userDrawerVisible = false">取 消</el-button>
      <el-button type="primary" @click="mergeParams()">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="js">
  import {mapState} from "vuex";
import gql from 'graphql-tag'

  export default {
    name: "siteInfo",
    data() {
      return {
        userDrawerVisible: false,
        siteInfoForm: {
          systemName: '',
          redirectUrl: '',
          systemLogoImageUrl: '',
          systemRobotImg: '',
          systemUserImg: '',
          systemBanner: '',
          openManualReply: false,
          hiddenQuesAndDocs: false,
          openThoughtSteps: false,
          showSource: false,
          openWXSDK: false,
          pageName: '',
          systemLogo: '',
          siteColor: '#4a7eff'
        },
        optionThemes:[
          {name:'color',text:'彩色'},
          {name:'monochrome',text:'单色'},
          {name:'mix',text:'混色'}
        ]
      }
    },
    watch: {
      editFinished: {
        immediate: true,
        handler(val, oldVal) {
          if(val !== oldVal){
            this.userDrawerVisible = false;
          }
        }
      },
      userDrawerVisible: {
        immediate: true,
        handler(oldV) {
          if (oldV) {
            this.$store.dispatch('manageConfig/getConfigSetting', {name: 'USER'});
          }
        }
      },
      siteInfo: {
        immediate: true,
        handler(val) {
          if (val) {
            this.siteInfoForm = Object.assign(this.siteInfoForm, val);
          }
        }
      }
    },
    computed: {
      ...mapState(['siteInfo', 'editFinished', 'configFormLoading']),
    },
    methods: {
      openForm() {
        this.userDrawerVisible = true
      },
      uploadFile(file) {
            return new Promise((resolve, reject) => {
                apolloProvider.clients.upload.mutate({
                    mutation: gql`mutation upload($file: Upload){
        upload(file:$file){
            id,name,uri,mime
        }
    }`,
                    variables: {
                        file
                    },
                    client: 'upload'
                }).then(res => {
                    resolve(res.data.upload.uri);
                }).catch(error => {
                    reject(error)
                })
            })
        },
      handleSystemSuccess(file) {
        this.uploadFile(file.file).then(res => {
          this.siteInfoForm.systemLogoImageUrl = res;
        })
      },
      handleSystemSuccessUser(file) {
        this.uploadFile(file.file).then(res => {
          this.siteInfoForm.systemUserImg = res;
        })
      },
      handleSystemSuccessRobot(file) {
        this.uploadFile(file.file).then(res => {
          this.siteInfoForm.systemRobotImg = res;
        })
      },
      handleBannerSuccess(file) {
        this.uploadFile(file.file).then(res => {
          this.siteInfoForm.systemBanner = res;
        })
      },
      handleLogoSuccess(file) {
        this.uploadFile(file.file).then(res => {
          this.siteInfoForm.systemLogo = res;
        })
      },
      mergeParams() {
        let infoForm = this.siteInfoForm;
        let params = {
          systemName: infoForm.systemName,
          redirectUrl: infoForm.redirectUrl,
          systemLogoImageUrl: infoForm.systemLogoImageUrl,

          systemRobotImg: infoForm.systemRobotImg,
          systemUserImg: infoForm.systemUserImg,

          systemBanner: infoForm.systemBanner,
          openManualReply: infoForm.openManualReply,
          hiddenQuesAndDocs: infoForm.hiddenQuesAndDocs,
          openThoughtSteps: infoForm.openThoughtSteps,
          showSource: infoForm.showSource,
          
          openWXSDK: infoForm.openWXSDK,
          pageName: infoForm.pageName,
          systemLogo: infoForm.systemLogo,
          siteColor: infoForm.siteColor
        }
        this.$store.dispatch('setSiteInfo', {name: 'copilotcenter', value: JSON.stringify(params)});
      }
    }
  }
</script>
