<template>
    <el-tabs class="tabsTable" style="background: #fff;width: 100%;" v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane 
        :label="item.title" 
        :name="'tbb'+idx"
        :key="'tbb'+idx"
        v-for="item,idx in dataParseData">
        <!-- {{item}} -->
        <a-table
    :columns="item.columns"
    :data-source="item.tableData"
    :bordered="false"
    style="width: 96%;margin:0px auto;--lineCube2: #1890ff;white-space:nowrap;"
  >
  <template slot="action" slot-scope="text, record">
      <el-button size="mini" @click="getUri(text.uri)">查看</el-button>
    </template>
    <!-- <template slot="name" slot-scope="name"> {{ name.first }} {{ name.last }} </template> -->
  </a-table>
      </el-tab-pane>
    </el-tabs>

    
  </template>
  <script>
    export default {
      data() {
        return {
          activeName: 'tbb0',
          titleParse: {
            "cc":"抄送",
            "doing": "进行中",
            "done": "已完成",
            "todo": "待办",
            "apply": "我申请的",
            "approval": "我审批的"
          },
          columns0: [
            {
                title:"事项名",
                dataIndex: "name",
            },
            {
                title:"申请人",
                dataIndex: "owner.name",
            },
            {
                title:"流水号",
                dataIndex: "entry"
            },
            {
    title: '操作',
    key: 'action',
    scopedSlots: { customRender: 'action' },
    width: 110

  },
          ],
          columns1: [
            {
                title:"事项名",
                dataIndex: "name",
            },
            {
                title:"流水号",
                dataIndex: "entry"
            },
            {
    title: '操作',
    key: 'action',
    scopedSlots: { customRender: 'action' },
    width: 110

  },
          ],
          columns2: [
            {
                title:"步骤名",
                dataIndex: "name",
            },
            {
                title:"事项名",
                dataIndex: "process.name",
            },
            {
                title:"流水号",
                dataIndex: "process.entry"
            },
            {
    title: '操作',
    key: 'action',
    scopedSlots: { customRender: 'action' },
    width: 110
  },
          ]
        };
      },
      props:['dataParse'],
      computed: {
        dataParseData() {
            if(this.dataParse.data){
            
            let res = this.dataParse.data;
            let data = [];
            for(let [ikey, it] of Object.entries(res)){
                // console.log(it, '----it1')
                it = it.edges ? it.edges.map(t=>t.node) : it;
                // console.log(it, '----it2')
                data.push({title:this.titleParse[ikey], tableData: it, columns: ikey === 'approval' ? this.columns0 : (ikey === 'todo' ? this.columns2 : this.columns1)})
                // it = it.slice(0,15)
            }

            // console.log(res, data, '----')
            return data;

            }else{
                return this.dataParse
            }
        }
      },
      methods: {
        handleClick(tab, event) {
          console.log(tab, event);
        },
        getUri(uri){
            // '[[https://wf.shmtu.edu.cn/infoplus/form/7411771/render](https://wf.shmtu.edu.cn/infoplus/form/7411771/render)](https://wf.shmtu.edu.cn/infoplus/form/7411771/render)'
            let uriA = uri.replaceAll('[', ' ').replaceAll(']', ' ').replaceAll('(', ' ').replaceAll(')', ' ')
            let uriStr = uriA.split(' ').filter(t=>t);
            window.open(uriStr[0], '_blank')
            
        }
      }
    };
  </script>