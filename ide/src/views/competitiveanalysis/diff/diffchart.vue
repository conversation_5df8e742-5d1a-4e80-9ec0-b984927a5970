<template>
  <div id="chart-show" class="diff-chart-show"></div>
</template>
<script setup>
import { computed, ref,inject,onMounted, watch } from 'vue';
import {getHashParams,versionLabel} from '@/utils'
import * as echarts from 'echarts';
let myChart = null;
const initChart = (data) => {
    const chartDom = document.getElementById('chart-show');
    if (!chartDom) return;
    // 如果图表已存在，则先销毁
    if (myChart) {
        myChart.dispose();
    }
    myChart = echarts.init(chartDom);
    let {xAxisData,ourScoreData,opponentScoreData}=prepareChartData(data);
    const option=chartOptions({xAxisData,ourScoreData,opponentScoreData})
    console.log(option)
    myChart.setOption(option);
};
const prepareChartData=(data)=> {
  const xAxisData = [];       // X轴数据（价值元素）
  const ourScoreData = [];    // 我方得分数据
  const opponentScoreData = []; // 行业得分数据

  // 遍历数据
  data.forEach(topicObj => {
    // 获取所有与"主题"同级的键（排除"主题"本身）
    const keys = Object.keys(topicObj).filter(key => key !== '主题');
    
    // 遍历每个键对应的数组
    keys.forEach(key => {
      const items = topicObj[key];
      if (Array.isArray(items)) {
        // 遍历数组中的每个对象
        items.forEach(item => {
          // 检查价值元素是否存在且不为空
          if (item['价值元素'] && item['价值元素'].trim() !== '') {
            // 处理我方得分
            let ourScore = 0;
            if (item['我方得分'] !== undefined && item['我方得分'] !== null) {
              ourScore = Number(item['我方得分']) || 0;
            }
            
            // 处理行业得分
            let opponentScore = 0;
            if (item['行业得分'] !== undefined && item['行业得分'] !== null) {
              opponentScore = Number(item['行业得分']) || 0;
            }
            
            // 添加到数据中
            xAxisData.push(item['价值元素']);
            ourScoreData.push(ourScore);
            opponentScoreData.push(opponentScore);
          }
        });
      }
    });
  });
  
  return {
    xAxisData,
    ourScoreData,
    opponentScoreData
  };
};
const chartOptions=(chartData)=>{
  return{
    title: {
      text: '价值元素得分对比'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['我方得分', '行业得分'],
      right:"right"
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.xAxisData,
      axisLabel: {
        rotate: 30, // 如果标签太长可以旋转
        interval: 0 // 强制显示所有标签
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 10 // 假设得分范围是0-10，可以根据实际情况调整
    },
    series: [
      {
        name: '我方得分',
        type: 'line',
        data: chartData.ourScoreData,
        itemStyle: {
          color: '#5470C6'
        },
        lineStyle: {
          width: 3
        }
      },
      {
        name: '行业得分',
        type: 'line',
        data: chartData.opponentScoreData,
        itemStyle: {
          color: '#91cd75'
        },
        lineStyle: {
          width: 3
        }
      }
    ]
  }

};
defineExpose({
  initChart
})
</script>