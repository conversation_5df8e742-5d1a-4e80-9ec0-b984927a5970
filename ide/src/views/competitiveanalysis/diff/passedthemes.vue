<template>
    <div class="passed-theme-wrap">
        <div class="passed-single"
        v-for="(item,index) in themelist"
        :key="'passed'+index">
            <div class="ps-name">{{item.name}}</div>
            <div class="ps-text">{{item.text}}</div>
            <div class="ps-tag" v-if="typeof item.tag ==='object'">
                <el-tag type="primary" v-for="(titem,tindex) in item.tag" :key="'ttag'+tindex">{{titem}}</el-tag>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted,nextTick, inject } from 'vue'
let themelist = ref([
    {
        "name": "增加会议及婚礼设施",
        "text": "讨论中提到增设会议及婚礼设施以吸引商务客群。但丽江和府洲际度假酒店已在这一领域占据优势地位，增加设施可能面临激烈的市场竞争。",
        "tag": [
            "外部-竞争强度",
            "内部-市场定位"
        ],
        "type": "否定主题"
    },
    {
        "name": "开发亲子旅游产品",
        "text": "讨论中提出开发针对亲子市场的旅游产品。然而，丽江金茂隐逸酒店·凯悦臻选已经在亲子旅游市场有一定份额，我司可能难以迅速建立竞争优势。",
        "tag": [
            "外部-竞争强度",
            "内部-市场细分"
        ],
        "type": "否定主题"
    }
])
const initThemes=(themes)=>{
    themelist.value=themes
}
defineExpose({
    initThemes
})
</script>