<template>
    <div class="competitive-diamension-wrap"  v-loading="loading">
        <template v-if="false&&versionnoinfo">
            <div class="empty">
                <div class="auto-btn">
                    <el-button type="primary" size="large" @click="toPostMsg(true)">我不熟悉当前行业，请自动执行</el-button>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="version-change">
                <div class="vc-title">
                </div>
                <el-select v-model="currentversion" @change="changeTabView" v-if="false&&viewitems.length>1">
                    <el-option
                    v-for="(item,index) in viewitems"
                    :key="item.id"
                    :label="versionLabel(item,index)"
                    :value="item.id">
                    </el-option>
                </el-select>
            </div>
            <div class="dimension-fields">
              <h2>竞争维度字段</h2>
                <div class="df-input">
                  <div class="rp-input">
                    <el-input
                    placeholder="您可以继续增加竞争维度字段"
                    ref="InputRef"
                    v-model="inputValue"
                    class="w-20"
                    size="large"
                    @keyup.enter="handleInputConfirm"
                    @blur="handleInputConfirm"
                    />
                    <el-button type="primary" @click="handleInputConfirm">添加竞争维度字段</el-button>
                  </div>
                    
                </div>
                <div class="df-tags">
                    <el-tag
                        v-for="(item,index) in fieldslist"
                        :key="item.name+index"
                        :closable="true"
                        :disable-transitions="false"
                        size="large"
                        @close="handleDelete(item)"
                        >
                        {{item.name}}
                    </el-tag>
                </div>
                <div class="df-btn">
                    <el-button class="button-new-tag" type="primary" @click="toPostMsg()">
                    生成竞争分析报告
                    </el-button>
                </div>
            </div>
        </template>
        

    </div>
</template>
<script setup>
import { computed, ref,inject,onMounted, watch, nextTick } from 'vue';
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel,systemsort,debounceCustom} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})

let hasviewid = getHashParams('viewid')
let baseData=ref(
    {
    "fields": [
        {
            "name": "品牌定位"
        },
        {
            "name": "目标客群"
        },
        {
            "name": "硬件设施"
        }
    ]
}
);
let fieldslist =ref([]);

let loading =ref(false);
const versionnoinfo=ref(true);
let dataid =ref('');
const getData=(ischange)=>{
  dataid.value='';
  baseData.value=[];
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      toGetViewData(hasviewid)
    }
};
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
      dataid.value=res?.data?.priceSessionVIew?.id
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            if(chartdatas){
              baseData.value=chartdatas
              countInitInfo()
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
};
// 
const countInitInfo=(init)=>{
    versionnoinfo.value=true;
    fieldslist.value=[]
    console.log('baseData.value',baseData.value)
    if(baseData.value?.fields?.length){
        versionnoinfo.value=false;
        fieldslist.value=baseData.value?.fields;
    }
}
const changeTabView=()=>{
    getData('ischange')
}
getData();
// countInitInfo();//测试


const inputValue = ref('')
const InputRef = ref(null) 

const handleDelete = (item) => {
  fieldslist.value.splice(fieldslist.value.findIndex((aitem)=>aitem.name===item.name), 1);
  preSave()
}
const handleInputConfirm = () => {
  if (inputValue.value) {
      if(!(fieldslist.value.find((aitem)=>aitem.name===inputValue.value))){
        fieldslist.value.push({name:inputValue.value})
        inputValue.value = '';
        preSave()
      }else{
          ElMessage.warning("已存在，不可重复添加")
      }
  }
  
};
const preSave=debounceCustom(()=>{
  // loading.value=true;
  updatePriceSessionView({
    entity:{
      id:dataid.value,
      data:JSON.stringify({
        fields:fieldslist.value
      })
    }
  }).then((res)=>{
    loading.value=false;
    if(res?.data?.updatePriceSessionView){
      // 暂无需处理
    }
  }).catch((err)=>{
    // loading.value=false;
  })
},1000);

const toPostMsg=(isauto)=>{
  console.log('作为子集发起了',{eventname:'triggerChangeEvent',data:{toolname:"生成竞争分析报告",nowtxt:'',sendnow:'true'}})
    parent.parent.postMessage({
        eventname:'triggerChangeEvent',
        data:{
            toolname:"生成竞争分析报告",
            nowtxt:isauto?'[自动执行]':'生成竞争分析报告',
            sendnow:'true'
        }
    });
  return
  if(toolname==='行业竞争维度定义'){
    console.log('作为子集发起了',{eventname:'triggerChangeEvent',data:{toolname:"行业竞争维度定义",nowtxt:'[自动执行]',sendnow:'true'}})
    parent.parent.postMessage({
        eventname:'triggerChangeEvent',
        data:{
            toolname:"行业竞争维度定义",
            nowtxt:'[自动执行]',
            sendnow:'true'
        }
    });
  }else if(toolname==='生成竞争分析报告'){
    console.log('作为子集发起了',{eventname:'triggerChangeEvent',data:{toolname:"生成竞争分析报告",nowtxt:'',sendnow:'true'}})
    parent.parent.postMessage({
        eventname:'triggerChangeEvent',
        data:{
            toolname:"生成竞争分析报告",
            nowtxt:'',
            sendnow:'true'
        }
    });
  }
};
window.addEventListener('message', function(event) {
    const message = event.data;
    let {eventname,data}=message;
    console.log('competitive======我是来自父级的message',message)
    if (eventname === 'parentToChild') {
        if(data.status=='done'){
            getData('ischange')
        }else{
            ElMessage.warning(`${data.toolname}执行${data.status==='stop'?'终止':'出错'}，任务已停止`);
            preCloseAutoTask()
        }
    }
});
</script>