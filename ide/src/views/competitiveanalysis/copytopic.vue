<template>
  <div class="copy-topic-container" v-loading="loading">
    <div class="cti-header" >
      <el-button type="primary" plain size="small" @click="preAdd(-1)">新增</el-button>
    </div>
    <div class="copy-topic-inner">
      <template v-if="blackbordlist?.length">
        <div class="single-copy"
          v-for="(item,index) in blackbordlist"
          :key="'copy'+index">
            <div class="copy-txt" v-if="false">
              {{item}}
            </div>
            <template v-if="typeof item === 'string'">
              <div class="copy-txt">{{item}}</div>
            </template>
            <template v-else>
              <div class="copy-txt">
                <div class="ct-img" v-if="item.avatar">
                  <div class="avavter-chart" v-if="item.istxtavatar" :title="item.avatar">{{item.avatar?.charAt(0)?item.avatar.charAt(0):''}}</div>
                  <img v-else :src="toSVGDataUrl(fimg[item.avatar])"></div>
                <div class="ct-txt">{{item.txt}}</div>
              </div>
            </template>
            <div class="copy-del">
              <el-button type="primary" plain size="small" @click="preAdd(index)">新增</el-button>
              <el-button type="primary" plain size="small" @click="preEdit(index,item)">修改</el-button>
              <el-button type="primary" plain size="small" @click="delCopy(index)">删除</el-button>
            </div>
          </div>
      </template>
      <el-empty v-else description="黑板暂无记录内容" />
    </div>
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch, nextTick } from 'vue';
import {getPriceViewInfo,updatePriceSessionView,executeTool} from '@/assets/api/api.js'
import {getHashParams,versionLabel,systemsort,toSVGDataUrl} from '@/utils'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import avater1 from '@/assets/images/avater/avater1.svg?raw'
import avater2 from '@/assets/images/avater/avater2.svg?raw'
import avater3 from '@/assets/images/avater/avater3.svg?raw'
import avater4 from '@/assets/images/avater/avater4.svg?raw'
import avater5 from '@/assets/images/avater/avater5.svg?raw'
import avater6 from '@/assets/images/avater/avater6.svg?raw'

let fimg=ref({
  avater1,
  avater2,
  avater3,
  avater4,
  avater5,
  avater6,
})
const store = useStore();

let dataid=ref("");
let loading=ref(false);
let baseData=ref([])
let blackbordlist=ref([])

const $emit=defineEmits()
const initCopyTopic=(viewId,blackbord,callback)=>{
  loading.value=false;
  if(blackbord?.length){
    blackbordlist.value=blackbord
  }else{
    getPriceViewInfo({viewId}).then((res)=>{
      loading.value=false;
      dataid.value=res?.data?.priceSessionVIew?.id;
      if(res?.data?.priceSessionVIew?.data){
          let chartdatas=[]
          try{
              chartdatas=JSON.parse(res.data.priceSessionVIew.data)
          }catch (err){
              ElMessage.warning("数据解析出错")
          }
          console.log('chartdatas',chartdatas)
          if(chartdatas){
            baseData.value=chartdatas;
            if(callback){//
              callback()
            }else{
              countInitInfo()
            }
          }else{
              ElMessage.warning("数据解析失败")
          }
      }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
  }
};
const countInitInfo=()=>{
  blackbordlist.value=baseData.value?.blackbord?baseData.value.blackbord:[]
};
const delCopy=(delindex)=>{
  loading.value=true
  $emit('delCopy',delindex)
}
const preEdit=(index,item)=>{
  ElMessageBox.prompt(`修改内容:`, '编辑', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: item.txt,
    inputType: 'textarea',
    inputValidator: (value) => {
      if (!value || value.trim() === '') {
        return '内容不能为空'; 
      }
      return true; 
    },
    inputErrorMessage: '请输入有效内容',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        item.txt=instance.inputValue;
        if(instance.inputValue){
          loading.value=true
          $emit('editCopy',index,item)
        }
        
      }
      done()
    }
  })
};
const preAdd=(index)=>{
  ElMessageBox.prompt(`输入新内容:`, '新增', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: "",
    inputType: 'textarea',
    inputValidator: (value) => {
      if (!value || value.trim() === '') {
        return '内容不能为空'; 
      }
      return true; 
    },
    inputErrorMessage: '请输入有效内容',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        let item={
          txt:instance.inputValue,
          avatar:store.state?.oidcStore?.user?.name,
          istxtavatar:true
        }
        blackbordlist.value.splice(index+1,0,item)
        loading.value=true
        $emit('editCopy',index+1,item)
      }
      done()
    }
  })
};
const stopLoading=()=>{
  loading.value=false
}
defineExpose({
  initCopyTopic,
  stopLoading
})
</script>

