<template>
  <div class="differentiation-container brainstorming-container" v-loading="loading">
    <div class="version-change">
      <h2 @click="testAtt">头脑风暴结果</h2>
      <el-select v-model="currentversion" @change="changeTabView" v-if="false&&viewitems.length>1">
        <el-option
          v-for="(item,index) in viewitems"
          :key="item.id"
          :label="versionLabel(item,index)"
          :value="item.id">
        </el-option>
      </el-select>
    </div>
    <div v-for="(item, index) in datalist" :key="index" class="item-card">
      <label >
        <div class="item-property">
          <div class="item-property-title">
            <input
            type="checkbox"
            :checked="isChecked(item.name)"
            @change="toggleSelection(item.name)" 
            />
            <div class="label-title">{{item.name}}</div>
          </div>
          <div class="value-item-property">
            {{item.text}}
          </div>
        </div>
      </label>
    </div>
    <template v-if="false">
      <h2>已选项目</h2>
      <!-- <pre>{{ checkeddata }}</pre> -->
      <div class="selected-items-container">
        <div v-if="checkeddata.length === 0" class="empty-message">
          暂无选中项目
        </div>
        <template v-else>
          <div v-for="(item, index) in checkeddata" :key="index" class="selected-item">
            <div class="selected-item-header">
              {{item}}
              <span class="property-key" v-if="false">
                <div v-for="(val_val_item, val_val_key) in item" :key="'check_val_item'+val_val_key" class="value-value-item-property">
                  <strong>{{ val_val_key }}:</strong>{{val_val_item}}
                </div>
              </span>
              <button @click="removeItem(index)" class="remove-btn" title="移除">
                ×
              </button>
            </div>
          </div>
        </template>
      </div>
    </template>
    <div class="differentiation-ope"><el-button type="primary" @click="preSave">差异化价值提取</el-button></div>
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch } from 'vue';
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'


// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})


let hasviewid = getHashParams('viewid')
let baseData=ref([
    {
        "name": "特色文化体验主题",
        "text": "通过开展纳西族文化体验日、传统节日庆典及东巴文化体验等活动，增强顾客的文化体验，提升酒店的吸引力，增加顾客黏性和口碑传播，无需大量资金投入即可显著提升顾客满意度。"
    },
    {
        "name": "户外探险与自然徒步主题",
        "text": "利用玉龙雪山的自然资源，设计安全性高且具季节性的户外探险和自然徒步活动，并提供装备租赁服务，满足不同细分市场需求，如家庭游、情侣游等，吸引更多户外爱好者，延长顾客停留时间，提升酒店收益。"
    }
])
const checkeddata = ref([]);
const datalist = ref([
]);
let loading =ref(false)
let dataid =ref('');
const getData=(ischange)=>{
  dataid.value='';
  baseData.value=[];
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      toGetViewData(hasviewid)
    }
};
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
      dataid.value=res?.data?.priceSessionVIew?.id
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            console.log('chartdatas',chartdatas)
            if(chartdatas){
              baseData.value=chartdatas
              countInitInfo()
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
}
const countInitInfo=()=>{
  let data=JSON.parse(JSON.stringify(baseData.value));
  datalist.value=data;
  checkeddata.value=[]
  data.forEach((item)=>{
    if(item.checked&&item.name){
      checkeddata.value.push(item.name)
    }
  });
}

const changeTabView=()=>{
    getData('ischange')
}

// 检查某项是否已被选中
const isChecked = (value) => {
  return checkeddata.value.some((item) => item===value);
};
// countInitInfo()//测试
getData();
// 切换选择状态
const toggleSelection = (value) => {
  const index = checkeddata.value.findIndex((item) => item===value);
  if (index === -1) {
    checkeddata.value.push(value);
  } else {
    // 移除选中项
    checkeddata.value.splice(index, 1);
  }
};

const removeItem = (index) => {
  checkeddata.value.splice(index, 1);
};
const areArraysContentEqual=(arr1, arr2)=>{
    // 首先检查长度是否相同
    if (arr1.length !== arr2.length) {
        return false;
    }
    
    // 创建一个数组的拷贝以便操作
    const arr2Copy = [...arr2];
    
    // 检查arr1中的每个元素是否都在arr2中
    for (const item of arr1) {
        const index = arr2Copy.indexOf(item);
        if (index === -1) {
            return false;
        }
        arr2Copy.splice(index, 1);
    }
    
    return true;
};

const preSave=()=>{
  loading.value=true;
  const data=datalist.value.map((item)=>{
    let newitem={
      name:item.name,
      text:item.text,
    }
    if(checkeddata.value.includes(item.name)){
      newitem.checked=true
    }
    return newitem
  })
  updatePriceSessionView({
    entity:{
      id:dataid.value,
      data:JSON.stringify(data)
    }
  }).then((res)=>{
    // loading.value=false;
    if(res?.data?.updatePriceSessionView){
      if(sessionStorage.getItem('autotask')==='1'){
        loading.value=false;
        toAutoParent()
      }else{
        toParent()
      }
      
    }
  }).catch((err)=>{
    loading.value=false;
    ElMessage.error('发起失败: ' + err)
  })
}
// 
const toAutoParent=()=>{
 
  console.log('自动触发由头脑风暴结果触发啦',{eventname:'autotasktrigger',data:{toolname:'',nowtxt:"头脑风暴结果",sendnow:'true'}})
  parent.postMessage({eventname:'autotasktrigger',data:{toolname:'',nowtxt:"头脑风暴结果",sendnow:'true'}});
}
const toParent=async()=>{
  try {
    // loading.value=false;
    console.log(parent.parent)
    console.log('作为子集发起了',{eventname:'triggerChangeEvent',data:{toolname:'差异化价值提取',nowtxt:"",sendnow:'true'}})
    parent.parent.postMessage({eventname:'triggerChangeEvent',data:{toolname:'差异化价值提取',nowtxt:"",sendnow:'true'}});
  } catch (err) {
    loading.value=false;
    console.error('操作失败，请重试', err)
    ElMessage.error('操作失败，请重试')
  }
};
const testAtt=()=>{
  
  parent.postMessage({eventname:'parentToChild',data:{
     "toolname": "头脑风暴总结",
        "status": "done"
}});
};

window.addEventListener('message', function(event) {
    const message = event.data;
    let {eventname}=message;
    if(eventname === 'autotasktrigger_fromparent'){
        console.log('自动触发---autotasktrigger_fromparent',eventname)
         if(sessionStorage.getItem('autotask')==='1'){
          if(baseData.value?.[0]?.name){
              toggleSelection(baseData.value[0].name)
              preSave()
              // toAutoParent()
            }else{
              //如果没有可选，直接去父级触发
              toAutoParent()
            }
        }
    }
});

</script>

