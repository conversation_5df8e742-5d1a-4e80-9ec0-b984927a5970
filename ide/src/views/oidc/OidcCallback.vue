<template>
  <div>
  </div>
</template>

<script se>
import { mapActions } from 'vuex'

export default {
  name: 'OidcCallback',
  methods: {
    ...mapActions('oidcStore', [
      'oidcSignInCallback'
    ])
  },
  created () {
    this.oidcSignInCallback().then((redirectPath) => {
      console.log('redirectPath',redirectPath)
      this.$router.push(redirectPath)
    }).catch((err) => {
      console.error(err)
      this.$router.push('/signin-oidc-error') // Handle errors any way you want
    })
  }
}
</script>
