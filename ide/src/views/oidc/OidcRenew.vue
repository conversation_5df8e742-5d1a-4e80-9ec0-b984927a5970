<template>
  <div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { vuexOidcProcessSilentSignInCallback } from 'vuex-oidc'
let qkVuexOidcProcessSilentSignInCallback;

export default {
  name: 'OidcRenew',
  mounted () {
    // if (window.proxy && window.proxy.vuexOidc) {
    //     qkVuexOidcProcessSilentSignInCallback = window.proxy.vuexOidc.vuexOidcProcessSilentSignInCallback
    // }else{
    //     qkVuexOidcProcessSilentSignInCallback=vuexOidcProcessSilentSignInCallback
    // }
    vuexOidcProcessSilentSignInCallback()
  }
}
</script>
