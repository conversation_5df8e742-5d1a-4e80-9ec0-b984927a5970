<template>
    <div class="price-chart-wrap">
        <div class="version-change" v-if="false&&viewitems.length>1">
            <h2></h2>
            <div>
                <el-select v-model="currentversion" @change="changeTabView">
                <el-option
                    v-for="(item,index) in viewitems"
                    :key="item.id"
                    :label="versionLabel(item,index)"
                    :value="item.id">
                </el-option>
                </el-select>
            </div>
        </div>
        <div class="price-chart-inner-wrap">
            <div class="excel-area">
                <div class="row-header">
                    <div class="header-item"
                    v-for="(item,index) in rowheaders"
                    :key="item+index">{{item.label?item.label:item.value}}</div>
                </div>
                <div class="row-content">
                    <div class="content-single-row"
                    v-for="(item,index) in rowdatas"
                    :key="item+index">
                        <div class="row-column"
                        v-for="(iitem,iindex) in item"
                        :key="iitem+iindex"
                        >{{iitem}}</div>
                    </div>
                </div>
            </div>
            <div id="chart-show" class="chart-show" ref="chartdom">
            </div>
        </div>
        
    </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick,computed } from 'vue';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
import moment from 'moment'
import {getPriceViewInfo} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage } from 'element-plus'

const route = useRoute();
let hasviewid = ref("");
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
});
let myChart = null;

let rowheaders=ref([
]);
let rowdatas=ref([
])
let baseData=ref({
})
let currentversion=ref("")
hasviewid.value=getHashParams('viewid');

// 初始化图表
let chartdom=ref(null)
const initChart = () => {
    // const chartDom = document.getElementById('chart-show');
    nextTick(()=>{
        const chartDom=chartdom.value
        if (!chartDom) return;
        // 如果图表已存在，则先销毁
        if (myChart) {
            myChart.dispose();
        }
        myChart = echarts.init(chartDom);
        getChartData()
    })
    
};

// 更新图表数据
const updateChart = (linedata=[],scatterdata=[],subtext) => {
    
    if (!myChart) return;
    const option = {
        "title": {
            "text": "需求价格弹性分析",
            "subtext":subtext?subtext: "点弹性测量特定价格点的瞬时弹性，计算公式为(dQ/dP)*(P/Q)",
            "left": "center",
            "top": 10,
            "subtextStyle": {
            "padding": [5, 0, 0, 0]
            }
        },
        "tooltip": {
            "trigger": "item",
            "formatter": function(params) {
            if (params.seriesName === '需求曲线') {
                return `价格: ${params.data[0]?.toFixed(1)}<br/>销量: ${params.data[1]?.toFixed(0)}`;
            } else {
                const data = params.data;
                return `价格点: ${data.price_point.toFixed(1)}<br/>
                        销量: ${data.quantity_point.toFixed(0)}<br/>
                        弹性系数: ${data.elasticity !== null ? data.elasticity.toFixed(2) : 'N/A'}<br/>
                        弹性类型: ${data.elastic_type}`;
            }
            }
        },
        "legend": {
            "data": ['需求曲线', '弹性点'],
            show:false,
            "top": 70,
            "itemGap": 20
        },
        "xAxis": {
            "name": "价格",
            "nameLocation": "middle",
            "nameGap": 30,
            "type": "value"
        },
        "yAxis": {
            "name": "需求量",
            "nameLocation": "middle",
            "nameGap": 30,
            "type": "value"
        },
        "series": [
            {
                "name": "弹性点",
                "type": "scatter",
                "symbolSize": function(data) {
                    return Math.abs(data.elasticity) * 8 || 8;
                },
                "data":scatterdata,
                "label": {
                    "show": false,
                    "formatter": function(params) {
                        return params.data.elasticity !== null ? params.data.elasticity?.toFixed(2) : "N/A";
                    },
                },
                "emphasis": {
                    "label": {
                        "show": true,  // hover 时显示 label
                        "formatter": function(params) {
                            return params.data.elasticity !== null ? params.data.elasticity?.toFixed(2) : "N/A";
                        },
                        "position": "top"
                    }
                }
            },
            {
                "name": "需求曲线",
                "type": "line",
                "symbol": "none",
                "data": linedata,
                "lineStyle": {
                    "color": "#5470C6",
                    "width": 1
                },
                "smooth": true  // 启用平滑曲线
            },
        ],
        "visualMap": {
            "type": "piecewise",
            "show": true,  // 显示图例以区分弹性类型
            "orient": "horizontal",
            "left": "center",
            "bottom": 10,
            "pieces": [
            {"gt": -1, "lte": 0, "color": "#EE6666", "label": "缺乏弹性(|E|<1)"},
            {"lt": -1, "color": "#91CC75", "label": "富有弹性(|E|>1)"},
            {"value": null, "color": "#999999", "label": "无法计算"}
            ],
            "dimension": 2
        },
        "grid": {
            "top": 100,
            "bottom": 80,  // 为visualMap留出空间
            "left": 60,
            "right": 40
        }
    };
    myChart.setOption(option);
};


const getChartData=()=>{
    if(hasviewid.value){
        getPriceViewInfo({viewId:hasviewid.value}).then((res)=>{
            if(res?.data?.priceSessionVIew?.data){
                let chartdatas=[]
                try{
                    chartdatas=JSON.parse(res.data.priceSessionVIew.data)
                    
                }catch (err){
                    ElMessage.warning("数据解析出错")
                }
                console.log('chartdatas',chartdatas)
                if(chartdatas&&chartdatas[0]){
                        baseData.value=chartdatas[0];
                        formExcelandChart()
                    }else{
                        ElMessage.warning("数据解析失败")
                    }
            }
        }).catch((err)=>{
            ElMessage.warning("获取内容失败"+err)
        });
    }
};

const formExcelandChart=()=>{
    let {excleData,echatsData}=baseData.value;
    rowheaders.value=[];
    rowdatas.value=[];
    let valuelength = []
    console.log('excleData',excleData)
    for(let key in excleData){
        valuelength.push(excleData[key]?.length?excleData[key].length:0)
        if(key.toLocaleLowerCase()==='quantities'){
            // rowheaders.value.push({label:"销量",value:key})
            rowheaders.value.splice(1,0,{label:"销量",value:key})
        }else if(key.toLocaleLowerCase()==='prices'){
            // rowheaders.value.push({label:"价格",value:key})
            rowheaders.value.splice(0,0,{label:"价格",value:key})
        }else{
            rowheaders.value.push({label:key,value:key})
        }
    }
    let valmaxlength = Math.max(...valuelength)
    // 列表值
    Array(valmaxlength).fill(0).forEach((item,index)=>{
        if(!rowdatas.value[index]){
            rowdatas.value[index]=[]
        }
        rowheaders.value.forEach((ritem,rindex)=>{
            if(!rowdatas.value[index][rindex]){
                rowdatas.value[index][rindex]=[]
            }
            let columnval=excleData?.[ritem.value]?excleData[ritem.value]:[];
            rowdatas.value[index][rindex]=columnval[index]?columnval[index]:'-'
        })
    })
    // 图表整理
    let point_elasticities=echatsData['point_elasticities'];
    let linedata = point_elasticities.map((item)=>([item.price_point,item.quantity_point]))
    let scatterdata = point_elasticities.map((item)=>{
        let nitem={
            price_point:item.price_point,
            quantity_point:item.quantity_point,
            elasticity:item.elasticity,
            elastic_type:item.elastic_type,
            value:[item.price_point,item.quantity_point],
        }
        if(item.elasticity){
            if(Math.abs(item.elasticity)>=1){
                nitem.itemStyle= {"color": "#91CC75"}
            }else{
                nitem.itemStyle= {"color": "#EE6666"}
            }
        }else{
            nitem.itemStyle= {"color": "#999999"}
        }
        return nitem
    })
    // 
    updateChart(linedata,scatterdata,echatsData.explanation)
};
const changeTabView=()=>{
    getChartData('ischange')
}
watch(() => route.hash, () => {
  hasviewid.value=getHashParams('viewid');
  initChart();
});
initChart();
</script>
