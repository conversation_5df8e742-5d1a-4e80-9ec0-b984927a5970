<template>
    <div class="price-chart-wrap">
        <div class="excel-area">
            <div class="row-header">
                <div class="header-item"
                v-for="(item,index) in rowheaders"
                :key="item+index">{{item.label?item.label:item.value}}</div>
            </div>
            <div class="row-content">
                <div class="content-single-row"
                v-for="(item,index) in rowdatas"
                :key="item+index">
                    <div class="row-column"
                    v-for="(iitem,iindex) in item"
                    :key="iitem+iindex"
                    >{{iitem}}</div>
                </div>
            </div>
        </div>
        <div id="chart-show" class="chart-show">

        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';
import * as echarts from 'echarts';

import {getPriceViewInfo} from '@/assets/api/api.js'
import {getHashParams} from '@/utils'
import { ElMessage } from 'element-plus'

let hasviewid = getHashParams('viewid')
const price = ref(50);
let myChart = null;

let rowheaders=ref([
    // {
    //     value:"quantities",
    //     label:"销量"
    // },
    // {
    //     value:"prices",
    //     label:"价格"
    // }
]);
let rowdatas=ref([
    // [100,10],[80,20],[60,30]
])
let baseData=ref({
    "excleData": {
        "quantities": [
            100,
            80,
            60
        ],
        "prices": [
            10,
            20,
        ]
    },
    "echatsData": {
        "point_elasticities": [
            {
                "price_point": 10,
                "quantity_point": 100,
                "elasticity": -0.2,
                "elastic_type": "缺乏弹性"
            },
            {
                "price_point": 20,
                "quantity_point": 80,
                "elasticity": -0.5,
                "elastic_type": "缺乏弹性"
            },
            {
                "price_point": 30,
                "quantity_point": 60,
                "elastic_type": "无法计算（终点）"
            }
        ],
        "explanation": "点弹性测量特定价格点的瞬时弹性，计算公式为(dQ/dP)*(P/Q)"
    }
})

const generateChartData = () => {
    return Array.from({ length: 7 }, () => Math.floor(Math.random() * 51)).map(item => item * price.value);
};
// 初始化图表
const initChart = () => {
    const chartDom = document.getElementById('chart-show');
    if (!chartDom) return;
    // 如果图表已存在，则先销毁
    if (myChart) {
        myChart.dispose();
    }
    myChart = echarts.init(chartDom);
    // getChartData()
    updateChart()
};
let excleData= {
        "quantities_a": [
            500,
            450,
            400
        ],
        "prices_b": [
            200,
            210,
            220
        ],
        "prices_a": [
            100,
            105,
            110
        ],
        "quantities_b": [
            300,
            280,
            260
        ]
    };
    let echartsData= {
        "average_cross_elasticity": -2.3436532507739933,
        "cross_elasticities": [
            {
                "arc_cross_elasticity": -2.157894736842105,
                "product_b_quantity_points": [
                    300,
                    280
                ],
                "product_a_price_points": [
                    100,
                    105
                ],
                "product_a_quantity_points": [
                    500,
                    450
                ],
                "product_b_price_points": [
                    200,
                    210
                ]
            },
            {
                "arc_cross_elasticity": -2.5294117647058822,
                "product_b_quantity_points": [
                    280,
                    260
                ],
                "product_a_price_points": [
                    105,
                    110
                ],
                "product_a_quantity_points": [
                    450,
                    400
                ],
                "product_b_price_points": [
                    210,
                    220
                ]
            }
        ],
        "recommendation": "可以考虑与相关商品进行捆绑销售或联合促销",
        "relationship": "互补品",
        "explanation": "交叉价格弹性测量一个产品需求对另一个产品价格变化的敏感度，正值表示替代品，负值表示互补品"
    }

// 更新图表数据
const updateChart = (linedata=[],scatterdata=[]) => {
    if (!myChart) return;
    const option= {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            legend: {
                data: ['产品A价格', '产品A需求量', '产品B价格', '产品B需求量', '交叉弹性值']
            },
            grid: [
                {
                    left: '10%',
                    right: '8%',
                    height: '35%'
                },
                {
                    left: '10%',
                    right: '8%',
                    top: '55%',
                    height: '35%'
                }
            ],
            xAxis: [
                {
                    type: 'category',
                    axisLabel: {
                        formatter: '区间 {value}'
                    },
                    data: ['区间1', '区间2'],
                    gridIndex: 0
                },
                {
                    type: 'category',
                    axisLabel: {
                        formatter: '区间 {value}'
                    },
                    data: ['区间1', '区间2'],
                    gridIndex: 1
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '价格/需求量',
                    gridIndex: 0
                },
                {
                    type: 'value',
                    name: '交叉弹性值',
                    gridIndex: 1
                }
            ],
            series: [
                {
                    name: '产品A价格',
                    type: 'line',
                    data: [
                        echartsData.cross_elasticities[0].product_a_price_points[0],
                        echartsData.cross_elasticities[1].product_a_price_points[1]
                    ],
                    xAxisIndex: 0,
                    yAxisIndex: 0,
                    symbol: 'diamond',
                    symbolSize: 10,
                    lineStyle: {
                        width: 3
                    }
                },
                {
                    name: '产品A需求量',
                    type: 'line',
                    data: [
                        echartsData.cross_elasticities[0].product_a_quantity_points[0],
                        echartsData.cross_elasticities[1].product_a_quantity_points[1]
                    ],
                    xAxisIndex: 0,
                    yAxisIndex: 0,
                    symbol: 'circle',
                    symbolSize: 10,
                    lineStyle: {
                        width: 3
                    }
                },
                {
                    name: '产品B价格',
                    type: 'line',
                    data: [
                        echartsData.cross_elasticities[0].product_b_price_points[0],
                        echartsData.cross_elasticities[1].product_b_price_points[1]
                    ],
                    xAxisIndex: 0,
                    yAxisIndex: 0,
                    symbol: 'triangle',
                    symbolSize: 10,
                    lineStyle: {
                        width: 3
                    }
                },
                {
                    name: '产品B需求量',
                    type: 'line',
                    data: [
                        echartsData.cross_elasticities[0].product_b_quantity_points[0],
                        echartsData.cross_elasticities[1].product_b_quantity_points[1]
                    ],
                    xAxisIndex: 0,
                    yAxisIndex: 0,
                    symbol: 'rect',
                    symbolSize: 10,
                    lineStyle: {
                        width: 3
                    }
                },
                {
                    name: '交叉弹性值',
                    type: 'bar',
                    data: [
                        echartsData.cross_elasticities[0].arc_cross_elasticity,
                        echartsData.cross_elasticities[1].arc_cross_elasticity
                    ],
                    xAxisIndex: 1,
                    yAxisIndex: 1,
                    itemStyle: {
                        color: function(params) {
                            // 负值为互补品(红色)，正值为替代品(绿色)
                            return params.data < 0 ? '#d14a61' : '#91cc75';
                        }
                    },
                    label: {
                        show: true,
                        position: 'top',
                        formatter: '{c}'
                    }
                }
            ],
            graphic: [
                {
                    type: 'text',
                    left: 'center',
                    top: '50%',
                    style: {
                        text: `平均交叉弹性值: ${echartsData.average_cross_elasticity.toFixed(4)} (${echartsData.relationship})`,
                        fill: '#333',
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                },
                {
                    type: 'text',
                    left: 'center',
                    top: '53%',
                    style: {
                        text: echartsData.explanation,
                        fill: '#666',
                        fontSize: 12
                    }
                },
                {
                    type: 'text',
                    left: 'center',
                    top: '95%',
                    style: {
                        text: `建议: ${echartsData.recommendation}`,
                        fill: '#333',
                        fontSize: 13,
                        fontWeight: 'bold'
                    }
                }
            ]
        };
    myChart.setOption(option);
};


const getChartData=()=>{
    if(hasviewid){
        getPriceViewInfo({viewId:hasviewid}).then((res)=>{
            if(res?.data?.priceSessionVIew?.data){
                let chartdatas=[]
                try{
                    chartdatas=JSON.parse(res.data.priceSessionVIew.data)
                    
                }catch (err){
                    ElMessage.warning("数据解析出错")
                }
                console.log('chartdatas',chartdatas)
                if(chartdatas&&chartdatas[0]){
                        baseData.value=chartdatas[0];
                        formExcelandChart()
                    }else{
                        ElMessage.warning("数据解析失败")
                    }
            }
        }).catch((err)=>{
            ElMessage.warning("获取内容失败"+err)
        });
    }
};
nextTick(()=>{
    initChart();
})

// 整理形成excel和chart
// {
    //     value:"quantities",
    //     label:"销量"
    // },
    // {
    //     value:"prices",
    //     label:"价格"
    // }
const formExcelandChart=()=>{
    let {excleData,echatsData}=baseData.value;
    rowheaders.value=[];
    rowdatas.value=[];
    let valuelength = []
    for(let key in excleData){
        valuelength.push(excleData[key]?.length?excleData[key].length:0)
        if(key.toLocaleLowerCase()==='quantities_a'){
            rowheaders.value.splice(1,0,{label:"销量A",value:key})
            // rowheaders.value.push({label:"销量A",value:key})
        }else if(key.toLocaleLowerCase()==='prices_a'){
            rowheaders.value.splice(0,0,{label:"价格A",value:key})
            // rowheaders.value.push({label:"价格A",value:key})
        }else if(key.toLocaleLowerCase()==='quantities_b'){
            rowheaders.value.splice(3,0,{label:"销量B",value:key})
            // rowheaders.value.push({label:"销量B",value:key})
        }else if(key.toLocaleLowerCase()==='prices_b'){
            rowheaders.value.splice(2,0,{label:"价格B",value:key})
            // rowheaders.value.push({label:"价格B",value:key})
        }else{
            rowheaders.value.push({label:key,value:key})
        }
    }
    let valmaxlength = Math.max(...valuelength)
    console.log('valmaxlength',valmaxlength)
    console.log('Array(valmaxlength)',Array(valmaxlength).fill(0))
    // 列表值
    Array(valmaxlength).fill(0).forEach((item,index)=>{
        if(!rowdatas.value[index]){
            rowdatas.value[index]=[]
        }
        rowheaders.value.forEach((ritem,rindex)=>{
            if(!rowdatas.value[index][rindex]){
                rowdatas.value[index][rindex]=[]
            }
            let columnval=excleData?.[ritem.value]?excleData[ritem.value]:[];
            rowdatas.value[index][rindex]=columnval[index]?columnval[index]:'-'
        })
    })
    // 图表整理
    let point_elasticities=echatsData['point_elasticities'];
    let linedata = point_elasticities.map((item)=>([item.price_point,item.quantity_point]))
    let scatterdata = point_elasticities.map((item)=>{
        let nitem={
            price_point:item.price_point,
            quantity_point:item.quantity_point,
            elasticity:item.elasticity,
            elastic_type:item.elastic_type,
            value:[item.price_point,item.quantity_point],
        }
        if(item.elasticity){
            if(Math.abs(item.elasticity)>=1){
                nitem.itemStyle= {"color": "#91CC75"}
            }else{
                nitem.itemStyle= {"color": "#EE6666"}
            }
        }else{
            nitem.itemStyle= {"color": "#999999"}
        }
        return nitem
    })
    // 
    console.log(linedata,scatterdata,echatsData.explanation)
    updateChart(linedata,scatterdata,echatsData.explanation)
};
// formExcelandChart()


</script>