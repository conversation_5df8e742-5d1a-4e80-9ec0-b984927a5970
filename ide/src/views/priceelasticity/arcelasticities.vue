<template>
    <div class="price-chart-wrap">
        <div class="version-change">
            <h2></h2>
            <div>
                <el-button type="primary" @click="totalSave" v-if="excelhaschaned">计算结果</el-button>
                <el-select v-model="currentversion" @change="changeTabView" style="margin-left:10px"  v-if="false&&viewitems.length>1">
                <el-option
                    v-for="(item,index) in viewitems"
                    :key="item.id"
                    :label="versionLabel(item,index)"
                    :value="item.id">
                </el-option>
                </el-select>
            </div>
        </div>
        <div class="price-chart-inner-wrap" :class="viewitems.length>1?'multi-version':''">
            <div class="excel-tables">
                <div class="excel-tabs">
                    <el-tabs tab-position="left" v-model="tabkey" @tab-change="changeTab">
                        <el-tab-pane label="财务数据" name="financial_price_and_sales"></el-tab-pane>
                        <el-tab-pane label="经济数据" name="economic_price_and_sales"></el-tab-pane>
                        <el-tab-pane label="预测数据" name="predict_price_and_sales"></el-tab-pane>
                    </el-tabs>
                </div>
                <chartcomponent ref="chartrefcomponent" @tableChanged="tableChanged"></chartcomponent>
            </div>
            <div id="chart-show" class="chart-show">
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick,watchEffect } from 'vue';
import * as echarts from 'echarts';

import {getPriceViewInfo,executeTool} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage } from 'element-plus'
import chartcomponent from './chartcomponent.vue'
// 
const viewitems = ref([]);
watchEffect(() => {
  try {
    viewitems.value = JSON.parse(sessionStorage.getItem('currenttabviews') || '[]');
  } catch (err) {
    viewitems.value = [];
  }
});
let loading=ref(false)
let dataid =ref('');
let viewid =ref('')
watchEffect(() => {
  try {
    viewid.value = sessionStorage.getItem('currenttabid') || '';
  } catch (err) {
    viewid.value = '';
  }
});
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})
let hasviewid=ref("")
hasviewid.value=getHashParams('viewid');

let myChart = null;
let chartrefcomponent=ref(null);
let tabkey=ref('economic_price_and_sales')
let baseData=ref({
    "excelData": {
        "quantities": [
            100,
            80,
            60
        ],
        "prices": [
            10,
            20,
        ]
    },
    "echatsData": {
        "arc_elasticities": [
            {
                "price_point": 10,
                "quantity_point": 100,
                "elasticity": -0.2,
                "elastic_type": "缺乏弹性"
            },
            {
                "price_point": 20,
                "quantity_point": 80,
                "elasticity": -0.5,
                "elastic_type": "缺乏弹性"
            },
            {
                "price_point": 30,
                "quantity_point": 60,
                "elastic_type": "无法计算（终点）"
            }
        ],
        "explanation": "点弹性测量特定价格点的瞬时弹性，计算公式为(dQ/dP)*(P/Q)"
    }
})

const generateChartData = () => {
    return Array.from({ length: 7 }, () => Math.floor(Math.random() * 51)).map(item => item * price.value);
};
// 初始化图表
const initChart = () => {
    const chartDom = document.getElementById('chart-show');
    if (!chartDom) return;
    // 如果图表已存在，则先销毁
    if (myChart) {
        myChart.dispose();
    }
    myChart = echarts.init(chartDom);
    getChartData()
};

// 更新图表数据
const updateChart = (series,graphicava) => {
    if (!myChart) return;
    const option = {
        title: {
            text: '多角度价格弧弹性对比分析',
            // subtext: '经济、金融与预测视角的价格弹性比较',
            left: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                const data = params.data;
                return `
                    <div style="font-weight:bold">${params.seriesName}</div>
                    价格区间: ${data.price_range[0]} - ${data.price_range[1]}<br/>
                    数量区间: ${data.quantity_range[0]} - ${data.quantity_range[1]}<br/>
                    弧弹性: ${data.arc_elasticity.toFixed(3)}<br/>
                    弹性类型: ${data.elastic_type}
                `;
            }
        },
        legend: {
            data: ['经济视角', '财务视角', '预测视角'],
            bottom: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true
        },
        xAxis: {
            // type: 'category',
            // data: ranges,
            "type": "value",
            name: '价格区间组',
        },
        yAxis: {
            type: 'value',
            name: '弧弹性值',
            axisLabel: {
                formatter: '{value}'
            }
        },
        series:series,
        graphic: [
            {
                type: 'text',
                left: 'center',
                top: 30,
                style: {
                    // '经济平均: -1.185(富有弹性) | 金融平均: -1.111(富有弹性) | 预测平均: -0.787(缺乏弹性)',
                    text: graphicava,
                    font: '14px',
                    fill: '#666'
                }
            },
            // {
            //     type: 'text',
            //     left: 'center',
            //     top: 50,
            //     style: {
            //         text: '注: 预测视角的价格区间与其他视角不同(右侧刻度)',
            //         font: '12px',
            //         fill: '#f56c6c'
            //     }
            // }
        ]
    };
    console.log('option',option)
    myChart.setOption(option);
};
const getChartData=(ischange)=>{
    
  dataid.value='';
  baseData.value={};
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid.value){
      toGetViewData(hasviewid.value)
    }
};


const toGetViewData=()=>{
    if(hasviewid.value){
        getPriceViewInfo({viewId:hasviewid.value}).then((res)=>{
            if(res?.data?.priceSessionVIew?.data){
                let chartdatas=[]
                try{
                    chartdatas=JSON.parse(res.data.priceSessionVIew.data)
                    
                }catch (err){
                    ElMessage.warning("数据解析出错")
                }
                console.log('chartdatas',chartdatas)
                if(chartdatas){
                    baseData.value=chartdatas
                    formExcelandChart()
                }else{
                    ElMessage.warning("数据解析失败")
                }
            }
        }).catch((err)=>{
            ElMessage.warning("获取内容失败"+err)
        });
    }
};
nextTick(()=>{
    initChart();
})
const formChartData=({arc_elasticities,average_arc_elasticity,elastic_type},label,color,ranges)=>{
    let data=[];
    arc_elasticities.forEach((item)=>{
        let newitem={
            value:[item.price_range[0],item.arc_elasticity.toFixed(3)],
            name:item.price_range.join('-'),
            price_range:item.price_range,
            quantity_range:item.quantity_range,
            arc_elasticity:item.arc_elasticity,
            elastic_type:item.elastic_type,
        }
        // 经济平均: -1.185(富有弹性)
        ranges.push(`${label}平均：${average_arc_elasticity.toFixed(3)}(${elastic_type})`)
        data.push(newitem)

    })
    let serieitem={
                name: label,
                type: 'line',
                smooth: true,
                barGap: 0,
                data,
                itemStyle: {
                    color: color
                },
                label: {
                    show: true,
                    position: 'top',
                    formatter: function(params) {
                        return params.data.arc_elasticity.toFixed(3);
                    }
                }
            }
    return {serieitem}
}
const formExcelandChart=(onlychart)=>{
    let {excelData,chartData}=baseData.value;
    let {economic_chart,financial_chart,predict_chart}=chartData;
    let ranges=[]
    let ecchartdata=formChartData(economic_chart,'经济视角','#c23531',ranges)
    let fcchartdata=formChartData(financial_chart,'财务视角','#2f4554',ranges)
    let pcchartdata=formChartData(predict_chart,'预测视角','#61a0a8',ranges)
    ranges=[...new Set(ranges)]
    console.log('ecchartdata',ecchartdata,ranges)
    let series=[ecchartdata.serieitem,fcchartdata.serieitem,pcchartdata.serieitem]
    
    updateChart(series,ranges.join())
    // 图表展示
    if(!onlychart){
        changeTab()
    }

};
const changeTabView=()=>{
    getChartData('ischange')
}

const tableChanged=(basedata)=>{
    excelhaschaned.value=true;
    baseData.value=JSON.parse(JSON.stringify(basedata))
};
// formExcelandChart()
let excelhaschaned=ref(false)
const changeTab=()=>{
    let {excelData}=baseData.value;
    nextTick(()=>{
        chartrefcomponent.value.formExcelandTable(excelData[tabkey.value],baseData.value,tabkey.value,dataid.value)
    })
}
const totalSave=()=>{
    loading.value=true;
    let {excelData}=baseData.value;
    const args={
        "e_data":{
            ...excelData
        }
    }
    const urlParams = new URLSearchParams(window.parent.location.search);
    let sessionId = urlParams.get("sessionId");
    if (!sessionId) {
        ElMessage.warning("SessionId 获取失败，无法发起计算，请检查");
        return
    }
    executeTool({
        toolName: 'calculate_arc_elasticity',
        sessionId ,
        autoTask: false,
        args
    }).then((res)=>{
        excelhaschaned.value=false;
        loading.value=false;
        if(res?.data?.executeTool){
            // 
            if(res.data.executeTool.isError){
                ElMessage.warning(res.data.executeTool.errorMessage)
            }else if(res?.data?.executeTool){
                if(res.data.executeTool?.url){
                    let hash = res.data.executeTool.url.split('#viewid')[1];
                    hasviewid.value=getHashParams('viewid','#viewid'+hash);
                    currentversion.value=getHashParams('viewid','#viewid'+hash);
                    let views=JSON.parse(JSON.stringify(viewitems.value));
                    viewitems.value.splice(0,0,{
                        ...views[0],
                        index:views[0]+1,
                        createdAt:new Date().getTime(),
                        id:hasviewid.value
                    })
                    sessionStorage.setItem('currenttabid', hasviewid.value);
                    sessionStorage.setItem('currenttabviews', JSON.stringify(views));
                }
                // chart更新
                let chartdatas=res.data.executeTool?.datas
                if(chartdatas&&chartdatas.length){
                baseData.value=chartdatas[0]
                formExcelandChart('onlychart')
                }else{
                    ElMessage.warning("数据解析失败")
                }
            }
        }
    }).catch((err)=>{
        
        loading.value=false;
        ElMessage.error('err: ' + err)
    })
}

</script>