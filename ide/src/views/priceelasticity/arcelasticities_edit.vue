<template>
    <div class="price-chart-wrap price-chart-edit-wrap ">
        <div class="excel-area">
            <div class="row-header">
                <div class="header-item">序号</div>
                <div class="header-item"
                v-for="(item,index) in rowheaders"
                :key="item+index">{{item.label?item.label:item.value}}</div>
                <div class="header-item">操作</div>
            </div>
            <div class="row-content">
                <div class="content-single-row"
                v-for="(item,index) in rowdatas"
                :key="'item'+index">
                <div class="row-column">{{index+1}}</div>
                    <div class="row-column"
                    v-for="(iitem,iindex) in item"
                    :key="'iitem'+iindex"
                    @click="editCell(index, iindex)"
                    >
                        <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iindex">{{iitem}}</span>
                        <el-input 
                            v-else
                            v-model="rowdatas[index][iindex]"
                            @blur="saveEdit"
                            @keyup.enter="saveEdit"
                            ref="inputRef"
                            class="edit-input"
                        />
                    </div>
                <div class="row-column row-edit"><el-icon @click="preAddData(index)"><CirclePlusFilled /></el-icon><el-icon  @click="preDelData(index)"><RemoveFilled /></el-icon></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';

import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,debounceCustom} from '@/utils'
import { ElMessage,ElMessageBox } from 'element-plus'

let hasviewid = getHashParams('viewid')

let rowheaders=ref([]);
let rowdatas=ref([]);
let baseData=ref({"quantities":[100,95],"prices":[10,10.5]})

// 编辑相关状态
const editingCell = ref(null);
const inputRef = ref(null);

let dataid =ref('')
const getChartData=()=>{
    dataid.value='';
    baseData.value={};
    if(hasviewid){
        getPriceViewInfo({viewId:hasviewid}).then((res)=>{
            dataid.value=res?.data?.priceSessionVIew?.id;
            if(res?.data?.priceSessionVIew?.data){
                let chartdatas=[]
                try{
                    chartdatas=JSON.parse(res.data.priceSessionVIew.data)
                    
                }catch (err){
                    ElMessage.warning("数据解析出错")
                }
                console.log('1111',chartdatas)
                if(chartdatas){
                        baseData.value=chartdatas
                        formExcelandChart()
                    }else{
                        ElMessage.warning("数据解析失败")
                    }
            }
        }).catch((err)=>{
            ElMessage.warning("获取内容失败"+err)
        });
    }
};
getChartData()

const formExcelandChart=()=>{
    let excleData=baseData.value;
    rowheaders.value=[];
    rowdatas.value=[];
    let valuelength = []
    for(let key in excleData){
        valuelength.push(excleData[key]?.length?excleData[key].length:0)
        if(key.toLocaleLowerCase()==='quantities'){
            rowheaders.value.push({label:"销量",value:key})
        }else if(key.toLocaleLowerCase()==='prices'){
            rowheaders.value.push({label:"价格",value:key})
        }else{
            rowheaders.value.push({label:key,value:key})
        }
    }
    let valmaxlength = Math.max(...valuelength)
    // 列表值
    Array(valmaxlength).fill(0).forEach((item,index)=>{
        if(!rowdatas.value[index]){
            rowdatas.value[index]=[]
        }
        rowheaders.value.forEach((ritem,rindex)=>{
            if(!rowdatas.value[index][rindex]){
                rowdatas.value[index][rindex]=[]
            }
            let columnval=excleData?.[ritem.value]?excleData[ritem.value]:[];
            rowdatas.value[index][rindex]=columnval[index]?columnval[index]:'-'
        })
    })
    // 图表整理
    
};
// formExcelandChart()
// 编辑单元格
const editCell = (rowIndex, colIndex) => {
    // 排除序号列和操作列
    if(colIndex < 0 || colIndex >= rowheaders.value.length) return;
    
    editingCell.value = {
        row: rowIndex,
        col: colIndex
    };
    
    nextTick(() => {
        if(inputRef.value && inputRef.value[0]) {
            inputRef.value[0].focus();
        }
    });
};

// 保存编辑
const saveEdit = debounceCustom(() => {
    editingCell.value = null;
    // 这里可以添加数据验证逻辑
    updateBaseData('inputchange');
},500)

// 更新baseData
const updateBaseData = (inputchange) => {
    const newBaseData = {};
    rowheaders.value.forEach((header, colIndex) => {
        const columnData = [];
        rowdatas.value.forEach((row, rowIndex) => {
            columnData.push(row[colIndex]/1);
        });
        newBaseData[header.value] = columnData;
    });
    baseData.value = newBaseData;
    if(inputchange=='inputchange'){
        toSaveChange()
    }
};

const preAddData=(index)=>{
    const newRow = rowheaders.value.map(() => 0);
    rowdatas.value.splice(index+1, 0, newRow);
    updateBaseData();
}

const preDelData=(index)=>{
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
      rowdatas.value.splice(index,1);
    updateBaseData('inputchange');
    }).catch(() => {
      // 取消编辑
    })
    
}

const toSaveChange=debounceCustom(()=>{
    updatePriceSessionView({
        entity:{
            id:dataid.value,
            data:JSON.stringify(baseData.value)
        }
    }).then((res)=>{
        if(res?.data?.updatePriceSessionView){
            ElMessage.success('修改成功')
        }
    }).catch((err)=>{
        ElMessage.error('修改失败: ' + err)
    })
})

</script>

