<template>
    <div class="price-chart-wrap" v-loading="loading">
        <div class="version-change" >
            <h2></h2>
            <div>
                <el-button type="primary" @click="totalSave" v-if="excelhaschaned">计算结果</el-button>
                <el-select v-model="currentversion" @change="changeTabView" style="margin-left:10px" v-if="false&&viewitems.length>1">
                <el-option
                    v-for="(item,index) in viewitems"
                    :key="item.id"
                    :label="versionLabel(item,index)"
                    :value="item.id">
                </el-option>
                </el-select>
            </div>
        </div>
        <div class="price-chart-inner-wrap" :class="viewitems.length>1?'multi-version':''">
            <div class="excel-tables">
                <div class="excel-tabs">
                    <el-tabs tab-position="left" v-model="tabkey" @tab-change="changeTab">
                        <el-tab-pane label="财务数据" name="financial_price_and_sales"></el-tab-pane>
                        <el-tab-pane label="经济数据" name="economic_price_and_sales"></el-tab-pane>
                        <el-tab-pane label="预测数据" name="predict_price_and_sales"></el-tab-pane>
                    </el-tabs>
                </div>
                <chartcomponent ref="chartrefcomponent" @tableChanged="tableChanged"></chartcomponent>
            </div>
            <div id="chart-show" class="chart-show" ref="chartdom"></div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick,computed,watchEffect } from 'vue';
import * as echarts from 'echarts';
import { useRoute } from 'vue-router';
import moment from 'moment'
import {getPriceViewInfo,executeTool} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage } from 'element-plus'
import chartcomponent from './chartcomponent.vue'

const route = useRoute();
let hasviewid = ref("");
const viewitems = ref([]);

// 监听 sessionStorage 变化
watchEffect(() => {
  try {
    viewitems.value = JSON.parse(sessionStorage.getItem('currenttabviews') || '[]');
  } catch (err) {
    viewitems.value = [];
  }
});
let myChart = null;

let loading=ref(false)
let baseData=ref({
    
})

let dataid =ref('');

let viewid =ref('')
watchEffect(() => {
  try {
    viewid.value = sessionStorage.getItem('currenttabid') || '';
  } catch (err) {
    viewid.value = '';
  }
});
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})
hasviewid.value=getHashParams('viewid');

// 初始化图表
let chartdom=ref(null)
const initChart = () => {
    // const chartDom = document.getElementById('chart-show');
    nextTick(()=>{
        const chartDom=chartdom.value
        if (!chartDom) return;
        // 如果图表已存在，则先销毁
        if (myChart) {
            myChart.dispose();
        }
        myChart = echarts.init(chartDom);
        getChartData()
        
        // formExcelandChart()//测试
    })
    
};

// 更新图表数据
const updateChart = (alltypes) => {
    let series=[];
    const linestyles=[{
        lineStyle: {
                width: 3
            },
            itemStyle: {
                color: '#5470C6'
            }
    },{
         lineStyle: {
                width: 3
            },
            itemStyle: {
                color: '#91CC75'
            }
    },{
        lineStyle: {
                width: 3,
                type: 'dashed'
            },
            itemStyle: {
                color: '#EE6666'
            }
    }]
    alltypes.forEach((item,index)=>{
        let scatter={
                "name": "弹性点",
                "type": "scatter",
                "symbolSize": function(data) {
                    return Math.abs(data.elasticity) * 8 || 8;
                },
                "data":item.scatterdata,
                "label": {
                    "show": false,
                    "formatter": function(params) {
                        return params.data.elasticity !== null ? params.data.elasticity?.toFixed(2) : "N/A";
                    },
                },
                "emphasis": {
                    "label": {
                        "show": true,  // hover 时显示 label
                        "formatter": function(params) {
                            return params.data.elasticity !== null ? params.data.elasticity?.toFixed(2) : "N/A";
                        },
                        "position": "top"
                    }
                }
            }
        let line={
            "name": "销量曲线",
            "type": "line",
            "symbol": "none",
            "data": item.linedata,
            ...linestyles[index],
            "smooth": true  // 启用平滑曲线
        }
        series.push(scatter)
        series.push(line)
    })
    if (!myChart) return;
    const option = {
        "title": {
            "text": "销量价格弹性分析",
            subtext: '财务 vs 经济 vs 预测',
            // "subtext":subtext?subtext: "点弹性测量特定价格点的瞬时弹性，计算公式为(dQ/dP)*(P/Q)",
            "left": "center",
            "top": 10,
            "subtextStyle": {
            "padding": [5, 0, 0, 0]
            }
        },
        "tooltip": {
            "trigger": "item",
            "formatter": function(params) {
                if (params.seriesName === '销量曲线') {
                    return `价格: ${params.data[0]?.toFixed(1)}<br/>销量: ${params.data[1]?.toFixed(0)}`;
                } else {
                    const data = params.data;
                    return `价格点: ${data.price_point.toFixed(1)}<br/>
                            销量: ${data.quantity_point.toFixed(0)}<br/>
                            弹性系数: ${data.elasticity !== null ? data.elasticity.toFixed(2) : 'N/A'}<br/>
                            弹性类型: ${data.elastic_type}`;
                }
            }
        },
        // "legend": {
        //     // "data": ['销量曲线', '弹性点'],
        //     data: ['财务', '经济', '预测'],
        //     "top": 70,
        //     "itemGap": 20
        // },
        "xAxis": {
            "name": "价格",
            "nameLocation": "middle",
            "nameGap": 30,
            "type": "value"
        },
        "yAxis": {
            "name": "销量",
            "nameLocation": "middle",
            "nameGap": 30,
            "type": "value"
        },
        "series":series,
        "visualMap": {
            "type": "piecewise",
            "show": true,  // 显示图例以区分弹性类型
            "orient": "horizontal",
            "left": "center",
            "bottom": 10,
            "pieces": [
            {"gt": -1, "lte": 0, "color": "#EE6666", "label": "缺乏弹性(|E|<1)"},
            {"lt": -1, "color": "#91CC75", "label": "富有弹性(|E|>1)"},
            {"value": null, "color": "#999999", "label": "无法计算"}
            ],
            "dimension": 4
        },
        "grid": {
            "top": 100,
            "bottom": 80,  // 为visualMap留出空间
            "left": 60,
            "right": 40
        }
    };
    myChart.setOption(option);
};

const getChartData=(ischange)=>{
    
  dataid.value='';
  baseData.value={};
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid.value){
      toGetViewData(hasviewid.value)
    }
};
const toGetViewData=(viewId)=>{
    if(viewId){
        loading.value=true;
        getPriceViewInfo({viewId}).then((res)=>{
            loading.value=false;
            if(res?.data?.priceSessionVIew?.data){
                let chartdatas=[]
                try{
                    chartdatas=JSON.parse(res.data.priceSessionVIew.data)
                    
                }catch (err){
                    ElMessage.warning("数据解析出错")
                }
                if(chartdatas){
                    baseData.value=chartdatas;
                    formExcelandChart()
                }else{
                    ElMessage.warning("数据解析失败")
                }
            }
        }).catch((err)=>{
            loading.value=false;
            ElMessage.warning("获取内容失败"+err)
        });
    }
};
const formChartData=({point_elasticities})=>{
    let linedata = point_elasticities?.map((item)=>([item.price_point,item.quantity_point]))
    let scatterdata = point_elasticities?.map((item)=>{
        let nitem={
            price_point:item.price_point,
            quantity_point:item.quantity_point,
            elasticity:item.elasticity,
            elastic_type:item.elastic_type,
            value:[item.price_point,item.quantity_point],
        }
        if(item.elasticity){
            if(Math.abs(item.elasticity)>=1){
                nitem.itemStyle= {"color": "#91CC75"}
            }else{
                nitem.itemStyle= {"color": "#EE6666"}
            }
        }else{
            nitem.itemStyle= {"color": "#999999"}
        }
        return nitem
    })
    return {linedata,scatterdata}
}
// 
// let formExcelandTable
let chartrefcomponent=ref(null);
let tabkey=ref('economic_price_and_sales')
const formExcelandChart=(onlychart)=>{
    let {excelData,chartData}=baseData.value;
    let {economic_chart,financial_chart,predict_chart}=chartData;
    let ecchartdata=formChartData(economic_chart)
    let fcchartdata=formChartData(financial_chart)
    let pcchartdata=formChartData(predict_chart)
    updateChart([ecchartdata,fcchartdata,pcchartdata])
    // 图表展示
    if(!onlychart){
        changeTab()
    }
    
};
// 来自子集触发
let excelhaschaned=ref(false)
const changeTab=()=>{
    let {excelData}=baseData.value;
    nextTick(()=>{
        chartrefcomponent.value.formExcelandTable(excelData[tabkey.value],baseData.value,tabkey.value,dataid.value)
    })
}
const presavechanged=(presave)=>{
    baseData.value=presave
}
const changeTabView=()=>{
    getChartData('ischange')
}

const tableChanged=(basedata)=>{
    excelhaschaned.value=true;
    baseData.value=JSON.parse(JSON.stringify(basedata))
};
const totalSave=()=>{
    loading.value=true;
    let {excelData}=baseData.value;
    const args={
        "e_data":{
            ...excelData
        }
    }
    const urlParams = new URLSearchParams(window.parent.location.search);
    let sessionId = urlParams.get("sessionId");
    if (!sessionId) {
        ElMessage.warning("SessionId 获取失败，无法发起计算，请检查");
        return
    }
    executeTool({
        toolName: 'calculate_point_elasticity',
        sessionId ,
        autoTask: false,
        args
    }).then((res)=>{
        excelhaschaned.value=false;
        loading.value=false;
        if(res?.data?.executeTool){
            // 
            if(res.data.executeTool.isError){
                ElMessage.warning(res.data.executeTool.errorMessage)
            }else if(res?.data?.executeTool){
                if(res.data.executeTool?.url){
                    let hash = res.data.executeTool.url.split('#viewid')[1];
                    hasviewid.value=getHashParams('viewid','#viewid'+hash);
                    currentversion.value=getHashParams('viewid','#viewid'+hash);
                    let views=JSON.parse(JSON.stringify(viewitems.value));
                    viewitems.value.splice(0,0,{
                        ...views[0],
                        index:views[0]+1,
                        createdAt:new Date().getTime(),
                        id:hasviewid.value
                    })
                    sessionStorage.setItem('currenttabid', hasviewid.value);
                    sessionStorage.setItem('currenttabviews', JSON.stringify(views));
                }
                // chart更新
                let chartdatas=res.data.executeTool?.datas
                if(chartdatas&&chartdatas.length){
                baseData.value=chartdatas[0]
                formExcelandChart('onlychart')
                }else{
                    ElMessage.warning("数据解析失败")
                }
            }
        }
    }).catch((err)=>{
        
        loading.value=false;
        ElMessage.error('err: ' + err)
    })
}

watch(() => route.hash, () => {
  hasviewid.value=getHashParams('viewid');
  initChart();
});
initChart();
</script>