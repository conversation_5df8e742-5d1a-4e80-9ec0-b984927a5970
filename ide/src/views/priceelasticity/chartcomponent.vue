<template>
    <div class="price-chart-edit-inner-wrap ">
        <div class="excel-area">
            <div class="row-header">
                <div class="header-item">序号</div>
                <div class="header-item"
                v-for="(item,index) in rowheaders"
                :key="item+index">{{item.label?item.label:item.value}}</div>
                <div class="header-item">操作</div>
            </div>
            <div class="row-content">
                <div class="content-single-row"
                v-for="(item,index) in rowdatas"
                :key="'item'+index">
                <div class="row-column">{{index+1}}</div>
                <div class="row-column"
                v-for="(iitem,iindex) in item"
                :key="'iitem'+iindex"
                @click="editCell(index, iindex)"
                >
                    <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iindex">{{iitem}}</span>
                    <el-input-number
                        v-else
                        v-model="rowdatas[index][iindex]"
                        class="edit-input"
                        :min="0"
                        :controls="false"
                        @focus="recordOriginalValue(rowdatas[index][iindex],index,iindex)"
                        @blur="saveEdit(rowdatas[index][iindex],index,iindex)"
                        ref="inputRef"
                        @keyup.enter="saveEdit(rowdatas[index][iindex],index,iindex)"
                    />
                </div>
                <div class="row-column row-edit"><el-icon @click="preAddData(index)"><CirclePlusFilled /></el-icon><el-icon  @click="preDelData(index)"><RemoveFilled /></el-icon></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';

import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,debounceCustom} from '@/utils'
import { ElMessage,ElMessageBox } from 'element-plus'

let hasviewid = getHashParams('viewid')

let rowheaders=ref([]);
let rowdatas=ref([]);
let baseData=ref([])
let parentbasedata=ref('')
let tabkey=ref('')
let viewid=ref("")

// 编辑相关状态
const editingCell = ref(null);
const inputRef = ref(null);
const $emit=defineEmits()
const formExcelandTable=(excelData,pparentbasedata,ptabkey,pviewid)=>{
    parentbasedata.value=pparentbasedata;
    tabkey.value=ptabkey;
    viewid.value=pviewid;
    baseData.value=excelData;
    rowheaders.value=[
        {label:"价格",value:'prices'},
        {label:"销量",value:'quantities'},
    ];
    console.log('excelData',excelData)
    rowdatas.value=excelData.map((item)=>({prices:item[0],quantities:item[1]}))
};
defineExpose({
    formExcelandTable
})
// formExcelandTable()
// 编辑单元格
const editCell = (rowIndex, colIndex) => {
    // 排除序号列和操作列
    if(colIndex < 0 || colIndex >= rowheaders.value.length) return;
    
    editingCell.value = {
        row: rowIndex,
        col: colIndex
    };
    nextTick(() => {
        if(inputRef.value && inputRef.value[0]) {
            inputRef.value[0].focus();
        }
    });
};
let beforeval=ref(0)
let beforeindex=ref(0)
let beforeiindex=ref(0)

const recordOriginalValue=(val,index,iiindex)=>{
    beforeval.value=val;
    beforeindex.value=index;
    beforeiindex.value=iiindex;
}
// 保存编辑
const saveEdit = debounceCustom((changed,index,iindex) => {
    editingCell.value = null;
    // 这里可以添加数据验证逻辑
    if(changed&&changed!==beforeval.value){//如果实际值没有发生变化，则无需发起保存
        
    }
    // 
    updateBaseData('inputchange');
},500)

// 更新baseData
const updateBaseData = (inputchange) => {
    let falseflag=false
    baseData.value = [];
    rowdatas.value.forEach((row, rowIndex) => {
        // const price=parseFloat(row.prices)?parseFloat(row.prices):0
        // const quantities=parseFloat(row.quantities)?parseFloat(row.quantities):0
        const price=row.prices
        const quantities=row.quantities
        if(price&&quantities){
            baseData.value.push([price,quantities])
        }else{
            falseflag=true
        }
    });

    if(inputchange=='inputchange'&&(!falseflag)){
        toSaveChange(baseData.value)
    }
};

const preAddData=(index)=>{
    const newRow = {
        prices:0,
        quantities:0
    }
    rowdatas.value.splice(index+1, 0, newRow);
    // updateBaseData();
}

const preDelData=(index)=>{
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
      rowdatas.value.splice(index,1);
        updateBaseData('inputchange');

    }).catch(() => {
      // 取消编辑
    })
    
}

const toSaveChange=debounceCustom(()=>{
    let {excelData,chartData}=parentbasedata.value;
    excelData[tabkey.value]=baseData.value;
    $emit('tableChanged',parentbasedata.value)
    return
    $emit('tableChanged',{
            "e_data":{
                ...excelData
            }})
    let data={
        chartData,
        excelData
    }
    let entity={
        id:viewid.value,
        data:JSON.stringify(data)
    }
    updatePriceSessionView({
        entity
    }).then((res)=>{
        if(res?.data?.updatePriceSessionView){
            // ElMessage.success('修改成功')
            $emit('tableChanged')
        }
    }).catch((err)=>{
        ElMessage.error('数据修改失败: ' + err)
    })
})

</script>
