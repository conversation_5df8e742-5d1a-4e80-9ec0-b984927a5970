<template>
    <div class="price-chart-wrap">
        <div class="excel-area">
            <div class="row-header">
                <div class="header-item"
                v-for="(item,index) in rowheaders"
                :key="item+index">{{item.label?item.label:item.value}}</div>
            </div>
            <div class="row-content">
                <div class="content-single-row"
                v-for="(item,index) in rowdatas"
                :key="item+index">
                    <div class="row-column"
                    v-for="(iitem,iindex) in item"
                    :key="iitem+iindex"
                    >{{iitem}}</div>
                </div>
            </div>
        </div>
        <div id="chart-show" class="chart-show">

        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';
import * as echarts from 'echarts';

import {getPriceViewInfo} from '@/assets/api/api.js'
import {getHashParams} from '@/utils'
import { ElMessage } from 'element-plus'

let hasviewid = getHashParams('viewid')
const price = ref(50);
let myChart = null;

let rowheaders=ref([
    // {
    //     value:"quantities",
    //     label:"销量"
    // },
    // {
    //     value:"prices",
    //     label:"价格"
    // }
]);
let rowdatas=ref([
    // [100,10],[80,20],[60,30]
])
let baseData=ref({
    "excleData": {
        "quantities": [
            100,
            80,
            60
        ],
        "prices": [
            10,
            20,
        ]
    },
    "echatsData": {
        "point_elasticities": [
            {
                "price_point": 10,
                "quantity_point": 100,
                "elasticity": -0.2,
                "elastic_type": "缺乏弹性"
            },
            {
                "price_point": 20,
                "quantity_point": 80,
                "elasticity": -0.5,
                "elastic_type": "缺乏弹性"
            },
            {
                "price_point": 30,
                "quantity_point": 60,
                "elastic_type": "无法计算（终点）"
            }
        ],
        "explanation": "点弹性测量特定价格点的瞬时弹性，计算公式为(dQ/dP)*(P/Q)"
    }
})

const generateChartData = () => {
    return Array.from({ length: 7 }, () => Math.floor(Math.random() * 51)).map(item => item * price.value);
};
// 初始化图表
const initChart = () => {
    const chartDom = document.getElementById('chart-show');
    if (!chartDom) return;
    // 如果图表已存在，则先销毁
    if (myChart) {
        myChart.dispose();
    }
    myChart = echarts.init(chartDom);
    getChartData()
};

// 更新图表数据
const updateChart = (linedata=[],scatterdata=[],subtext) => {
    if (!myChart) return;
    const option = {
            title: {
                text: '价格与销量关系弧弹性分析',
                subtext: subtext?subtext:'弧弹性测量两个价格点之间的平均弹性，使用中点公式计算，避免了起点依赖问题',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    const data = params[0].data;
                    return `价格: ${data[0].toFixed(2)}<br/>销量: ${data[1]}`;
                }
            },
            legend: {
                data: ['价格-销量关系'],
                bottom: 10
            },
            xAxis: {
                type: 'value',
                name: '价格',
                nameLocation: 'middle',
                nameGap: 30,
                axisLabel: {
                    formatter: '{value}'
                }
            },
            yAxis: {
                type: 'value',
                name: '销量',
                nameLocation: 'middle',
                nameGap: 30
            },
            series: [
                {
                    name: '价格-销量关系',
                    type: 'line',
                    symbol: 'circle',
                    symbolSize: 8,
                    data: linedata,
                    markLine: {
                        data: scatterdata,
                        label: {
                            show: true,
                            position: 'middle',
                            formatter: '{b}'
                        },
                        lineStyle: {
                            type: 'dashed'
                        },
                        emphasis: {
                            label: {
                                show: true
                            }
                        }
                    },
                    itemStyle: {
                        color: '#5470C6'
                    },
                    lineStyle: {
                        color: '#5470C6',
                        width: 2
                    }
                }
            ],
            grid: {
                top: 80,
                bottom: 80,
                left: 80,
                right: 80
            },
            animationDuration: 2000
        };
    myChart.setOption(option);
};



const getChartData=()=>{
    if(hasviewid){
        getPriceViewInfo({viewId:hasviewid}).then((res)=>{
            if(res?.data?.priceSessionVIew?.data){
                let chartdatas=[]
                try{
                    chartdatas=JSON.parse(res.data.priceSessionVIew.data)
                    
                }catch (err){
                    ElMessage.warning("数据解析出错")
                }
                console.log('chartdatas',chartdatas)
                if(chartdatas&&chartdatas[0]){
                        baseData.value=chartdatas[0];
                        formExcelandChart()
                    }else{
                        ElMessage.warning("数据解析失败")
                    }
            }
        }).catch((err)=>{
            ElMessage.warning("获取内容失败"+err)
        });
    }
};
nextTick(()=>{
    initChart();
})

const formExcelandChart=()=>{
    let {excleData,echatsData}=baseData.value;
    rowheaders.value=[];
    rowdatas.value=[];
    let valuelength = []
    for(let key in excleData){
        valuelength.push(excleData[key]?.length?excleData[key].length:0)
        if(key.toLocaleLowerCase()==='quantities'){
            // rowheaders.value.push({label:"销量",value:key})
            rowheaders.value.splice(1,0,{label:"销量",value:key})
        }else if(key.toLocaleLowerCase()==='prices'){
            // rowheaders.value.push({label:"价格",value:key})
            rowheaders.value.splice(0,0,{label:"价格",value:key})
        }else{
            rowheaders.value.push({label:key,value:key})
        }
    }
    let valmaxlength = Math.max(...valuelength)
    // 列表值
    Array(valmaxlength).fill(0).forEach((item,index)=>{
        if(!rowdatas.value[index]){
            rowdatas.value[index]=[]
        }
        rowheaders.value.forEach((ritem,rindex)=>{
            if(!rowdatas.value[index][rindex]){
                rowdatas.value[index][rindex]=[]
            }
            let columnval=excleData?.[ritem.value]?excleData[ritem.value]:[];
            rowdatas.value[index][rindex]=columnval[index]?columnval[index]:'-'
        })
    })
    // 图表整理
    let arc_elasticities=echatsData['arc_elasticities'];
    let linedata = excleData?.prices?.map((price, index) => {
            return [price, excleData.quantities[index]];
        });
    let scatterdata = arc_elasticities.map((item, index) => {
        return {
            name: `弧弹性: ${item.arc_elasticity.toFixed(4)} (${item.elastic_type})`,
            xAxis: item.price_range[0],
            yAxis: item.quantity_range[0],
            symbol: 'none',
            label: {
                formatter: `弧弹性 ${item.arc_elasticity.toFixed(4)}\n(${item.elastic_type})`,
                position: 'end',
                distance: [15, 20]
            },
            lineStyle: {
                type: 'dashed',
                color: index === 0 ? '#5470C6' : '#91CC75'
            },
            data: [
                [
                    {coord: [item.price_range[0], item.quantity_range[0]]},
                    {coord: [item.price_range[1], item.quantity_range[1]]}
                ]
            ]
        };
    });
    // 
    updateChart(linedata,scatterdata,echatsData.explanation)
};
// formExcelandChart()


</script>