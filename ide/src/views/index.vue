<template>
  <div class="pricing-model">
    <div class="index-welcome">
      <div class="index-welcome-header">
        <div class="c-header">
          智能定价课程
        </div>
        <div class="c-useinfo">
          <el-avatar :size="30">{{userName?.charAt(0)?userName.charAt(0):''}}</el-avatar>{{userName}}
        </div>
      </div>
      <div class="index-welcome-content">
        <div class="cc-welcome">欢迎使用智能定价课程</div>
        <div class="iwc-wrap">
          <div class="iwc-item"
          v-for="(item,index) in baseData"
          :key="item.id+index"
          :class="[item.active?'':'disabled',item.id===choosed?.id?'choosed':'']"
          @click="changeChoosed(item)">
          <div class="i-title" :title="item.title">{{item.title}}</div>
          <div class="i-detail-wrap">
            <div class="i-image">
              <img :src="item.image" >
            </div>
            <div class="i-detail"  :title="item.description">{{item.description}}</div>
          </div>
          </div>
        </div>
        <div class="file-detail">
          <div class="fi-title">{{choosed.title}}</div>
          <div class="fi-image">
            <img :src="choosed.image">
          </div>
          <div class="fi-detail">
            {{choosed.description}}
          </div>
        </div>
      </div>
      <div class="index-welcome-footer">
        <el-button type="primary" @click="goAIBot" :disabled="!choosed?.id">加载案例</el-button>
      </div>
    </div>
    <div class="triangle-container" ref="triangleContainer">
      <div class="coner-point" >
        <div class="cc-wrap">
          <div class="corner top" ref="topCorner">
            <div class="corner-content corner-content-top">
              价值定价
              <div class="cc-menu">
                <div
                  class="cc-menu-item ccm-top"
                  v-for="(item, index) in topmenu"
                  :key="item.id + index"
                >
                <div class="ccm-title">
                  <img class="img-icon" :src="toSVGDataUrl(fimg.flash_blue)" />
                  <div class="ccmt-label">{{ item.title }}</div>
                </div>
                <div class="ccm-des">{{item.des}}</div>
                </div>
              </div>
            </div>
            
          </div>
          
        </div>
        <div class="cc-wrap">
          <div class="corner left" ref="leftCorner">
            <div class="corner-content corner-content-left">成本定价
              <div class="cc-menu">
                <div
                  class="cc-menu-item ccm-left"
                  v-for="(item, index) in leftmenu"
                  :key="item.id + index"
                >
                <div class="ccm-title">
                  <img class="img-icon" :src="toSVGDataUrl(fimg.flash_green)" />
                  <div class="ccmt-label">{{ item.title }}</div>
                </div>
                <div class="ccm-des">{{item.des}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="cc-wrap">
          <div class="corner right" ref="rightCorner">
            <div class="corner-content corner-content-right">市场定价
              <div class="cc-menu">
                <div
                  class="cc-menu-item ccm-right"
                  v-for="(item, index) in rightmenu"
                  :key="item.id + index"
                >
                <div class="ccm-title">
                  <img class="img-icon" :src="toSVGDataUrl(fimg.flash_red)" />
                  <div class="ccmt-label">{{ item.title }}</div>
                </div>
                <div class="ccm-des">{{item.des}}</div>
                </div>
              </div>
            </div>
          </div>
          
        </div>
        
        
        
      </div>
      <svg class="connectors" width="100%" height="100%">
        <polygon ref="triangleFill" class="triangle-fill" />
        <line ref="topLeftLine" class="connector-line connector-line-ltt" />
        <text ref="topLeftText" class="connector-text">营销战略</text>

        <line ref="topRightLine" class="connector-line" />
        <text ref="topRightText" class="connector-text">竞争强度</text>

        <line ref="leftRightLine" class="connector-line connector-line-rtl" />
        <text ref="leftRightText" class="connector-text">
          供应过剩vs.需求不足
        </text>
      </svg>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted,computed } from "vue";

import { toSVGDataUrl } from "@/utils";
import {caseFiles} from '@/assets/api/api.js'
// oidcStore的store.state
import { useoidcStoreMapState } from '@/hooks/useoidcStoreMapState.js'

import flash_blue from "@/assets/images/flash_blue.svg?raw";
import flash_green from "@/assets/images/flash_green.svg?raw";
import flash_red from "@/assets/images/flash_red.svg?raw";

let userName =computed(() => {
  let {user} =useoidcStoreMapState(['user']);
  return (user.value&&user.value.name)?user.value.name:(user.value?user.value.openid:"")
});

let fimg = ref({
  flash_blue,
  flash_red,
  flash_green
});
const topmenu=[
  {
    title:"头脑风暴",
    des:"和智能体讨论您的想法或让它给您点灵感意见",
    id:"1-1"
  },
  {
    title:"2C市场分析",
    des:"通过提示次让智能体进行2C市场分析",
    id:"1-2"
  },
  {
    title:"差异化价值提取",
    des:"针对您头脑风暴选定的主题，来进行差异化价值分析",
    id:"1-3"
  }
]
const leftmenu=[
  {
    title:"竞争分析提取",
    des:"分析案例，提取自己和对手的基本信息",
    id:"2-1"
  }
]
const rightmenu=[
  {
    title:"提取细分市场信息",
    des:"提取2C细分市场支付意愿分析信息，包括消费者画像、市场规模、价格点、支付概率和预期需求量等关键数据",
    id:"3-1"
  },
  {
    title:"查看财务报表",
    des:"查看原始财务报表",
    id:"3-2"
  },
  {
    title:"财务报表分析",
    des:"从财务视角或者经济学视角提取财务报表信息，包含固定成本、变动成本和撤销数据",
    id:"3-3"
  },
  {
    title:"成本定价计算",
    des:"分别对2C市场分析的预测数据、财务报表提取的财务、经济学财务数据进行成本定价",
    id:"3-4"
  }
]


const triangleContainer = ref(null);
const topCorner = ref(null);
const leftCorner = ref(null);
const rightCorner = ref(null);
const topLeftLine = ref(null);
const topRightLine = ref(null);
const leftRightLine = ref(null);
const topLeftText = ref(null);
const topRightText = ref(null);
const leftRightText = ref(null);
  const triangleFill = ref(null); // 新增的三角形填充引用

onMounted(() => {
  updateConnectors();
  window.addEventListener("resize", updateConnectors);
});

const updateConnectors = () => {
  if (!topCorner.value || !leftCorner.value || !rightCorner.value) return;

  // 获取圆心位置
  const getCenter = (el) => {
    const rect = el.getBoundingClientRect();
    const containerRect = triangleContainer.value.getBoundingClientRect();
    return {
      x: rect.left + rect.width / 2 - containerRect.left,
      y: rect.top + rect.height / 2 - containerRect.top,
    };
  };

  const topCenter = getCenter(topCorner.value);
  const leftCenter = getCenter(leftCorner.value);
  const rightCenter = getCenter(rightCorner.value);

  // 更新连线
  if (topLeftLine.value) {
    topLeftLine.value.setAttribute("x1", topCenter.x);
    topLeftLine.value.setAttribute("y1", topCenter.y);
    topLeftLine.value.setAttribute("x2", leftCenter.x);
    topLeftLine.value.setAttribute("y2", leftCenter.y);
  }

  if (topRightLine.value) {
    topRightLine.value.setAttribute("x1", topCenter.x);
    topRightLine.value.setAttribute("y1", topCenter.y);
    topRightLine.value.setAttribute("x2", rightCenter.x);
    topRightLine.value.setAttribute("y2", rightCenter.y);
  }

  if (leftRightLine.value) {
    leftRightLine.value.setAttribute("x1", leftCenter.x);
    leftRightLine.value.setAttribute("y1", leftCenter.y);
    leftRightLine.value.setAttribute("x2", rightCenter.x);
    leftRightLine.value.setAttribute("y2", rightCenter.y);
  }

  // 更新文字位置
  if (topLeftText.value) {
    const midX = (topCenter.x + leftCenter.x - 180) / 2;
    const midY = (topCenter.y + leftCenter.y) / 2;
    topLeftText.value.setAttribute("x", midX);
    topLeftText.value.setAttribute("y", midY);
  }

  if (topRightText.value) {
    const midX = (topCenter.x + rightCenter.x + 50) / 2;
    const midY = (topCenter.y + rightCenter.y) / 2;
    topRightText.value.setAttribute("x", midX);
    topRightText.value.setAttribute("y", midY);
  }

  if (leftRightText.value) {
    const midX = (leftCenter.x + rightCenter.x - 120) / 2;
    const midY = (leftCenter.y + rightCenter.y + 50) / 2;
    leftRightText.value.setAttribute("x", midX);
    leftRightText.value.setAttribute("y", midY);
  }
  // 更新三角形填充区域
    if (triangleFill.value) {
      triangleFill.value.setAttribute('points', 
        `${topCenter.x},${topCenter.y} ` +
        `${leftCenter.x},${leftCenter.y} ` +
        `${rightCenter.x},${rightCenter.y}`
      );
    }
};
const goAIBot=()=>{
  const urlParams= new URLSearchParams(window.location.search);
  const urlObj = new URL(window.location.href);
  if(!urlParams){
    urlObj.searchParams.append('createNew', true);
    urlObj.searchParams.append('id', '01JWX234YV4CNTRE5FGRNQY6DQ');
    // urlObj.searchParams.append('useMobile', true);
    // urlObj.searchParams.append('sendMsg', true);
  }else{
    let createNew = urlParams.get("createNew");
    let id = urlParams.get("id");
    // let useMobile = urlParams.get("useMobile");
    // let sendMsg = urlParams.get("sendMsg");
    if(!createNew){
      urlObj.searchParams.append('createNew', true);
    }
    if(!id){
      urlObj.searchParams.append('id', '01JWX234YV4CNTRE5FGRNQY6DQ');
    }
    // if(!useMobile){
    //   urlObj.searchParams.append('useMobile', true);
    // }
    // if(!sendMsg){
    //   urlObj.searchParams.append('sendMsg', true);
    // }
  }
  // 
  // console.log('/copilot-center/airobotsquare${urlObj.search}',`/copilot-center/airobotsquare${urlObj.search}`);
  // window.location.href=`/copilot-center/airobotsquare${urlObj.search}`
  window.location.href=`/copilot-center/airobotsquare-v2${urlObj.search}`
};
// 
let baseData=ref([
  
]);
let choosed=ref({});
let loading=ref(false);
const getCaseFiles=()=>{
  loading.value=true;
  baseData.value=[]
  caseFiles().then((res)=>{
    loading.value=false;
      if(res?.data?.caseFiles){
          let chartdatas=res.data.caseFiles;
          if(chartdatas){
            baseData.value=chartdatas;
            changeChoosed()
          }else{
              ElMessage.warning("数据解析失败")
          }
      }
  }).catch((err)=>{
    loading.value=false;
      ElMessage.warning("获取内容失败"+err)
  });
};
const changeChoosed=(item)=>{
  if(item&&(!item?.active)){
    return
  }
  choosed.value={};

  if(item){
    choosed.value=item
  }else{
    let matched = baseData.value?.find((fitem)=>fitem.active);
    if(matched){
      choosed.value=matched
    }
  }
}
// countInitInfo();
getCaseFiles()
</script>