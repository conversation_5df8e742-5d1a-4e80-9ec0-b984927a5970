<template>
  <div class="text-area-edit" v-loading="loading">
    
    <el-input v-model="textval" :rows="14" type="textarea" placeholder="请输入内容"></el-input>
    <div class="text-operation">
      <el-button type="primary" size="max" @click="copyAndJump">复制内容并跳转</el-button>
      <el-button type="warning" size="max" @click="textval=''">清空</el-button>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import {updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,updateUrlHash} from '@/utils';
import { ElMessage } from 'element-plus'
let hasviewid = getHashParams('viewid')
let textval=ref(`请帮我根据以下诉求进行消费者调研，并输出分析报告：

背景：丽江金茂璞修·雪山酒店遭遇了比较大的经营困境，均入住率不到三成，远低于丽江其他高端酒店的一半水平。

酒店优势：酒店硬件一流，在雪山之中自然景观无与伦比
酒店劣势：之前定位模糊，既想做超五星又想兼顾养生度假，却没抓住核心吸引力，游客来了觉得‘豪华但没特色’；加上离古城远，缺少持续客流

现在我有3个核心诉求：
1. 现在酒店希望利用雪山资源，做“天文主题”的自身定位，通过天文主题重构消费者价值感知，突破区位劣势；请帮我找到哪些消费者群体会对天文主题感兴趣，并有意愿入住酒店

2. 请结合这个定位，与丽江古城的其他知名度假酒店如悦榕庄、安缦等国际品牌相对比：提取人有我无 (竞争劣势)，人有我有（同质化），人有我优（直面竞争），人无我有（差异化）的价值要素；

3. 请帮我分析这些消费者群体，分别对2中分析出的哪些价值要素敏感；“人有我无”的部分是否要投入精力追赶，如果不做任何动作，是否会对支付意愿打折扣；以及“人有我优”、“人无我有”的核心优势有多大的溢价，用户的支付意愿有多强`);
let loading=ref(false)
const copyAndJump=async()=>{
  if(hasviewid){
    if (!textval.value) {
      ElMessage.warning('请输入内容')
      return
    }
    loading.value=true
    try {
      await navigator.clipboard.writeText(textval.value)
      
      await updatePriceSessionView({entity:{
        id:hasviewid,
        data:textval.value
      }}).then((res)=>{
        if(res?.data?.updatePriceSessionView?.id){
          ElMessage.success('复制成功，即将跳转')
          const newUrl=updateUrlHash(window.top.location.href,'viewid',res.data.updatePriceSessionView.id);
          if (window.self !== window.top) {
            // 当前页面在iframe中，操作父窗口
            window.top.location.href = newUrl;
          } else {
            // 普通页面直接跳转
            window.location.href = newUrl
          }
        }
      })
    } catch (err) {
      loading.value=false;
      console.error('操作失败，请重试', err)
      ElMessage.error('操作失败，请重试')
    }
  }else{

  }
}


</script>
