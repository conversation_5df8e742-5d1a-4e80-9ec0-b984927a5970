

<template>
    <div class="atypica-wrap" v-loading="loading">
        <div class="version-change">
          <div>
            <el-button type="primary" v-if="ablegetreport"  @click="preSubMitReport" size="small">提交报告</el-button>
            <el-button type="primary" plain v-if="ablegetreport"  @click="toNextCostPricing" size="small">提取细分市场报告</el-button>
          </div>
          <el-select v-model="currentversion" @change="changeTabView"  v-if="false&&viewitems.length>1">
              <el-option
              v-for="(item,index) in viewitems"
              :key="item.id"
              :label="versionLabel(item,index)"
              :value="item.id">
              </el-option>
          </el-select>
        </div>
        <div class="atypica-not-started" v-if="!isstarted">
          <div class="atypica-type">
            市场范围：<el-radio-group v-model="market" @change="changeMarket()">
                      <el-radio label="global">全球</el-radio>
                      <el-radio label="domestic">国内</el-radio>
                    </el-radio-group>
            
          </div>
          <div class="atypica-textarea">
            <el-input v-model="prompttxt" type="textarea" :rows="24"></el-input>
            <div class="at-btn">
              <el-button  type="primary" :disabled="!prompttxt" @click="toStart">开始</el-button>
            </div>
          </div>
        </div>
        <iframe 
        v-show="isstarted"
          class="iframe-atypica-inner"
          src="about:blank" 
          id="atypica-iframe"
          width="100%" 
          height="100%"
          @load="iframeLoaded"
          ref="iframeRef"
          frameborder="0"></iframe>
          
     </div>
</template>

<script setup ts>
import { ref, watch, computed, render, onMounted, nextTick } from 'vue'
import {getPriceViewInfo,updatePriceSessionView,submitPriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment';
import { ChangeEvent } from '@antv/g6/lib/constants';

const loading = ref(false);
let studyCreated=ref(false);
let ablegetreport=ref(false);
let hasreports=ref(false);
let studycontent=ref('')
let currentUrl=ref('');
let intervalId=ref("");
// 
let market=ref("domestic");
let prompttxt=ref("");
let isstarted=ref(true);

// iframe 加载完成后 loading结束
const iframeLoaded = () => {
  loading.value = false;
}
const endsWithMusedam=(url)=> {
    const suffix = "musedam.cc/";
    return url.endsWith(suffix);
};

// 监听来自 atypica 的消息
window.addEventListener("message", function (event) {
  console.log('event.data',event.data)
  if (event.data.source !== "atypica") return;
  console.log("收到消息:", event.data);
  switch (event.data.type) {
    case "href":
      currentUrl.value = event.data.href;
      console.log("URL 变化:", currentUrl.value);
      // 调用 接口 保存地址变更
      // 不存以 musedam.cc/结尾的链接，
      if(!endsWithMusedam(currentUrl.value)){
        let {marketPositioning,prompt}=baseData.value;
        toSaveEntity({
          currentUrl:currentUrl.value,
          marketPositioning,
          prompt,
          started:true //20250725 增加
        })
      }
      
      // 如果是首页且还未创建研究，则自动创建研究
      if (endsWithMusedam(currentUrl.value) && !studyCreated.value) {
        studyCreated.value = true;
        setTimeout(createStudy, 2000); // 延时确保页面加载完成
      }
      // 如果跳转到了研究页面，自动查询报告
      if (currentUrl.value.includes("/study/")) {
        console.log("已进入研究页面，开始查询报告");
        // setTimeout(fetchReports, 2000); // 等待页面完全加载
        if(intervalId.value){
          clearInterval(intervalId.value);
        }
        fetchReports(); // 执行你的函数

        intervalId.value = setInterval(() => {
          console.log('我又触发fetchReports啦')
          fetchReports(); // 执行你的函数
          // 检查是否满足停止条件
          if (hasreports.value) {
            console.log('计时器被清空，不会再出发自动获取报告')
            clearInterval(intervalId.value); // 清除定时器
          }
        }, 30000);
      }
      break;

    case "auth_status":
      const status = event.data.authenticated ? "已登录" : "未登录";
      const user = event.data.user ? ` (${event.data.user.email})` : "";
      // authStatus.textContent = status + user;
      console.log('status + user',status + user)
      break;
    case "action_result":
      if (event.data.action === "fetchAnalystReportsOfStudyUserChat") {
        console.log('1221212',event.data.result)
        if(event.data.result?.length){
          if (intervalId.value) {
            console.log('action_result___计时器被清空，不会再出发自动获取报告')
            clearInterval(intervalId.value); // 清除定时器
          }
          console.log("fetchAnalystReportsOfStudyUserChat，即将将报告传递给接口");
          hasreports.value=true;
          displayReports(event.data.result);
        }else{
          hasreports.value=false
          // ElMessage.warning("报告暂无内容，请稍后")
          console.log(event.data)
        }
      } else if (event.data.action === "createStudyUserChat") {
        // displayCreateResult(event.data.result);
        console.log("createStudyUserChat____event.data.result",event.data.result);
      }
      break;
    case "action_error":
      if (event.data.action === "createStudyUserChat") {
        // displayCreateError(event.data.error);
        console.log(event.data.error)
      }
      break;
  }
});
// 如果是首页且还未创建研究，则创建研究
const createStudy=()=>{
  if(!studycontent.value){
    ElMessage.warning("获取信息失败，无法发起 创建研究 ")
    return
  }
  const iframe = document.getElementById('atypica-iframe');
  iframe.contentWindow.postMessage(
    {
      target: "atypica",
      type: "action",
      action: "createStudyUserChat",
      args: {
        content: studycontent.value,
      },
      timestamp: new Date().toISOString(),
    },
    "*",
  );
  console.log("createStudycreateStudycreateStudy已经传递给iframe消息",studycontent.value)
};
// 如果跳转到了研究页面，开始查询报告
const fetchReports=()=>{
  console.log('跳转到了研究页面，开始查询报告')
  const iframe = document.getElementById('atypica-iframe');
  iframe.contentWindow.postMessage(
    {
      target: "atypica",
      type: "action",
      action: "fetchAnalystReportsOfStudyUserChat",
      timestamp: new Date().toISOString(),
    },
    "*",
  );
  
};
// 
const toSaveEntity=(entitydata,isstart)=>{
  updatePriceSessionView({
    entity:{
      id:dataid.value,
      data:JSON.stringify(entitydata)
    }
  }).then((res)=>{
    loading.value=false;
    if(res?.data?.updatePriceSessionView){
      if(isstart=='isstart'){
        getData()
      }else{
        // 如果是最后的报告保存，检测是否存在reports 如果存在则展示 开启下一步按钮
        if(entitydata?.reports){
          ablegetreport.value=true;
        }
      }
      
    }else{
      ElMessage.error('保存失败请重试')
    }
  }).catch((err)=>{
    ElMessage.error('计算失败: ' + err)
  })
};
// 获得结果，返回到
const displayReports=(reports)=>{
  let html = `<h3>找到 ${reports.length} 个分析师报告:</h3>`;
  reports.forEach((report, index) => {
    html += `
    <div style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin: 10px 0; background: white;">
      <h4>报告 ${index + 1}</h4>
      <div style="margin: 10px 0;">
        <strong>Token:</strong> <span style="color: #007bff;">${report.token || "N/A"}</span>
      </div>
      <div style="margin: 10px 0;">
        <strong>报告链接:</strong>
        <div style="margin-top: 5px;">
        <div
          style="width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 4px; overflow: auto; padding: 10px; background: white;"
          innerHTML="${report.onePageHtml || ""}">
        </div>
        </div>
      </div>
    </div>
    `;
  });
  // 
  let {marketPositioning,prompt}=baseData.value;
  console.log('我是获取到报告结果，即将完成接口传递',{
    reports:html,
    currentUrl:currentUrl.value,
    marketPositioning,
    prompt
  })
  toSaveEntity({
    reports:html,
    currentUrl:currentUrl.value,
    marketPositioning,
    prompt,
    started:true
  })
}

const displayCreateResult=(reports)=>{
  console.log('displayCreateResult',reports)
}
// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
// 
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
currentversion.value=getHashParams('viewid')
let dataid=ref('')
watch(viewid.value,()=>{
  if(viewid.value){
    currentversion.value=viewid.value
  }
},{immediate:true})
let hasviewid = getHashParams('viewid')
let baseData=ref({
    started:false,
    "currentUrl": "https://atypica.musedam.cc/auth/impersonation-login?token=qb8czZGqyiALFPQY3KEbQITE1rUSJUIrMZFikqJFjnnIOIHZNQO2MozrLp3Sl_KWeZIWA-q1Bsp-k8ryjvsGMlJI0b7kpIeR37kIvNj1V_NNSRgK_o2Cjv-3RzKPg0Qk",
    "marketPositioning": "1:利用高海拔地理优势，提供专业的高原养生设施和服务（高海拔养生设施）2:相比竞争对手的纳西建筑风格和茶马古道文化体验，文化特色不够突出（文化沉浸体验）",
    "prompt":`你好，我是一名商业分析顾问，现在接到了一个重要项目，请帮我根据以下诉求进行消费者调研，并输出分析报告：

背景：隐奢逸境是一家位于泸沽湖畔的高端度假酒店，提供特色Spa、文化体验、户外活动等服务。该酒店拥有独特的文化主题定位，由专业的跨领域团队运营，其设施包括全景露台客房、摩梭文化元素设计和绝美的自然景观，平均价格为1000元人民币。面对丽江金茂璞修雪山酒店、凉山文旅木里长海隐柏酒店、三亚珊瑚湾文华东方酒店和深圳京基瑞吉酒店等竞争对手，隐奢逸境希望通过融合自然探险与文化体验的新战略主题来提升其市场竞争力。

酒店优势：独特的文化主题定位，专业的跨领域团队，高效的运营模式，以及泸沽湖畔的绝美自然景观。

酒店劣势：平均价格可能高于一些国际品牌的竞争对手，且没有明确的国际品牌背书。

调研范围：本次调研、采访范围 [请限制在中国市场]，

现在我有3个核心诉求：
1. 现在酒店希望做“融合自然探险与文化体验的综合体主题”的自身定位，通过新主题重构消费者价值感知，突破区位劣势；请帮我找到哪些消费者群体会对融合自然探险与文化体验的综合体主题感兴趣，并有意愿入住酒店。

2. 请结合这个定位，与丽江金茂璞修雪山酒店、凉山文旅木里长海隐柏酒店、三亚珊瑚湾文华东方酒店和深圳京基瑞吉酒店这些竞争对手相对比：提取人有我无 (竞争劣势)，人有我有（同质化），人有我优（直面竞争），人无我有（差异化）的价值要素；

3. 请帮我分析这些消费者群体，分别对2中分析出的哪些价值要素敏感；“人有我无”的部分是否要投入精力追赶，如果不做任何动作，是否会对支付意愿打折扣；以及“人有我优”、“人无我有”的核心优势有多大的溢价，用户的支付意愿有多强。

4: 预测的细分市场和支付意愿数值需要偏向于保守数值。

请生成一份关于隐奢逸境融合自然探险与文化体验的综合体主题定位的完整conjoint分析报告，包含以下内容：

1. 研究背景和目标
2. Conjoint分析方法概述
3. 调查问卷设计详情
   - 完整的conjoint分析调查问卷内容
   - 属性选择和水平设计说明
   - 产品组合方案设计逻辑
4. 调查实施过程
   - 受访者选择和分布
   - 数据收集方法
5. 调查结果分析
   - 各用户群体对不同产品方案的偏好分析
   - 各属性重要性排序结果
   - 价格敏感度和支付意愿分析
6. 关键发现和洞察
   - 消费者新主题的价值感知
   - 不同客群的偏好差异
   - 最优产品组合建议
7. 战略建议和实施建议
   - 产品设计优化建议
   - 定价策略建议
   - 目标客群营销策略

报告应包含详细的调查问卷内容、数据分析图表，以及基于conjoint分析结果的具体战略建议。报告风格应专业、数据驱动，并提供清晰的执行指导。最终呈现方式参考--- markdown--- 内容。
--- markdown---
## 不同细分市场人群的支付意愿

给虚拟 persona bots 做 Conjoint 或者 PSM 问卷，得出不同细分市场在不同价位的支付概率【进行保守估计，不要太乐观】。

## 输出

PDF 专项营销报告 + 数据表格【参考下表,数据仅参考，真实数据以你调研分析结果为准，行和列的数据应该动态生成】

## Conjoint 分析结果 - 细分市场支付意愿与需求预测

| 消费者画像                                                   | 市场规模                             | (根据分析结果生成价格点，例如：1800元) XX元                                | (根据分析结果生成价格点，例如：2600元) XX元     | ....(有多少价格点则自动添加多少列) |
| ------------------------------------------------------------ | :----------------------------------- |-----------------------------------------------------------| ----------------------------------------------- | ---------------------------------- |
| XXX群体(根据分析自动生成,例如：专业摄影师 / 极致视野专业群创作者) | XX万  (根据分析自动生成,例如：2.5万) | (根据分析自动生成价格点对应群体的支付意愿) XX % , (根据分析自动生成价格点对应群体的预期需求量) XX万 | (根据分析自动生成价格点对应群体的支付意愿) XX % , (根据分析自动生成价格点对应群体的预期需求量) XX万|                                    |
| XXX群体(根据分析自动生成)                                    | (根据分析自动生成) XX万              | (根据分析自动生成价格点对应群体的支付意愿) XX % , (根据分析自动生成价格点对应群体的预期需求量) XX万 | (根据分析自动生成价格点对应群体的支付意愿) XX % , (根据分析自动生成价格点对应群体的预期需求量) XX万|                                    |
| ...群体(根据分析结果有多少群里则有多少行)                    | .... 万                              | ...%, ...万                                                | ...%, ...万                                        |                                    |


### 表格数据说明

表格数据将作为下一模块：成本定价模块的输入 - 结合运营成本、目标利润率、市场竞争等因素，制定最优定价策略。

### 市场总量与收益预测

- XX 万：总目标市场规模 (年度潜在客户)
- XXX 元：最优价格点 (综合支付意愿)
- XX万：预期年度需求 (在最优价格点)
- XX 亿：预期年度收入 (最优定价情况下)

### Conjoint 分析方法论

#### 研究方法与数据来源

- 虚拟 Persona 构建：基于市场调研和客户访谈，构建 5 个核心客群的详细画像。
- PSM 价格敏感性分析：通过 "太便宜 - 便宜 - 贵 - 太贵" 四点量表测试价格接受度。
- Conjoint 联合分析：测试价格、服务、设施等属性组合的相对重要性。
- Monte Carlo 模拟：基于概率分布进行 10,000 次需求预测模拟。
- 竞争对标分析：结合安缦、悦榕庄等竞品的定价策略。

#### 核心发现

- 核心发现 1：XXXX
- 核心发现 2：XXXX

#### 定价策略建议

- 基础定价策略
  - 基准价格：XXX 元 / 晚 (覆盖 XX% 目标客群)。
  - 旺季上浮：XXX-XXX 元 (XX 月 - XX 月)。
  - 淡季下调：XXX-XXXX 元 (XX 月 - XX 月)。
  - 早鸟优惠：提前 30 天预订享 8.5 折。
- 套餐定价策略
  - .....
- 会员定价策略
  - .....

--- markdown---`})
const getData=(ischange)=>{
    if(ischange=='ischange'&&currentversion.value){
        iframeRef.value.src = 'about:blank';
        toGetViewData(currentversion.value)
    }else if(hasviewid){
        toGetViewData(hasviewid)
    }
};
const toGetViewData=(viewId)=>{
  loading.value=true;
  baseData.value={}
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
    dataid.value=res?.data?.priceSessionVIew?.id
    if(res?.data?.priceSessionVIew?.data){
        let chartdatas=[]
        try{
            chartdatas=JSON.parse(res.data.priceSessionVIew.data)
        }catch (err){
            ElMessage.warning("数据解析出错")
        }
        if(chartdatas){
            baseData.value=chartdatas;
            countInitInfo()
        }else{
            ElMessage.warning("数据解析失败")
        }
    }
    }).catch((err)=>{
        loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
};
const iframeRef = ref(null);
const renderIframe=()=>{
  let {currentUrl,marketPositioning,prompt}=baseData.value;
  studycontent.value=''
  if(currentUrl){
    // 
    hasreports.value=false;
    if(currentUrl.includes('musedam.cc/auth')){
      studyCreated.value=false;
    }
    // 
    console.log('currentUrl',iframeRef.value.src)
    iframeRef.value.src = currentUrl
  }
  if(prompt){
    studycontent.value=prompt
  }
};
// 增加渲染前逻辑处理
const countInitInfo=()=>{
  let {started,prompt}=baseData.value;
  isstarted.value=true;
  market.value='domestic';
  prompttxt.value=""
  if(started){//
    renderIframe()
  }else{
    // 展示对话框
    isstarted.value=false;
    prompttxt.value=baseData.value?.prompt;
    changeMarket()

  }
};

const replaceResearchScope=(text, changeto)=>{
  // 使用正则表达式匹配"调研范围：本次调研、采访范围[xxxx]"模式
  // const regex = /调研范围：本次调研、采访范围\[([^\]]*)\]/g;
  const regex = /调研范围\s*[：:]\s*本次调研[、,，]\s*采访范围\s*\[\s*([^\]]*?)\s*\]/g;
  // 替换方括号中的内容
  const replacedText = text.replace(regex, `调研范围：本次调研、采访范围[${changeto}]`);
  console.log('replacedText',replacedText)
  return replacedText;
}
const changeMarket=()=>{
  let newtxt ='全国范围'
  console.log(market.value)
  if(market.value==='global'){
    newtxt="全球范围"
  }
  prompttxt.value=replaceResearchScope(prompttxt.value,newtxt);
}
const toStart=()=>{
  let {currentUrl,marketPositioning}=baseData.value;
  toSaveEntity({
    currentUrl,
    marketPositioning,
    prompt:prompttxt.value,
    started:true //20250725 增加
  },'isstart')
}
// countInitInfo() //测试
getData()
const changeTabView=()=>{
  getData('ischange')
}
//去成本定价 
const toNextCostPricing=()=>{
  // 
  console.log('parent.parent',parent.parent)
  console.log("已经发起postmessage",{toolname:'提取细分市场报告',nowtxt:'',sendnow:'true'})
  parent.parent.postMessage({eventname:'triggerChangeEvent',data:{toolname:'提取细分市场报告',nowtxt:'',sendnow:'true'}});
}

const preSubMitReport=()=>{
  if(!currentversion.value){
    ElMessage.warning("没有找到可用viewID，请稍后再试")
    return
  }
  loading.value=true;
  submitPriceSessionView({viewId:currentversion.value}).then((res)=>{
    if(res?.data?.submitPriceSessionView){
      ElMessage.success("报告提交成功")
      loading.value=false;
    }
  }).catch(()=>{
    loading.value=false;
    ElMessage.warning("报告提交出错，请稍后再试")
  })
}

</script>