<template>
    <div class="course-view-wrap">
        <div class="current-course">
            <div class="cc-name">{{currentcource.className}}</div>
            <div class="cc-info">
                <div class="cc-teacher">教师：{{currentcource?.teacher?.name}}</div>
                <div class="cc-start">开课时间：{{currentcource?.startTime&&moment(currentcource.startTime).format('YYYY年MM月DD日')}}</div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { computed, ref,inject,onMounted, watch } from 'vue';
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import moment from 'moment'

import {getCurrentClass,getGroupUsers} from '@/assets/api/api.js'
// 课程基本信息
let currentcource=ref({
    id:"",
    startTime:"",
    teacher:{
        name:"张老师"
    },
    className:"信息技术基础与应用"
})
// 组信息
let grouplist =ref([
    {
        id:"a1",
    },
    {
        id:"a2",
    },
    {
        id:"a3",
    },
    {
        id:"a4",
    }
])
// 人员信息
// 获取课程信息
const fetchCurrentCourse=()=>{
    getCurrentClass().then((res)=>{
        if(res?.data?.currentClass){
            currentcource.value=res.data.currentClass
        }
    })
};
const fetchGroupInfo=()=>{
    getGroupUsers().then((res)=>{
        if(res?.data?.groupUsers){
            grouplist.value=res.data.grouplist
        }
    })
};


</script>