<template>
    <div class="course-view-wrap">
        <div class="current-course">
            {{currentcource.value}}
            <div class="cc-name">{{currentcource.className}}</div>
            <div class="cc-info">
                <div class="cc-teacher">教师：{{currentcource?.teacherName}}</div>
                <div class="cc-start" v-if="currentcource.startTime">开课时间：{{currentcource?.startTime&&moment(currentcource.startTime).format('YYYY年MM月DD日')}}</div>
            </div>
        </div>
        <div class="course-group-info-wrap">
            <div class="group-view-step"
                v-for="(item,index) in grouplist"
                :key="item.id+index">
                    <div class="gvs-header">
                        <div class="gvs-label">组{{index+1}}</div>
                        <div class="gvs-info">
                            <div class="gvs-users" v-if="item.users">
                                <el-select v-model="groupuser['user'+index]" @change="(e)=>viewVersionChange(e,index,'user',item)">
                                    <el-option
                                    v-for="(uitem,uindex) in item.users"
                                    :key="uitem.openId+uindex"
                                    :label="uitem.name"
                                    :value="uitem.openId"
                                    />
                                </el-select>
                            </div>
                            <div class="gvs-users-sessions" v-if="item.usersessions">
                                <el-select v-model="groupuser['session'+index]" @change="(e)=>viewVersionChange(e,index,'session',item)">
                                    <el-option
                                    v-for="(uitem,uindex) in item.usersessions"
                                    :key="uitem.id+uindex"
                                    :label="uitem.id"
                                    :value="uitem.id"
                                    />
                                </el-select>
                            </div>
                        </div>
                    </div>
                    <div class="gvs-steps">
                        <div class="per-step"
                        v-for="(sitem,sindex,spindex) in item.steps"
                        :key="'step'+sindex"
                        :data-step="'Step ' + (sindex + 1) +' step'+(spindex)">
                        <template v-for="(ssitem,ssindex) in sitem"
                        :key="'ssitem'+ssindex">
                            <div class="step-step"
                            :class="ssitem?.id?'':'step-no-id'"
                                v-if="!ssitem?.disabled"
                                :data-step="ssindex + 1"
                            >{{ssitem?.viewName}}</div>
                        </template>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</template>
<script setup>
import { computed, ref,inject,onMounted, watch } from 'vue';
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import moment from 'moment'

import {getCurrentClass,getGroupUsers,studentClassInfo} from '@/assets/api/api.js'
// 课程基本信息
let currentcource=ref({})
// 每一个组 user0/session0---userN/sessionN
let groupuser=ref({
})
// 组信息
let grouplist =ref([
    {
        id:"1",
        groupIndex:2,
        users:[{
            name:"张三",
            openId:"a1"
        },{
            name:"张四",
            openId:"a2"
        }],
        usersessions:[
            {
                id:"a1111",
                views:[{
                    "id": "948f6534-4057-48b2-9733-96203eb6b9b6",
                    "viewName": "头脑风暴11",
                    "viewDesc": "头脑风暴",
                    "createdAt": 1752743516328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "1-1",
                },
                {
                    "id": "4057-48b2-9733-96203eb6b9b6",
                    "viewName": "头脑风暴21",
                    "viewDesc": "头脑风暴",
                    "createdAt": 1752743513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "2-1",
                },
                {
                    "id": "4057-48b2-9733-96203eb6b9b6",
                    "viewName": "头脑风暴22",
                    "viewDesc": "头脑风暴",
                    "createdAt": 1752743513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "2-2",
                },{
                    "id": "4057-48b2-9733-96203eb6b9b6",
                    "viewName": "智能对话",
                    "viewDesc": "智能对话",
                    "createdAt": 1752643513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "2-3",
                },]
            },
            {
                id:"a11112",
                views:[{
                    "id": "4057-48b2-9733-96203eb6b9b6",
                    "viewName": "智能对话23",
                    "viewDesc": "智能对话",
                    "createdAt": 1752643513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "2-3",
                },
                {
                    "id": "4wew057-48b2-9733-96203eb6b9b6",
                    "viewName": "竞争分析12",
                    "viewDesc": "竞争分析",
                    "createdAt": 1752743513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "1-2",
                }]
            }
        ]
    },{
        id:"2",
        groupIndex:1,
        users:[{
            name:"张三2",
            openId:"a12"
        },{
            name:"张四2",
            openId:"a22"
        }],
        usersessions:[
            {
                id:"11212",
                views:[{
                    "id": "948f6534-4057-48b2-9733-96203eb6b9b6",
                    "viewName": "头脑风暴11",
                    "viewDesc": "头脑风暴",
                    "createdAt": 1752743516328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "1-1",
                },
                {
                    "id": "948f6534-4057-48b2-9733-96203eb6b9b6",
                    "viewName": "头脑风暴11",
                    "viewDesc": "头脑风暴",
                    "createdAt": 1752743513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "1-1",
                },
                {
                    "id": "4057-48b2-9733-96203eb6b9b6",
                    "viewName": "头脑风暴21",
                    "viewDesc": "头脑风暴",
                    "createdAt": 1752743513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "2-1",
                }]
            },
            {
                id:"s1212",
                views:[{
                    "id": "4057-48b2-9733-96203eb6b9b6",
                    "viewName": "智能对话23",
                    "viewDesc": "智能对话",
                    "createdAt": 1752643513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "2-3",
                },
                {
                    "id": "4wew057-48b2-9733-96203eb6b9b6",
                    "viewName": "竞争分析15",
                    "viewDesc": "竞争分析",
                    "createdAt": 1752743513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "1-5",
                }]
            }
        ]
    }
]);
// 人员信息
// 获取课程信息
const fetchCurrentCourse=()=>{
    getCurrentClass().then((res)=>{
        if(res?.data?.currentClass){
            currentcource.value=JSON.parse(JSON.stringify(res.data.currentClass));
            if(currentcource.value?.groupUsers){
                grouplist.value=JSON.parse(JSON.stringify(currentcource.value?.groupUsers));
                // 每一个组获取第一个人员，去获取session&views信息
                formGroupInfo()
            }
        }
    })
};
const fetchGroupInfo=()=>{
    getGroupUsers({classId:currentcource.value?.id}).then((res)=>{
        if(res?.data?.groupUsers){
            grouplist.value=JSON.parse(JSON.stringify(res.data.groupUsers));
            // 每一个组获取第一个人员，去获取session&views信息
            formGroupInfo()
        }
    })
};
// 
const formGroupInfo=()=>{
    grouplist.value.forEach((item,index)=>{
        if(item?.users?.length){
            groupuser.value['user'+index]=item.users[0].openId;
            fetchStudentClassInfo(item.users[0].openId,index)
        }
    })
};
// 
const fetchStudentClassInfo=(openid,gindex)=>{
    studentClassInfo({
        openid,
        startTime:currentcource.value?.startTime,
        endTime:currentcource.value?.endTime,
    }).then((res)=>{
        if(res?.data?.studentClassInfo){
            let sessions =res.data.studentClassInfo
            // .sort((a, b) => {
            //     return b.createdAt - a.createdAt;
            // });
            grouplist.value[gindex].usersessions=sessions
            groupuser.value['session'+gindex]=sessions?.[0]?.id;
        }
    }).catch((err)=>{
        // 测试数据
        let sessions =[
            {
                id:"1",
                views:[{
                    "id": "948f6534-4057-48b2-9733-96203eb6b9b6",
                    "viewName": "头脑风暴",
                    "viewDesc": "头脑风暴",
                    "createdAt": 1752743516328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "1-1",
                },
                {
                    "id": "4057-48b2-9733-96203eb6b9b6",
                    "viewName": "头脑风暴",
                    "viewDesc": "头脑风暴",
                    "createdAt": 1752743513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "2-1",
                }]
            },
            {
                id:"2",
                views:[{
                    "id": "4057-48b2-9733-96203eb6b9b6",
                    "viewName": "智能对话",
                    "viewDesc": "智能对话",
                    "createdAt": 1752643513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "2-3",
                },
                {
                    "id": "4wew057-48b2-9733-96203eb6b9b6",
                    "viewName": "竞争分析",
                    "viewDesc": "竞争分析",
                    "createdAt": 1752743513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "1-1",
                }]
            }
        ]
        grouplist.value[gindex].usersessions=sessions;
        groupuser.value['session'+gindex]=sessions?.[0]?.id;
        
    })
};
// step : 1-1  拆分步骤，第二步骤按照setp排序，同名步骤按照时间顺序排序
const groupAndSortViews=(views)=> {
  const grouped = {};
  views.forEach(view => {
    const [stepPrefix, stepSuffix] = view.step.split('-').map(Number);
    const groupKey = `step${stepPrefix}`;
    
    if (!grouped[groupKey]) {
      grouped[groupKey] = [];
    }
    grouped[groupKey].push({
      ...view,
      _stepPrefix: stepPrefix,
      _stepSuffix: stepSuffix
    });
  });
  
  for (const groupKey in grouped) {
    grouped[groupKey].sort((a, b) => {
      if (a._stepSuffix !== b._stepSuffix) {
        return a._stepSuffix - b._stepSuffix;
      }
      return b.createdAt - a.createdAt;
    });
    grouped[groupKey].forEach(view => {
      delete view._stepPrefix;
      delete view._stepSuffix;
    });
  }
  
  return grouped;
};
// 
const viewVersionChange=(e,index,type,data)=>{
    if(type==='user'){//修改session[index]
        groupuser.value['session'+index]=data?.usersessions?.[0]?.id;
    }
    let sessionkey = groupuser.value['session'+index]
    let sessiondata = data?.usersessions?.find((item)=>item.id ==sessionkey)
    if(sessiondata){
        let views = sessiondata?.views;
        grouplist.value[index].steps=groupAndSortViews(views)
        // console.log('grouplist.value[index].steps',grouplist.value[index].steps)
        // 
        console.log(JSON.parse(JSON.stringify(grouplist.value)))
        console.log(processListItems(grouplist.value))
    }

}
const processListItems=(list)=>{
    list.forEach((item)=>{
        if(item?.steps){
            for(let key in item.steps){
                // 去除同版本多个展示
                item.steps[key] = item.steps[key]?.map((item, index, array) => {
                    if (index > 0 && (item.viewName === array[index - 1].viewName&&item.step === array[index - 1].step)) {
                        return { ...item, disabled: true };
                    }
                    return item;
                });
                // 去掉左右没有id的step，避免切换后空站位数据越来越多
                item.steps[key]=item.steps[key]?.filter((fitem)=>fitem?.id)
                // 
                list.forEach((initem)=>{
                    if(initem.id!=item.id){
                        if(initem?.steps?.[key]){
                            // 
                            if(item.steps?.[key]){
                                initem?.steps?.[key].forEach((sitem)=>{
                                if(!item.steps?.[key]?.some((isitem)=>isitem.viewName===sitem.viewName&&isitem.step===sitem.step)){
                                    if(sitem.id){
                                        item.steps?.[key].push({viewName:sitem.viewName,step:sitem.step})

                                    }
                                    }
                                })
                            }else{
                                item.steps[key]=initem.steps[key].map((nitem)=>(nitem.id&&{viewName:nitem.viewName,step:nitem.step}))
                            }
                        }
                    }
                })
            }
        }else{
            item.steps={};
            // 
            list.forEach((initem)=>{
                if(initem.id!=item.id){
                    if(initem?.steps){
                        for(let key in initem.steps){
                            if(initem?.steps?.[key]){
                                if(item.steps?.[key]){
                                    initem?.steps?.[key].forEach((sitem)=>{
                                    if(!item.steps?.[key]?.some((isitem)=>isitem.viewName===sitem.viewName&&isitem.step===sitem.step)){
                                            if(sitem.id){
                                                item.steps?.[key].push({viewName:sitem.viewName,step:sitem.step})
                                            }
                                        }
                                    })
                                }else{
                                    item.steps[key]=initem.steps[key].map((nitem)=>(nitem.id&&{viewName:nitem.viewName,step:nitem.step}))
                                }
                            }
                        }
                    }
                    
                    
                }
            })
        }
        item.steps=sortStepsData(item.steps)
    })
    
    return list
    
}
const sortStepsData=(data)=> {
  // 对第一层对象按键名排序（step1, step2, step3...）
  const sortedData = {};
  Object.keys(data)
    .sort((a, b) => {
      const numA = parseInt(a.replace('step', ''));
      const numB = parseInt(b.replace('step', ''));
      return numA - numB;
    })
    .forEach(key => {
      sortedData[key] = data[key];
    });

  // 对每个step数组中的对象按step值中的第二个数字排序
  for (const stepKey in sortedData) {
    const stepList = sortedData[stepKey];
    stepList.sort((a, b) => {
      const aStep = a.step ? parseInt(a.step.split('-')[1]) : 0;
      const bStep = b.step ? parseInt(b.step.split('-')[1]) : 0;
      return aStep - bStep;
    });
  }

  return sortedData;
}
fetchCurrentCourse()

</script>