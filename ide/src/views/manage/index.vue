<template>
    <div class="manage-index-wrap" v-loading="loading">
        <div class="mi-topbar"  v-if="!hideoutter">
            <div class="mit-logo">
                <div class="logo-logo"></div>
                <div class="logo-title">智能课程后台管理</div>
            </div>
            <div class="mit-info"></div>
        </div>
        <div class="mi-main" >
            <div class="splitter">
                <div class="splitter-side" size="200px" min="40" v-if="!hideoutter">
                    <el-menu class="manage-side-menu" router>
                        <el-menu-item index="index">
                            <el-icon><setting /></el-icon>
                            案例管理
                        </el-menu-item>
                        <el-menu-item index="courseview">
                            <el-icon><setting /></el-icon>
                            课程预览
                        </el-menu-item>
                    </el-menu>
                </div>
                <div class="splitter-content" :class="hideoutter?'full-page':''">
                    <router-view></router-view>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref } from 'vue';

// 隐藏菜单等
let hideoutter = ref(false);
const urlParams = new URLSearchParams(window.location.search)
hideoutter.value = urlParams.get('hideoutter');
</script>