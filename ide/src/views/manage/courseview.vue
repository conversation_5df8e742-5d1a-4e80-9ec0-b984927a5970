<template>
    <div class="course-view-wrap" v-loading="loading">
        <template v-if="currentcource?.id">
            <div class="current-course">
                {{currentcource.value}}
                <div class="cc-name">{{currentcource.className}}</div>
                <div class="cc-info">
                    <div class="cc-teacher">教师：{{currentcource?.teacherName}}</div>
                    <div class="cc-start" v-if="currentcource.startTime">开课时间：{{currentcource?.startTime&&moment(currentcource.startTime).format('YYYY年MM月DD日')}}</div>
                </div>
            </div>
            <div class="course-group-info-wrap">
                <div class="group-view-step"
                    v-for="(item,index) in grouplist"
                    :key="item.id+index">
                        <div class="gvs-header">
                            <div class="gvs-label">
                                <div>组{{index+1}}</div>
                                <div @click="toChat(index)" v-if="false"><el-link type="primary" underline="never"><el-icon><ChatLineSquare /></el-icon> 对话详情</el-link></div>
                                </div>
                            <div class="gvs-info">
                                <div class="gvs-users" v-if="item.users">
                                    <el-select v-model="groupuser['user'+index]" @change="(e)=>viewVersionChange(e,index,'user',item)">
                                        <el-option
                                        v-for="(uitem,uindex) in item.users"
                                        :key="uitem.openId+uindex"
                                        :label="uitem.name"
                                        :value="uitem.openId"
                                        />
                                    </el-select>
                                </div>
                                <div class="gvs-users-sessions" v-if="false&&item.usersessions">
                                    <el-select v-model="groupuser['session'+index]" @change="(e)=>viewVersionChange(e,index,'session',item)">
                                        <el-option
                                        v-for="(uitem,uindex) in item.usersessions"
                                        :key="uitem.id+uindex"
                                        :label="versionLabel(uitem,uindex)"
                                        :value="uitem.sessionId"
                                        />
                                    </el-select>
                                </div>
                            </div>
                        </div>
                        <div class="gvs-steps">
                            <template
                            v-for="(sitem,sindex,spindex) in item.steps"
                            :key="'step'+sindex">
                                <div class="per-step"
                                v-if="sitem?.length"
                                :data-step="'Step ' + (sindex + 1) +' step'+(spindex)">
                                <template v-for="(ssitem,ssindex) in sitem"
                                :key="'ssitem'+ssindex">
                                    <div class="step-step"
                                    :class="ssitem?.id?'':'step-no-id'"
                                        v-if="!ssitem?.disabled"
                                        :data-step="ssindex + 1"
                                    >
                                    <div>{{ssitem?.viewName}}</div>
                                    <el-link v-if="ssitem.id" class="report-link"  @click.native="viewReport(ssitem,index)" underline="never"><el-icon><Document /></el-icon>报告</el-link>
                                    </div>
                                </template>
                                </div>
                            </template>
                        </div>
                    </div>
            </div>
        </template>
        <el-empty description="暂无课程信息" v-else/>
    </div>
</template>
<script setup>
import { computed, ref,inject,onMounted, watch } from 'vue';
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import moment from 'moment'
import {versionLabel} from '@/utils/index.js'
import {getCurrentClass,getGroupUsers,studentClassInfo} from '@/assets/api/api.js'
import { ElMessage } from 'element-plus';
const store = useStore();
let fetchinterval=ref({})
// 课程基本信息
let currentcource=ref({});
let loading=ref(false);
// 每一个组 user0/session0---userN/sessionN
let groupuser=ref({});
// 组信息
let grouplist =ref([]);
// 人员信息
// 获取课程信息
const fetchCurrentCourse=()=>{
    loading.value=true;
    getCurrentClass().then((res)=>{
        loading.value=false;
        if(res?.data?.currentClass){
            currentcource.value=JSON.parse(JSON.stringify(res.data.currentClass));
            if(currentcource.value?.groupUsers){
                grouplist.value=JSON.parse(JSON.stringify(currentcource.value?.groupUsers));
                // 每一个组获取第一个人员，去获取session&views信息
                formGroupInfo()
            }
        }
    }).catch((err)=>{
        loading.value=false;
        // 测试数据
        let sessions =[
            {
                id:"1",
                views:[{
                    "id": "948f6534-4057-48b2-9733-96203eb6b9b6",
                    "viewName": "头脑风暴",
                    "viewDesc": "头脑风暴",
                    "createdAt": 1752743516328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "1-1",
                },
                {
                    "id": "4057-48b2-9733-96203eb6b9b6",
                    "viewName": "头脑风暴",
                    "viewDesc": "头脑风暴",
                    "createdAt": 1752743513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "2-1",
                }]
            },
            {
                id:"2",
                views:[{
                    "id": "4057-48b2-9733-96203eb6b9b6",
                    "viewName": "智能对话",
                    "viewDesc": "智能对话",
                    "createdAt": 1752643513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "2-3",
                },
                {
                    "id": "4wew057-48b2-9733-96203eb6b9b6",
                    "viewName": "竞争分析",
                    "viewDesc": "竞争分析",
                    "createdAt": 1752743513328,
                    "viewUrl": "https://cloud.ketanyun.cn/hub-price/brainstormingBot",
                    "index": 0,
                    "step": "1-1",
                }]
            }
        ]
        grouplist.value[gindex].usersessions=sessions;
        groupuser.value['session'+gindex]=sessions?.[0]?.sessionId;
        
    })
};
const fetchGroupInfo=()=>{
    getGroupUsers({classId:currentcource.value?.id}).then((res)=>{
        if(res?.data?.groupUsers){
            grouplist.value=JSON.parse(JSON.stringify(res.data.groupUsers));
            // 每一个组获取第一个人员，去获取session&views信息
            formGroupInfo()
        }
    })
};
// 
const formGroupInfo=()=>{
    grouplist.value.forEach((item,index)=>{
        if(item?.users?.length){
            groupuser.value['user'+index]=item.users[0].openId;
            if(fetchinterval.value['user'+index]){
                console.log('user'+index)
                clearInterval(fetchinterval.value['user'+index])
            }
            fetchStudentClassInfo(item.users[0].openId,index)
            
            let interval=10000;//线上
            let maxcount=2;
            if (process.env.NODE_ENV !== "production") {
                interval=3000
            } 
            fetchinterval.value['user'+index]=setInterval(()=>{
                if (process.env.NODE_ENV !== "production") {
                    maxcount--
                }
                if(maxcount<=0){
                    clearInterval(fetchinterval.value['user'+index])
                    return
                }
                fetchStudentClassInfo(item.users[0].openId,index)
            },interval)
            
        }
    })
};
// 
const fetchStudentClassInfo=(openid,gindex)=>{
    studentClassInfo({
        openid,
        classId:currentcource.value?.id,
        startTime:currentcource.value?.startTime,
        endTime:currentcource.value?.endTime,
    }).then((res)=>{
        if(res?.data?.studentClassInfo){
            let sessions = JSON.parse(JSON.stringify(res.data.studentClassInfo));
            sessions.sort((a, b) => {
                return b.createdAt - a.createdAt;
            });
            console.log('sessions',sessions)
            grouplist.value[gindex].usersessions=sessions
            groupuser.value['session'+gindex]=sessions?.[0]?.sessionId;
            viewVersionChange(groupuser.value['session'+gindex],gindex,'session',grouplist.value[gindex])
        }
    })
};
// step : 1-1  拆分步骤，第二步骤按照setp排序，同名步骤按照时间顺序排序
const groupAndSortViews=(views)=> {
  const grouped = {};
  views.forEach(view => {
    const [stepPrefix, stepSuffix] = view.step.split('-').map(Number);
    const groupKey = `step${stepPrefix}`;
    
    if (!grouped[groupKey]) {
      grouped[groupKey] = [];
    }
    grouped[groupKey].push({
      ...view,
      _stepPrefix: stepPrefix,
      _stepSuffix: stepSuffix
    });
  });
  
  for (const groupKey in grouped) {
    grouped[groupKey].sort((a, b) => {
      if (a._stepSuffix !== b._stepSuffix) {
        return a._stepSuffix - b._stepSuffix;
      }
      return b.createdAt - a.createdAt;
    });
    grouped[groupKey].forEach(view => {
      delete view._stepPrefix;
      delete view._stepSuffix;
    });
  }
  
  return grouped;
};
// 
const viewVersionChange=(e,index,type,data)=>{
    if(type==='user'){//修改session[index]
        if(fetchinterval.value['user'+index]){
            clearInterval(fetchinterval.value['user'+index])
        }
        fetchStudentClassInfo(groupuser.value['user'+index],index)
        // 
        let maxcount=2;//线下仅需要循环两次即可
        let interval=10000;//线上
        if (process.env.NODE_ENV !== "production") {
            interval=3000
        } 
        fetchinterval.value['user'+index]=setInterval(()=>{
            if (process.env.NODE_ENV !== "production") {
                maxcount--
            }
            if(maxcount<=0){
                clearInterval(fetchinterval.value['user'+index])
                return
            }
            //
            fetchStudentClassInfo(groupuser.value['user'+index],index) 
        },interval)
        return
    }
    let sessionkey = groupuser.value['session'+index]
    let sessiondata = data?.usersessions?.find((item)=>item?.sessionId ==sessionkey)
    grouplist.value[index].steps={}
    if(sessiondata){
        let views = sessiondata?.views;
        grouplist.value[index].steps=groupAndSortViews(views)
        grouplist.value[index].steps=sortStepsData(grouplist.value[index].steps)
        // 任宸赐说 先不要最大化每一步的同志话操作，目前所见即所得
        // console.log(JSON.parse(JSON.stringify(grouplist.value)))
        // console.log(processListItems(grouplist.value))
    }

}
const processListItems=(list)=>{
    list.forEach((item)=>{
        if(item?.steps){
            for(let key in item.steps){
                // 去除同版本多个展示
                item.steps[key] = item.steps[key]?.map((item, index, array) => {
                    if (index > 0 && (item.viewName === array[index - 1].viewName&&item.step === array[index - 1].step)) {
                        return { ...item, disabled: true };
                    }
                    return item;
                });
                // 去掉左右没有id的step，避免切换后空站位数据越来越多
                item.steps[key]=item.steps[key]?.filter((fitem)=>fitem?.id)
                // if(!item.steps[key]?.length){
                //     delete item.steps[key]
                // }
            }
        }
    })
    list.forEach((item)=>{
        if(item?.steps){
            for(let key in item.steps){
                list.forEach((initem)=>{
                    if(initem.id!=item.id){
                        // initem 中 item同名的steo
                        if(initem?.steps?.[key]){
                            // 
                            if(item.steps?.[key]){
                                initem?.steps?.[key].forEach((sitem)=>{
                                if(!item.steps?.[key]?.some((isitem)=>isitem.viewName===sitem.viewName&&isitem.step===sitem.step)){
                                    if(sitem.id&&!sitem.disabled){
                                        item.steps?.[key].push({viewName:sitem.viewName,step:sitem.step})
                                    }
                                    }
                                })
                            }else{
                                if(!item.steps[key]){
                                    item.steps[key]=[]
                                }
                                initem.steps[key].forEach((nitem)=>{
                                    if(nitem.id&&!nitem.disabled){
                                        item.steps[key].push({viewName:nitem.viewName,step:nitem.step})
                                    }
                                })
                                // item.steps[key]=initem.steps[key].map((nitem)=>((!nitem.disabled)&&nitem.id&&{viewName:nitem.viewName,step:nitem.step}))
                            }
                        }
                        // inittem中有 item中没有的
                        for(let key2 in initem?.steps){
                            if(!(item.steps?.[key2])){
                                if(!item.steps[key2]){
                                    item.steps[key2]=[]
                                }
                                initem.steps[key2].forEach((nitem)=>{
                                    if(nitem.id&&!nitem.disabled){
                                        item.steps[key2].push({viewName:nitem.viewName,step:nitem.step})
                                    }
                                })
                                // item.steps[key2]=initem.steps[key2].map((nitem)=>((!nitem.disabled)&&nitem.id&&{viewName:nitem.viewName,step:nitem.step}))
                            }
                        }
                    }
                })
            }
        }else{
            item.steps={};
            // 
            list.forEach((initem)=>{
                if(initem.id!=item.id){
                    if(initem?.steps){
                        for(let key in initem.steps){
                            if(initem?.steps?.[key]){
                                if(item.steps?.[key]){
                                    initem?.steps?.[key].forEach((sitem)=>{
                                    if(!item.steps?.[key]?.some((isitem)=>isitem.viewName===sitem.viewName&&isitem.step===sitem.step)){
                                            if(sitem.id&&!sitem.disabled){
                                                item.steps?.[key].push({viewName:sitem.viewName,step:sitem.step})
                                            }
                                        }
                                    })
                                }else{
                                    if(!item.steps[key]){
                                        item.steps[key]=[]
                                    }
                                    initem.steps[key].forEach((nitem)=>{
                                        if(nitem.id&&!nitem.disabled){
                                            item.steps[key].push({viewName:nitem.viewName,step:nitem.step})
                                        }
                                    })
                                    // item.steps[key]=initem.steps[key].map((nitem)=>((!nitem.disabled)&&nitem.id&&{viewName:nitem.viewName,step:nitem.step}))
                                }
                            }
                        }
                    }
                    
                    
                }
            })
        }
        item.steps=sortStepsData(item.steps)
    })
    
    return list
    
}
const sortStepsData=(data)=> {
  // 对第一层对象按键名排序（step1, step2, step3...）
  const sortedData = {};
  Object.keys(data)
    .sort((a, b) => {
      const numA = parseInt(a.replace('step', ''));
      const numB = parseInt(b.replace('step', ''));
      return numA - numB;
    })
    .forEach(key => {
      sortedData[key] = data[key];
    });

  // 对每个step数组中的对象按step值中的第二个数字排序
  for (const stepKey in sortedData) {
    const stepList = sortedData[stepKey];
    stepList.sort((a, b) => {
      const aStep = a.step ? parseInt(a.step.split('-')[1]) : 0;
      const bStep = b.step ? parseInt(b.step.split('-')[1]) : 0;
      return aStep - bStep;
    });
  }

  return sortedData;
}
const toChat=(index)=>{
    let sessionid = groupuser.value?.['session'+index];
    if(!sessionid){
        ElMessage.warning("暂无可对话查看对象")
        return
    }
    let winorigin=window.location.origin;
    if (process.env.NODE_ENV !== "production") {
        winorigin='https://cloud.ketanyun.cn'
    } 
    window.open(`${winorigin}/copilot-factory/#/knowledge/conversation?copilotId=${store.getters.getagent_id}&conversationId=${sessionid}`)
}
const viewReport=(vitem,index)=>{
    let viewid = vitem.id;
    let viewname = vitem.viewName;
    let sessionid = groupuser.value?.['session'+index];
    let pageview={
        竞争分析:store.getters.getcontextPath+"/matchviewreport",
        头脑风暴:store.getters.getcontextPath+"/differentiationreport",
        市场分析:store.getters.getcontextPath+"/atypicareport",
        市场分析提取:store.getters.getcontextPath+"/marketsegmentsreport",
        成本定价:store.getters.getcontextPath+"/costpricingreport",
    }
    let pathrouter = pageview[vitem.viewName]
    if(pathrouter&&viewname){
        if(viewname=='市场分析提取'){
            window.open(`${pathrouter}?sessionId=${sessionid}#viewid=${viewid}`)
        }else{
            window.open(`${pathrouter}#viewid=${viewid}`)
        }
    }
}
fetchCurrentCourse()

</script>