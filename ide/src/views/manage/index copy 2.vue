<template>
    <div class="manage-index-wrap" v-loading="loading">
        <div class="mi-topbar">
            <div class="mit-logo">
                <div class="logo-logo"></div>
                <div class="logo-title">智能课程后台管理</div>
            </div>
            <div class="mit-info"></div>
        </div>
        <div class="mi-main" >
            <div class="splitter">
                <div class="splitter-side" size="200px" min="40">
                    <el-menu class="manage-side-menu">
                        <el-menu-item index="1">
                            <el-icon><setting /></el-icon>
                            案例管理
                        </el-menu-item>
                    </el-menu>
                </div>
                <div class="splitter-content" >
                    <div class="main-wrap">
                        <div class="main-title">
                            <div>
                                <el-button type="primary" @click="editCase({active:false,agentId:store.getters.getagent_id})" ><el-icon style="margin-right:5px"><Plus /></el-icon> 新增案例</el-button>
                                <el-select v-model="searchstate" placeholder="全部" clearable
                                @change="changeState" style="width: 180px;margin-left:10px">
                                    <el-option
                                    v-for="item in statelist"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                    />
                                </el-select>
                            </div>
                            <div>
                                <el-input v-model="searchkey" style="width: 240px"  placeholder="搜索案例标题" clearable @change="changeState">
                                    <template #prefix>
                                        <el-icon class="el-input__icon"><Search /></el-icon>
                                    </template>
                                </el-input>
                            </div>
                        </div>
                        <div class="main-content">
                            <div class="mc-inner" v-if="casefilteredlist?.length">
                                <div class="mci-item"
                                v-for="(item,index) in casefilteredlist"
                                :key="item.id+index"
                                @click="editCase(item)">
                                    <div class="mci-item-img">
                                        <img :src="toSVGDataUrl(fimg.courselearning)" v-if="!item.image">
                                        <img :src="item.image" v-else>
                                    </div>
                                    <div class="mci-item-info">
                                        <div class="mciinfo-title" :title="item.title">
                                            {{item.title}}
                                        </div>
                                        <div class="mciinfo-description" :title="item.description">
                                            
                                            {{item.description}}
                                        </div>
                                        <div class="mciinfo-opes">
                                            <div class="mci-state">
                                                <template v-if="item.knowledgeStatus">
                                                <!-- 0:上传中 1:上传失败 2.解析中 3.解析失败 4.学习中 5:学习失败 6.已发布 -->
                                                    <el-tag type="warning" plain v-if="item.knowledgeStatus?.includes('学习中')"><el-icon class="is-loading"><Loading /></el-icon>{{item.knowledgeStatus}}</el-tag>
                                                    <el-tag type="warning" plain v-else-if="item.knowledgeStatus?.includes('上传中')"><el-icon class="is-loading"><Loading /></el-icon>{{item.knowledgeStatus}}</el-tag>
                                                    <el-tag type="warning" plain v-else-if="item.knowledgeStatus?.includes('解析中')"><el-icon class="is-loading"><Loading /></el-icon>{{item.knowledgeStatus}}</el-tag>
                                                    <el-tag type="error" plain v-else-if="item.knowledgeStatus?.includes('失败')">{{item.knowledgeStatus}}</el-tag>
                                                    <el-tag type="success" v-else-if="item.knowledgeStatus?.includes('已发布')&&!item.active">学习完成</el-tag>
                                                    <el-tag type="warning" v-else-if="!item.active">{{item.knowledgeStatus}}</el-tag>
                                                    <el-tag type="primary" plain v-if="item.active">已启用</el-tag>
                                                    <!-- <el-icon v-if="item.knowledgeStatus?.includes('已发布')&&!item.active" class="doc-ready" title="知识库已准备完成"><CircleCheck /></el-icon> -->
                                                </template>
                                            </div>
                                            <div class="per-ope">
                                                <div class="ope-item ope-edit" title="修改案例"  @click.stop="editCase(item)"><el-icon><Edit /></el-icon></div>
                                                <div class="ope-item ope-preview" title="预览案例" @click.stop="toPreviewCase(item)"><el-icon><View /></el-icon></div>
                                                <div class="ope-item ope-active" 
                                                v-if="item.knowledgeStatus?.includes('已发布')"
                                                :class="item.active?'':'isactive'" 
                                                :title="item.active?'禁用案例':'启用案例'" 
                                                @click.stop="preActiveCase(item)"><el-icon><Open v-if="item.active" /><TurnOff v-else /></el-icon>{{item.active?'禁用':'启用'}}</div>
                                                <div class="ope-item ope-del" title="删除案例"  @click.stop="preDel(item)"><el-icon><DeleteFilled /></el-icon></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="content-blank" v-else>
                                <el-empty description="暂无案例可展示" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <el-drawer
      v-model="casedrawer"
      :with-header="false"
      size="80%"
      title="案例详情"
      class="casedetail-drawer-wrap"
    >
      <div class="casedetail-drawer-inner-wrap">
        <el-alert type="primary" :closable="false"   v-if="currentcase?.knowledgeStatus?.includes('学习中')" :title="currentcase?.knowledgeStatus"/>
        <el-alert type="primary" :closable="false"   v-if="currentcase?.knowledgeStatus?.includes('上传中')" :title="currentcase?.knowledgeStatus"/>
        <el-alert type="primary" :closable="false"    v-if="currentcase?.knowledgeStatus?.includes('解析中')" :title="currentcase?.knowledgeStatus"/>
        <el-alert type="error" :closable="false"    v-if="currentcase?.knowledgeStatus?.includes('失败')" :title="currentcase?.knowledgeStatus"/>
        <el-form
            ref="ruleFormRef"
            :model="currentcase"
            :rules="rules"
            label-width="120px"
        >
            <el-form-item label="案例智能体" prop="url">
               <fileupload @fileUploaded="fileUploaded" v-if="!currentcase.id"></fileupload>
               <el-input v-model="currentcase.url" disabled v-if="currentcase.url"></el-input>
               <!-- <el-input v-model="currentcase.url" ></el-input> -->
               <!-- https://cloud.ketanyun.cn/file/bd300d47-72f1-45ff-ba45-958dda46c294 -->
            </el-form-item>
            <el-form-item label="关联智能体" prop="agentId">
                <!-- :disabled="currentcase.id" -->

                <el-select v-model="currentcase.agentId" filterable   placeholder="请选择关联智能体" disabled>
                    <el-option
                        v-for="item in agentIds"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
                <!-- <el-input v-model="currentcase.agentId" /> -->
            </el-form-item>
            <el-form-item label="案例名" prop="title">
                <el-input v-model="currentcase.title" placeholder="请输入案例名称"/>
            </el-form-item>
            <el-form-item label="启用" prop="active" v-if="false">
                <el-segmented
                :disabled="!currentcase.id"
                v-model="currentcase.active"
                :options="[{value:true,label:'启用'},{value:false,label:'禁用'}]"
                />
            </el-form-item>
            <el-form-item label="案例图" prop="image">
                <imguploadbase64 :imgobj="{imgbase64:currentcase.image}" @changedImg="changedImg"></imguploadbase64>
            </el-form-item>
            <el-form-item label="描述" prop="description">
                <el-input v-model="currentcase.description" type="textarea" :rows="5"  placeholder="请输入案例描述"/>
            </el-form-item>
        </el-form>
        <div class="dia-ope">
            <el-button type="primary" @click="preSaveCase()">确认</el-button>
            <el-button plain @click="cancelSave">取消</el-button>
            <el-button type="success" plain v-if="currentcase.id" @click="toPreviewCase(currentcase)">预览</el-button>
            <el-button plain type="danger" v-if="currentcase.id" @click="preDel(currentcase)">删除</el-button>

        </div>
      </div>
    </el-drawer>
    </div>
</template>
<script setup>
import { ref, onMounted,computed } from "vue";
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import * as yaml from "js-yaml";
import {caseFiles,deleteCaseFile,addCaseFile,updateCaseFile,
knowledgecategories,
knowledges,
createKnowledges,
updateKnowledges,
deleteKnowledges,
updateKnowledgeState,
getAssembleParts,
} from '@/assets/api/api.js'

import { ElMessage, ElMessageBox } from 'element-plus'
import {
    takeoffEdges,
    toSVGDataUrl,
    systemsort
} from '@/utils'

import imguploadbase64 from '@/components/imguploadbase64.vue'
import fileupload from '@/components/fileupload.vue'
import courselearning from '@/assets/images/courselearning.svg?raw'

let fimg=ref({
    courselearning
})
const router = useRouter()
const store = useStore();


let searchstate=ref("")
let searchkey=ref("")
const statelist=[
    {
        value:"学习中",
        label:"学习中",
    },
    {
        value:"上传中",
        label:"上传中",
    },
    {
        value:"解析中",
        label:"解析中",
    },
    {
        value:"已发布",
        label:"学习完成",
    },
    {
        value:"失败",
        label:"失败",
    }
]

const rules={
    url: [
        { required: true, message: '请上传案例智能体', trigger: 'blur' },
    ],
    title: [
        { required: true, message: '请输入案例名称', trigger: 'blur' },
    ],
    agentId: [
        { required: true, message: '请选择关联智能体', trigger: 'blur' },
    ],
}
let agentIds=ref([])
    
let ruleFormRef=ref(null);
let casedrawer=ref(false);
let currentcase=ref({});
let loading=ref(false);
// 
let caselist=ref([]);
let casefilteredlist=ref([]);
// 
let casecategory=ref('');
let currentdocinfo=ref({});
// 
const getCaseFiles=()=>{
  loading.value=true;
  caseFiles().then((res)=>{
    loading.value=false;
      if(res?.data?.caseFiles){
          
          let chartdatas=res.data.caseFiles;
          if(chartdatas){
            caselist.value=JSON.parse(JSON.stringify(chartdatas));
            getKnowledges()
          }else{
            ElMessage.warning("数据解析失败")
          }
      }
  }).catch((err)=>{
    loading.value=false;
    ElMessage.warning("获取内容失败"+err)
  });
};
//  获取 knowledge 分类为 【案例】的 name
const getKnowCates=(copilot)=>{
    if(!copilot){
        return
    }
    casecategory.value='';
    knowledgecategories({copilot}).then((res)=>{
        if(res?.data?.categories){
            let knowscategories=res.data.categories;
            const matched = knowscategories?.find((item)=>item.text==='案例');
            if(matched?.name){
                casecategory.value=matched.name
            }
        }
    })
};
const getKnowledges=()=>{
    if(!caselist.value.find((item)=>(!(item.knowledgeStatus))||(item?.knowledgeStatus?.includes('上传中')||item?.knowledgeStatus?.includes('解析中')||item?.knowledgeStatus?.includes('学习中')
    ))){
        console.log('caselist.value 中不再存在进行中或者空状态，无需再循环调用更新状态');
        // 
        changeState()
        return
    }
    knowledges({
        "copilot": store.getters.getagent_id,
        "first": caselist.value.length,
        "offset": 0,
        "filter": {
            "kind": {
            "eq": "doc"
            }
        }
    }).then((res)=>{
        if(res?.data?.knowledges){
            let knows=takeoffEdges(res.data,'knowledges');
            compareAndTrigger(knows,caselist.value);
            // 
            changeState()
        }
    })
};
const compareAndTrigger=(alist, blist)=> {
    for (const item2 of blist) {
        const {knowledgeName,knowledgeStatus}=item2;
        if (knowledgeName) {
            const matchedItem = alist.find((item1) => 
                item1.doc && item1.doc.name === knowledgeName
            );
            if (matchedItem) {
                if (matchedItem.doc.statusDesc != knowledgeStatus) {
                    finalUpdateCase(item2,matchedItem.doc)
                    break; // 终止循环
                }
            }
        }
    }
    // 如果状态没有没有变化
    // 3秒后重新
    setTimeout(()=>{
        getKnowledges()
    },3000)
};

const editCase=(item)=>{
    currentcase.value=item;
    casedrawer.value=true;
}
// 
const fileUploaded=(url,fileinfo)=>{
    currentcase.value.url=url;
    currentdocinfo.value=fileinfo;
}
const changedImg=({imgbase64})=>{
    currentcase.value.image=imgbase64
}
const preSaveCase=(item)=>{
    ruleFormRef.value.validate((valid, fields)=>{
        if(valid){
            item=item||currentcase.value;
            cancelSave()
            saveCase(item)
        }
    })
};
const saveCase=(item,isactive)=>{
    if(isactive=='isactive'){
        if(!item.knowledgeStatus?.includes('已发布')){
            // 不是已发布状态不可
            item.active=!item.active;
            ElMessage.warning("当前状态不可启用");
            return
        }
        loading.value=true;
    }
    let {id,
    title,
    image,
    description,
    active,
    url,
    text,
    type,
    agentId,
    knowledgeName,
    knowledgeStatus,
    }=item
    if(id){//updateCaseFile
        const entity={id,
            title,
            image,
            description,
            active,
            url,
            text,
            type,
            agentId,
            knowledgeName,
            knowledgeStatus
        };
        // 
        const toupdata=()=>{
            const caseupdate =()=>{
                updateCaseFile({entity}).then((res)=>{
                    if(res?.data?.updateCaseFile){
                        ElMessage.success("修改成功");
                        cancelSave()
                        getCaseFiles()
                    }
                })
            }
            // 启用或者禁用knowledge
            if(isactive=='isactive'){//如果是只触发启用状态
                toToggleActiveKnowledge([knowledgeName],active,caseupdate)
            }else{
                caseupdate()
            }
        }
        if(isactive=='isactive'&&active){//如果为启用
            //关闭其他状态为 启用的 case以及knowledge
            let activeedcase=caselist.value?.filter((citem)=>{
                if(citem?.id!=id&&citem.active){
                    return citem
                }
            })
            if(activeedcase?.length){
                toUnactiveOthers(activeedcase,toupdata)
            }else{
               toupdata() 
            }
        }else{
            toupdata()
        }
    }else{//addCaseFile
        addCaseFile({entity:{
            title,
            image,
            description,
            active,
            agentId,
            url,
            text,
            type,
        }}).then((res)=>{
            if(res?.data?.addCaseFile){
                ElMessage.success("新增成功");
                let id = res.data.addCaseFile.id;
                saveKnowledge({
                        ...item,
                        id,
                    },'isnew')
                // cancelSave()
                // getCaseFiles()
            }
        }).catch(({graphQLErrors,
                networkError,
                response})=>{
            console.log(graphQLErrors)
            if(graphQLErrors?.[0]?.message){
                ElMessage.warning(graphQLErrors[0].message);
            }else{
                ElMessage.warning("请求出错，请重试");
            }
            
        })
    }
};
const preDel=(item,isdia)=>{
    ElMessageBox.confirm(
    `确认删除【${item.title}】`,
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      deleSingleCase(item)
      if(isdia=='isdia'){
        cancelSave()
      }
    })
};
const deleSingleCase=(item)=>{
    deleteCaseFile({id:item.id}).then((res)=>{
        if(res.data){
            ElMessage.success("删除成功")
            if(item?.knowledgeName){//一并删除智能体知识库
                delKnowledges(item.knowledgeName)
            }
            cancelSave();
            getCaseFiles()
        }
    }).catch((err)=>{
        ElMessage.warning("删除失败，请重试")
    })
};
const delKnowledges=(knowname)=>{
    deleteKnowledges({
        copilot:store.getters.getagent_id,
        kind:"doc",
        names:[knowname]
    }).then((res)=>{
        if(!res?.data?.deleteKnowledges){
            setTimeout(()=>{
                delKnowledges(knowname)
            },5000)
        }
    }).catch(()=>{
        setTimeout(()=>{
            delKnowledges(knowname)
        },10000)
    })
};
const cancelSave=()=>{
    casedrawer.value=false;
}

// 
const saveKnowledge=(caseinfo,isnew)=>{
    let {id,title,image,description,active,agentId,url,text,type}=caseinfo
    if(isnew==='isnew'){//新增
        createKnowledges({
            copilot:agentId,
            knowledges:[{
                kind:"doc",//问答还是案例 此处默认案例 doc
                state:false,//默认不启用
                category: casecategory.value,
                text:`创建【${title}】案例时，自动创建`,
                doc:{
                    kind:currentdocinfo.value?.uploadtype,//文档类型
                    text:currentdocinfo.value?.filename,
                    uri: url,
                    userDownloadable:true,//是否可下载
                }
            }]
        }).then((res)=>{
            if(res?.data?.createKnowledges){//
                finalUpdateCase(caseinfo,res.data.createKnowledges[0].doc)
            }
        }).catch(({graphQLErrors,
                networkError,
                response})=>{
            console.log(graphQLErrors)
            if(graphQLErrors?.[0]?.message){
                ElMessage.warning(graphQLErrors[0].message);
            }else{
                ElMessage.warning("请求出错，请重试");
            }
        })
    }else{//修改
        let knowledgeName=caseinfo.knowledgeName;
        let knowledgeStatus=caseinfo.knowledgeStatus;
        updateKnowledges({
            copilot:agentId,
            knowledges:[{
                kind:"doc",//问答还是案例 此处默认案例 doc
                category: casecategory.value,
                text:`创建【${title}】案例时，自动创建`,
            }]
        }).then((res)=>{
            if(res?.data?.createKnowledges){
                ElMessage.success("修改成功");
                cancelSave()
                getCaseFiles()
            }
        }).catch(({graphQLErrors,
                networkError,
                response})=>{
            console.log(graphQLErrors)
            if(graphQLErrors?.[0]?.message){
                ElMessage.warning(graphQLErrors[0].message);
            }else{
                ElMessage.warning("请求出错，请重试");
            }
        })
    }
};
// knlwledge 新增或者修改后需要更新case的相关字段
// 
const finalUpdateCase=(caseinfo,doc)=>{
    let {
        id,
        title,
        image,
        description,
        active,
        agentId,
        url,
        text,
        type,
    }=caseinfo;
    let {name,status,chunks,statusDesc}=doc
    let entity={
            id,
            title,
            image,
            description,
            active,
            agentId,
            url,
            text,
            type,
        }
    if(name){
        entity.knowledgeName=name
    }
    entity.knowledgeStatus=statusDesc
    if(chunks?.length&&status===6){//已发布的情况下才需要存chunks
        entity.text=chunks.join('')
    }
    updateCaseFile({entity}).then((res)=>{
        if(res?.data?.updateCaseFile){
            // ElMessage.success("案例编辑成功");
            cancelSave()
            getCaseFiles()
        }
    }).catch(()=>{
        ElMessage.warning("请求出错，请重试");
    })
};
const toUnactiveOthers=(unactivecases,callback)=>{
    let allvariables = [];
    let knownames = [];
    unactivecases.forEach((item)=>{
        let {
        id,
        title,
        image,
        description,
        active,
        agentId,
        url,
        text,
        type,
        knowledgeStatus,
        knowledgeName
        }=item;
        // 
        if(knowledgeName){
           knownames.push(knowledgeName) 
        }
        // 
        allvariables.push({
            id,
            title,
            image,
            description,
            active:false,
            agentId,
            url,
            text,
            type,
            knowledgeStatus,
            knowledgeName
        })
    })
    // 先禁用知识库
    toUnactiveKnowledge(knownames,()=>{
        toUnactiveCase(allvariables,callback)
    })
};
// 
const toUnactiveCase=(allvariables,callback)=>{
    Promise.all([...allvariables.map((data)=>{
        return updateCaseFile({entity:data})
    })]).then((reslist) => {
        let failed=[]
        if(reslist.length){
            reslist.forEach((res,index)=>{
                if(!(res?.data?.updateCaseFile)){
                    failed.push(allvariables[index])
                }
            })
        }
        // 全部成功后去 禁用当前 konwlwdge和case
        if(failed?.length){
            // 如果没有全部成功，则2秒后重试没有成功的项
            ElMessage.warning("操作失败，重试中")
            setTimeout(()=>{
                toUnactiveCase(failed,callback)
            },2000)
        }else {//没有失败项 禁用当前 konwlwdge和case
            callback()
        }
    }).catch(()=>{
        // 如果没有成功，则5秒后重试
        ElMessage.warning("操作失败，重试中")
        setTimeout(()=>{
            toUnactiveCase(allvariables,callback)
        },5000)
    })
}
const toUnactiveKnowledge=(knownames,cb)=>{
    updateKnowledgeState({
        // copilot: "01JTMDGAFK6FSAPWVCKTRRV0H5",
        copilot:store.getters.getagent_id,
        enabled: false,
        kind: 'doc',
        names:knownames 
    }).then((res)=>{
        if(!res?.data?.updateKnowledgeState){
            // 如果不成功，3s后重新发起一次
            ElMessage.warning("操作失败，重试中")
            setTimeout(()=>{
                toUnactiveKnowledge(knownames,cb)
            },3000)
        }else{
            cb()
        }
    }).catch((err)=>{
        // 如果不成功，5s后重新发起一次
        ElMessage.warning("操作失败，重试中")
        setTimeout(()=>{
            toUnactiveKnowledge(knownames,cb)
        },5000)
    })
}
const toToggleActiveKnowledge=(knownames,enabled,cb)=>{
    updateKnowledgeState({
        // copilot: "01JTMDGAFK6FSAPWVCKTRRV0H5",
        copilot: store.getters.getagent_id,
        enabled,
        kind: 'doc',
        names:knownames 
    }).then((res)=>{
        if(!res?.data?.updateKnowledgeState){
            // 如果不成功，3s后重新发起一次
            ElMessage.warning("操作失败，重试中")
            setTimeout(()=>{
                toToggleActiveKnowledge(knownames,enabled,cb)
            },3000)
        }else{
            cb()
        }
    }).catch((err)=>{
        // 如果不成功，5s后重新发起一次
        ElMessage.warning("操作失败，重试中")
        setTimeout(()=>{
            toToggleActiveKnowledge(knownames,enabled,cb)
        },5000)
    })
};
const toPreviewCase=(item)=>{
    if(item?.url){
        const route = router.resolve({
            path: '/case/preview',
            query: { 
                previewcaseurl: item.url,
            }
        })
        window.open(route.href, '_blank')
    }else{
        ElMessage.warning("没有找到可预览内容")
    }
};
// 状态过滤 关键字过滤
const changeState=()=>{
    let filtered = JSON.parse(JSON.stringify(caselist.value));
    // 按状态过滤
    if (searchstate.value) {
        filtered = filtered.filter(item => 
            item.knowledgeStatus?.includes(searchstate.value)
        );
    }
    // 按关键字过滤
    if (searchkey.value) {
        const searchKeyLower = searchkey.value.toLowerCase();
        filtered = filtered.filter(item => 
            item?.title?.toLowerCase()?.includes(searchKeyLower)
        );
    }
    // 按照active排序
    filtered=systemsort(filtered,'active').reverse()
    // 
    casefilteredlist.value = filtered;
};
const getCoplitPart=()=>{
    getAssembleParts().then((res)=>{
        if(res?.data?.parts){
            let allparts = res.data.parts;
            console.log(allparts)
            let allsource = []
            allparts.forEach((item)=>{
                if(item?.versions?.length){
                    item.versions.forEach((vitem)=>{
                        if(vitem?.sources?.length){
                            vitem.sources.forEach((sitem)=>{
                                if(sitem?.content&&sitem?.name){
                                    allsource.push(sitem)
                                }
                            })
                        }
                    })
                }
            })
            agentIds.value=toAgentIds(allsource)
            console.log(agentIds.value)
        }
    })
};
const toAgentIds=(allsource)=> {
    return allsource.map(item => {
        try {
            // 尝试解析YAML内容为JSON
            const contentObj = yaml.load(item.content);
            console.log(contentObj)
            // 获取copilot.text的值，如果没有则使用name
            const text = contentObj?.spec?.copilot?.text || item.name;
            return {
                value: item.name,
                label: text
            };
        } catch (e) {
            // 如果解析失败，使用name作为text
            return {
                value: item.name,
                label: item.name
            };
        }
    });
};
// 
const preActiveCase = async (item) => {
  if (!item.active) {
    const actived = caselist.value?.find(item => item.active);
    if (actived) {
      try {
        await ElMessageBox.confirm(
          `当前只允许启用一份案例，本次操作将禁用掉【${actived.title}】案例，是否继续？`,
          '启用提示',
          { confirmButtonText: '确定', cancelButtonText: '取消' }
        );
      } catch {
        return; // 取消
      }
    }
  }
  
  item.active = !item.active;
  saveCase(item, 'isactive');
}

//
getKnowCates(store.getters.getagent_id);
// 获取案例列表
getCaseFiles();
// 获取智能体可选列表
getCoplitPart()


</script>