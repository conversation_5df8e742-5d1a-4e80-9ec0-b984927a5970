<template>
<div class="cost-pricing-wrap" v-loading="loading">
    <div class="comparison-header"  >
      <div>
          <el-button type="primary"  @click="preSubMitReport" size="small">提交报告</el-button>
      </div>
        <div class="version-change">
            <el-select v-model="currentversion" @change="changeTabView" v-if="false&viewitems.length>1">
            <el-option
                v-for="(item,index) in viewitems"
                :key="item.id"
                :label="versionLabel(item,index)"
                :value="item.id">
            </el-option>
            </el-select>
        </div>
    </div>
    <!-- :class="viewitems.length>1?'multi-version':''" -->
    <div class="cost-pricing-content" >
      <marketcomponent ref="marketrefcomponent" v-show="baseData?.market_segments_view_id" :class="baseData?.market_segments_view_id?'component-market-segment':'component-market-segment-hide'" @tableChanged="tableChanged"></marketcomponent>
      <div class="chart-slider-wrap" :class="baseData?.market_segments_view_id?'':'chart-slider-full-wrap'"> 
        <div class="slide-val">
          <div class="slide-per">
            <div ><div>财务视角FC：</div><el-slider :marks="marks" v-model="financialfcper" :step="10" show-stops @change="countInitInfo" :max="90"/></div>   
            <div  class="final"><div>财务视角VC：</div><el-slider :marks="marks" v-model="financialvcper" :step="10" show-stops  @change="countInitInfo" :max="90"/></div>  
          </div>
          <div class="slide-per">
            <div ><div>经济视角FC：</div><el-slider :marks="marks" v-model="economicfcper" :step="10" show-stops @change="countInitInfo" :max="90"/></div>   
            <div class="final"><div>经济视角VC：</div><el-slider  :marks="marks" v-model="economicvcper" :step="10" show-stops  @change="countInitInfo" :max="90"/></div>   
          </div>
        </div>
        <div class="chart-inner" id="chartinner"></div>
      </div>
    </div>
    
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch, nextTick } from 'vue';
import {getPriceViewInfo,executeTool,submitPriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router';
import * as echarts from 'echarts';
import marketcomponent from './componentmarketsegment.vue'

const route = useRoute();
// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
    if(viewid.value){
        currentversion.value=viewid.value
    }
},{immediate:true})

let myChart = null;
let marks=({
  0:"0%",
  10:'10%',
  20:'20%',
  30:'30%',
  40:'40%',
  50:'50%',
  60:'60%',
  70:'70%',
  80:'80%',
  90:'90%',
})
let economicfcper=ref(0)
let economicvcper=ref(0)
let financialfcper=ref(0)
let financialvcper=ref(0)
// 编辑相关状态
let hasviewid = getHashParams('viewid')
currentversion.value = getHashParams('viewid')
let baseData=ref(
    {
        "base_cases": [
            {
                "price_and_base_sales": [
                    [
                        3000,
                        17882
                    ],
                    [
                        4500,
                        11335
                    ],
                    [
                        6000,
                        8135
                    ],
                    [
                        8000,
                        5860
                    ]
                ],
                "yearly_fixed": 40538995.08,
                "intercept": 1253.620413370723,
                "fixed_cost_increase": "0%",
                "slope": -0.029114036984168423,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        18353
                    ],
                    [
                        4500,
                        11606
                    ],
                    [
                        6000,
                        8295
                    ],
                    [
                        8000,
                        5951
                    ]
                ],
                "yearly_fixed": 40538995.08,
                "intercept": 1378.9824547077956,
                "fixed_cost_increase": "0%",
                "slope": -0.03202544068258527,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        18825
                    ],
                    [
                        4500,
                        11885
                    ],
                    [
                        6000,
                        8461
                    ],
                    [
                        8000,
                        6044
                    ]
                ],
                "yearly_fixed": 40538995.08,
                "intercept": 1504.3444960448676,
                "fixed_cost_increase": "0%",
                "slope": -0.034936844381002106,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        19297
                    ],
                    [
                        4500,
                        12170
                    ],
                    [
                        6000,
                        8630
                    ],
                    [
                        8000,
                        6139
                    ]
                ],
                "yearly_fixed": 40538995.08,
                "intercept": 1629.70653738194,
                "fixed_cost_increase": "0%",
                "slope": -0.03784824807941895,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        19768
                    ],
                    [
                        4500,
                        12462
                    ],
                    [
                        6000,
                        8805
                    ],
                    [
                        8000,
                        6237
                    ]
                ],
                "yearly_fixed": 40538995.08,
                "intercept": 1755.0685787190123,
                "fixed_cost_increase": "0%",
                "slope": -0.04075965177783579,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        20236
                    ],
                    [
                        4500,
                        12760
                    ],
                    [
                        6000,
                        8984
                    ],
                    [
                        8000,
                        6337
                    ]
                ],
                "yearly_fixed": 40538995.08,
                "intercept": 1880.4306200560845,
                "fixed_cost_increase": "0%",
                "slope": -0.043671055476252635,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        20699
                    ],
                    [
                        4500,
                        13065
                    ],
                    [
                        6000,
                        9168
                    ],
                    [
                        8000,
                        6440
                    ]
                ],
                "yearly_fixed": 40538995.08,
                "intercept": 2005.792661393157,
                "fixed_cost_increase": "0%",
                "slope": -0.04658245917466948,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        21157
                    ],
                    [
                        4500,
                        13375
                    ],
                    [
                        6000,
                        9358
                    ],
                    [
                        8000,
                        6546
                    ]
                ],
                "yearly_fixed": 40538995.08,
                "intercept": 2131.154702730229,
                "fixed_cost_increase": "0%",
                "slope": -0.04949386287308632,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        21609
                    ],
                    [
                        4500,
                        13691
                    ],
                    [
                        6000,
                        9551
                    ],
                    [
                        8000,
                        6654
                    ]
                ],
                "yearly_fixed": 40538995.08,
                "intercept": 2256.5167440673017,
                "fixed_cost_increase": "0%",
                "slope": -0.05240526657150316,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22054
                    ],
                    [
                        4500,
                        14011
                    ],
                    [
                        6000,
                        9750
                    ],
                    [
                        8000,
                        6765
                    ]
                ],
                "yearly_fixed": 40538995.08,
                "intercept": 2381.878785404374,
                "fixed_cost_increase": "0%",
                "slope": -0.05531667026992,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        19315
                    ],
                    [
                        4500,
                        12365
                    ],
                    [
                        6000,
                        8908
                    ],
                    [
                        8000,
                        6431
                    ]
                ],
                "yearly_fixed": 44592894.588,
                "intercept": 1253.620413370723,
                "fixed_cost_increase": "10%",
                "slope": -0.029114036984168423,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        19779
                    ],
                    [
                        4500,
                        12646
                    ],
                    [
                        6000,
                        9078
                    ],
                    [
                        8000,
                        6528
                    ]
                ],
                "yearly_fixed": 44592894.588,
                "intercept": 1378.9824547077956,
                "fixed_cost_increase": "10%",
                "slope": -0.03202544068258527,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        20242
                    ],
                    [
                        4500,
                        12934
                    ],
                    [
                        6000,
                        9253
                    ],
                    [
                        8000,
                        6628
                    ]
                ],
                "yearly_fixed": 44592894.588,
                "intercept": 1504.3444960448676,
                "fixed_cost_increase": "10%",
                "slope": -0.034936844381002106,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        20703
                    ],
                    [
                        4500,
                        13228
                    ],
                    [
                        6000,
                        9433
                    ],
                    [
                        8000,
                        6730
                    ]
                ],
                "yearly_fixed": 44592894.588,
                "intercept": 1629.70653738194,
                "fixed_cost_increase": "10%",
                "slope": -0.03784824807941895,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        21160
                    ],
                    [
                        4500,
                        13528
                    ],
                    [
                        6000,
                        9616
                    ],
                    [
                        8000,
                        6835
                    ]
                ],
                "yearly_fixed": 44592894.588,
                "intercept": 1755.0685787190123,
                "fixed_cost_increase": "10%",
                "slope": -0.04075965177783579,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        21611
                    ],
                    [
                        4500,
                        13832
                    ],
                    [
                        6000,
                        9805
                    ],
                    [
                        8000,
                        6942
                    ]
                ],
                "yearly_fixed": 44592894.588,
                "intercept": 1880.4306200560845,
                "fixed_cost_increase": "10%",
                "slope": -0.043671055476252635,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22057
                    ],
                    [
                        4500,
                        14142
                    ],
                    [
                        6000,
                        9998
                    ],
                    [
                        8000,
                        7052
                    ]
                ],
                "yearly_fixed": 44592894.588,
                "intercept": 2005.792661393157,
                "fixed_cost_increase": "10%",
                "slope": -0.04658245917466948,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22495
                    ],
                    [
                        4500,
                        14457
                    ],
                    [
                        6000,
                        10196
                    ],
                    [
                        8000,
                        7165
                    ]
                ],
                "yearly_fixed": 44592894.588,
                "intercept": 2131.154702730229,
                "fixed_cost_increase": "10%",
                "slope": -0.04949386287308632,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22927
                    ],
                    [
                        4500,
                        14776
                    ],
                    [
                        6000,
                        10398
                    ],
                    [
                        8000,
                        7280
                    ]
                ],
                "yearly_fixed": 44592894.588,
                "intercept": 2256.5167440673017,
                "fixed_cost_increase": "10%",
                "slope": -0.05240526657150316,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23349
                    ],
                    [
                        4500,
                        15099
                    ],
                    [
                        6000,
                        10605
                    ],
                    [
                        8000,
                        7398
                    ]
                ],
                "yearly_fixed": 44592894.588,
                "intercept": 2381.878785404374,
                "fixed_cost_increase": "10%",
                "slope": -0.05531667026992,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        20707
                    ],
                    [
                        4500,
                        13379
                    ],
                    [
                        6000,
                        9675
                    ],
                    [
                        8000,
                        6999
                    ]
                ],
                "yearly_fixed": 48646794.09599999,
                "intercept": 1253.620413370723,
                "fixed_cost_increase": "20%",
                "slope": -0.029114036984168423,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        21162
                    ],
                    [
                        4500,
                        13669
                    ],
                    [
                        6000,
                        9854
                    ],
                    [
                        8000,
                        7103
                    ]
                ],
                "yearly_fixed": 48646794.09599999,
                "intercept": 1378.9824547077956,
                "fixed_cost_increase": "20%",
                "slope": -0.03202544068258527,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        21613
                    ],
                    [
                        4500,
                        13964
                    ],
                    [
                        6000,
                        10037
                    ],
                    [
                        8000,
                        7209
                    ]
                ],
                "yearly_fixed": 48646794.09599999,
                "intercept": 1504.3444960448676,
                "fixed_cost_increase": "20%",
                "slope": -0.034936844381002106,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22059
                    ],
                    [
                        4500,
                        14265
                    ],
                    [
                        6000,
                        10225
                    ],
                    [
                        8000,
                        7318
                    ]
                ],
                "yearly_fixed": 48646794.09599999,
                "intercept": 1629.70653738194,
                "fixed_cost_increase": "20%",
                "slope": -0.03784824807941895,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22500
                    ],
                    [
                        4500,
                        14570
                    ],
                    [
                        6000,
                        10417
                    ],
                    [
                        8000,
                        7429
                    ]
                ],
                "yearly_fixed": 48646794.09599999,
                "intercept": 1755.0685787190123,
                "fixed_cost_increase": "20%",
                "slope": -0.04075965177783579,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22934
                    ],
                    [
                        4500,
                        14879
                    ],
                    [
                        6000,
                        10614
                    ],
                    [
                        8000,
                        7543
                    ]
                ],
                "yearly_fixed": 48646794.09599999,
                "intercept": 1880.4306200560845,
                "fixed_cost_increase": "20%",
                "slope": -0.043671055476252635,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23360
                    ],
                    [
                        4500,
                        15192
                    ],
                    [
                        6000,
                        10815
                    ],
                    [
                        8000,
                        7659
                    ]
                ],
                "yearly_fixed": 48646794.09599999,
                "intercept": 2005.792661393157,
                "fixed_cost_increase": "20%",
                "slope": -0.04658245917466948,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23779
                    ],
                    [
                        4500,
                        15509
                    ],
                    [
                        6000,
                        11020
                    ],
                    [
                        8000,
                        7778
                    ]
                ],
                "yearly_fixed": 48646794.09599999,
                "intercept": 2131.154702730229,
                "fixed_cost_increase": "20%",
                "slope": -0.04949386287308632,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24188
                    ],
                    [
                        4500,
                        15830
                    ],
                    [
                        6000,
                        11229
                    ],
                    [
                        8000,
                        7900
                    ]
                ],
                "yearly_fixed": 48646794.09599999,
                "intercept": 2256.5167440673017,
                "fixed_cost_increase": "20%",
                "slope": -0.05240526657150316,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24589
                    ],
                    [
                        4500,
                        16152
                    ],
                    [
                        6000,
                        11443
                    ],
                    [
                        8000,
                        8024
                    ]
                ],
                "yearly_fixed": 48646794.09599999,
                "intercept": 2381.878785404374,
                "fixed_cost_increase": "20%",
                "slope": -0.05531667026992,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22062
                    ],
                    [
                        4500,
                        14379
                    ],
                    [
                        6000,
                        10435
                    ],
                    [
                        8000,
                        7564
                    ]
                ],
                "yearly_fixed": 52700693.604,
                "intercept": 1253.620413370723,
                "fixed_cost_increase": "30%",
                "slope": -0.029114036984168423,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22504
                    ],
                    [
                        4500,
                        14675
                    ],
                    [
                        6000,
                        10622
                    ],
                    [
                        8000,
                        7674
                    ]
                ],
                "yearly_fixed": 52700693.604,
                "intercept": 1378.9824547077956,
                "fixed_cost_increase": "30%",
                "slope": -0.03202544068258527,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22941
                    ],
                    [
                        4500,
                        14976
                    ],
                    [
                        6000,
                        10813
                    ],
                    [
                        8000,
                        7787
                    ]
                ],
                "yearly_fixed": 52700693.604,
                "intercept": 1504.3444960448676,
                "fixed_cost_increase": "30%",
                "slope": -0.034936844381002106,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23371
                    ],
                    [
                        4500,
                        15281
                    ],
                    [
                        6000,
                        11009
                    ],
                    [
                        8000,
                        7901
                    ]
                ],
                "yearly_fixed": 52700693.604,
                "intercept": 1629.70653738194,
                "fixed_cost_increase": "30%",
                "slope": -0.03784824807941895,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23794
                    ],
                    [
                        4500,
                        15590
                    ],
                    [
                        6000,
                        11208
                    ],
                    [
                        8000,
                        8019
                    ]
                ],
                "yearly_fixed": 52700693.604,
                "intercept": 1755.0685787190123,
                "fixed_cost_increase": "30%",
                "slope": -0.04075965177783579,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24209
                    ],
                    [
                        4500,
                        15902
                    ],
                    [
                        6000,
                        11412
                    ],
                    [
                        8000,
                        8139
                    ]
                ],
                "yearly_fixed": 52700693.604,
                "intercept": 1880.4306200560845,
                "fixed_cost_increase": "30%",
                "slope": -0.043671055476252635,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24616
                    ],
                    [
                        4500,
                        16217
                    ],
                    [
                        6000,
                        11619
                    ],
                    [
                        8000,
                        8261
                    ]
                ],
                "yearly_fixed": 52700693.604,
                "intercept": 2005.792661393157,
                "fixed_cost_increase": "30%",
                "slope": -0.04658245917466948,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25013
                    ],
                    [
                        4500,
                        16534
                    ],
                    [
                        6000,
                        11831
                    ],
                    [
                        8000,
                        8386
                    ]
                ],
                "yearly_fixed": 52700693.604,
                "intercept": 2131.154702730229,
                "fixed_cost_increase": "30%",
                "slope": -0.04949386287308632,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25401
                    ],
                    [
                        4500,
                        16854
                    ],
                    [
                        6000,
                        12046
                    ],
                    [
                        8000,
                        8514
                    ]
                ],
                "yearly_fixed": 52700693.604,
                "intercept": 2256.5167440673017,
                "fixed_cost_increase": "30%",
                "slope": -0.05240526657150316,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25780
                    ],
                    [
                        4500,
                        17176
                    ],
                    [
                        6000,
                        12265
                    ],
                    [
                        8000,
                        8644
                    ]
                ],
                "yearly_fixed": 52700693.604,
                "intercept": 2381.878785404374,
                "fixed_cost_increase": "30%",
                "slope": -0.05531667026992,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23383
                    ],
                    [
                        4500,
                        15365
                    ],
                    [
                        6000,
                        11189
                    ],
                    [
                        8000,
                        8127
                    ]
                ],
                "yearly_fixed": 56754593.111999996,
                "intercept": 1253.620413370723,
                "fixed_cost_increase": "40%",
                "slope": -0.029114036984168423,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23810
                    ],
                    [
                        4500,
                        15666
                    ],
                    [
                        6000,
                        11383
                    ],
                    [
                        8000,
                        8243
                    ]
                ],
                "yearly_fixed": 56754593.111999996,
                "intercept": 1378.9824547077956,
                "fixed_cost_increase": "40%",
                "slope": -0.03202544068258527,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24231
                    ],
                    [
                        4500,
                        15970
                    ],
                    [
                        6000,
                        11581
                    ],
                    [
                        8000,
                        8361
                    ]
                ],
                "yearly_fixed": 56754593.111999996,
                "intercept": 1504.3444960448676,
                "fixed_cost_increase": "40%",
                "slope": -0.034936844381002106,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24643
                    ],
                    [
                        4500,
                        16278
                    ],
                    [
                        6000,
                        11783
                    ],
                    [
                        8000,
                        8481
                    ]
                ],
                "yearly_fixed": 56754593.111999996,
                "intercept": 1629.70653738194,
                "fixed_cost_increase": "40%",
                "slope": -0.03784824807941895,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25047
                    ],
                    [
                        4500,
                        16589
                    ],
                    [
                        6000,
                        11989
                    ],
                    [
                        8000,
                        8604
                    ]
                ],
                "yearly_fixed": 56754593.111999996,
                "intercept": 1755.0685787190123,
                "fixed_cost_increase": "40%",
                "slope": -0.04075965177783579,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25442
                    ],
                    [
                        4500,
                        16902
                    ],
                    [
                        6000,
                        12199
                    ],
                    [
                        8000,
                        8730
                    ]
                ],
                "yearly_fixed": 56754593.111999996,
                "intercept": 1880.4306200560845,
                "fixed_cost_increase": "40%",
                "slope": -0.043671055476252635,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25828
                    ],
                    [
                        4500,
                        17217
                    ],
                    [
                        6000,
                        12412
                    ],
                    [
                        8000,
                        8858
                    ]
                ],
                "yearly_fixed": 56754593.111999996,
                "intercept": 2005.792661393157,
                "fixed_cost_increase": "40%",
                "slope": -0.04658245917466948,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26204
                    ],
                    [
                        4500,
                        17534
                    ],
                    [
                        6000,
                        12629
                    ],
                    [
                        8000,
                        8989
                    ]
                ],
                "yearly_fixed": 56754593.111999996,
                "intercept": 2131.154702730229,
                "fixed_cost_increase": "40%",
                "slope": -0.04949386287308632,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26571
                    ],
                    [
                        4500,
                        17852
                    ],
                    [
                        6000,
                        12849
                    ],
                    [
                        8000,
                        9122
                    ]
                ],
                "yearly_fixed": 56754593.111999996,
                "intercept": 2256.5167440673017,
                "fixed_cost_increase": "40%",
                "slope": -0.05240526657150316,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26927
                    ],
                    [
                        4500,
                        18171
                    ],
                    [
                        6000,
                        13073
                    ],
                    [
                        8000,
                        9258
                    ]
                ],
                "yearly_fixed": 56754593.111999996,
                "intercept": 2381.878785404374,
                "fixed_cost_increase": "40%",
                "slope": -0.05531667026992,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24671
                    ],
                    [
                        4500,
                        16337
                    ],
                    [
                        6000,
                        11937
                    ],
                    [
                        8000,
                        8687
                    ]
                ],
                "yearly_fixed": 60808492.62,
                "intercept": 1253.620413370723,
                "fixed_cost_increase": "50%",
                "slope": -0.029114036984168423,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25082
                    ],
                    [
                        4500,
                        16641
                    ],
                    [
                        6000,
                        12138
                    ],
                    [
                        8000,
                        8808
                    ]
                ],
                "yearly_fixed": 60808492.62,
                "intercept": 1378.9824547077956,
                "fixed_cost_increase": "50%",
                "slope": -0.03202544068258527,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25485
                    ],
                    [
                        4500,
                        16948
                    ],
                    [
                        6000,
                        12342
                    ],
                    [
                        8000,
                        8932
                    ]
                ],
                "yearly_fixed": 60808492.62,
                "intercept": 1504.3444960448676,
                "fixed_cost_increase": "50%",
                "slope": -0.034936844381002106,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25878
                    ],
                    [
                        4500,
                        17258
                    ],
                    [
                        6000,
                        12550
                    ],
                    [
                        8000,
                        9058
                    ]
                ],
                "yearly_fixed": 60808492.62,
                "intercept": 1629.70653738194,
                "fixed_cost_increase": "50%",
                "slope": -0.03784824807941895,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26262
                    ],
                    [
                        4500,
                        17569
                    ],
                    [
                        6000,
                        12761
                    ],
                    [
                        8000,
                        9186
                    ]
                ],
                "yearly_fixed": 60808492.62,
                "intercept": 1755.0685787190123,
                "fixed_cost_increase": "50%",
                "slope": -0.04075965177783579,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26637
                    ],
                    [
                        4500,
                        17882
                    ],
                    [
                        6000,
                        12975
                    ],
                    [
                        8000,
                        9317
                    ]
                ],
                "yearly_fixed": 60808492.62,
                "intercept": 1880.4306200560845,
                "fixed_cost_increase": "50%",
                "slope": -0.043671055476252635,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27001
                    ],
                    [
                        4500,
                        18196
                    ],
                    [
                        6000,
                        13193
                    ],
                    [
                        8000,
                        9450
                    ]
                ],
                "yearly_fixed": 60808492.62,
                "intercept": 2005.792661393157,
                "fixed_cost_increase": "50%",
                "slope": -0.04658245917466948,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27356
                    ],
                    [
                        4500,
                        18510
                    ],
                    [
                        6000,
                        13415
                    ],
                    [
                        8000,
                        9586
                    ]
                ],
                "yearly_fixed": 60808492.62,
                "intercept": 2131.154702730229,
                "fixed_cost_increase": "50%",
                "slope": -0.04949386287308632,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27701
                    ],
                    [
                        4500,
                        18825
                    ],
                    [
                        6000,
                        13639
                    ],
                    [
                        8000,
                        9724
                    ]
                ],
                "yearly_fixed": 60808492.62,
                "intercept": 2256.5167440673017,
                "fixed_cost_increase": "50%",
                "slope": -0.05240526657150316,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28035
                    ],
                    [
                        4500,
                        19140
                    ],
                    [
                        6000,
                        13866
                    ],
                    [
                        8000,
                        9865
                    ]
                ],
                "yearly_fixed": 60808492.62,
                "intercept": 2381.878785404374,
                "fixed_cost_increase": "50%",
                "slope": -0.05531667026992,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25931
                    ],
                    [
                        4500,
                        17296
                    ],
                    [
                        6000,
                        12679
                    ],
                    [
                        8000,
                        9245
                    ]
                ],
                "yearly_fixed": 64862392.128,
                "intercept": 1253.620413370723,
                "fixed_cost_increase": "60%",
                "slope": -0.029114036984168423,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26323
                    ],
                    [
                        4500,
                        17602
                    ],
                    [
                        6000,
                        12885
                    ],
                    [
                        8000,
                        9371
                    ]
                ],
                "yearly_fixed": 64862392.128,
                "intercept": 1378.9824547077956,
                "fixed_cost_increase": "60%",
                "slope": -0.03202544068258527,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26706
                    ],
                    [
                        4500,
                        17910
                    ],
                    [
                        6000,
                        13095
                    ],
                    [
                        8000,
                        9500
                    ]
                ],
                "yearly_fixed": 64862392.128,
                "intercept": 1504.3444960448676,
                "fixed_cost_increase": "60%",
                "slope": -0.034936844381002106,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27079
                    ],
                    [
                        4500,
                        18220
                    ],
                    [
                        6000,
                        13307
                    ],
                    [
                        8000,
                        9630
                    ]
                ],
                "yearly_fixed": 64862392.128,
                "intercept": 1629.70653738194,
                "fixed_cost_increase": "60%",
                "slope": -0.03784824807941895,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27443
                    ],
                    [
                        4500,
                        18530
                    ],
                    [
                        6000,
                        13523
                    ],
                    [
                        8000,
                        9764
                    ]
                ],
                "yearly_fixed": 64862392.128,
                "intercept": 1755.0685787190123,
                "fixed_cost_increase": "60%",
                "slope": -0.04075965177783579,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27796
                    ],
                    [
                        4500,
                        18842
                    ],
                    [
                        6000,
                        13742
                    ],
                    [
                        8000,
                        9899
                    ]
                ],
                "yearly_fixed": 64862392.128,
                "intercept": 1880.4306200560845,
                "fixed_cost_increase": "60%",
                "slope": -0.043671055476252635,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28139
                    ],
                    [
                        4500,
                        19153
                    ],
                    [
                        6000,
                        13964
                    ],
                    [
                        8000,
                        10037
                    ]
                ],
                "yearly_fixed": 64862392.128,
                "intercept": 2005.792661393157,
                "fixed_cost_increase": "60%",
                "slope": -0.04658245917466948,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28472
                    ],
                    [
                        4500,
                        19465
                    ],
                    [
                        6000,
                        14189
                    ],
                    [
                        8000,
                        10178
                    ]
                ],
                "yearly_fixed": 64862392.128,
                "intercept": 2131.154702730229,
                "fixed_cost_increase": "60%",
                "slope": -0.04949386287308632,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28795
                    ],
                    [
                        4500,
                        19776
                    ],
                    [
                        6000,
                        14417
                    ],
                    [
                        8000,
                        10321
                    ]
                ],
                "yearly_fixed": 64862392.128,
                "intercept": 2256.5167440673017,
                "fixed_cost_increase": "60%",
                "slope": -0.05240526657150316,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29108
                    ],
                    [
                        4500,
                        20086
                    ],
                    [
                        6000,
                        14647
                    ],
                    [
                        8000,
                        10466
                    ]
                ],
                "yearly_fixed": 64862392.128,
                "intercept": 2381.878785404374,
                "fixed_cost_increase": "60%",
                "slope": -0.05531667026992,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27162
                    ],
                    [
                        4500,
                        18243
                    ],
                    [
                        6000,
                        13415
                    ],
                    [
                        8000,
                        9800
                    ]
                ],
                "yearly_fixed": 68916291.63599999,
                "intercept": 1253.620413370723,
                "fixed_cost_increase": "70%",
                "slope": -0.029114036984168423,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27535
                    ],
                    [
                        4500,
                        18550
                    ],
                    [
                        6000,
                        13626
                    ],
                    [
                        8000,
                        9931
                    ]
                ],
                "yearly_fixed": 68916291.63599999,
                "intercept": 1378.9824547077956,
                "fixed_cost_increase": "70%",
                "slope": -0.03202544068258527,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27897
                    ],
                    [
                        4500,
                        18857
                    ],
                    [
                        6000,
                        13840
                    ],
                    [
                        8000,
                        10064
                    ]
                ],
                "yearly_fixed": 68916291.63599999,
                "intercept": 1504.3444960448676,
                "fixed_cost_increase": "70%",
                "slope": -0.034936844381002106,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28250
                    ],
                    [
                        4500,
                        19166
                    ],
                    [
                        6000,
                        14057
                    ],
                    [
                        8000,
                        10200
                    ]
                ],
                "yearly_fixed": 68916291.63599999,
                "intercept": 1629.70653738194,
                "fixed_cost_increase": "70%",
                "slope": -0.03784824807941895,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28592
                    ],
                    [
                        4500,
                        19474
                    ],
                    [
                        6000,
                        14277
                    ],
                    [
                        8000,
                        10338
                    ]
                ],
                "yearly_fixed": 68916291.63599999,
                "intercept": 1755.0685787190123,
                "fixed_cost_increase": "70%",
                "slope": -0.04075965177783579,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28923
                    ],
                    [
                        4500,
                        19783
                    ],
                    [
                        6000,
                        14500
                    ],
                    [
                        8000,
                        10478
                    ]
                ],
                "yearly_fixed": 68916291.63599999,
                "intercept": 1880.4306200560845,
                "fixed_cost_increase": "70%",
                "slope": -0.043671055476252635,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29245
                    ],
                    [
                        4500,
                        20091
                    ],
                    [
                        6000,
                        14725
                    ],
                    [
                        8000,
                        10620
                    ]
                ],
                "yearly_fixed": 68916291.63599999,
                "intercept": 2005.792661393157,
                "fixed_cost_increase": "70%",
                "slope": -0.04658245917466948,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29556
                    ],
                    [
                        4500,
                        20398
                    ],
                    [
                        6000,
                        14952
                    ],
                    [
                        8000,
                        10765
                    ]
                ],
                "yearly_fixed": 68916291.63599999,
                "intercept": 2131.154702730229,
                "fixed_cost_increase": "70%",
                "slope": -0.04949386287308632,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29857
                    ],
                    [
                        4500,
                        20704
                    ],
                    [
                        6000,
                        15182
                    ],
                    [
                        8000,
                        10912
                    ]
                ],
                "yearly_fixed": 68916291.63599999,
                "intercept": 2256.5167440673017,
                "fixed_cost_increase": "70%",
                "slope": -0.05240526657150316,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30148
                    ],
                    [
                        4500,
                        21009
                    ],
                    [
                        6000,
                        15414
                    ],
                    [
                        8000,
                        11061
                    ]
                ],
                "yearly_fixed": 68916291.63599999,
                "intercept": 2381.878785404374,
                "fixed_cost_increase": "70%",
                "slope": -0.05531667026992,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28367
                    ],
                    [
                        4500,
                        19178
                    ],
                    [
                        6000,
                        14146
                    ],
                    [
                        8000,
                        10353
                    ]
                ],
                "yearly_fixed": 72970191.144,
                "intercept": 1253.620413370723,
                "fixed_cost_increase": "80%",
                "slope": -0.029114036984168423,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28719
                    ],
                    [
                        4500,
                        19484
                    ],
                    [
                        6000,
                        14361
                    ],
                    [
                        8000,
                        10488
                    ]
                ],
                "yearly_fixed": 72970191.144,
                "intercept": 1378.9824547077956,
                "fixed_cost_increase": "80%",
                "slope": -0.03202544068258527,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29060
                    ],
                    [
                        4500,
                        19790
                    ],
                    [
                        6000,
                        14579
                    ],
                    [
                        8000,
                        10626
                    ]
                ],
                "yearly_fixed": 72970191.144,
                "intercept": 1504.3444960448676,
                "fixed_cost_increase": "80%",
                "slope": -0.034936844381002106,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29391
                    ],
                    [
                        4500,
                        20096
                    ],
                    [
                        6000,
                        14799
                    ],
                    [
                        8000,
                        10766
                    ]
                ],
                "yearly_fixed": 72970191.144,
                "intercept": 1629.70653738194,
                "fixed_cost_increase": "80%",
                "slope": -0.03784824807941895,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29711
                    ],
                    [
                        4500,
                        20402
                    ],
                    [
                        6000,
                        15022
                    ],
                    [
                        8000,
                        10908
                    ]
                ],
                "yearly_fixed": 72970191.144,
                "intercept": 1755.0685787190123,
                "fixed_cost_increase": "80%",
                "slope": -0.04075965177783579,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30021
                    ],
                    [
                        4500,
                        20707
                    ],
                    [
                        6000,
                        15248
                    ],
                    [
                        8000,
                        11052
                    ]
                ],
                "yearly_fixed": 72970191.144,
                "intercept": 1880.4306200560845,
                "fixed_cost_increase": "80%",
                "slope": -0.043671055476252635,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30320
                    ],
                    [
                        4500,
                        21011
                    ],
                    [
                        6000,
                        15475
                    ],
                    [
                        8000,
                        11198
                    ]
                ],
                "yearly_fixed": 72970191.144,
                "intercept": 2005.792661393157,
                "fixed_cost_increase": "80%",
                "slope": -0.04658245917466948,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30610
                    ],
                    [
                        4500,
                        21313
                    ],
                    [
                        6000,
                        15705
                    ],
                    [
                        8000,
                        11347
                    ]
                ],
                "yearly_fixed": 72970191.144,
                "intercept": 2131.154702730229,
                "fixed_cost_increase": "80%",
                "slope": -0.04949386287308632,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30889
                    ],
                    [
                        4500,
                        21613
                    ],
                    [
                        6000,
                        15936
                    ],
                    [
                        8000,
                        11498
                    ]
                ],
                "yearly_fixed": 72970191.144,
                "intercept": 2256.5167440673017,
                "fixed_cost_increase": "80%",
                "slope": -0.05240526657150316,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31160
                    ],
                    [
                        4500,
                        21911
                    ],
                    [
                        6000,
                        16170
                    ],
                    [
                        8000,
                        11651
                    ]
                ],
                "yearly_fixed": 72970191.144,
                "intercept": 2381.878785404374,
                "fixed_cost_increase": "80%",
                "slope": -0.05531667026992,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29548
                    ],
                    [
                        4500,
                        20102
                    ],
                    [
                        6000,
                        14871
                    ],
                    [
                        8000,
                        10903
                    ]
                ],
                "yearly_fixed": 77024090.652,
                "intercept": 1253.620413370723,
                "fixed_cost_increase": "90%",
                "slope": -0.029114036984168423,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29878
                    ],
                    [
                        4500,
                        20406
                    ],
                    [
                        6000,
                        15090
                    ],
                    [
                        8000,
                        11043
                    ]
                ],
                "yearly_fixed": 77024090.652,
                "intercept": 1378.9824547077956,
                "fixed_cost_increase": "90%",
                "slope": -0.03202544068258527,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30197
                    ],
                    [
                        4500,
                        20709
                    ],
                    [
                        6000,
                        15311
                    ],
                    [
                        8000,
                        11184
                    ]
                ],
                "yearly_fixed": 77024090.652,
                "intercept": 1504.3444960448676,
                "fixed_cost_increase": "90%",
                "slope": -0.034936844381002106,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30505
                    ],
                    [
                        4500,
                        21012
                    ],
                    [
                        6000,
                        15534
                    ],
                    [
                        8000,
                        11328
                    ]
                ],
                "yearly_fixed": 77024090.652,
                "intercept": 1629.70653738194,
                "fixed_cost_increase": "90%",
                "slope": -0.03784824807941895,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30803
                    ],
                    [
                        4500,
                        21314
                    ],
                    [
                        6000,
                        15760
                    ],
                    [
                        8000,
                        11474
                    ]
                ],
                "yearly_fixed": 77024090.652,
                "intercept": 1755.0685787190123,
                "fixed_cost_increase": "90%",
                "slope": -0.04075965177783579,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31091
                    ],
                    [
                        4500,
                        21614
                    ],
                    [
                        6000,
                        15987
                    ],
                    [
                        8000,
                        11622
                    ]
                ],
                "yearly_fixed": 77024090.652,
                "intercept": 1880.4306200560845,
                "fixed_cost_increase": "90%",
                "slope": -0.043671055476252635,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31368
                    ],
                    [
                        4500,
                        21913
                    ],
                    [
                        6000,
                        16216
                    ],
                    [
                        8000,
                        11772
                    ]
                ],
                "yearly_fixed": 77024090.652,
                "intercept": 2005.792661393157,
                "fixed_cost_increase": "90%",
                "slope": -0.04658245917466948,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31636
                    ],
                    [
                        4500,
                        22209
                    ],
                    [
                        6000,
                        16447
                    ],
                    [
                        8000,
                        11924
                    ]
                ],
                "yearly_fixed": 77024090.652,
                "intercept": 2131.154702730229,
                "fixed_cost_increase": "90%",
                "slope": -0.04949386287308632,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31894
                    ],
                    [
                        4500,
                        22503
                    ],
                    [
                        6000,
                        16680
                    ],
                    [
                        8000,
                        12079
                    ]
                ],
                "yearly_fixed": 77024090.652,
                "intercept": 2256.5167440673017,
                "fixed_cost_increase": "90%",
                "slope": -0.05240526657150316,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        32143
                    ],
                    [
                        4500,
                        22794
                    ],
                    [
                        6000,
                        16914
                    ],
                    [
                        8000,
                        12235
                    ]
                ],
                "yearly_fixed": 77024090.652,
                "intercept": 2381.878785404374,
                "fixed_cost_increase": "90%",
                "slope": -0.05531667026992,
                "variable_cost_increase": "90%"
            }
        ],
        "market_segments_view_id": "27971950-570f-466a-a4a2-4e1fc5d5d7be",
        "predict_price_and_sales": [
            [
                3000,
                206150
            ],
            [
                4500,
                171600
            ],
            [
                6000,
                119850
            ],
            [
                8000,
                66050
            ]
        ],
        "base_cases_economic": [
            {
                "price_and_base_sales": [
                    [
                        3000,
                        18512
                    ],
                    [
                        4500,
                        11274
                    ],
                    [
                        6000,
                        7905
                    ],
                    [
                        8000,
                        5597
                    ]
                ],
                "yearly_fixed": 37016656.668,
                "intercept": 1553.7517934393422,
                "fixed_cost_increase": "0%",
                "slope": -0.029885229166254485,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        19244
                    ],
                    [
                        4500,
                        11661
                    ],
                    [
                        6000,
                        8121
                    ],
                    [
                        8000,
                        5713
                    ]
                ],
                "yearly_fixed": 37016656.668,
                "intercept": 1709.1269727832766,
                "fixed_cost_increase": "0%",
                "slope": -0.03287375208287994,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        19985
                    ],
                    [
                        4500,
                        12064
                    ],
                    [
                        6000,
                        8346
                    ],
                    [
                        8000,
                        5834
                    ]
                ],
                "yearly_fixed": 37016656.668,
                "intercept": 1864.5021521272106,
                "fixed_cost_increase": "0%",
                "slope": -0.03586227499950538,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        20731
                    ],
                    [
                        4500,
                        12483
                    ],
                    [
                        6000,
                        8581
                    ],
                    [
                        8000,
                        5959
                    ]
                ],
                "yearly_fixed": 37016656.668,
                "intercept": 2019.877331471145,
                "fixed_cost_increase": "0%",
                "slope": -0.03885079791613083,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        21478
                    ],
                    [
                        4500,
                        12919
                    ],
                    [
                        6000,
                        8826
                    ],
                    [
                        8000,
                        6088
                    ]
                ],
                "yearly_fixed": 37016656.668,
                "intercept": 2175.252510815079,
                "fixed_cost_increase": "0%",
                "slope": -0.04183932083275628,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22223
                    ],
                    [
                        4500,
                        13369
                    ],
                    [
                        6000,
                        9080
                    ],
                    [
                        8000,
                        6223
                    ]
                ],
                "yearly_fixed": 37016656.668,
                "intercept": 2330.6276901590136,
                "fixed_cost_increase": "0%",
                "slope": -0.04482784374938173,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22963
                    ],
                    [
                        4500,
                        13835
                    ],
                    [
                        6000,
                        9345
                    ],
                    [
                        8000,
                        6362
                    ]
                ],
                "yearly_fixed": 37016656.668,
                "intercept": 2486.0028695029478,
                "fixed_cost_increase": "0%",
                "slope": -0.04781636666600718,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23693
                    ],
                    [
                        4500,
                        14314
                    ],
                    [
                        6000,
                        9621
                    ],
                    [
                        8000,
                        6506
                    ]
                ],
                "yearly_fixed": 37016656.668,
                "intercept": 2641.378048846882,
                "fixed_cost_increase": "0%",
                "slope": -0.05080488958263262,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24410
                    ],
                    [
                        4500,
                        14807
                    ],
                    [
                        6000,
                        9907
                    ],
                    [
                        8000,
                        6656
                    ]
                ],
                "yearly_fixed": 37016656.668,
                "intercept": 2796.753228190816,
                "fixed_cost_increase": "0%",
                "slope": -0.05379341249925807,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25114
                    ],
                    [
                        4500,
                        15312
                    ],
                    [
                        6000,
                        10204
                    ],
                    [
                        8000,
                        6811
                    ]
                ],
                "yearly_fixed": 37016656.668,
                "intercept": 2952.1284075347503,
                "fixed_cost_increase": "0%",
                "slope": -0.05678193541588352,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        19939
                    ],
                    [
                        4500,
                        12288
                    ],
                    [
                        6000,
                        8654
                    ],
                    [
                        8000,
                        6141
                    ]
                ],
                "yearly_fixed": 40718322.3348,
                "intercept": 1553.7517934393422,
                "fixed_cost_increase": "10%",
                "slope": -0.029885229166254485,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        20666
                    ],
                    [
                        4500,
                        12692
                    ],
                    [
                        6000,
                        8884
                    ],
                    [
                        8000,
                        6267
                    ]
                ],
                "yearly_fixed": 40718322.3348,
                "intercept": 1709.1269727832766,
                "fixed_cost_increase": "10%",
                "slope": -0.03287375208287994,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        21398
                    ],
                    [
                        4500,
                        13110
                    ],
                    [
                        6000,
                        9124
                    ],
                    [
                        8000,
                        6397
                    ]
                ],
                "yearly_fixed": 40718322.3348,
                "intercept": 1864.5021521272106,
                "fixed_cost_increase": "10%",
                "slope": -0.03586227499950538,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22130
                    ],
                    [
                        4500,
                        13544
                    ],
                    [
                        6000,
                        9372
                    ],
                    [
                        8000,
                        6531
                    ]
                ],
                "yearly_fixed": 40718322.3348,
                "intercept": 2019.877331471145,
                "fixed_cost_increase": "10%",
                "slope": -0.03885079791613083,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22860
                    ],
                    [
                        4500,
                        13991
                    ],
                    [
                        6000,
                        9631
                    ],
                    [
                        8000,
                        6670
                    ]
                ],
                "yearly_fixed": 40718322.3348,
                "intercept": 2175.252510815079,
                "fixed_cost_increase": "10%",
                "slope": -0.04183932083275628,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23583
                    ],
                    [
                        4500,
                        14453
                    ],
                    [
                        6000,
                        9899
                    ],
                    [
                        8000,
                        6814
                    ]
                ],
                "yearly_fixed": 40718322.3348,
                "intercept": 2330.6276901590136,
                "fixed_cost_increase": "10%",
                "slope": -0.04482784374938173,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24297
                    ],
                    [
                        4500,
                        14927
                    ],
                    [
                        6000,
                        10177
                    ],
                    [
                        8000,
                        6963
                    ]
                ],
                "yearly_fixed": 40718322.3348,
                "intercept": 2486.0028695029478,
                "fixed_cost_increase": "10%",
                "slope": -0.04781636666600718,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24999
                    ],
                    [
                        4500,
                        15413
                    ],
                    [
                        6000,
                        10466
                    ],
                    [
                        8000,
                        7118
                    ]
                ],
                "yearly_fixed": 40718322.3348,
                "intercept": 2641.378048846882,
                "fixed_cost_increase": "10%",
                "slope": -0.05080488958263262,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25688
                    ],
                    [
                        4500,
                        15910
                    ],
                    [
                        6000,
                        10765
                    ],
                    [
                        8000,
                        7277
                    ]
                ],
                "yearly_fixed": 40718322.3348,
                "intercept": 2796.753228190816,
                "fixed_cost_increase": "10%",
                "slope": -0.05379341249925807,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26360
                    ],
                    [
                        4500,
                        16417
                    ],
                    [
                        6000,
                        11074
                    ],
                    [
                        8000,
                        7443
                    ]
                ],
                "yearly_fixed": 40718322.3348,
                "intercept": 2952.1284075347503,
                "fixed_cost_increase": "10%",
                "slope": -0.05678193541588352,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        21320
                    ],
                    [
                        4500,
                        13286
                    ],
                    [
                        6000,
                        9396
                    ],
                    [
                        8000,
                        6683
                    ]
                ],
                "yearly_fixed": 44419988.0016,
                "intercept": 1553.7517934393422,
                "fixed_cost_increase": "20%",
                "slope": -0.029885229166254485,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22040
                    ],
                    [
                        4500,
                        13704
                    ],
                    [
                        6000,
                        9640
                    ],
                    [
                        8000,
                        6818
                    ]
                ],
                "yearly_fixed": 44419988.0016,
                "intercept": 1709.1269727832766,
                "fixed_cost_increase": "20%",
                "slope": -0.03287375208287994,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22759
                    ],
                    [
                        4500,
                        14135
                    ],
                    [
                        6000,
                        9892
                    ],
                    [
                        8000,
                        6956
                    ]
                ],
                "yearly_fixed": 44419988.0016,
                "intercept": 1864.5021521272106,
                "fixed_cost_increase": "20%",
                "slope": -0.03586227499950538,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23475
                    ],
                    [
                        4500,
                        14580
                    ],
                    [
                        6000,
                        10154
                    ],
                    [
                        8000,
                        7100
                    ]
                ],
                "yearly_fixed": 44419988.0016,
                "intercept": 2019.877331471145,
                "fixed_cost_increase": "20%",
                "slope": -0.03885079791613083,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24185
                    ],
                    [
                        4500,
                        15037
                    ],
                    [
                        6000,
                        10424
                    ],
                    [
                        8000,
                        7248
                    ]
                ],
                "yearly_fixed": 44419988.0016,
                "intercept": 2175.252510815079,
                "fixed_cost_increase": "20%",
                "slope": -0.04183932083275628,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24885
                    ],
                    [
                        4500,
                        15506
                    ],
                    [
                        6000,
                        10705
                    ],
                    [
                        8000,
                        7401
                    ]
                ],
                "yearly_fixed": 44419988.0016,
                "intercept": 2330.6276901590136,
                "fixed_cost_increase": "20%",
                "slope": -0.04482784374938173,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25574
                    ],
                    [
                        4500,
                        15987
                    ],
                    [
                        6000,
                        10995
                    ],
                    [
                        8000,
                        7560
                    ]
                ],
                "yearly_fixed": 44419988.0016,
                "intercept": 2486.0028695029478,
                "fixed_cost_increase": "20%",
                "slope": -0.04781636666600718,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26249
                    ],
                    [
                        4500,
                        16477
                    ],
                    [
                        6000,
                        11295
                    ],
                    [
                        8000,
                        7723
                    ]
                ],
                "yearly_fixed": 44419988.0016,
                "intercept": 2641.378048846882,
                "fixed_cost_increase": "20%",
                "slope": -0.05080488958263262,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26908
                    ],
                    [
                        4500,
                        16976
                    ],
                    [
                        6000,
                        11605
                    ],
                    [
                        8000,
                        7892
                    ]
                ],
                "yearly_fixed": 44419988.0016,
                "intercept": 2796.753228190816,
                "fixed_cost_increase": "20%",
                "slope": -0.05379341249925807,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27551
                    ],
                    [
                        4500,
                        17483
                    ],
                    [
                        6000,
                        11924
                    ],
                    [
                        8000,
                        8067
                    ]
                ],
                "yearly_fixed": 44419988.0016,
                "intercept": 2952.1284075347503,
                "fixed_cost_increase": "20%",
                "slope": -0.05678193541588352,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        22661
                    ],
                    [
                        4500,
                        14268
                    ],
                    [
                        6000,
                        10132
                    ],
                    [
                        8000,
                        7223
                    ]
                ],
                "yearly_fixed": 48121653.6684,
                "intercept": 1553.7517934393422,
                "fixed_cost_increase": "30%",
                "slope": -0.029885229166254485,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23369
                    ],
                    [
                        4500,
                        14697
                    ],
                    [
                        6000,
                        10388
                    ],
                    [
                        8000,
                        7365
                    ]
                ],
                "yearly_fixed": 48121653.6684,
                "intercept": 1709.1269727832766,
                "fixed_cost_increase": "30%",
                "slope": -0.03287375208287994,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24074
                    ],
                    [
                        4500,
                        15139
                    ],
                    [
                        6000,
                        10652
                    ],
                    [
                        8000,
                        7513
                    ]
                ],
                "yearly_fixed": 48121653.6684,
                "intercept": 1864.5021521272106,
                "fixed_cost_increase": "30%",
                "slope": -0.03586227499950538,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24772
                    ],
                    [
                        4500,
                        15593
                    ],
                    [
                        6000,
                        10925
                    ],
                    [
                        8000,
                        7665
                    ]
                ],
                "yearly_fixed": 48121653.6684,
                "intercept": 2019.877331471145,
                "fixed_cost_increase": "30%",
                "slope": -0.03885079791613083,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25460
                    ],
                    [
                        4500,
                        16058
                    ],
                    [
                        6000,
                        11207
                    ],
                    [
                        8000,
                        7822
                    ]
                ],
                "yearly_fixed": 48121653.6684,
                "intercept": 2175.252510815079,
                "fixed_cost_increase": "30%",
                "slope": -0.04183932083275628,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26137
                    ],
                    [
                        4500,
                        16533
                    ],
                    [
                        6000,
                        11499
                    ],
                    [
                        8000,
                        7983
                    ]
                ],
                "yearly_fixed": 48121653.6684,
                "intercept": 2330.6276901590136,
                "fixed_cost_increase": "30%",
                "slope": -0.04482784374938173,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26800
                    ],
                    [
                        4500,
                        17017
                    ],
                    [
                        6000,
                        11799
                    ],
                    [
                        8000,
                        8151
                    ]
                ],
                "yearly_fixed": 48121653.6684,
                "intercept": 2486.0028695029478,
                "fixed_cost_increase": "30%",
                "slope": -0.04781636666600718,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27448
                    ],
                    [
                        4500,
                        17510
                    ],
                    [
                        6000,
                        12109
                    ],
                    [
                        8000,
                        8323
                    ]
                ],
                "yearly_fixed": 48121653.6684,
                "intercept": 2641.378048846882,
                "fixed_cost_increase": "30%",
                "slope": -0.05080488958263262,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28079
                    ],
                    [
                        4500,
                        18009
                    ],
                    [
                        6000,
                        12428
                    ],
                    [
                        8000,
                        8501
                    ]
                ],
                "yearly_fixed": 48121653.6684,
                "intercept": 2796.753228190816,
                "fixed_cost_increase": "30%",
                "slope": -0.05379341249925807,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28693
                    ],
                    [
                        4500,
                        18514
                    ],
                    [
                        6000,
                        12756
                    ],
                    [
                        8000,
                        8684
                    ]
                ],
                "yearly_fixed": 48121653.6684,
                "intercept": 2952.1284075347503,
                "fixed_cost_increase": "30%",
                "slope": -0.05678193541588352,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        23965
                    ],
                    [
                        4500,
                        15235
                    ],
                    [
                        6000,
                        10862
                    ],
                    [
                        8000,
                        7760
                    ]
                ],
                "yearly_fixed": 51823319.3352,
                "intercept": 1553.7517934393422,
                "fixed_cost_increase": "40%",
                "slope": -0.029885229166254485,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        24659
                    ],
                    [
                        4500,
                        15674
                    ],
                    [
                        6000,
                        11128
                    ],
                    [
                        8000,
                        7910
                    ]
                ],
                "yearly_fixed": 51823319.3352,
                "intercept": 1709.1269727832766,
                "fixed_cost_increase": "40%",
                "slope": -0.03287375208287994,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25347
                    ],
                    [
                        4500,
                        16125
                    ],
                    [
                        6000,
                        11403
                    ],
                    [
                        8000,
                        8066
                    ]
                ],
                "yearly_fixed": 51823319.3352,
                "intercept": 1864.5021521272106,
                "fixed_cost_increase": "40%",
                "slope": -0.03586227499950538,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26025
                    ],
                    [
                        4500,
                        16586
                    ],
                    [
                        6000,
                        11687
                    ],
                    [
                        8000,
                        8226
                    ]
                ],
                "yearly_fixed": 51823319.3352,
                "intercept": 2019.877331471145,
                "fixed_cost_increase": "40%",
                "slope": -0.03885079791613083,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26692
                    ],
                    [
                        4500,
                        17056
                    ],
                    [
                        6000,
                        11979
                    ],
                    [
                        8000,
                        8391
                    ]
                ],
                "yearly_fixed": 51823319.3352,
                "intercept": 2175.252510815079,
                "fixed_cost_increase": "40%",
                "slope": -0.04183932083275628,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27344
                    ],
                    [
                        4500,
                        17534
                    ],
                    [
                        6000,
                        12280
                    ],
                    [
                        8000,
                        8561
                    ]
                ],
                "yearly_fixed": 51823319.3352,
                "intercept": 2330.6276901590136,
                "fixed_cost_increase": "40%",
                "slope": -0.04482784374938173,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27982
                    ],
                    [
                        4500,
                        18021
                    ],
                    [
                        6000,
                        12590
                    ],
                    [
                        8000,
                        8736
                    ]
                ],
                "yearly_fixed": 51823319.3352,
                "intercept": 2486.0028695029478,
                "fixed_cost_increase": "40%",
                "slope": -0.04781636666600718,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28603
                    ],
                    [
                        4500,
                        18513
                    ],
                    [
                        6000,
                        12909
                    ],
                    [
                        8000,
                        8917
                    ]
                ],
                "yearly_fixed": 51823319.3352,
                "intercept": 2641.378048846882,
                "fixed_cost_increase": "40%",
                "slope": -0.05080488958263262,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29206
                    ],
                    [
                        4500,
                        19011
                    ],
                    [
                        6000,
                        13236
                    ],
                    [
                        8000,
                        9103
                    ]
                ],
                "yearly_fixed": 51823319.3352,
                "intercept": 2796.753228190816,
                "fixed_cost_increase": "40%",
                "slope": -0.05379341249925807,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29791
                    ],
                    [
                        4500,
                        19512
                    ],
                    [
                        6000,
                        13571
                    ],
                    [
                        8000,
                        9294
                    ]
                ],
                "yearly_fixed": 51823319.3352,
                "intercept": 2952.1284075347503,
                "fixed_cost_increase": "40%",
                "slope": -0.05678193541588352,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25234
                    ],
                    [
                        4500,
                        16187
                    ],
                    [
                        6000,
                        11585
                    ],
                    [
                        8000,
                        8294
                    ]
                ],
                "yearly_fixed": 55524985.002,
                "intercept": 1553.7517934393422,
                "fixed_cost_increase": "50%",
                "slope": -0.029885229166254485,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        25913
                    ],
                    [
                        4500,
                        16635
                    ],
                    [
                        6000,
                        11862
                    ],
                    [
                        8000,
                        8452
                    ]
                ],
                "yearly_fixed": 55524985.002,
                "intercept": 1709.1269727832766,
                "fixed_cost_increase": "50%",
                "slope": -0.03287375208287994,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26582
                    ],
                    [
                        4500,
                        17092
                    ],
                    [
                        6000,
                        12146
                    ],
                    [
                        8000,
                        8615
                    ]
                ],
                "yearly_fixed": 55524985.002,
                "intercept": 1864.5021521272106,
                "fixed_cost_increase": "50%",
                "slope": -0.03586227499950538,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27239
                    ],
                    [
                        4500,
                        17558
                    ],
                    [
                        6000,
                        12439
                    ],
                    [
                        8000,
                        8783
                    ]
                ],
                "yearly_fixed": 55524985.002,
                "intercept": 2019.877331471145,
                "fixed_cost_increase": "50%",
                "slope": -0.03885079791613083,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27883
                    ],
                    [
                        4500,
                        18032
                    ],
                    [
                        6000,
                        12741
                    ],
                    [
                        8000,
                        8956
                    ]
                ],
                "yearly_fixed": 55524985.002,
                "intercept": 2175.252510815079,
                "fixed_cost_increase": "50%",
                "slope": -0.04183932083275628,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28511
                    ],
                    [
                        4500,
                        18512
                    ],
                    [
                        6000,
                        13051
                    ],
                    [
                        8000,
                        9134
                    ]
                ],
                "yearly_fixed": 55524985.002,
                "intercept": 2330.6276901590136,
                "fixed_cost_increase": "50%",
                "slope": -0.04482784374938173,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29123
                    ],
                    [
                        4500,
                        18999
                    ],
                    [
                        6000,
                        13369
                    ],
                    [
                        8000,
                        9317
                    ]
                ],
                "yearly_fixed": 55524985.002,
                "intercept": 2486.0028695029478,
                "fixed_cost_increase": "50%",
                "slope": -0.04781636666600718,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29717
                    ],
                    [
                        4500,
                        19490
                    ],
                    [
                        6000,
                        13695
                    ],
                    [
                        8000,
                        9505
                    ]
                ],
                "yearly_fixed": 55524985.002,
                "intercept": 2641.378048846882,
                "fixed_cost_increase": "50%",
                "slope": -0.05080488958263262,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30294
                    ],
                    [
                        4500,
                        19985
                    ],
                    [
                        6000,
                        14028
                    ],
                    [
                        8000,
                        9698
                    ]
                ],
                "yearly_fixed": 55524985.002,
                "intercept": 2796.753228190816,
                "fixed_cost_increase": "50%",
                "slope": -0.05379341249925807,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30852
                    ],
                    [
                        4500,
                        20482
                    ],
                    [
                        6000,
                        14370
                    ],
                    [
                        8000,
                        9897
                    ]
                ],
                "yearly_fixed": 55524985.002,
                "intercept": 2952.1284075347503,
                "fixed_cost_increase": "50%",
                "slope": -0.05678193541588352,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        26471
                    ],
                    [
                        4500,
                        17126
                    ],
                    [
                        6000,
                        12303
                    ],
                    [
                        8000,
                        8826
                    ]
                ],
                "yearly_fixed": 59226650.6688,
                "intercept": 1553.7517934393422,
                "fixed_cost_increase": "60%",
                "slope": -0.029885229166254485,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27132
                    ],
                    [
                        4500,
                        17580
                    ],
                    [
                        6000,
                        12588
                    ],
                    [
                        8000,
                        8992
                    ]
                ],
                "yearly_fixed": 59226650.6688,
                "intercept": 1709.1269727832766,
                "fixed_cost_increase": "60%",
                "slope": -0.03287375208287994,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27782
                    ],
                    [
                        4500,
                        18042
                    ],
                    [
                        6000,
                        12882
                    ],
                    [
                        8000,
                        9162
                    ]
                ],
                "yearly_fixed": 59226650.6688,
                "intercept": 1864.5021521272106,
                "fixed_cost_increase": "60%",
                "slope": -0.03586227499950538,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28417
                    ],
                    [
                        4500,
                        18512
                    ],
                    [
                        6000,
                        13183
                    ],
                    [
                        8000,
                        9337
                    ]
                ],
                "yearly_fixed": 59226650.6688,
                "intercept": 2019.877331471145,
                "fixed_cost_increase": "60%",
                "slope": -0.03885079791613083,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29037
                    ],
                    [
                        4500,
                        18987
                    ],
                    [
                        6000,
                        13493
                    ],
                    [
                        8000,
                        9517
                    ]
                ],
                "yearly_fixed": 59226650.6688,
                "intercept": 2175.252510815079,
                "fixed_cost_increase": "60%",
                "slope": -0.04183932083275628,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29641
                    ],
                    [
                        4500,
                        19468
                    ],
                    [
                        6000,
                        13810
                    ],
                    [
                        8000,
                        9702
                    ]
                ],
                "yearly_fixed": 59226650.6688,
                "intercept": 2330.6276901590136,
                "fixed_cost_increase": "60%",
                "slope": -0.04482784374938173,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30227
                    ],
                    [
                        4500,
                        19954
                    ],
                    [
                        6000,
                        14135
                    ],
                    [
                        8000,
                        9892
                    ]
                ],
                "yearly_fixed": 59226650.6688,
                "intercept": 2486.0028695029478,
                "fixed_cost_increase": "60%",
                "slope": -0.04781636666600718,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30795
                    ],
                    [
                        4500,
                        20442
                    ],
                    [
                        6000,
                        14467
                    ],
                    [
                        8000,
                        10087
                    ]
                ],
                "yearly_fixed": 59226650.6688,
                "intercept": 2641.378048846882,
                "fixed_cost_increase": "60%",
                "slope": -0.05080488958263262,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31345
                    ],
                    [
                        4500,
                        20933
                    ],
                    [
                        6000,
                        14807
                    ],
                    [
                        8000,
                        10288
                    ]
                ],
                "yearly_fixed": 59226650.6688,
                "intercept": 2796.753228190816,
                "fixed_cost_increase": "60%",
                "slope": -0.05379341249925807,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31877
                    ],
                    [
                        4500,
                        21424
                    ],
                    [
                        6000,
                        15153
                    ],
                    [
                        8000,
                        10494
                    ]
                ],
                "yearly_fixed": 59226650.6688,
                "intercept": 2952.1284075347503,
                "fixed_cost_increase": "60%",
                "slope": -0.05678193541588352,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        27679
                    ],
                    [
                        4500,
                        18052
                    ],
                    [
                        6000,
                        13014
                    ],
                    [
                        8000,
                        9356
                    ]
                ],
                "yearly_fixed": 62928316.335599996,
                "intercept": 1553.7517934393422,
                "fixed_cost_increase": "70%",
                "slope": -0.029885229166254485,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28321
                    ],
                    [
                        4500,
                        18511
                    ],
                    [
                        6000,
                        13308
                    ],
                    [
                        8000,
                        9528
                    ]
                ],
                "yearly_fixed": 62928316.335599996,
                "intercept": 1709.1269727832766,
                "fixed_cost_increase": "70%",
                "slope": -0.03287375208287994,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28949
                    ],
                    [
                        4500,
                        18976
                    ],
                    [
                        6000,
                        13610
                    ],
                    [
                        8000,
                        9705
                    ]
                ],
                "yearly_fixed": 62928316.335599996,
                "intercept": 1864.5021521272106,
                "fixed_cost_increase": "70%",
                "slope": -0.03586227499950538,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29562
                    ],
                    [
                        4500,
                        19448
                    ],
                    [
                        6000,
                        13919
                    ],
                    [
                        8000,
                        9887
                    ]
                ],
                "yearly_fixed": 62928316.335599996,
                "intercept": 2019.877331471145,
                "fixed_cost_increase": "70%",
                "slope": -0.03885079791613083,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30158
                    ],
                    [
                        4500,
                        19924
                    ],
                    [
                        6000,
                        14235
                    ],
                    [
                        8000,
                        10074
                    ]
                ],
                "yearly_fixed": 62928316.335599996,
                "intercept": 2175.252510815079,
                "fixed_cost_increase": "70%",
                "slope": -0.04183932083275628,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30737
                    ],
                    [
                        4500,
                        20404
                    ],
                    [
                        6000,
                        14559
                    ],
                    [
                        8000,
                        10266
                    ]
                ],
                "yearly_fixed": 62928316.335599996,
                "intercept": 2330.6276901590136,
                "fixed_cost_increase": "70%",
                "slope": -0.04482784374938173,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31298
                    ],
                    [
                        4500,
                        20887
                    ],
                    [
                        6000,
                        14890
                    ],
                    [
                        8000,
                        10463
                    ]
                ],
                "yearly_fixed": 62928316.335599996,
                "intercept": 2486.0028695029478,
                "fixed_cost_increase": "70%",
                "slope": -0.04781636666600718,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31841
                    ],
                    [
                        4500,
                        21372
                    ],
                    [
                        6000,
                        15228
                    ],
                    [
                        8000,
                        10664
                    ]
                ],
                "yearly_fixed": 62928316.335599996,
                "intercept": 2641.378048846882,
                "fixed_cost_increase": "70%",
                "slope": -0.05080488958263262,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        32365
                    ],
                    [
                        4500,
                        21857
                    ],
                    [
                        6000,
                        15572
                    ],
                    [
                        8000,
                        10872
                    ]
                ],
                "yearly_fixed": 62928316.335599996,
                "intercept": 2796.753228190816,
                "fixed_cost_increase": "70%",
                "slope": -0.05379341249925807,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        32871
                    ],
                    [
                        4500,
                        22342
                    ],
                    [
                        6000,
                        15923
                    ],
                    [
                        8000,
                        11084
                    ]
                ],
                "yearly_fixed": 62928316.335599996,
                "intercept": 2952.1284075347503,
                "fixed_cost_increase": "70%",
                "slope": -0.05678193541588352,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        28859
                    ],
                    [
                        4500,
                        18966
                    ],
                    [
                        6000,
                        13720
                    ],
                    [
                        8000,
                        9883
                    ]
                ],
                "yearly_fixed": 66629982.002399996,
                "intercept": 1553.7517934393422,
                "fixed_cost_increase": "80%",
                "slope": -0.029885229166254485,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        29481
                    ],
                    [
                        4500,
                        19428
                    ],
                    [
                        6000,
                        14021
                    ],
                    [
                        8000,
                        10062
                    ]
                ],
                "yearly_fixed": 66629982.002399996,
                "intercept": 1709.1269727832766,
                "fixed_cost_increase": "80%",
                "slope": -0.03287375208287994,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30087
                    ],
                    [
                        4500,
                        19895
                    ],
                    [
                        6000,
                        14330
                    ],
                    [
                        8000,
                        10246
                    ]
                ],
                "yearly_fixed": 66629982.002399996,
                "intercept": 1864.5021521272106,
                "fixed_cost_increase": "80%",
                "slope": -0.03586227499950538,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30677
                    ],
                    [
                        4500,
                        20367
                    ],
                    [
                        6000,
                        14646
                    ],
                    [
                        8000,
                        10434
                    ]
                ],
                "yearly_fixed": 66629982.002399996,
                "intercept": 2019.877331471145,
                "fixed_cost_increase": "80%",
                "slope": -0.03885079791613083,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31249
                    ],
                    [
                        4500,
                        20842
                    ],
                    [
                        6000,
                        14969
                    ],
                    [
                        8000,
                        10627
                    ]
                ],
                "yearly_fixed": 66629982.002399996,
                "intercept": 2175.252510815079,
                "fixed_cost_increase": "80%",
                "slope": -0.04183932083275628,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31803
                    ],
                    [
                        4500,
                        21320
                    ],
                    [
                        6000,
                        15298
                    ],
                    [
                        8000,
                        10825
                    ]
                ],
                "yearly_fixed": 66629982.002399996,
                "intercept": 2330.6276901590136,
                "fixed_cost_increase": "80%",
                "slope": -0.04482784374938173,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        32339
                    ],
                    [
                        4500,
                        21800
                    ],
                    [
                        6000,
                        15634
                    ],
                    [
                        8000,
                        11028
                    ]
                ],
                "yearly_fixed": 66629982.002399996,
                "intercept": 2486.0028695029478,
                "fixed_cost_increase": "80%",
                "slope": -0.04781636666600718,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        32856
                    ],
                    [
                        4500,
                        22280
                    ],
                    [
                        6000,
                        15977
                    ],
                    [
                        8000,
                        11237
                    ]
                ],
                "yearly_fixed": 66629982.002399996,
                "intercept": 2641.378048846882,
                "fixed_cost_increase": "80%",
                "slope": -0.05080488958263262,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        33355
                    ],
                    [
                        4500,
                        22759
                    ],
                    [
                        6000,
                        16325
                    ],
                    [
                        8000,
                        11450
                    ]
                ],
                "yearly_fixed": 66629982.002399996,
                "intercept": 2796.753228190816,
                "fixed_cost_increase": "80%",
                "slope": -0.05379341249925807,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        33836
                    ],
                    [
                        4500,
                        23237
                    ],
                    [
                        6000,
                        16678
                    ],
                    [
                        8000,
                        11668
                    ]
                ],
                "yearly_fixed": 66629982.002399996,
                "intercept": 2952.1284075347503,
                "fixed_cost_increase": "80%",
                "slope": -0.05678193541588352,
                "variable_cost_increase": "90%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30014
                    ],
                    [
                        4500,
                        19867
                    ],
                    [
                        6000,
                        14420
                    ],
                    [
                        8000,
                        10408
                    ]
                ],
                "yearly_fixed": 70331647.66919999,
                "intercept": 1553.7517934393422,
                "fixed_cost_increase": "90%",
                "slope": -0.029885229166254485,
                "variable_cost_increase": "0%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        30614
                    ],
                    [
                        4500,
                        20331
                    ],
                    [
                        6000,
                        14728
                    ],
                    [
                        8000,
                        10593
                    ]
                ],
                "yearly_fixed": 70331647.66919999,
                "intercept": 1709.1269727832766,
                "fixed_cost_increase": "90%",
                "slope": -0.03287375208287994,
                "variable_cost_increase": "10%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31198
                    ],
                    [
                        4500,
                        20799
                    ],
                    [
                        6000,
                        15044
                    ],
                    [
                        8000,
                        10783
                    ]
                ],
                "yearly_fixed": 70331647.66919999,
                "intercept": 1864.5021521272106,
                "fixed_cost_increase": "90%",
                "slope": -0.03586227499950538,
                "variable_cost_increase": "20%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        31764
                    ],
                    [
                        4500,
                        21270
                    ],
                    [
                        6000,
                        15365
                    ],
                    [
                        8000,
                        10977
                    ]
                ],
                "yearly_fixed": 70331647.66919999,
                "intercept": 2019.877331471145,
                "fixed_cost_increase": "90%",
                "slope": -0.03885079791613083,
                "variable_cost_increase": "30%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        32311
                    ],
                    [
                        4500,
                        21744
                    ],
                    [
                        6000,
                        15694
                    ],
                    [
                        8000,
                        11177
                    ]
                ],
                "yearly_fixed": 70331647.66919999,
                "intercept": 2175.252510815079,
                "fixed_cost_increase": "90%",
                "slope": -0.04183932083275628,
                "variable_cost_increase": "40%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        32841
                    ],
                    [
                        4500,
                        22218
                    ],
                    [
                        6000,
                        16028
                    ],
                    [
                        8000,
                        11381
                    ]
                ],
                "yearly_fixed": 70331647.66919999,
                "intercept": 2330.6276901590136,
                "fixed_cost_increase": "90%",
                "slope": -0.04482784374938173,
                "variable_cost_increase": "50%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        33351
                    ],
                    [
                        4500,
                        22693
                    ],
                    [
                        6000,
                        16368
                    ],
                    [
                        8000,
                        11590
                    ]
                ],
                "yearly_fixed": 70331647.66919999,
                "intercept": 2486.0028695029478,
                "fixed_cost_increase": "90%",
                "slope": -0.04781636666600718,
                "variable_cost_increase": "60%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        33844
                    ],
                    [
                        4500,
                        23168
                    ],
                    [
                        6000,
                        16714
                    ],
                    [
                        8000,
                        11803
                    ]
                ],
                "yearly_fixed": 70331647.66919999,
                "intercept": 2641.378048846882,
                "fixed_cost_increase": "90%",
                "slope": -0.05080488958263262,
                "variable_cost_increase": "70%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        34318
                    ],
                    [
                        4500,
                        23641
                    ],
                    [
                        6000,
                        17065
                    ],
                    [
                        8000,
                        12022
                    ]
                ],
                "yearly_fixed": 70331647.66919999,
                "intercept": 2796.753228190816,
                "fixed_cost_increase": "90%",
                "slope": -0.05379341249925807,
                "variable_cost_increase": "80%"
            },
            {
                "price_and_base_sales": [
                    [
                        3000,
                        34775
                    ],
                    [
                        4500,
                        24111
                    ],
                    [
                        6000,
                        17421
                    ],
                    [
                        8000,
                        12246
                    ]
                ],
                "yearly_fixed": 70331647.66919999,
                "intercept": 2952.1284075347503,
                "fixed_cost_increase": "90%",
                "slope": -0.05678193541588352,
                "variable_cost_increase": "90%"
            }
        ]
    }
)
// 
let loading =ref(false)
let dataid =ref('');
const getData=(ischange)=>{
  hasgettabledata.value=false;
  dataid.value='';
  baseData.value={};
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      toGetViewData(hasviewid)
    }
};
let marketrefcomponent=ref(null)
let hasgettabledata=ref(false)
const getTableData=()=>{
  if(hasgettabledata.value){
    return
  }
  const viewId=baseData.value?.market_segments_view_id;
  if(viewId){
    nextTick(()=>{
        marketrefcomponent.value.toGetViewData(viewId)
    })
  }
  // 
  
}
// 
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
      dataid.value=res?.data?.priceSessionVIew?.id
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            if(chartdatas&&chartdatas.length){
              baseData.value=chartdatas[0]
              countInitInfo()
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
};
// 初始化图表
const initChart = () => {
    const chartDom = document.getElementById('chartinner');
    if (!chartDom) return;
    // 如果图表已存在，则先销毁
    if (myChart) {
        myChart.dispose();
    }
    myChart = echarts.init(chartDom);
    getData()
    // countInitInfo()//测试
};
// 更新图表数据
const updateChart = (option) => {
    if (!myChart) return;
    myChart.setOption(option);
};
const getChartOption = ({financialData,economicData,predictedData}) => {
  return {
    title: {
        text: '价格与销量对比分析',
        subtext: '财务视角销量 vs 经济视角销量 vs 预测销量',
        left: 'center'
    },
    tooltip: {
        trigger: 'axis',
        formatter: function(params) {
            let result = `价格: ${params[0].axisValue}<br>`;
            params.forEach(param => {
                result += `${param.seriesName}: ${param.value.toLocaleString()}<br>`;
            });
            return result;
        }
    },
    legend: {
        data: ['财务视角销量', '经济视角销量', '预测销量'],
        bottom: 10
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        name: '价格',
        nameLocation: 'middle',
        nameGap: 30,
        data: financialData.map(item => item[0])
    },
    yAxis: {
        type: 'value',
        name: '销量',
        axisLabel: {
            formatter: function(value) {
                return value.toLocaleString();
            }
        }
    },
    series: [
        {
            name: '财务视角销量',
            type: 'line',
            data: financialData?.map(item => item[1]),
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
                width: 3
            },
            itemStyle: {
                color: '#5470C6'
            }
        },
        {
            name: '经济视角销量',
            type: 'line',
            data: economicData?.map(item => item[1]),
            smooth: true,
            symbol: 'diamond',
            symbolSize: 8,
            lineStyle: {
                width: 3
            },
            itemStyle: {
                color: '#91CC75'
            }
        },
        {
            name: '预测销量',
            type: 'line',
            data: predictedData?.map(item => item[1]),
            smooth: true,
            symbol: 'triangle',
            symbolSize: 8,
            lineStyle: {
                width: 3,
                type: 'dashed'
            },
            itemStyle: {
                color: '#EE6666'
            }
        }
    ]
};
};
const processData = (dataset) => {
  
  // 分离价格和销量数据
  const baseData = dataset.price_and_base_sales;
  const predictData = dataset.predict_price_and_sales;
  
  // 提取价格序列（x轴）
  const prices = baseData.map(item => item[0]);
  
  // 提取基础销量和预测销量
  const baseSales = baseData.map(item => item[1]);
  const predictSales = predictData.map(item => item[1]);
  
  return {
    prices,
    baseSales,
    predictSales,
    baseData,
    predictData
  };
};
const countInitInfo=()=>{
    getTableData()
    console.log('baseData.value.base_cases',baseData.value)

    // 提取财务数据（base_cases）中fixed_cost_increase为0%的数据
    const financialData = baseData.value.base_cases.find(
        item => item.fixed_cost_increase === (financialfcper.value+"%") && item.variable_cost_increase === (financialvcper.value+"%")
    )?.price_and_base_sales;

    // if(!financialData?.length){
    //   ElMessage.warning("财务视角数据获取失败")
    //   return
    // }
    // 提取经济数据（base_cases_economic）中fixed_cost_increase为0%的数据
    const economicData = baseData.value.base_cases_economic.find(
        item => item.fixed_cost_increase === (economicfcper.value+"%") && item.variable_cost_increase === (economicvcper.value+"%")
    )?.price_and_base_sales;
    // if(!economicData?.length){
    //   ElMessage.warning("经济视角数据获取失败")
    //   return
    // }
    // 预测销量数据
    const predictedData = baseData.value.predict_price_and_sales;
    // const chartData = processData(JSON.parse(JSON.stringify(baseData.value)));
    // if(!predictedData?.length){
    //   ElMessage.warning("预测数据获取失败")
    //   return
    // }
    
    console.log({financialData,economicData,predictedData})
    const option = getChartOption({financialData,economicData,predictedData})
    updateChart(option)
}

const changeTabView=()=>{
    getData('ischange')
};
// 来自子集触发
const tableChanged=()=>{
  loading.value=true;
  hasgettabledata.value=true;
  const urlParams = new URLSearchParams(window.parent.location.search);
  let sessionId = urlParams.get("sessionId");
  if (!sessionId) {
      ElMessage.warning("SessionId 获取失败，无法发起计算，请检查");
      return
  }
  executeTool({
      toolName: 'calculate_cost_pricing',
      sessionId ,
      autoTask: false
  }).then((res)=>{
    loading.value=false;
    if(res?.data?.executeTool){
        // 
        if(res.data.executeTool.isError){
          ElMessage.warning(res.data.executeTool.errorMessage)
        }else if(res?.data?.executeTool?.datas){
          console.log()
          let chartdatas=res.data.executeTool.datas
          if(chartdatas&&chartdatas.length){
            baseData.value=chartdatas[0]
            countInitInfo()
          }else{
              ElMessage.warning("数据解析失败")
          }
      }
    }
  }).catch((err)=>{
    loading.value=false;
    ElMessage.error('err: ' + err)
  })
}
watch(() => route.hash, () => {
  hasviewid=getHashParams('viewid');
  initChart();
});
nextTick(()=>{
  initChart();
})
const preSubMitReport=()=>{
  if(!currentversion.value){
    ElMessage.warning("没有找到可用viewID，请稍后再试")
    return
  }
  loading.value=true;
  submitPriceSessionView({viewId:currentversion.value}).then((res)=>{
    if(res?.data?.submitPriceSessionView){
      ElMessage.success("报告提交成功")
      loading.value=false;
    }
  }).catch(()=>{
    loading.value=false;
    ElMessage.warning("报告提交出错，请稍后再试")
  })
}
</script>

