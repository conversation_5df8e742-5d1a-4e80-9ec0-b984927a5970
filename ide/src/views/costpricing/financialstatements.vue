<template>
<div class="financial-statements-wrap" v-loading="loading">
    <div class="comparison-header">
        <div class="version-change">
            <div class="toget-report">
                <el-button type="primary" v-if="!(financialreports||economicreports)" @click="toGetStatement('财务视角和经济学视角')">分析财务数据</el-button>
            </div>
            <el-select v-model="currentversion" @change="changeTabView"  v-if="false&&viewitems.length>1">
            <el-option
                v-for="(item,index) in viewitems"
                :key="item.id"
                :label="versionLabel(item,index)"
                :value="item.id">
            </el-option>
            </el-select>
        </div>
    </div>
    <div class="content-inner-wrap">
        <div class="iframe-inner-wrap"
        ref="leftPanel"
        :style="{ width: leftWidth + '%' }">
            <iframe 
            :src="iframeurl"
            width="100%" 
            height="100%"
            ref="inneriframe"
            @load="iframeLoaded"
            frameborder="0"></iframe>
        </div>
        <div 
        class="drag-bar" 
        @mousedown="startDrag"
        v-if="financialreports||economicreports"
        ></div>
        <div class="excel-area"
        :style="{ width: (100 - leftWidth) + '%' }"
         v-if="financialreports||economicreports">
            <div class="excel-total-info">
                <div class="ana-types-change">
                    <el-select v-model="currentview" @change="countInitInfo">
                        <el-option
                            label="财务分析报表"
                            :disabled="!financialreports"
                            value="financial">
                        </el-option>
                        <el-option
                            label="经济分析报表"
                            :disabled="!economicreports"
                            value="economic">
                        </el-option>
                    </el-select>
                </div>
                <div class="excel-info">
                    <div>
                        <div class="total-fc">总FC：{{annualfc}}（年度）/{{monthlyfc}}（月度）</div>
                        <div class="total-vc">总VC：{{annualvc}}（年度）/{{monthlyvc}}（月度）</div>
                    </div>
                    <div>
                        <div class="total-all">总销量：{{annualvolume}}（年度）/{{monthlyvolume}}（月度）</div>
                        <div class="qua-all">VC（Q）：</div>
                    </div>
                </div>
            </div>
            <div class="row-header">
                <div class="header-item"
                v-for="(item,index) in rowheaders"
                :class="item.value"
                :key="item+index">{{item.label?item.label:item.value}}</div>
                <div class="header-item">操作</div>
            </div>
            <div class="row-content">
                    <div class="content-single-row"
                    v-for="(item,index) in rowdatas"
                    :key="'item'+index">
                    <template v-for="(iitem,iikey) in item" key="templater">
                        <div v-if="rowheaders.find((ritem)=>ritem.value===iikey)"
                            class="row-column column-cell "
                            :class="'row-column-'+iikey">
                            <div class="edit-able" v-if="iikey!=='consumer_profile'" @click="editCell(index, iikey)">
                                <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey">{{iitem}}</span>
                                <el-input 
                                    v-else
                                    v-model="rowdatas[index][iikey]"
                                    @blur="saveEdit"
                                    @keyup.enter="saveEdit"
                                    ref="inputRef"
                                    class="edit-input"
                                />
                            </div>
                            <div class="row-column-consumer_profile-inner" v-else  >
                                <div class="column-label">
                                    <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey"  @click="editCell(index, iikey)">{{iitem}}</span>
                                    <el-input 
                                        v-else
                                        v-model="rowdatas[index][iikey]"
                                        @blur="saveEdit"
                                        @keyup.enter="saveEdit"
                                        ref="inputRef"
                                        class="edit-input"
                                    />
                                    <!-- {{item.consumer_profile}} -->
                                </div>
                                <div class="column-description">
                                    <span v-if="!editingCell || editingCell.row !== index || (editingCell.col !== iikey &&editingCell.col !== 'description')"
                                    @click="editCell(index, 'description')">{{rowdatas[index].description}}</span>
                                    <el-input 
                                        v-else
                                        v-model="rowdatas[index].description"
                                        @blur="saveEdit"
                                        @keyup.enter="saveEdit"
                                        ref="inputRef"
                                        class="edit-input"
                                    />
                                    <!-- {{item.description}} -->
                                </div>
                            </div>
                        </div>
                    </template>
                    <div class="row-column row-edit">
                        <el-icon @click="preAddData(index)"><CirclePlusFilled /></el-icon>
                        <el-icon  @click="preDelData(index)"><RemoveFilled /></el-icon>
                    </div>

                    </div>
            </div>
        </div>
    </div>
    
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch,nextTick } from 'vue';
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel,debounceCustom,updateUrlHash} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router';
import moment from 'moment'
const route = useRoute();
// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})
watch(() => route.hash, () => {
    getData()
});

// 编辑相关状态
const editingCell = ref(null);
const inputRef = ref(null);
let hasviewid = getHashParams('viewid')
let baseData=ref({
    "excelPreviewUrl": "https://baidu.com/",
    // "financial":[{
    //     "annual_volume": 121212113,
    //     "monthly_volume": 967,
    //     "fixed_costs": [
    //         {
    //             "annual_amount": 3023602.668,
    //             "monthly_amount": 242771.928,
    //             "name": "后勤及人事部费用Administration & General"
    //         },
    //         {
    //             "annual_amount": 302312602.668,
    //             "monthly_amount": 242771.928,
    //             "name": "后勤及人事部费用Administration & General"
    //         }
    //     ],
    //     "variable_costs": [
    //         {
    //             "annual_amount": 1975506.576,
    //             "monthly_amount": 158427.624,
    //             "name": "食品Food"
    //         },
    //         {
    //             "annual_amount": 1975506.576,
    //             "monthly_amount": 158427.624,
    //             "name": "食品22222Food"
    //         }
    //     ]
    //     }],
    // "economic":{
    //         "annual_volume": 121212113,
    //         "monthly_volume": 967,
    //         "fixed_costs": [
    //             {
    //                 "annual_amount": 735850.128,
    //                 "monthly_amount": 97740.036,
    //                 "name": "工程部费用Repairs & Maintenance (管理部门)"
    //             }
    //         ],
    //         "variable_costs": [
    //             {
    //                 "annual_amount": 1975506.576,
    //                 "monthly_amount": 158427.624,
    //                 "name": "食品Food"
    //             }
    //             {
    //                 "annual_amount": 1745014.212,
    //                 "monthly_amount": 150254.2152,
    //                 "name": "60%的能源费用Heat, Light & Power"
    //             }
    //         ]
    // },
})
let rowheaders=ref([]);
let rowdatas=ref([]);
let rowcolumns=ref([]);
let currentview=ref("financial")
let financialreports=ref(false)
let economicreports=ref(false)


// 
let loading =ref(false)
let dataid =ref('');
let textval =ref('');
const getData=(ischange)=>{
  textval.value="";
  dataid.value='';
  baseData.value={};
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      toGetViewData(hasviewid)
    }
};
// 
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
      dataid.value=res?.data?.priceSessionVIew?.id
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            if(chartdatas){
            console.log(chartdatas)
              baseData.value=chartdatas
              countInitInfo()
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    })
    // .catch((err)=>{
    //   loading.value=false;
    //     ElMessage.warning("获取内容失败"+err)
    // });
};
let iframeurl =ref('');
// let financialrowdata=ref([]);
// let economicrowdata=ref([]);
let annualvolume=ref(0);
let monthlyvolume=ref(0);
let annualvc=ref(0);
let monthlyvc=ref(0);
let annualfc=ref(0);
let monthlyfc=ref(0);
let ablechange=ref(false);
// 默认左侧宽度比例
const leftWidth = ref(40);
const leftPanel = ref(null);
let isDragging = false;
let startX = 0;
let startWidth = 0;

const processTypeDatas=(reportitem)=>{
    let {fixed_costs,variable_costs}=reportitem;
    let results=[];
    let annualFcSum = 0;
    let monthlyFcSum = 0;
    let annualVcSum = 0;
    let monthlyVcSum = 0;
    fixed_costs?.forEach((item)=>{
        let newitem={
            name:item.name,
            type:'FC',
            annual_amount:item.annual_amount,
            monthly_amount:item.monthly_amount,
        }
        results.push(newitem);
        // 处理 annual_amount，转换为数字并确保是浮点数
        const annual = typeof item.annual_amount === 'string' 
            ? parseFloat(item.annual_amount) 
            : Number(item.annual_amount);
        annualFcSum += annual;
        
        // 处理 monthly_amount，转换为数字并确保是浮点数
        const monthly = typeof item.monthly_amount === 'string' 
            ? parseFloat(item.monthly_amount) 
            : Number(item.monthly_amount);
        monthlyFcSum += monthly;
    })
    variable_costs?.forEach((item)=>{
        let newitem={
            name:item.name,
            type:'VC',
            annual_amount:item.annual_amount,
            monthly_amount:item.monthly_amount,
        }
        results.push(newitem);
        // 
        // 处理 annual_amount
        const annual = typeof item.annual_amount === 'string' 
            ? parseFloat(item.annual_amount) 
            : Number(item.annual_amount);
        annualVcSum += annual;
        
        // 处理 monthly_amount
        const monthly = typeof item.monthly_amount === 'string' 
            ? parseFloat(item.monthly_amount) 
            : Number(item.monthly_amount);
        monthlyVcSum += monthly;
    })
    // 
    annualfc.value = parseFloat(annualFcSum.toFixed(3));
    monthlyfc.value = parseFloat(monthlyFcSum.toFixed(3));
    annualvc.value = parseFloat(annualVcSum.toFixed(3));
    monthlyvc.value = parseFloat(monthlyVcSum.toFixed(3));
    return results
};
function reverseProcessTypeDatas(processedList) {
    // 初始化结果对象
    const reportitem = {
        fixed_costs: [],  
        variable_costs: [] 
    };
    // 分离固定成本和可变成本
    processedList.forEach(item => {
        const costItem = {
            name: item.name,
            monthly_amount: item.monthly_amount,
            annual_amount: item.annual_amount
        };

        if (item.type === 'FC') {
            reportitem.fixed_costs.push(costItem);
        } else if (item.type === 'VC') {
            reportitem.variable_costs.push(costItem);
        }
    });
    return reportitem;
}
const countInitInfo=()=>{
    annualvolume.value=0;
    monthlyvolume.value=0;
    rowheaders.value=[{
        label:"项目名称",
        value:"name"
    },{
        label:"分类",
        value:"type"
    },{
        label:"年度",
        value:"annual_amount"
    },{
        label:"月度",
        value:"monthly_amount"
    }];
    let {excelPreviewUrl,financial,economic}=JSON.parse(JSON.stringify(baseData.value));
    iframeurl.value=excelPreviewUrl;
    if(financial&&economic){
        financialreports.value=true;
        economicreports.value=true;
    }else if(financial){
        currentview.value='financial'
        financialreports.value=true;
        economicreports.value=false;
    }else if(economic){
        currentview.value='economic';
        financialreports.value=false;
        economicreports.value=true;
    }
    if(!(financialreports.value||economicreports.value)){
        leftWidth.value=100
    }else {
        leftWidth.value=40
    }
    if(financial||economic){
        if(currentview.value==='financial'){
            rowdatas.value=processTypeDatas(financial[0]);
            annualvolume.value=financial.annual_volume
            monthlyvolume.value=financial.monthly_volume
        }else if(currentview.value==='economic'){
            rowdatas.value=processTypeDatas(economic[0])
            annualvolume.value=economic.annual_volume
            monthlyvolume.value=economic.monthly_volume
        }
    }
    
    console.log(rowdatas.value)
}
const changeTabView=()=>{
    getData('ischange')
}
const iframeLoaded=()=>{
    console.log('iframe loaded')
}


// countInitInfo()//测试
getData();
const editCell = (rowIndex, colIndex) => {
    // 排除序号列和操作列
    if(colIndex < 0 || colIndex >= rowheaders.value.length) return;
    
    editingCell.value = {
        row: rowIndex,
        col: colIndex
    };
    
    nextTick(() => {
        if(inputRef.value && inputRef.value[0]) {
            inputRef.value[0].focus();
        }
    });
};
// 更新baseData
const updateBaseData = (inputchange) => {
    let {excelPreviewUrl,financial,economic}=JSON.parse(JSON.stringify(baseData.value));
    let { fixed_costs,variable_costs }=reverseProcessTypeDatas(JSON.parse(JSON.stringify(rowdatas.value)));
    console.log(economic)
    if(currentview.value==='financial'){
        financial[0].fixed_costs=fixed_costs
        financial[0].variable_costs=variable_costs
    }else if(currentview.value==='economic'){
        economic[0].fixed_costs=fixed_costs
        economic[0].variable_costs=variable_costs
    }
    baseData.value={
        excelPreviewUrl,financial:financial,economic:economic
    }
    // 
    console.log('baseData.value',baseData.value)
    if(inputchange=='inputchange'){
        toSaveChange()
    }
};
const toSaveChange=debounceCustom(()=>{
    updatePriceSessionView({
        entity:{
            id:dataid.value,
            data:JSON.stringify(baseData.value)
        }
    }).then((res)=>{
        if(res?.data?.updatePriceSessionView){
            ElMessage.success('修改成功')
        }
    }).catch((err)=>{
        ElMessage.error('修改失败: ' + err)
    })
})
const saveEdit= debounceCustom(()=>{
    editingCell.value = null;
    // 这里可以添加数据验证逻辑
    updateBaseData('inputchange');
})
// 
const preAddData=(index)=>{
    const newRow = {
        name: "新增项目",
        type: "FC",
        annual_amount:"0",
        monthly_amount:"0"
    }
    rowdatas.value.splice(index+1, 0, newRow);
    updateBaseData();
}
// 
const preDelData=(index)=>{
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
      rowdatas.value.splice(index,1);
        updateBaseData('inputchange');
    }).catch(() => {
      // 取消编辑
    })
    
};
//去成本定价 
const toGetStatement=(type)=>{
  console.log("已经发起postmessage",{toolname:"财务报表分析",nowtxt:`帮我从‘${type}’ 分析这份财务数据`,sendnow:'true'})
  console.log('parent.parent',parent.parent)
        // 增加t
        // window.location.href = updateUrlHash(window.location.href, 't', (new Date().getTime()));
  parent.parent.postMessage({eventname:'triggerChangeEvent',data:{toolname:"财务报表分析",nowtxt:`帮我从‘${type}’ 分析这份财务数据`,sendnow:'true'}});
}


const startDrag = (e) => {
  isDragging = true;
  startX = e.clientX;
  startWidth = leftWidth.value;
  
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  
  // 防止选中文本
  document.body.style.userSelect = 'none';
};

const handleDrag = (e) => {
  if (!isDragging) return;
  
  const containerWidth = leftPanel.value.parentElement.offsetWidth;
  const deltaX = e.clientX - startX;
  const deltaPercent = (deltaX / containerWidth) * 100;
  
  let newWidth = startWidth + deltaPercent;
  
  newWidth = Math.max(20, Math.min(80, newWidth));
  
  leftWidth.value = newWidth;
};

const stopDrag = () => {
  isDragging = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.body.style.userSelect = '';
};
</script>

