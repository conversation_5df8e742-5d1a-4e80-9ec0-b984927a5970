<template>
  <div class="cost-pricing-wrap">
    <div class="version-change">
      <h2>分类列表</h2>
      <div>
        <el-button type="primary" @click="preSave" style="margin-right:20px">计算结果</el-button>
        <el-select v-model="currentversion" @change="changeTabView" v-if="false&&viewitems.length>1">
          <el-option
            v-for="(item,index) in viewitems"
            :key="item.id"
            :label="versionLabel(item,index)"
            :value="item.id">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="cost-content">
      <div class="category-card-wrap"
      ref="leftPanel"
      :style="{ width: leftWidth + '%' }">
        <div v-for="(category, catIndex) in categorylist" :key="catIndex" class="category-card">
          <div class="category-header">
            <div v-if="!category.editing" class="view-mode" @click="toggleCategoryEdit(catIndex)">
              <span class="category-name">{{ category.category }} · {{ category.categoryItems.length }}</span>
            </div>
            <div v-else class="edit-mode"   @click="toggleCategoryEdit(catIndex)">
              <el-input 
              @click.stop
                v-model="category.category" 
                placeholder="分类名称"
                @keyup.enter="toggleCategoryEdit(catIndex)"
                ref="categoryInputs"
                class="edit-input"
              />
            </div>
            <div class="category-actions">
              <el-tooltip
                :content="category.toggleitems?'收起':'展开'"
                placement="bottom"
                effect="light"
              >
                <el-icon @click="toggleCategoryItems(catIndex)"
                  class="arrow-icon" :class="{ 'arrow-down': category.toggleitems, 'arrow-up': !category.toggleitems }"
                  ><ArrowDown v-if="category.toggleitems" /><ArrowUp v-else /></el-icon>
              </el-tooltip>
              <el-tooltip
                content="增加分类"
                placement="bottom"
                effect="light"
              >
                <el-icon @click="addCategory(catIndex)" title="增加分类" class="add-catagory"><FolderAdd /></el-icon>
              </el-tooltip>
              <el-tooltip
                content="删除当前分类"
                placement="bottom"
                effect="light"
              >
                <el-icon  class="del-catagory" @click="preRemoveCategory(catIndex)"><Delete /></el-icon>
              </el-tooltip>
            </div>
          </div>
          
          <div class="category-items" v-if="category.toggleitems">
            <div v-for="(item, itemIndex) in category.categoryItems" :key="itemIndex" class="item-card">
              <div v-if="!item.editing" class="view-mode" @click="toggleItemEdit(catIndex, itemIndex)">
                <span class="item-text" v-if="item.text">{{ item.text }}：</span>
                <span class="item-value" :class="{ '': !item.value }">
                  {{ item.value || '' }}
                </span>
              </div>
              <div v-else class="edit-mode"  @click="toggleItemEdit(catIndex, itemIndex)">
                <el-input
                @click.stop
                  v-model="item.text"
                  placeholder="项目"
                  @keyup.enter="toggleItemEdit(catIndex, itemIndex)"
                  class="edit-input"
                />
                <el-input
                @click.stop
                  v-model="item.value"
                  placeholder="内容"
                  @keyup.enter="toggleItemEdit(catIndex, itemIndex)"
                  class="edit-input"
                />
              </div>
              <div class="item-ope">
                <el-tooltip
                    content="新增条目"
                    placement="bottom"
                    effect="light"
                  >
                    <el-icon @click="addItem(catIndex,itemIndex)"><CirclePlusFilled /></el-icon>
                  </el-tooltip>
                  <el-tooltip
                    content="删除当前条目"
                    placement="bottom"
                    effect="light"
                  >
                    <el-icon  @click="preRemoveItem(catIndex, itemIndex)"><RemoveFilled /></el-icon>
                  </el-tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div 
      class="drag-bar" 
      @mousedown="startDrag"
    ></div>
      <div class="cost-pricing-result"
      :style="{ width: (100 - leftWidth) + '%' }">
        计算结果：
        <div>{{countresult}}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick,watch,computed,onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router';
import moment from 'moment'
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel} from '@/utils'
const route = useRoute();
let hasviewid = ref("")
const baseData = ref({})

// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
});
// 
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
// let costpricingviewid=ref("f12b180c-5cef-463a-b366-1c40a3ec0fcf")
// let currentversion=ref("24fd7dd4-2a85-42c8-83e2-92e0cfe6ff4e")
let costpricingviewid=ref("")
let currentversion=ref("")
hasviewid.value=getHashParams('viewid');
watch(() => route.hash, () => {
  hasviewid.value=getHashParams('viewid');
  getData();
});
const categoryviewinfo=ref([])
const getCategoryInfo=()=>{
  getPriceViewInfo({viewId:costpricingviewid.value}).then((res)=>{
      dataid.value=res?.data?.priceSessionVIew?.id;
      console.log('112',dataid.value)
      if(res?.data?.priceSessionVIew?.data){
          let chartdatas=[]
          try{
              chartdatas=JSON.parse(res.data.priceSessionVIew.data)
          }catch (err){
              ElMessage.warning("数据解析出错")
          }
          if(chartdatas){
            categoryviewinfo.value=chartdatas;
            countCategoryViewInfo()
          }else{
            ElMessage.warning("数据解析失败")
          }
      }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
};
const categorylist = ref([])
let categoryexcellist=ref([]);
let categoryviewlist=ref([]);
const categoryInputs = ref([])
let loading =ref(false)
let dataid =ref('');
const getData=(ischange)=>{
  baseData.value={};
  if(ischange=='ischange'){
    if(currentversion.value){
      toGetViewData(currentversion.value);
    }
  }else if(hasviewid.value){
    toGetViewData(hasviewid.value)
  }
};
// 
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
      loading.value=false;
      
      if(res?.data?.priceSessionVIew?.data){
          let chartdatas=[]
          try{
              chartdatas=JSON.parse(res.data.priceSessionVIew.data)
          }catch (err){
              ElMessage.warning("数据解析出错")
          }
          if(chartdatas){
            console.log('chartdatas',chartdatas)
            baseData.value=chartdatas
            countInitInfo()
          }else{
              ElMessage.warning("数据解析失败")
          }
      }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
}
// 
let countresult=ref("")
const countInitInfo=()=>{
  let {currentExcel,result}=JSON.parse(JSON.stringify(baseData.value));
  categoryexcellist.value=currentExcel?.map((item)=>({...item,toggleitems:true}));
  countresult.value=result
};
// 仅分类展示
const countCategoryViewInfo=()=>{
  let {defaultExcel,currentExcel}=JSON.parse(JSON.stringify(categoryviewinfo.value));
  categoryviewlist.value=currentExcel?.map((item)=>({...item,toggleitems:true}));
};
const showcategory = computed(()=>{
  let list =[];
  if(categoryexcellist.value?.length){
    list =[...categoryexcellist.value]
  }else if(categoryviewinfo.value){
    let {defaultExcel,currentExcel}=JSON.parse(JSON.stringify(categoryviewinfo.value));
    if(currentExcel?.length){
      list =[...currentExcel]
    }else if(defaultExcel?.length){
      list =[...defaultExcel]
    }
  }
  return list
})
watch(()=>showcategory.value,(newval)=>{
  categorylist.value=newval.map((item)=>{
    item.editing=false;
    item.toggleitems=true;
    return item
  })
})
// 
getData();
const changeTabView=()=>{
    getData('ischange')
}

const addCategory = (catIndex) => {
  const newCategory = {
    category: '新分类'+(catIndex+1),
    categoryItems: [
      {
        text: '',
        value: '',
        editing: true
      }
    ],
    editing: true,
    toggleitems: true,
  }
  categorylist.value.splice(catIndex+1,0,newCategory);
  
  nextTick(() => {
    if (categoryInputs.value.length > 0) {
      const lastInput = categoryInputs.value[categoryInputs.value.length - 1]
      lastInput.focus()
    }
  })
}
const preRemoveCategory=(index)=>{
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
      removeCategory(index)
    }).catch(() => {
      // 取消编辑
    })
}
const removeCategory = (index) => {
  categorylist.value.splice(index, 1)
}
// 
const toggleCategoryEdit = (catIndex) => {
  categorylist.value[catIndex].editing = !categorylist.value[catIndex].editing
}

const toggleCategoryItems=(catIndex)=>{
  categorylist.value[catIndex].toggleitems = !categorylist.value[catIndex].toggleitems
}

const addItem = (catIndex,itemIndex) => {
  const newItem = {
    text: '',
    value: '',
    editing: true,
    toggleitems: true
  }
  categorylist.value[catIndex].categoryItems.splice(itemIndex+1,0,newItem)
}
const preRemoveItem = (catIndex, itemIndex) => {
  ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
      removeItem(catIndex, itemIndex)
    }).catch(() => {
      // 取消编辑
    })
}
const removeItem = (catIndex, itemIndex) => {
  categorylist.value[catIndex].categoryItems.splice(itemIndex, 1)
}

const toggleItemEdit = (catIndex, itemIndex) => {
  categorylist.value[catIndex].categoryItems[itemIndex].editing = 
    !categorylist.value[catIndex].categoryItems[itemIndex].editing
}
// 
const preSave=()=>{
  loading.value=true;
  updatePriceSessionView({
    entity:{
      id:dataid.value,
      data:JSON.stringify({
        defaultExcel:categoryviewinfo.value?.defaultExcel?categoryviewinfo.value.defaultExcel:[],
        currentExcel:JSON.parse(JSON.stringify(categorylist.value))?.map((item)=>{
          delete item.toggleitems;
          item?.categoryItems?.forEach((citem)=>delete citem.editing)
          return item
        })
      })
    }
  }).then((res)=>{
    loading.value=false;
    if(res?.data?.updatePriceSessionView){
      toParent()
    }else{
      ElMessage.error('计算失败请重试')
    }
  }).catch((err)=>{
    loading.value=false;
    ElMessage.error('计算失败: ' + err)
  })
};
const toParent=()=>{
  loading.value=false;
  console.log('作为子集发起了',{eventname:'triggerChangeEvent',data:{toolname:'成本定价',nowtxt:'',sendnow:'true'}})
  parent.parent.postMessage({eventname:'triggerChangeEvent',data:{toolname:'成本定价',nowtxt:'',sendnow:'true'}});
};
watch(viewid.value,()=>{
  costpricingviewid.value="";
  if(viewid.value?.includes('_@_')){
    currentversion.value=viewid.value.split('_@_')[0];
    costpricingviewid.value=viewid.value.split('_@_')[1]
  }else{
    currentversion.value=viewid.value
  }
  // 如果分类id与结果id相同则无需请求结果view，因为当前是无结果
  if(currentversion.value===costpricingviewid.value){
    currentversion.value=""
  }
  // 获取默认分类信息
  if(costpricingviewid.value){
    getCategoryInfo()
  }
},{immediate:true});

// 默认左侧宽度比例
const leftWidth = ref(60);
const leftPanel = ref(null);
let isDragging = false;
let startX = 0;
let startWidth = 0;
const startDrag = (e) => {
  isDragging = true;
  startX = e.clientX;
  startWidth = leftWidth.value;
  
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  
  // 防止选中文本
  document.body.style.userSelect = 'none';
};

const handleDrag = (e) => {
  if (!isDragging) return;
  
  const containerWidth = leftPanel.value.parentElement.offsetWidth;
  const deltaX = e.clientX - startX;
  const deltaPercent = (deltaX / containerWidth) * 100;
  
  let newWidth = startWidth + deltaPercent;
  
  newWidth = Math.max(30, Math.min(70, newWidth));
  
  leftWidth.value = newWidth;
};

const stopDrag = () => {
  isDragging = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.body.style.userSelect = '';
};
</script>

