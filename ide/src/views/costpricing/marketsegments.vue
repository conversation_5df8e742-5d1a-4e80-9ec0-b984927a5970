<template>
<div class="market-segments-wrap market-segments-withchart-wrap" v-loading="loading">
    <div class="comparison-header" >
        <div class="version-change">
            <div class="btn" >
            <el-button type="primary"  @click="preSubMitReport" size="small">提交报告</el-button>
            <el-button type="primary" plain @click="toGetChartInfo()" size="small">生成预期价格与销量图</el-button>
            </div>
            <el-select v-model="currentversion" @change="changeTabView" v-if="false&&viewitems.length>1">
            <el-option
                v-for="(item,index) in viewitems"
                :key="item.id"
                :label="versionLabel(item,index)"
                :value="item.id">
            </el-option>
            </el-select>
        </div>
    </div>
    <div class="excel-area" id="excel-area-container">
        <div class="excel-area-inner">
            <div class="row-header" ref="headerRef">
                <div class="header-item"
                v-for="(item,index) in rowheaders"
                :class="[item.value,item.value.startsWith('price_')?'header-item-price':'']"
                :key="item+index"
                >
                <div class="header-inner" v-if="item.value.startsWith('price_')">
                    <el-dropdown>
                        <div class="header-inner-label" @click="changePrice(item,index)">
                            {{item.label?item.label:item.value}}
                            <el-icon style="margin-left:5px"><ArrowDownBold /></el-icon>
                        </div>
                        <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item @click.native.stop="addPricePoint(item.value,index,0)"><el-icon><Back /></el-icon>新增价格点</el-dropdown-item>
                            <el-dropdown-item @click.native.stop="addPricePoint(item.value,index,1)"><el-icon><Right /></el-icon>新增价格点</el-dropdown-item>
                            <el-dropdown-item divided  @click.native.stop="preDeletePricePoint(item.value,index)">
                                <el-icon><Delete /></el-icon>删除价格点
                            </el-dropdown-item>
                        </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <div class="header-inner-types">
                        <div>支付概率</div>
                        <div>预期需求</div>
                    </div>
                </div>
                <div class="header-inner-others" v-else>{{item.label?item.label:item.value}}</div>
                </div>
                <div class="header-item">
                    <div class="header-inner-others" @click="addPricePoint(rowheaders.slice(-1)[0].value,(rowheaders.length-1),1)"><a class="header-adder"><el-icon><CirclePlus /></el-icon>价格点</a></div>
                </div>
            </div>
            <div class="row-content" ref="contentRef">
                    <div class="content-single-row"
                    v-for="(item,index) in rowdatas"
                    :key="'item'+index">
                    <template v-for="(iitem,iikey) in item" :key="iikey">
                        <div v-if="rowheaders.find((ritem)=>ritem.value===iikey)"
                            class="row-column column-cell "
                            :class="['row-column-'+iikey,
                                iikey.startsWith('price_')?'row-column-price':''
                            ]">
                            <div class="row-column-consumer_profile-inner" v-if="iikey=='consumer_profile'"  >
                                <div class="column-label">
                                    <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey"  @click="editCell(index, iikey)">{{iitem}}</span>
                                    <el-input 
                                        v-else
                                        v-model="rowdatas[index][iikey]"
                                        @blur="saveEdit"
                                        @keyup.enter="saveEdit"
                                        ref="inputRef"
                                        class="edit-input"
                                    />
                                    <!-- {{item.consumer_profile}} -->
                                </div>
                                <div class="column-description">
                                    <span v-if="!editingCell || editingCell.row !== index || (editingCell.col !== 'description')"
                                    @click="editCell(index, 'description')">{{rowdatas[index].description}}</span>
                                    <el-input 
                                        v-else
                                        v-model="rowdatas[index].description"
                                        @blur="saveEdit"
                                        @keyup.enter="saveEdit"
                                        ref="inputRef"
                                        class="edit-input"
                                    />
                                    <!-- {{item.description}} -->
                                </div>
                            </div>
                            <div class="edit-able" v-else-if="iikey=='market_size'" @click="editCell(index, iikey)">
                                <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey">{{iitem}}</span>
                                <el-input 
                                    v-else
                                    v-model="rowdatas[index][iikey]"
                                    @blur="saveEdit"
                                    @keyup.enter="saveEdit"
                                    ref="inputRef"
                                    class="edit-input"
                                />
                            </div>
                            <div class="edit-able" v-else-if="iikey=='market_percent'" @click="editCell(index, iikey)">
                                <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey">{{iitem}}%</span>
                                <el-input 
                                    v-else
                                    v-model="rowdatas[index][iikey]"
                                    @blur="saveEdit"
                                    @keyup.enter="saveEdit"
                                    ref="inputRef"
                                    class="edit-input"
                                />
                            </div>
                            
                            <div class="edit-able" v-else-if="iikey.startsWith('price_')">
                                <div class="edit-able-price">
                                    <div class="column-probability-pair">
                                    <!-- 支付概率： -->
                                    <span v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey|| editingCell.key !== 'price_probability_pairs'"  @click="editCell(index, iikey,'price_probability_pairs')">
                                        {{iitem[0]&&iitem[0]!='-'?iitem[0]+"%":iitem[0]}}
                                    </span>
                                    <el-input 
                                        v-else
                                        v-model="rowdatas[index][iikey][0]"
                                        @blur="saveEdit"
                                        @keyup.enter="saveEdit"
                                        ref="inputRef"
                                        class="edit-input"
                                    />
                                    </div>
                                    <div class="column-demand-pair" style="cursor:not-allowed">
                                        <!-- 预期需求： -->
                                        <span  v-if="!editingCell || editingCell.row !== index || editingCell.col !== iikey|| editingCell.key !== 'price_demand_pairs'" >
                                            {{iitem[1]}}
                                        </span>
                                        <el-input 
                                            v-else
                                            v-model="rowdatas[index][iikey][1]"
                                            @blur="saveEdit"
                                            @keyup.enter="saveEdit"
                                            ref="inputRef"
                                            class="edit-input"
                                        />
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </template>
                    <div class="row-column row-edit">
                        <el-icon @click="preAddData(item,index)"><CirclePlusFilled /></el-icon>
                        <el-icon  @click="preDelData(index)"><RemoveFilled /></el-icon>
                    </div>

                    </div>
            </div>
        </div>
        <div class="scroll-hint">向下滚动</div>
    </div>
    <div class="chart-area">
        <div class="chart-inner" id="chartinner"></div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch,nextTick } from 'vue';
import {getPriceViewInfo,updatePriceSessionView,executeTool,submitPriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel,debounceCustom} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import * as echarts from 'echarts';

// import { startsWith } from 'lodash';



// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
currentversion.value=getHashParams("viewid")
watch(viewid.value,()=>{
    if(viewid.value){
        currentversion.value=viewid.value
    }
},{immediate:true})

onMounted(()=>{
    const container = document.getElementById('excel-area-container');
    const wrapper = container.parentElement;
    const hint = wrapper.querySelector('.scroll-hint');
    
    // 初始检查
    checkScroll();
    
    // 滚动时检查
    container.addEventListener('scroll', checkScroll);
    window.addEventListener('resize', checkScroll);
    
    function checkScroll() {
        // 是否滚动到底部
        const atBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 1;
        
        // 内容是否足够需要滚动
        const needsScroll = container.scrollHeight > container.clientHeight;
        
        container.classList.toggle('at-bottom', atBottom);
        hint.style.display = needsScroll ? 'flex' : 'none';
    }
})

// 编辑相关状态
const editingCell = ref(null);
const inputRef = ref(null);
let hasviewid = getHashParams('viewid')
let baseData=ref({
    "flag": true,
    "extracted_data": {
        "market_segments": [
            {
                "consumer_profile": "艺术鉴赏家",
                "description": "艺术品拍卖行总监，追求'灵魂的艺术'",
                "market_size": 12000,
                "market_percent": 30,
                "price_demand_pairs": [
                    [
                        2000,
                        85
                    ],
                    [
                        3000,
                        78
                    ],
                    [
                        4000,
                        65
                    ],
                    [
                        5000,
                        45
                    ]
                ],
                "price_probability_pairs": [
                    [
                        2000,
                        0.85
                    ],
                    [
                        3000,
                        0.78
                    ],
                    [
                        4000,
                        0.65
                    ],
                    [
                        5000,
                        0.45
                    ]
                ]
            },
            {
                "consumer_profile": "艺术鉴赏家",
                "description": "艺术品拍卖行总监，追求'灵魂的艺术'",
                "market_size": 12000,
                "market_percent": 30,
                "price_demand_pairs": [
                    [
                        2000,
                        85
                    ],
                    [
                        3000,
                        78
                    ],
                    [
                        4000,
                        65
                    ],
                    [
                        5000,
                        45
                    ]
                ],
                "price_probability_pairs": [
                    [
                        2000,
                        0.85
                    ],
                    [
                        3000,
                        0.78
                    ],
                    [
                        4000,
                        0.65
                    ],
                    [
                        5000,
                        0.45
                    ]
                ]
            },
            {
                "consumer_profile": "艺术鉴赏家",
                "description": "艺术品拍卖行总监，追求'灵魂的艺术'",
                "market_size": 12000,
                "market_percent": 30,
                "price_demand_pairs": [
                    [
                        2000,
                        85
                    ],
                    [
                        3000,
                        78
                    ],
                    [
                        4000,
                        65
                    ],
                    [
                        5000,
                        45
                    ]
                ],
                "price_probability_pairs": [
                    [
                        2000,
                        0.85
                    ],
                    [
                        3000,
                        0.78
                    ],
                    [
                        4000,
                        0.65
                    ],
                    [
                        5000,
                        0.45
                    ]
                ]
            },
            {
                "consumer_profile": "艺术鉴赏家",
                "description": "艺术品拍卖行总监，追求'灵魂的艺术'",
                "market_size": 12000,
                "market_percent": 30,
                "price_demand_pairs": [
                    [
                        2000,
                        85
                    ],
                    [
                        3000,
                        78
                    ],
                    [
                        4000,
                        65
                    ],
                    [
                        5000,
                        45
                    ]
                ],
                "price_probability_pairs": [
                    [
                        2000,
                        0.85
                    ],
                    [
                        3000,
                        0.78
                    ],
                    [
                        4000,
                        0.65
                    ],
                    [
                        5000,
                        0.45
                    ]
                ]
            },
            {
                "consumer_profile": "艺术鉴赏家",
                "description": "艺术品拍卖行总监，追求'灵魂的艺术'",
                "market_size": 12000,
                "market_percent": 30,
                "price_demand_pairs": [
                    [
                        2000,
                        85
                    ],
                    [
                        3000,
                        78
                    ],
                    [
                        4000,
                        65
                    ],
                    [
                        5000,
                        45
                    ]
                ],
                "price_probability_pairs": [
                    [
                        2000,
                        0.85
                    ],
                    [
                        3000,
                        0.78
                    ],
                    [
                        4000,
                        0.65
                    ],
                    [
                        5000,
                        0.45
                    ]
                ]
            },
        ]
    }
})
let rowheaders=ref([]);
let rowdatas=ref([]);
let rowcolumns=ref([]);


// 
let loading =ref(false)
let dataid =ref('');
let textval =ref('');
const getData=(ischange)=>{
  textval.value="";
  dataid.value='';
  baseData.value={};
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      toGetViewData(hasviewid)
    }
};
// 
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
      dataid.value=res?.data?.priceSessionVIew?.id
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            if(chartdatas&&chartdatas.length){
              baseData.value=chartdatas[0];
              console.log('chartdatas[0]',chartdatas[0])
              countInitInfo()
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    }).catch((err)=>{
      loading.value=false;
        ElMessage.warning("获取内容失败"+err)
    });
};
const  processMarketSegments=(market_segments)=> {
    const headers = [
        { label: '消费者画像', value: 'consumer_profile' },
        { label: '市场规模', value: 'market_size' },
        { label: '市场份额', value: 'market_percent' }
    ];

    // 从第一个数据项中提取价格点作为表头的其余部分
    if (market_segments.length > 0 && market_segments[0].price_probability_pairs.length > 0) {
        market_segments[0].price_probability_pairs.forEach(pair => {
            headers.push({
                label: `${pair?.[0]?.toLocaleString()?pair[0].toLocaleString():'-'}元`,
                value: `price_${pair[0]}`
            });
        });
    }
    console.log('headers',headers)

    // 构建内容列表
    const content = market_segments.map((item) => {
        const mainRow = {};
        headers.forEach(header => {
            switch (header.value) {
                case 'consumer_profile':
                    mainRow[header.value] = item.consumer_profile;
                    mainRow.description = item.description;
                    break;
                case 'market_size':
                    mainRow[header.value] = item.market_size?.toLocaleString()?item.market_size.toLocaleString():'-';
                    break;
                case 'market_percent':
                    mainRow[header.value] = item.market_percent?.toLocaleString()?item.market_percent.toLocaleString():'-';
                    break;
                default:
                    if (header.value.startsWith('price_')) {
                        const price = parseInt(header.value.split('_')[1]);
                        const probPair = item.price_probability_pairs.find(p => p[0] === price);
                        let probability=probPair&&probPair[1]?`${Math.round(probPair[1] * 100)}`:'-';
                        const demandPair = item.price_demand_pairs?.find(p => p[0] === price);
                        let demand=demandPair&&demandPair[1]?demandPair[1]:'-';
                        mainRow[header.value] = [probability,demand]
                    } else {
                        mainRow[header.value] = ['-','-'];
                    }
            }
        });

        return [mainRow];
    }).flat();

    return {
        headers,
        content
    };
};
const changePrice=(item,index)=>{
    console.log(item,index)
    let value = item.value.split('price_').join('');
    ElMessageBox.prompt(`请输入新值:`, '编辑', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: value,
        beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
            const newValue = instance.inputValue;
            // 
            let {extracted_data}=JSON.parse(JSON.stringify(baseData.value));
            let {market_segments}=extracted_data;
            market_segments.forEach((mitem)=>{
                mitem.price_demand_pairs[index-3][0]=newValue/1
                mitem.price_probability_pairs[index-3][0]=newValue/1
            })
            console.log('market_segments',market_segments)
            setTimeout(()=>{
                let {headers,content}=processMarketSegments(market_segments);
                console.log(headers,content)
                rowheaders.value=headers
                rowdatas.value=content
                saveEdit()
            },1000)
        }
        done()
        }
    })
}
const reverseProcessMarketSegments=(processedData)=>{
   // 提取所有以price_开头的键
        const priceKeys = Object.keys(processedData).filter(key => key.startsWith('price_'));
        
        // 将价格概率对和价格需求对分开处理
        const priceProbabilityPairs = [];
        const priceDemandPairs = [];
        let market_size=parseInt(processedData.market_size.replace(/,/g, ''))
        // let market_percent=parseInt(processedData.market_percent.replace(/,/g, ''))
        let market_percent=parseFloat(processedData.market_percent.replace('%', ''));

        priceKeys.forEach(key => {
            const price = parseInt(key.split('_')[1]);
            const [probabilityStr, demandStr] = processedData[key];
            // 处理概率
            if (probabilityStr && probabilityStr !== '-') {
                const probability = parseFloat(probabilityStr.replace('%', '')) / 100;
                const market_percent_p = market_percent / 100;
                priceProbabilityPairs.push([price, probability]);
                if(probability){
                    const demand =Math.round(market_size *market_percent_p* probability);
                    priceDemandPairs.push([price, demand]);
                }
            }
        });
        
        // 按价格排序
        priceProbabilityPairs.sort((a, b) => a[0] - b[0]);
        priceDemandPairs.sort((a, b) => a[0] - b[0]);
        
        return {
            consumer_profile: processedData.consumer_profile,
            market_size:market_size,
            market_percent:market_percent,
            price_probability_pairs: priceProbabilityPairs,
            price_demand_pairs: priceDemandPairs.length > 0 ? priceDemandPairs : [],
            description: processedData.description
        };
};
const countInitInfo=()=>{
  let {extracted_data}=JSON.parse(JSON.stringify(baseData.value));
  let {market_segments}=extracted_data;
  console.log('market_segments',market_segments)
  
    let {headers,content}=processMarketSegments(market_segments);
   
    rowheaders.value=headers
    rowdatas.value=content
}
const changeTabView=()=>{
    getData('ischange')
}

// countInitInfo()//测试
getData();
const editCell = (rowIndex, colIndex,key) => {
    // 排除序号列和操作列
    if(colIndex < 0 || colIndex >= rowheaders.value.length) return;
    
    editingCell.value = {
        row: rowIndex,
        col: colIndex,
        key:key
    };
    
    nextTick(() => {
        if(inputRef.value && inputRef.value[0]) {
            inputRef.value[0].focus();
        }
    });
};
const saveEdit= debounceCustom(()=>{
    editingCell.value = null;
    // 这里可以添加数据验证逻辑
    updateBaseData('inputchange');
})
// 更新baseData
const updateBaseData = (inputchange) => {
    const market_segments = [];
    rowdatas.value.forEach((item)=>{
        item.market_size=item.market_size?item.market_size:'0';
        item.market_percent=item.market_percent?item.market_percent:'0';
        let market_size=parseInt(item.market_size.replace(/,/g, ''))
        let market_percent=parseFloat(item.market_percent.replace('%', ''));
        for (let key in item){
            if(key.startsWith('price_')){
                if(item?.[key]?.[0]&&item[key][0]!='-'){
                    const probability = parseFloat(item[key][0].replace('%', '')) / 100;
                    const market_percent_p = market_percent / 100;
                    if(probability){
                        item[key][1]=Math.round(market_size*market_percent_p * probability)
                    }
                }
            }
        }
        let newitem =reverseProcessMarketSegments(item)
        market_segments.push(newitem)
    })
    baseData.value = {
        flag:true,
        extracted_data:{
            market_segments
        }
    };
    console.log("我要保存了",baseData.value)
    if(inputchange=='inputchange'){
        toSaveChange()
    }
};
const toSaveChange=debounceCustom(()=>{
    updatePriceSessionView({
        entity:{
            id:dataid.value,
            data:JSON.stringify([baseData.value])
        }
    }).then((res)=>{
        if(res?.data?.updatePriceSessionView){
            ElMessage.success('修改成功')
        }
    }).catch((err)=>{
        ElMessage.error('修改失败: ' + err)
    })
})
// 增加价格点
// space 0 向前添加 1为向后添加
const addPricePoint=(target,index,space)=>{
    ElMessageBox.prompt('请输入一个价格点', '新增价格点', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPattern:/^\d*$/,//只能输入数字
        inputErrorMessage: '请输入有效价格',
        inputValidator: (value) => {
            if (!value) return true; // 允许空输入
            const newpricevalue = `price_${value}`;
            const isExisting = rowheaders.value?.some(item => item.value === newpricevalue);
            return isExisting ? '该价格点已存在，请更换' : true;
        },
    }).then(({ value }) => {
        rowheaders.value.splice(index+space, 0, {
            value:`price_${value}`,
            label:`${value?.toLocaleString()?value.toLocaleString():'-'}元`
        });
        rowdatas.value=rowdatas.value.map((item)=>{
            return {...insertKeyAfterTarget(target,`price_${value}`,item,space)}
        })
        updateBaseData('inputchange');
    })
};
const preDeletePricePoint=(target,index)=>{
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
      deletePricePoint(target,index)
    })
};
const deletePricePoint=(target,index)=>{
    rowheaders.value.splice(index, 1);
    rowdatas.value.forEach((item)=>{
        delete item[target]
    })
    updateBaseData('inputchange')
}
// 
const insertKeyAfterTarget=(target,newkey,obj,space)=>{
    const entries = Object.entries(obj);
    const index = entries.findIndex(([key]) => key === target);

    entries.splice(index + space, 0, [newkey, ["-", "-"]]);
    const newData = Object.fromEntries(entries);


    // const keys = Object.keys(obj);
    // const newObj = {};
    // for (const key of keys) {
    //     newObj[key] = obj[key];
    //     if (key === target) {
    //         newObj[newkey] = ['-','-']; 
    //     }
    // }
    // console.log(newObj)
    return newData;
}
// 增加行
const preAddData=(item,index)=>{
    let newRow = {}
    for(let key in item){
        if(key.startsWith('price_')){
            newRow[key]=['-','-']
        }else if(key==='consumer_profile'){
            newRow[key]="新增消费者画像"
        }else if(key==='description'){
            newRow[key]="新增消费者说明"
        }else{
            newRow[key]=""
        }
    }
    rowdatas.value.splice(index+1, 0, newRow);
    updateBaseData();
}
// 
const preDelData=(index)=>{
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
        rowdatas.value.splice(index,1);
        updateBaseData('inputchange');
    }).catch(() => {
      // 取消编辑
    })
    
};
const toAutoParent=()=>{
  console.log('点击按钮后——————自动触发由[提取细分市场报告]触发啦',{eventname:'autotasktrigger',data:{toolname:'',nowtxt:"提取细分市场报告",sendnow:'true'}})
  parent.postMessage({eventname:'autotasktrigger',data:{toolname:'',nowtxt:"提取细分市场报告",sendnow:'true'}});
}
const toGetChartInfo=(auto)=>{
    loading.value=true;
    const urlParams = new URLSearchParams(window.parent.location.search);
    let sessionId = urlParams.get("sessionId");
    if (process.env.NODE_ENV !== "production") {
        sessionId="123"
    } 
    if (!sessionId) {
        ElMessage.warning("SessionId 获取失败，无法发起计算，请检查");
        return
    }
    executeTool({
      toolName: 'forecast_price_sales',
      autoTask:auto==='auto',
      sessionId,
    }).then((res)=>{
        loading.value=false;
      if(res?.data?.executeTool){
          if(res.data.executeTool.isError){
            ElMessage.warning(res.data.executeTool.errorMessage)
          }else if(res?.data?.executeTool?.datas){
            let chartdatas=res.data.executeTool.datas
            console.log('chartdatas',chartdatas)
            if(chartdatas&&chartdatas.length){
                let predict_price_and_sales=chartdatas[0].predict_price_and_sales;
                let xval=[]
                let pval=[]
                predict_price_and_sales.forEach((item)=>{
                    xval.push(item[0])
                    pval.push(item[1])
                })
                if(xval?.length&&pval?.length){
                    initChart(xval,pval)
                }else{
                    ElMessage.warning("暂无数据"+predict_price_and_sales)
                }
            }
        }
      }
      if(auto=='auto'){
          setTimeout(()=>{
            toAutoParent()
          },2000)
      }
    }).catch((err)=>{
      loading.value=false;
      ElMessage.error('err: ' + err)
    })
}
let myChart=null;
// 初始化图表
const initChart = (xval,pval) => {
    const chartDom = document.getElementById('chartinner');
    if (!chartDom) return;
    // 如果图表已存在，则先销毁
    if (myChart) {
        myChart.dispose();
    }
    myChart = echarts.init(chartDom);
    const option = getChartOption({xval,pval})
    myChart.setOption(option);
};
const getChartOption = ({xval,pval}) => {
  return {
    xAxis: {
        type: 'category',
        data: xval
    },
    yAxis: {
        type: 'value'
    },
    tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                let result = `价格: ${params[0].axisValue}<br>`;
                params.forEach(param => {
                    result += `销量: ${param.value.toLocaleString()}<br>`;
                });
                return result;
            }
        },
    title: {
            text: '预期价格与销量',
            left: 'center'
        },
    series: [
        {
        data: pval,
        type: 'line'
        }
    ]
    }
};

window.addEventListener('message', function(event) {
    const message = event.data;
    let {eventname}=message;
    console.log('eventname',eventname)
    if(sessionStorage.getItem('autotask')==='1'){
      
        if(eventname === 'autotasktrigger_fromparent'){
        toGetChartInfo('auto')
        //   if(sessionStorage.getItem('autotask')==='1'){
        //       //如果没有可选，直接去父级触发
        //       // 差异化，选中，触发下一步，不需要updata，不走2C
        //       if(baseData.value?.[0]?.['主题']){
        //         toggleSelection(baseData.value[0]['主题'])
        //         toAutoParent()
        //       }else{
        //         //如果没有可选，直接去父级触发
        //         toAutoParent()
        //       }
        //   }
        }
    }

});
const preSubMitReport=()=>{
  if(!currentversion.value){
    ElMessage.warning("没有找到可用viewID，请稍后再试")
    return
  }
  loading.value=true;
  submitPriceSessionView({viewId:currentversion.value}).then((res)=>{
    if(res?.data?.submitPriceSessionView){
      ElMessage.success("报告提交成功")
      loading.value=false;
    }
  }).catch(()=>{
    loading.value=false;
    ElMessage.warning("报告提交出错，请稍后再试")
  })
}
</script>

