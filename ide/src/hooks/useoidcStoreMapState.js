import { computed } from "vue";
import { useStore } from "vuex"
import { createNamespacedHelpers } from 'vuex';
const { mapState } = createNamespacedHelpers('oidcStore')
export const useoidcStoreMapState = (getKeys) => {
    const store = useStore();
    const storeState = {}
    const storeFns = mapState(getKeys)
    Object.keys(storeFns).forEach((fnKeys) => {
        const fn = storeFns[fnKeys].bind({ $store: store })
        storeState[fnKeys] = computed(fn)
    })
    return storeState
}