import { createApp, provide, h } from 'vue'
import { ApolloClients } from '@vue/apollo-composable'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import App from './App.vue'
import router from './router'
import store from './store'
import i18n from './locale';

import chroma from "chroma-js";
// 
import apolloProvider from "@/assets/js/apolloclient.js";
import "@/views/competitiveanalysis/brainstormingbotchat/ai/css/common.scss";
import "@/views/competitiveanalysis/brainstormingbotchat/ai/css/other.scss";
import '@/assets/style/style.scss'


import IconUse from "@/views/competitiveanalysis/brainstormingbotchat/ai/icons.vue"

const app = createApp({
    setup() {
        // apollo-client注册
        provide(ApolloClients, {
            ...apolloProvider.clients
        })
    },
    render() {
        return h(App)
    }
});
// eleplusicons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.use(ElementPlus, {
    locale: zhCn,
});



// import katex from 'katex'
// !import 不能忘记引入katex的样式
// import 'katex/dist/katex.css'
// 引入katex下的自动渲染函数
// import renderMathInElement from 'katex/contrib/auto-render/auto-render'

const renderOption = {
        delimiters: [
            { left: '$$', right: '$$', display: true },
            { left: '$', right: '$', display: false },
            { left: '\(', right: '\)', display: false },
            { left: '\\(', right: '\\)', display: true },
            { left: '\\[', right: '\\]', display: true },
            { left: '\[', right: '\]', display: false }
        ],
        throwOnError: false
    }
    // {left: '\[', right: '\]', display: true}

// 挂载自动渲染函数到vue原型
// app.config.globalProperties.$formula = function(dom) {
//     // console.log(dom, '----get it')
//     // renderMathInElement(dom, renderOption)
// }

app.config.globalProperties.$userAg = function() {
    var isMobile = false;
    var flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
    );

    if (flag != null) {
        isMobile = true;
    }

    var iswx = navigator.userAgent.toLowerCase().indexOf('micromessenger') !== -1;
    console.log(iswx, '----------')

    if (iswx == true) {
        return 'wechat';
    } else if (isMobile) {
        return 'mobile';
    } else {
        return 'desktop'
    }

}

app.config.globalProperties.$getColors = function(siteColor) {
    let cColor = chroma(siteColor);
    let cube2 = [
        255 - cColor._rgb[0],
        255 - cColor._rgb[1],
        255 - cColor._rgb[2],
        1,
    ];
    let colors2 = chroma.scale([siteColor, `rgb(${cube2[0]},${cube2[1]},${cube2[2]})`]).mode('lch').colors(7);
    let colorAll = `--bg: #fff;
    --theme: ${siteColor};
    --themeDark: ${cColor.darken(2)};
    --themeRept: ${siteColor};
    --themelight: ${cColor.brighten(1).tint(0.75)};
    --themelight3: ${cColor.brighten(1).tint(0.85)};
    --personTxtBg: ${cColor.brighten(1).tint(0.85)};
    --personTxtColor:  rgba(0, 0, 0, 0.9);
    --themelight2: ${cColor.saturate(1).darken(1).tint(0.95)};
    --line1: ${cColor.brighten(0.5).tint(0.2)};
    --line2: ${cColor.saturate(2).alpha(0.9)};
    --line3: ${cColor.brighten(1)};
  
    --lineCube1: ${siteColor};
    --lineCube2: ${cColor.mix(chroma(cube2), 0.2).saturate(2)};
  
    --cate: ${cColor.brighten(2).tint(0.9)};
    --cateGray: ${cColor.tint(0.85)};
    --txtColorR: rgba(0,1,10,0.94);
    --txtColorP: #fff;
  
    --wholeBg: #fff;
    --rbtBg: #fcfcfc;
  
    --shadowR: 0px 0px 4px #ccc;
    --shadowH: 0px 0px 4px #ccc;
    --shadowP: 0px 0px 4px #ccc;
  
    --dropShadow: 0px 0px 5px transparent;
  
    --adminBg1:${cColor.saturate(1).tint(0.1)};
    --adminBg2:${cColor.saturate(1).brighten(1.05)};
  
    --tagColor1:${cColor.saturate(1).brighten(1.2).mix(colors2[3], 0.12)};
    --tagColor2:${cColor.saturate(1).brighten(1.2).mix(colors2[4], 0.12)};
    --tagShadow:${cColor.saturate(3).brighten(2).mix("#ffffff", 0.5)};
  
    --codeColor: ${siteColor};
    --codeBgColor: ${cColor.mix("#ffffff", 0.88)};`;

    sessionStorage.setItem('colorAll', colorAll)
    return colorAll
}

app.component('IconUse', IconUse);
app.use(router);
app.use(store);
app.use(i18n);
app.mount('#app');