/**
 * 接口获取数据及相关处理
 * **/
import gql from "graphql-tag";
import apolloclient from "@/assets/js/apolloclientcreater.js";
import apolloProvider from "@/assets/js/apolloclient.js";

import store from '@/store/index'
const priceclient = apolloclient.initAppllo(store.state.api_gateway_uri + "/hub-price");
const copilotclient = apolloclient.initAppllo(store.state.api_gateway_uri + "/copilot");
const copilotassembleclient = apolloclient.initAppllo(store.state.api_gateway_uri + "/copilot_assemble");

// 获取price info
export const getPriceSessionInfo = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.query({ query: gql `query priceSessionInfo($sessionId: String!){
      priceSessionInfo(sessionId:$sessionId){
      id
      currentStep
      autoTask
      views{
        id
        viewName
        viewDesc
        createdAt
        viewUrl
        index
        step
      }
    }
  }`, variables });
};
// 获取price viewinfo
export const getPriceViewInfo = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.query({ query: gql `query priceSessionVIew($viewId: String!){
      priceSessionVIew(viewId:$viewId){
    id
    data
  }
}`, variables });
};
// 获取price viewinfo
export const getFullPriceViewInfo = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.query({ query: gql `query priceSessionVIew($viewId: String!){
    priceSessionVIew(viewId:$viewId){
      id
      viewName
      viewDesc
      createdAt
      viewUrl
      index
      step
}
}`, variables });
};
export const closeAutoTask = (variables, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation closeAutoTask(
    $sessionId: String!) {
      closeAutoTask(
        sessionId:$sessionId
    ) {
    id
    }
  }`, variables });
};

// //
export const updatePriceSessionView = (variables, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation updatePriceSessionView(
      $entity: PriceSessionViewInput!) {
        updatePriceSessionView(
          entity:$entity
      ) {
      id
      }
    }`, variables });
};

export const generateToCPrompt = (variables, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation generateToCPrompt(
    $entity: PriceSessionViewInput!) {
      generateToCPrompt(
        entity:$entity
    ) {
    str
    }
  }`, variables });
};
export const executeTool = (variables, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `
    mutation executeTool(
      $toolName: String!,
      $sessionId: String,
      $args: Map,
      $autoTask: Boolean
    ) {
        executeTool(
          toolName: $toolName,
          sessionId: $sessionId,
          args: $args,
          autoTask: $autoTask
      ) {
        isError
        datas
        url
        errorMessage
      }
    }`, variables });
};
export const cases = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.query({ query: gql `query cases {
      cases {
        id
        active
        title
        image
        description
        text
        type
        creator
        agentId
        creatorName
        caseFiles{
          id
          name
          active
          url
          knowledgeStatus
          knowledgeName
        }
      }
    }`, variables });
};
// 
export const caseFiles = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.query({ query: gql `query caseFiles{
    caseFiles{
      id
      active
      url
      knowledgeStatus
      knowledgeName
}
}`, variables });
};

export const updateSingleCase = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation updateCase(
  $entity: CaseInput!) {
    updateCase(
      entity:$entity
  ) {
    id
    caseFiles{
      id
      name
      active
      url
      knowledgeStatus
      knowledgeName
    }
  }
}`, variables });
};
export const updateCaseFile = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation updateCaseFile(
    $entity: CaseFileInput!) {
      updateCaseFile(
        entity:$entity
    ) {
      id
      title
      knowledgeName
      knowledgeStatus
    }
  }`, variables });
};

export const addSingleCase = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation addCase(
$entity: CaseInput!) {
  addCase(
    entity:$entity
) {
  id
  caseFiles{
    id
    name
    active
    url
    knowledgeStatus
    knowledgeName
  }
}
}`, variables });
};
export const addCaseFile = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation addCaseFile(
  $entity: CaseFileInput!) {
    addCaseFile(
      entity:$entity
  ) {
    id
    title
  }
}`, variables });
};
export const deleteSingleCase = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation deleteCase(
$id: String!) {
  deleteCase(
  id:$id
) {
title
}
}`, variables });
};
export const deleteCaseFile = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation deleteCaseFile(
$id: String!) {
  deleteCaseFile(
    id:$id
) {
title
}
}`, variables });
};
export const syncCaseFiles = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation syncCaseFiles {
  syncCaseFiles {
title
}
}`, variables });
};
// 
export const uploadFile = (variables = {}, clientid) => {
    return apolloProvider.clients.uploadclient.mutate({ mutation: gql ` mutation upload($file: Upload){
    upload(file: $file){
        uri
    }
}`, variables });
}
export const getCopilitGenerate = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation generate(
      $type: String!
      $data: String) {
        generate(
          type:$type,
          data:$data,
      ) {
      text
      }
    }`, variables });
};

// 
export const knowledgecategories = (variables = {}, customclient) => {
    customclient = customclient ? customclient : copilotclient;
    return customclient.query({ query: gql `query categories($copilot: ID!){
    categories(copilot:$copilot){
      name
      text
      sort
      description
}
}`, variables });
};

export const knowledges = (variables = {}, customclient) => {
    customclient = customclient ? customclient : copilotclient;
    return customclient.query({ query: gql `query knowledges($copilot: ID!,
    $keyword: String,
    $first: Int,
    $offset: Int,
    $filter: KnowledgeFilter){
    knowledges(copilot:$copilot,
      keyword:$keyword,
      first:$first,
      offset:$offset,
      filter:$filter){
        edges{
          node{
            doc{
              name
              kind
              chunks
              status
              statusDesc
            }
          }
        }
      }
    }`, variables });
};
export const updateKnowledges = (variables = {}, customclient) => {
    customclient = customclient ? customclient : copilotclient;
    return customclient.mutate({ mutation: gql `mutation updateKnowledges(
  $copilot: ID!,
  $knowledges: [KnowledgeInput]) {
    updateKnowledges(
      copilot:$copilot,
      knowledges:$knowledges,
  ) {
    doc{
      status
      name
      chunks
      statusDesc
    }
  }
}`, variables });
};
export const createKnowledges = (variables = {}, customclient) => {
    customclient = customclient ? customclient : copilotclient;
    return customclient.mutate({ mutation: gql `mutation createKnowledges(
$copilot: ID!,
$knowledges: [KnowledgeInput]) {
  createKnowledges(
    copilot:$copilot,
    knowledges:$knowledges,
) {
  doc{
    status
    name
    chunks
    statusDesc
  }
}
}`, variables });
};
export const deleteKnowledges = (variables = {}, customclient) => {
    customclient = customclient ? customclient : copilotclient;
    return customclient.mutate({ mutation: gql `mutation deleteKnowledges(
    $copilot: ID!,
    $kind: String!,
    $names: [ID!]) {
      deleteKnowledges(
      copilot:$copilot,
      kind:$kind,
      names:$names,
    ) {
    doc{
      status
      name
      chunks
      statusDesc
    }
    }
    }`, variables });
};
export const updateKnowledgeState = (variables = {}, customclient) => {
    customclient = customclient ? customclient : copilotclient;
    return customclient.mutate({ mutation: gql `mutation updateKnowledgeState($copilot: ID!, $enabled: Boolean!, $kind: String!, $names: [ID!]) {
    updateKnowledgeState(
      copilot: $copilot
      enabled: $enabled
      kind: $kind
      names: $names
    ) {
      name
    }
  }`, variables });
};



export const getAssembleParts = (variables = {}, customclient) => {
    customclient = customclient ? customclient : copilotassembleclient;
    return customclient.query({ query: gql `query parts($filter: PartFilter) {
    parts(filter: $filter) {
      versions {
        name
        sources {
          name
          kind
          content
        }
      }
    }
  }
  `, variables });
};


// 课程预览
export const getCurrentClass = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.query({ query: gql `query{
    currentClass {
      id
      startTime
      endTime
      submitDeadline
      teacher
      teacherName
      courseName
      groupUsers{
        id
        groupIndex
        users{
          openId
          name
        }
      }
    }
  }`, variables });
};
export const getGroupUsers = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.query({ query: gql `query groupUsers($classId: String!){
    groupUsers(classId:$classId) {
      id
      groupIndex
      users{
        openId
        name
      }
    }
  }`, variables });
};
export const studentClassInfo = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.query({ query: gql `query studentClassInfo($classId: String,
    $openid: String!,
    $startTime: Timestamp,
    $endTime: Timestamp){
    studentClassInfo(classId:$classId,
      openid:$openid,
      startTime:$startTime,
      endTime:$endTime) {
      id
      createdAt
      sessionId
      views{
        id
        viewName
        createdAt
        step
      }
  }
}`, variables });
};
export const submitPriceSessionView = (variables = {}, customclient) => {
    customclient = customclient ? customclient : priceclient;
    return customclient.mutate({ mutation: gql `mutation submitPriceSessionView($viewId: String!) {
    submitPriceSessionView(
      viewId: $viewId
  ) {
    id
  }
}`, variables });
};