import { ApolloClient, createHttpLink, InMemoryCache, ApolloLink } from '@apollo/client'
import { onError } from '@apollo/client/link/error'
import store from '@/store/index'
const errorexclude = (graphQLErrors,
    networkError,
    response
) => {
    let errorMsg = "";
    if (response && response.data) {
        graphQLErrors[0].responsedata = response.data;
    }
    if (graphQLErrors && graphQLErrors.length) {
        errorMsg = graphQLErrors[0].message ? graphQLErrors[0].message : "";
    }
    if (!!response && response.errors !== undefined && response.errors.length) {
        errorMsg = !response.errors[0].message ? "服务器错误" : response.errors[0].message;
    }
    if (networkError) {
        if (networkError.statusCode == 503) {
            errorMsg = "服务器报错503"
        }
    }
    if (errorMsg) {
        console.log(errorMsg)
    }
}
export default {
    initAppllo(url, tokenpass) {
        // 20230530 增加多平台登出检测
        const customFetch = (uri, options) => {
            let id_token = null;
            if (store && store.state && store.state.oidcStore && store.state.oidcStore.id_token) {
                id_token = store.state.oidcStore.id_token;
            }
            return fetch(uri + '?id_token_hint=' + id_token, options);
            // return fetch(uri , options);
        };
        // HTTP connection to the API
        const httpLink = new createHttpLink({
            uri: url,
            fetch: customFetch
        });
        // 
        let middlewareLink = new ApolloLink((operation, forward) => {
            let token = store.state.oidcStore.access_token;
            if (tokenpass) {
                token = tokenpass
            }
            operation.setContext({
                headers: {
                    Authorization: `Bearer ${token}` || null
                }
            });
            return forward(operation);
        });

        let authLink = middlewareLink.concat(httpLink);
        let errorLink = onError(({
            graphQLErrors,
            networkError,
            response
        }) => {
            if (response && response.errors && response.errors.length) {
                let errcode = response.errors[0].message;
                if (errcode === 'OIDC_BC_LOGOUT') { //多应用退出
                    store.dispatch("oidcStore/authenticateOidc").then(res => {})
                } else {
                    errorexclude(graphQLErrors,
                        networkError,
                        response)
                }
            } else {
                errorexclude(graphQLErrors,
                    networkError,
                    response)
            }
        });
        // Cache implementation
        const cache = new InMemoryCache({
                dataIdFromObject: o => {
                    o.id ? `${o.__typename}-${o.id}` : `${o.__typename}-${o.cursor}`
                },
            })
            // Create the apollo client
        const apolloClient = new ApolloClient({
            link: errorLink.concat(authLink),
            cache,
            defaultOptions: {
                watchQuery: {
                    fetchPolicy: 'network-only',
                },
                query: {
                    fetchPolicy: 'network-only',
                }
            }
        })
        return apolloClient
    }
}