import { ApolloClient, createHttpLink, InMemoryCache } from '@apollo/client/core'
import createUploadLink from "apollo-upload-client/createUploadLink.mjs";
import { onError } from '@apollo/client/link/error'
import { createApolloProvider } from '@vue/apollo-option'
import { ApolloLink } from 'apollo-link'
import store from '@/store/index'
import { ElMessage } from 'element-plus'
const defaultOptions = {
    watchQuery: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
    },
    query: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
    },
    mutate: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
    },
};
const errorexclude = (graphQLErrors,
    networkError,
    response) => {
    let errorMsg = "";
    if (response && response.data) {
        graphQLErrors[0].responsedata = response.data
    }
    if (graphQLErrors && graphQLErrors[0]) {
        errorMsg = graphQLErrors[0].message
        if (graphQLErrors[0].extensions && graphQLErrors[0].extensions.prompt) {
            errorMsg = graphQLErrors[0].extensions.prompt
        }
    } else if (response && response.errors && response.errors[0]) {
        errorMsg = response.errors[0].message
        if (response.errors[0].extensions && response.errors[0].extensions.prompt) {
            errorMsg = response.errors[0].extensions.prompt
        }
    } else if (networkError) {
        if (networkError.statusCode == 503) {
            errorMsg = "服务器报错503"
        } else {
            errorMsg = `服务器报错 ${networkError.statusCode}`
        }
    }
    if (errorMsg) {
        // ElMessage.clear();
        ElMessage.warning(errorMsg);
    }
}

const appllolinkclient = (url, isuploadtype) => {
    const customFetch = (uri, options) => {
        const id_token = (store?.state?.oidcStore?.id_token) ? (store?.state?.oidcStore?.id_token) : null;
        // uri = store.getters.getapigatewayuri + uri;
        uri = uri;
        return fetch(uri + '?id_token_hint=' + id_token, options);
    };
    // HTTP connection to the API
    const httpLink = new createHttpLink({
        uri: url,
        fetch: customFetch
    });
    const httpUploadLink = new createUploadLink({
        uri: url,
        fetch: customFetch
    });

    const middlewareLink = new ApolloLink((operation, forward) => {
        const token = (store?.state?.oidcStore?.access_token) ? (store?.state?.oidcStore?.access_token) : null;
        operation.setContext({
            headers: {
                Authorization: `Bearer ${token}` || null,
            }
        })
        return forward(operation)
    })
    const errorloink = onError(({ graphQLErrors, networkError, response }) => {
        if (response && response.errors && response.errors.length) {
            let errcode = response.errors[0].message;
            if (errcode === 'OIDC_BC_LOGOUT') { //多应用退出
                store.dispatch("oidcStore/authenticateOidc").then(res => {})
            } else if (response.errors[0].extensions && response.errors[0].extensions.code === 'ACCESS_TOKEN_INVALID') {
                store.dispatch("oidcStore/signOutOidc").then(res => {})
            } else {
                errorexclude(graphQLErrors,
                    networkError,
                    response)
            }
        } else {
            errorexclude(graphQLErrors,
                networkError,
                response)
        }
    })
    const cache = new InMemoryCache({
        dataIdFromObject: (o) => {
            o.id ? `${o.__typename}-${o.id}` : `${o.__typename}-${o.cursor}`
        }
    })
    const asingleLinkStr = middlewareLink.concat(httpLink);
    const asingleLinkUploadStr = middlewareLink.concat(httpUploadLink);
    if (isuploadtype) {
        const asingleclient = new ApolloClient({
            link: errorloink.concat(asingleLinkUploadStr),
            cache: cache,
            connectToDevTools: true,
            defaultOptions: {
                watchQuery: {
                    fetchPolicy: 'no-cache'
                },
                query: {
                    fetchPolicy: 'no-cache'
                }
            }
        });
        return asingleclient

    } else {
        const asingleclient = new ApolloClient({
            link: errorloink.concat(asingleLinkStr),
            cache,
            defaultOptions: {
                watchQuery: {
                    fetchPolicy: 'no-cache',
                },
                query: {
                    fetchPolicy: 'no-cache',
                }
            }
        });
        return asingleclient
    }
}



// store.state.api_gateway_uri + "/hub-price"

const apolloProvider = createApolloProvider({
    clients: {
        // iamClient: appllolinkclient(store.state.api_gateway_uri +"/iam"),
        iam: appllolinkclient(store.state.api_gateway_uri + "/iam"),
        priceclient: appllolinkclient(store.state.api_gateway_uri + "/hub-price"),
        uploadclient: appllolinkclient(store.state.api_gateway_uri + "/file_v2", 'upload'),


        // igaClient: appllolinkclient(store.state.api_gateway_uri +"/iga"),
        // contactClient: appllolinkclient(store.state.api_gateway_uri +"/contacts"),
        // contactuploadClient: appllolinkclient(store.state.api_gateway_uri +"/contacts", 'upload'),
        // appcenterClient: appllolinkclient(store.state.api_gateway_uri +"/appcenter"),
        copilot: appllolinkclient(window.Global.COPILOT_URL),
        copilotAuth: appllolinkclient(window.Global.COPILOT_URL),
        copilotAssemble: appllolinkclient(window.Global.COPILOT_ASSEMBLE_URL),
        copilotBus: appllolinkclient(window.Global.COPILOT_BUS_URL)
    },
    defaultClient: appllolinkclient(store.state.api_gateway_uri + "/builtin"),
    defaultOptions: defaultOptions
})

export default apolloProvider;