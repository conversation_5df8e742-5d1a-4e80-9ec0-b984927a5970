<template>
  <div class="img-upload-base64">
    <el-upload
      class="upload-img img-to-base64-picture-card"
      list-type="picture-card"
      action=""
      :show-file-list="false"
      :accept="'.'+imgtypes.join(',.')"
      :on-change="getFile">
        <div class="upload-label">
            <div class="upload-icon" v-show="(!edititem.imgbase64)">
                <el-icon><Camera /></el-icon>
            </div>
            <div class="preview-img" v-if="edititem.imgbase64">
                <img :src="edititem.imgbase64">
            </div>
        </div>
      </el-upload>
      <div class="sub-hint" v-show="(edititem.imgbase64)">
          <el-icon @click="deleteImg()"><Delete /></el-icon>
          <el-icon @click="zoomInImg()"><ZoomIn /></el-icon>
      </div>
      <div class="sub-hint-txt">仅支持：{{'*.'+imgtypes.join('/*.')}}，且不超过{{imgmaxsize}}kb。</div>
      <el-dialog v-model="imgdia" width="90%">
        <img w-full :src="edititem.imgbase64" style="width:100%" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  // 允许的图片类型
  imgtypes: {
    type: Array,
    default: () => ['jpg', 'png']
  },
  imgmaxsize: {
    type: Number,
    default: () => 2000
  },
  // 图片变更载体
  imgobj: {
    type: Object,
    default: () => ({
      imgbase64: ""
    })
  }
})

const $emit = defineEmits(['changedImg'])

const imgdia = ref(false)
const changeimg = ref(false)
const edititem = ref({...props.imgobj})

watch(() => props.imgobj, (val) => {
  edititem.value = JSON.parse(JSON.stringify(val))
}, { deep: true })

// 图片转base64    
const getFile = (file) => {
  let uploadtype = ""
  if(file && file.name) {
    uploadtype = file.name.substring((file.name).lastIndexOf('.') + 1)
  }
  if(file && file.size && file.size > (props.imgmaxsize * 1000)) {
    ElMessage({
      type: "warning",
      message: "文件大小超过" + props.imgmaxsize + "kb",
      duration: 5000
    })
    return
  }
  if(props.imgtypes.indexOf(uploadtype) < 0) {
    ElMessage({
      type: "warning",
      message: "仅支持上传*." + props.imgtypes.join('/*.') + "文件",
      duration: 5000
    })
    return
  }
  changeimg.value = false
  getBase64(file.raw).then((res) => {
    changeimg.value = true
    edititem.value.imgbase64 = res
    // 修改保存至父级
    changedImg()
  })
}

// 获取图片转base64
const getBase64 = (file) => {
  return new Promise(function(resolve, reject) {
    const reader = new FileReader()
    let imgResult = ''
    reader.readAsDataURL(file)
    reader.onload = function() {
      imgResult = reader.result
    }
    reader.onerror = function(error) {
      reject(error)
    }
    reader.onloadend = function() {
      resolve(imgResult)
    }
  })
}

const deleteImg = () => {
  changeimg.value = false
  edititem.value.imgbase64 = ''
  // 修改保存至父级
  changedImg()
}
const zoomInImg=()=>{
    imgdia.value=true
}

const changedImg = () => {
  $emit('changedImg', edititem.value)
}
</script>

<style lang="scss" scoped>
// .img-to-base64-picture-card{
//   /deep/ .el-upload--picture-card{
//     position: relative;
//   }
// }
</style>