<template>
  <div class="file-upload">
      
    <!-- -->
    <el-upload
      action=""
      accept=".pdf,.doc,.docx"
      :show-file-list="false"
      :multiple="false"
      :on-error="handleError"
      :on-change="handleFileChange"
      :http-request="toUploadFile">
        <template #trigger>
          <el-button type="primary">选择文件</el-button>
        </template>
        <template #tip>
          <div class="el-upload__tip text-red">
          请上传智能体文件
          </div>
        </template>
      </el-upload>
          <el-progress
          v-if="fileprogress?.file"
          :percentage="fileprogress.progress"
          :color="fileprogress.color"
          class="progress-bar"
        ></el-progress>
      
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useStore } from 'vuex'
import {uploadFile} from '@/assets/api/api.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import {debounceCustom} from '@/utils'


const props=defineProps({
  showfilelist: {
    type: <PERSON><PERSON>an,
    default: () => false
  },
  multiple: {
    type: Boolean,
    default: () => false
  },
})
let fileprogress=ref({});
let allfiles=ref([]);
const handleFileChange=debounceCustom((file, fileList)=>{
  console.log(file, fileList)
},500);
const handleError = (error, file) => {
  // 显示错误提示
  ElMessage.error('文件上传失败，请重试'+error);
}
const $emit = defineEmits()
const toUploadFile=(data)=>{
      let uploadtype = "";
      let filename = "";
      console.log('data',data)
      if(data.file&&data.file.name){
        filename=data.file.name;
        uploadtype=data.file.name.substring((data.file.name).lastIndexOf('.') + 1);
      }
      fileprogress.value.file=data.file
      let variables={
        file:data.file
      }
      // 虚拟进度条
      let timer= startRandomProgressBar();
      // return
      uploadFile(variables).then((res)=>{
        if(res?.data?.upload?.uri){
          clearInterval(timer);
          fileprogress.value.progress=100;
          $emit('fileUploaded',res.data.upload.uri,{uploadtype,filename})
          setTimeout(()=>{
            fileprogress.value={}
          },300)
        }else{
           fileprogress.value={}
        }
      }).catch((err)=>{
        fileprogress.value={}
        ElMessage.warning(err)
      })
};
// 虚拟进度条进展
const startRandomProgressBar=()=>{
    let progress = 0;
    const target = 95;//
    // 随机时间间隔（100ms到1000ms）
    const interval = Math.random() * 900 + 100;
    const timer = setInterval(() => {
        // 随机增量（1%到10%）
        const increment = Math.round(Math.random() * 9 + 1);
        progress = Math.min(progress + increment, target);
        fileprogress.value.progress=progress;
        fileprogress.value.type='danger';
        if(progress>50){
          fileprogress.value.type='warning';
        }else if(progress>80){
          fileprogress.value.type='success';
        }
        if (progress >= target) {
            clearInterval(timer);
        }
    }, interval);
    
    return timer; 
}
</script>