<template>
    <div class="data-inner" v-loading="loading">
        <iframe 
            :src="iframeSrc"
            width="100%" 
            height="100%"
            ref="inneriframe"
            @load="iframeLoaded"
            id="inneridiframe"
            frameborder="0"></iframe>
     </div>
</template>

<script setup>
import { ref, watch, onMounted,nextTick, inject } from 'vue'
const props = defineProps({
  url: {
    type: String,
    required: true
  },
})
const loading = ref(true)
const inneriframe = ref(null)
const iframeSrc = ref('')

// 添加防抖避免频繁刷新
let updateTimer = null

const updateIframeSrc = () => {
  loading.value = true
  // 添加时间戳避免缓存
  const timestamp = new Date().getTime()
  iframeSrc.value = addTimestampToUrl(props.url)
  if (process.env.NODE_ENV !== "production") {
    iframeSrc.value='http://localhost:8001'+iframeSrc.value.split('https://cloud.ketanyun.cn').join('')
  } 
  
  // 清除之前的定时器
  if (updateTimer) clearTimeout(updateTimer)
  
  // 设置超时处理
  updateTimer = setTimeout(() => {
    if (loading.value) {
      loading.value = false
      console.error('页面加载超时')
    }
  }, 5000) // 5秒超时
  // 
}

// autoTaskToChildren({
//   autoTaskToChildren
// });
// 智能添加时间戳到URL
const addTimestampToUrl = (url) => {
  if (!url) return url
  
  try {
    const timestamp = new Date().getTime()
    const urlObj = new URL(url, window.location.origin)
    
    // 如果URL已经有查询参数
    if (urlObj.search) {
      // 检查是否已有_t参数
      const params = new URLSearchParams(urlObj.search)
      params.set('_t', timestamp)
      urlObj.search = params.toString()
    } else {
      // 没有查询参数则添加
      urlObj.search = `_t=${timestamp}`
    }
    
    // 处理hash部分
    if (url.includes('#')) {
      const [baseUrl, hash] = url.split('#')
      // 如果原始URL是相对路径，保留hash部分
      if (!url.startsWith('http') && hash) {
        return `${urlObj.toString()}#${hash}`
      }
    }
    
    return urlObj.toString()
  } catch (e) {
    console.error('URL处理错误:', e)
    // 如果URL解析失败，回退到简单添加方式
    return `${url}${url.includes('?') ? '&' : '?'}_t=${new Date().getTime()}`
  }
}
watch(() => props.url, () => {
  updateIframeSrc()
}, { immediate: true })


const iframeLoaded = () => {
  loading.value = false
  if (updateTimer) clearTimeout(updateTimer);
}

onMounted(() => {
  // 确保 iframe 能正确加载
  if (!iframeSrc.value && props.url) {
    updateIframeSrc()
  }
})
</script>