import { createStore } from 'vuex'
import { vuexOidcCreateStoreModule } from 'vuex-oidc'
import { WebStorageStateStore } from 'oidc-client'
import { v4 as uuidv4 } from 'uuid';
let g = window.Global;

let oidcSettings = {};
if (!g) {
    g = {
        contextPath: '/hub-price',
        oauth2_uri: 'https://cloud.ketanyun.cn/sso/oauth2',
        oauth2_logout_uri: 'https://cloud.ketanyun.cn/sso/logout',
        // client_id: "qCNuDOd17NoGpwUnEqtx", //devel
        client_id: "UpKLLrdlQzEcrGLNhJ1O", //cloud
        // client_id: "YSVJnxuw4zx68L3rRQA0", //priceagent
        api_gateway_uri: "/bus/graphql",
        consoleDomain: 'http://localhost:8000/local',
        tenant_code: '',
        // 机器人
        COPILOT_URL: '/copilot-api/internal-graphql',
        COPILOT_ASSEMBLE_URL: '/bus/graphql/copilot_assemble',
        COPILOT_BUS_URL: '/bus/graphql/copilot',
        WSS_ORIGIN: 'wss://cloud.ketanyun.cn',
        copilot_brainstorming_id: '01JZSWBYKJYP5W80S32D5QRYFY',
        preview_url: '',
        agent_id: '01JTMDGAFK6FSAPWVCKTRRV0H5',

    };
    window.Global = {...g }
}
let authority = g.oauth2_uri;
let silentRedirectUri = ((window.Global.consoleDomain || window.origin) + g.contextPath + "/oidc-silent")
oidcSettings = {
    authority: authority,
    metadata: {
        issuer: authority,
        authorization_endpoint: authority + "/authorize",
        userinfo_endpoint: authority + "/userinfo",
        end_session_endpoint: g.oauth2_logout_uri + "?redirect_uri=" + window.location.origin + g.contextPath,
        jwks_uri: authority + "/jwks.json"
    },
    clientId: g.client_id,
    redirectUri: window.location.origin + g.contextPath + "/oidc-callback",
    responseType: "id_token token",
    scope: "data openid ",
    automaticSilentRenew: true,
    silentRedirectUri: silentRedirectUri,
    silentRequestTimeout: 1000,
    //将用户信息存储到sessionStorage 结合sso/admin配置关闭浏览器即登出，使得用户实现关闭浏览器后再次进入系统恒需要重新登录
    // userStore: new WebStorageStateStore({ store: window.sessionStorage }),
    // 

};


console.log('oidcSettings', oidcSettings)

export default createStore({
    state: {
        api_gateway_uri: window.Global && window.Global.api_gateway_uri ? window.Global.api_gateway_uri : '/bus/graphql',
        dataselectorurl: window.Global && window.Global.console_data_selector_url ? window.Global.console_data_selector_url : '',
        userselectorurl: window.Global && window.Global.console_user_selector_url ? window.Global.console_user_selector_url : '',
        consoleDomain: window.Global && window.Global.consoleDomain ? window.Global.consoleDomain : window.location.origin,
        tenant_code: window.Global && window.Global.tenant_code ? window.Global.tenant_code : '',
        COPILOT_BUS_URL: window.Global && window.Global.COPILOT_BUS_URL ? window.Global.COPILOT_BUS_URL : '/bus/graphql/copilot',
        copilot_brainstorming_id: window.Global && window.Global.copilot_brainstorming_id ? window.Global.copilot_brainstorming_id : '',
        preview_url: window.Global && window.Global.preview_url ? window.Global.preview_url : '',
        agent_id: window.Global && window.Global.agent_id ? window.Global.agent_id : '',
        contextPath: window.Global && window.Global.contextPath ? window.Global.contextPath : '/hub-price',
        currenttabviews: [],
        siteInfo: {
            "systemName": "上海科探信息技术有限公司",
            "redirectUrl": "/airobotsquare-v2",
            "systemLogoImageUrl": "",
            "systemRobotImg": "",
            "systemUserImg": "",
            "systemBanner": "",
            "openManualReply": false,
            "hiddenQuesAndDocs": false,
            "openThoughtSteps": true,
            "showSource": true,
            "openWXSDK": false,
            "pageName": "上海科探信息技术有限公司",
            "systemLogo": "",
            "siteColor": "#9D2222"
        },
    },
    mutations: {
        setCurrentTabView(state, data) {
            state.currenttabviews = data;
        },
    },
    getters: {
        getCurrentTabView(state) {
            return state.currenttabviews
        },
        getCopilotBrainstormingId(state) {
            return state.copilot_brainstorming_id
        },
        getpreview_url(state) {
            return state.preview_url
        },
        getagent_id(state) {
            return state.agent_id
        },
        getcontextPath(state) {
            return state.contextPath
        },

    },
    actions: {},
    modules: {
        oidcStore: vuexOidcCreateStoreModule(
            oidcSettings, {
                namespaced: true,
                dispatchEventsOnWindow: true
            }, {
                userLoaded: (user) => {
                    console.log('OIDC user is loaded:', user);
                },
                userUnloaded: () => console.log('OIDC user is unloaded'),
                accessTokenExpiring: () => console.log('Access token will expire'),
                accessTokenExpired: () => console.log('Access token did expire'),
                silentRenewError: () => console.log('OIDC user is unloaded'),
                userSignedOut: () => console.log('OIDC user is signed out'),
                oidcError: (payload) => console.log('OIDC error', payload)
            }
        )
    }
})