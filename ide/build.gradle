plugins {
    id "com.github.node-gradle.node" version "3.5.1"
}

group = 'ketanyun.contact'
version = '2.0'

apply plugin: 'base'

repositories {
    mavenCentral()
}

node {
    download = true
    version = '14.21.3'
    npmVersion = '6.14.18'
    distBaseUrl = "https://npmmirror.com/mirrors/node"
    nodeProjectDir = file("${project.projectDir}")
}

// 创建自定义的npm任务，替代默认任务
task customNpmInstall(type: NpmTask) {
    dependsOn npmSetup
    group = 'node'
    args = ['install', '--registry=https://registry.npmmirror.com']
    inputs.file('package.json')
    outputs.upToDateWhen { file('node_modules').exists() }
}

task npmBuild(type: NpmTask) {
    dependsOn customNpmInstall
    group = 'node'
    args = ['run', 'build']
    inputs.files fileTree('src')
}

task clean (type: Delete, overwrite: true){
}
