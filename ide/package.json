{"name": "contact", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@antv/g": "^6.1.25", "@antv/g6": "^5.0.49", "@wecom/jssdk": "^2.3.0", "chroma-js": "^3.1.2", "dompurify": "^3.2.4", "element-ui": "^2.15.9", "file-saver": "^2.0.5", "graphql-ws": "^5.16.0", "impress.js": "^1.1.0", "js-yaml": "^4.1.0", "katex": "^0.16.11", "lodash": "^4.17.21", "marked": "^14.0.0", "mitt": "^3.0.1", "vue": "^3.3.4"}, "devDependencies": {"@apollo/client": "^3.5.10", "@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^4.2.3", "@vue/apollo-composable": "^4.0.0-alpha.16", "@vue/apollo-option": "^4.0.0-alpha.20", "@vueuse/core": "^10.3.0", "apollo-link": "^1.2.14", "apollo-upload-client": "^18.0.1", "babel-polyfill": "^6.26.0", "echarts": "^5.5.0", "element-plus": "^2.7.5", "graphql": "^16.3.0", "graphql-tag": "^2.12.6", "moment": "^2.29.1", "oidc-client": "^1.11.5", "react": "^17.0.2", "sass": "^1.27.0", "sass-loader": "^10.0.4", "typescript": "^4.4.3", "url-search-params-polyfill": "^8.2.5", "uuid": "^9.0.1", "vite": "^4.4.5", "vue-apollo": "^3.1.0", "vue-i18n": "^9.2.2", "vue-json-editor": "^1.4.3", "vue-json-viewer": "^3.0.4", "vue-router": "^4.2.4", "vuex": "^4.1.0", "vuex-oidc": "^3.10.3"}}