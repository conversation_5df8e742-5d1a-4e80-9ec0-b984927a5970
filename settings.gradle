pluginManagement {
  repositories {
    maven {
      url 'https://maven.aliyun.com/repository/public/'
    }
    maven {
      url 'https://maven.aliyun.com/repository/spring/'
    }    
    maven {
      url 'https://maven.aliyun.com/repository/spring-plugin/'
    }
    maven {
      url 'https://maven.aliyun.com/repository/central/'
    }
    maven {
      url 'https://maven.aliyun.com/repository/google/'
    }
    maven {
      url 'https://maven.aliyun.com/repository/jcenter/'
    }    
    maven {
      url 'https://maven.aliyun.com/repository/releases/'
    }    
    maven {
      url 'https://maven.aliyun.com/repository/gradle-plugin/'
    } 
    maven {
      url 'https://maven.aliyun.com/repository/grails-core/'
    }
           
    mavenLocal()
    mavenCentral()
  }
}
rootProject.name = 'price'
include 'operator'
include 'ide'

