var Utils = require('./util');
var TreeModel = require('tree-model');

var randomId = function () {
    return Math.random().toString(36).replace(/[^a-z]+/g, '').substr(0, 5)
        + Math.random().toString(36).replace(/[^a-z]+/g, '').substr(0, 5);
};

/**
 *  This class implements CRUD operations that can build a directory tree.
 *
 *  Dependent on GQLUtil class.
 *
 *  Init:
 *    var directory = new Directory({
            GQL: new GQLUtil({introspectionQuery: introspectionQuery})
        });
 *
 *  directory.data : Complete tree structure data
 *  For example:
 {
"id": "dbgyybzcwq",
"name": "D 01",
"children": [{
    "id": "sgkdrttwsg",
    "name": "D 01-0",
    "pid": "dbgyybzcwq",
    "children": [{
        "id": "User",
        "name": "T 01",
        "pid": "sgkdrttwsg",
        "children": [],
        "service": "builtin/User",
        "isType": true
    }]
}]
}
 *
 *
 */
var Directory = (function () {
    function Directory(config) {
        Utils.__assertArgs({"config.GQL": config.GQL});
        this.GQL = config.GQL;
        this.tree = new TreeModel();

        var model = {};
        if (this.GQL._schema.getTypeMap().VoidType) {
            var description = this.GQL._schema.getTypeMap().VoidType.description;
            if (description) {
                model = eval("(" + description + ")");
            }
        }

        this.root = this.tree.parse(model);
        this.data = this.root.model;
    }

    var _proto_ = Directory.prototype;

    /**
     * Add a directory node named `${name}` to the root or directory node.
     * If the pid is empty, it is used as the parent node id, otherwise it is added under the root node
     */
    _proto_.add = function (name, pid) {
        Utils.__assertArgs({"name": name});

        var handler = new __handler(this.tree, this.root);
        var id = randomId();
        if (pid) {
            handler.find(pid).add(id, name, pid);
        }
        // no pid , looks like adding a root
        else {
            var id = randomId();
            this.root.model.id = id , this.root.model.name = name;
        }
        return this;
    };

    /**
     *   Change the name of the directory node which is the id equals `${id}`
     */
    _proto_.rename = function (name, id) {
        Utils.__assertArgs({"name": name, "id": id});

        var handler = new __handler(this.tree, this.root);
        handler.find(id).rename(name);
        return this;
    };

    /**
     *  Remove  directory node
     */
    _proto_.remove = function (id) {
        Utils.__assertArgs({"id": id});

        var handler = new __handler(this.tree, this.root);
        handler.find(id).remove();
        return this;
    }

    /**
     *  Print the Schema Definition By the schema structure object which is extracted by GraphQL introspection
     *  and Annotate the type VoidType with data
     */
    _proto_.schema = function () {

        if (this.GQL._schema.getTypeMap().VoidType) {
            this.GQL._schema.getTypeMap().VoidType.description = JSON.stringify(this.data);
        }

        return this.GQL.schema();
    };

    return Directory;
}());

/**
 *  This class implements CRUD operations that can make GQL Types under a directory node.
 *
 *      Dependent on Directory class.
 *
 *      Init:
 *          var gqlType = new GQLType({
                directory: directory
            });
 *
 *
 */
var GQLType = (function () {
    function GQLType(config) {
        Utils.__assertArgs({"config.directory": config.directory});
        this.directory = config.directory;
        this.GQL = this.directory.GQL;
        this.tree = this.directory.tree;
        this.root = this.directory.root;
    }

    var _proto_ = GQLType.prototype;

    /**
     *  Add a type node named `${alias}` to a directory node with id equal to `${did}`
     *
     *   service:
     *   example: builtin/User
     *   If the argument name is empty then the service type(example: User) is used as the name
     *
     *     introspectionQuery:  @see GQLUtil:introspectionQuery
     *
     */
    _proto_.add = function (alias, did, introspectionQuery, service, name) {
        Utils.__assertArgs({"alias": alias, "did": did, "introspectionQuery": introspectionQuery, "service": service});

        var handler = new __handler(this.tree, this.root);
        var serviceAndType = Utils.__assertService(service);

        var exist = handler.findAll(serviceAndType.typeName);
        if (exist && exist.length > 0)
            throw new Error("ID repeat: " + serviceAndType.typeName);

        this.GQL.addType(service, introspectionQuery, name);

        handler.find(did).add(serviceAndType.typeName, alias, did, {
            "service": service, "isType": true
        });

        return this;
    };

    /**
     *  Remove type node by the type name
     */
    _proto_.remove = function (name) {
        Utils.__assertArgs({"name": name});
        var handler = new __handler(this.tree, this.root);
        handler.find(name).remove();

        this.GQL.removeType(name);

        return this;
    };

    /**
     *   Change the name of the directory node as `${alias}` which is the id equals `${id}`
     */
    _proto_.rename = function (name, alias) {
        Utils.__assertArgs({"name": name, "alias": alias});
        var handler = new __handler(this.tree, this.root);
        handler.find(name).rename(alias);
        return this;
    };

    /**
     *  Returns all types under a directory node with the given ID
     */
    _proto_.all = function (did) {
        var handler = new __handler(this.tree, this.root);

        var types = this.GQL.getTypes();
        if (!did) return types;
        var node = handler.find(did);
        if (types && node) {
            return types.filter(function (T) {
                return node._node.children.filter(function (N) {
                    return N.model.id && N.model.id === T.name;
                }).length > 0;
            });
        }
        return [];
    };

    return GQLType;
}());

function __handler(tree, root) {
    this.tree = tree;
    this.root = root;
    __handler.prototype.findAll = function (id) {
        return this.root.all(function (node) {
            return node.model.id === id;
        });
    };
    __handler.prototype.find = function (id) {
        var _node = this.root.first(function (node) {
            return node.model.id === id;
        });
        if (!_node)
            throw new Error("invalid id: " + id);
        this._node = _node;
        return this;
    };
    __handler.prototype.add = function (id, name, pid, args) {
        var data = {
            id: id,
            name: name,
            pid: pid,
            children: []
        };
        if (args)
            for (var K in args)
                data[K] = args[K];

        this._node.addChild(this.tree.parse(data));
    };
    __handler.prototype.rename = function (name) {
        this._node.model.name = name;
    };
    __handler.prototype.remove = function () {
        this._node.drop();
    };
}

module.exports = {
    Directory,
    GQLType
};