function __assertArgs(args) {
    for (var name in args)
        if (!args[name])
            throw new Error("invalid arg: " + name);
};

function __assertService(service) {
    var serviceAndType = service.split("/");
    if (serviceAndType.length != 2)
        throw new Error("invalid argument: service" + service);
    var serviceName = serviceAndType[0];
    var typeName = serviceAndType[1];
    __assertArgs({"serviceName": serviceName, "typeName": typeName});
    return {serviceName: serviceName, typeName: typeName};
};

module.exports = {
    __assertArgs,
    __assertService
};