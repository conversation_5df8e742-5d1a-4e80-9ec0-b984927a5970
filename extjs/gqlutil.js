var Utils = require('./util');
const https = require("https");
const { parse, execute, buildSchema, GraphQLSchema } = require("graphql");
const { makeExecutableSchema } = require("graphql-tools");
const { printSchema, printIntrospectionSchema, buildClientSchema, introspectionQuery } = require("graphql/utilities");
const fetch = require("node-fetch");

const gqlBuilder = require('gql-query-builder');


/**
 *   This class implements CRUD operations that can build a GQL schema.
 *
 *   Init:
 *      new GQLUtil({introspectionQuery: introspectionQuery})
 *
 *      Required argument: introspectionQuery
 *     introspectionQuery:  Results of A GQL introspection, We take its data attribute as a parameter
 *     example :
 {
     "data": {
         "__schema": {
             "queryType": {
                 "name": "Query"
             }
         }
     }
 }

 introspectionQuery equals:
 {
     "__schema": {
         "queryType": {
             "name": "Query"
         }
     }
 }






 A Schema example

 schema {
    query: Query
    mutation: Mutation
}
 type Query {
  id: ID
}
 type Mutation {
  id: ID
}
 interface Connection {
    edges: [Edge]
    pageInfo: PageInfo
    totalCount: Int
}
 interface Edge {
    cursor: String
    node: Node
}
 type PageInfo {
    endCursor: String
    hasNextPage: Boolean
    hasPreviousPage: Boolean
    startCursor: String
}
 interface Node {
  id: ID
}
 """
 {
"id": "dbgyybzcwq",
"name": "D 01",
"children": [{
   "id": "sgkdrttwsg",
   "name": "D 01-0",
   "pid": "dbgyybzcwq",
   "children": [{
       "id": "User",
       "name": "T 01",
       "pid": "sgkdrttwsg",
       "children": [],
       "service": "builtin/User",
       "isType": true
   }]
}]
 }
 """
 type VoidType  {
  id: ID
}


 *
 */
var GQLUtil = (function() {
    function GQLUtil(config) {
        Utils.__assertArgs({ "config.introspectionQuery": config.introspectionQuery });
        this._introspectionQuery = config.introspectionQuery;
        this._schema = buildClientSchema(this._introspectionQuery);
    }

    var __nativeType = function(schema, name) {
        var _types = Object.values(schema._typeMap);
        if (_types && _types.length > 0) {
            return _types.filter(function(T) {
                return T.name.toLocaleUpperCase() === name.toLocaleUpperCase();
            }).shift();
        }
    };

    var __nativeQueryField = function(queryType, name) {
        var query = queryType && queryType._fields ? Object.values(queryType._fields).filter(function(F) {
            if ((F.name === (name.replace(name[0], name[0].toLowerCase()) + "s")) ||
                (F.name === (name.replace(name[0], name[0].toLowerCase()) + "es"))) { //方法名加s或者es，或者直接为方法名
                return true
            } else if ((F.name === (name + "s")) || (F.name === (name + "es"))) {
                return true
            } else if (F.name === name.replace(name[0], name[0].toLowerCase())) {
                return true
            } else if (F.name === name) {
                return true
            }
            // 否则返回false
            return false
        }).shift() : null;
        if (!query)
            throw new Error("No default query of type: " + name);
        return query;
    };

    //jump out dead circulation
    var __deadCirculation = function() {
        this.deadCirculation = [];
        __deadCirculation.prototype.isCircle = function(node, secondary) {
            this.deadCirculation.push(node + "()" + secondary);
            if (this.deadCirculation.includes(secondary + "()" + node)) {
                console.log("skipped , found dead circulation type: " + node);
                return true;
            }
            return false;
        };
    };

    var _proto_ = GQLUtil.prototype;

    /**
     *  Returns all types that implement the Node interface
     */
    _proto_.getTypes = function() {
        var thiz = this;
        var _types = Object.values(this._schema._typeMap);
        if (_types && _types.length > 0) {
            var _each = function(fields) {
                return fields ? Object.values(fields).map(function(F) {
                    return {
                        name: F.name,
                        description: F.description,
                        arguments: F.args ? F.args.map(function(_args) {
                            return _args.name;
                        }) : [],
                        type: (function() {
                            if (F.type && F.type.constructor.name === 'GraphQLObjectType') {
                                return F.type.name;
                            } else if (F.type && F.type.constructor.name === 'GraphQLList') {
                                return "[" + F.type.ofType.name + "]";
                            }
                            return F.type ? F.type.name : null
                        })()
                    };
                }) : []
            };
            return _types.map(function(T) {
                var Q = _each(thiz._schema._queryType._fields).filter(function(F) {
                    if ((T.name.replace(T.name[0], T.name[0].toLowerCase()) + "s") == F.name) {
                        return true
                    } else if ((T.name.replace(T.name[0], T.name[0].toLowerCase()) + "es") == F.name) {
                        return true
                    } else if ((T.name + "s") == F.name) {
                        return true
                    } else if ((T.name + "es") == F.name) {
                        return true
                    } else if (T.name.replace(T.name[0], T.name[0].toLowerCase()) == F.name) {
                        return true
                    } else if (T.name == F.name) {
                        return true
                    }
                    // 否则返回false
                    return false
                        // return F.name.toLocaleUpperCase() === T.name.toLocaleUpperCase();
                }).shift();
                return {
                    query: Q,
                    mutation: {},
                    name: T.name,
                    type: T.constructor.name,
                    description: T.description,
                    fields: _each(T._fields),
                    interfaces: T._interfaces ? T._interfaces.map(function(I) {
                        return I.name;
                    }) : [],
                    isEnablePaging: Q ? Q.arguments.includes('first') : false,
                    isEnableKeywords: Q ? Q.arguments.includes('keywords') : false,
                    isEnableSorting: Q ? Q.arguments.includes('sortBy') : false,
                };
            });
        }
        return [];
    };

    /**
     * extract GQL type by service argument as a GQL schema type
     */
    _proto_.addType = function(service, introspectionQuery, name) {
        var serviceAndType = Utils.__assertService(service);
        var schema = buildClientSchema(introspectionQuery);
        var typeName = serviceAndType.typeName;
        var type = schema._implementations.Node ? schema._implementations.Node.filter(function(T) {
            return T.name === typeName;
        }).shift() : null;
        Utils.__assertArgs({ "Schema Type": type });

        var query = __nativeQueryField(schema._queryType, type.name);
        var types = [];
        var deadCirculation = new __deadCirculation();
        var __search = function(type) {
            types.push(type);
            type._fields ? Object.values(type._fields).forEach(function(F) {
                if (F.type.constructor.name === 'GraphQLObjectType') {
                    types.push(F.type);
                    if (deadCirculation.isCircle(type.name, F.type.name))
                        return;
                    __search(F.type);
                } else if (F.type.constructor.name === 'GraphQLList') {
                    types.push(F.type.ofType);
                    if (deadCirculation.isCircle(type.name, F.type.ofType.name))
                        return;
                    __search(F.type.ofType);
                }
            }) : [];
        };
        __search(type);
        deadCirculation = new __deadCirculation();
        if (query.type.constructor.name === 'GraphQLList') {
            __search(query.type.ofType);
        } else if (query.type.constructor.name === 'GraphQLObjectType') {
            __search(query.type);
        }

        types = Array.from(new Set(types));

        var thiz = this;
        types.forEach(function(T) {
            thiz._schema.getTypeMap()[T.name] = T;
        });

        this._schema._queryType.getFields()[query.name] = query;

        if (this._schema._implementations.Node)
            this._schema._implementations.Node.push(type);
        else
            this._schema._implementations.Node = [type];
    };

    /**
     *  Remove a GQL schema type by type name
     */
    _proto_.removeType = function(name) {
        Utils.__assertArgs({ "type name": name });
        if (this._schema.getTypeMap()[name]) {
            delete this._schema.getTypeMap()[name];
            delete this._schema._queryType._fields[name.toLocaleLowerCase() + "s"];
            if (this._schema._implementations.Node) {
                for (var i = 0; i < this._schema._implementations.Node.length; i++) {
                    if (this._schema._implementations.Node[i].name === name)
                        this._schema._implementations.Node.splice(i, 1);
                }
            }
        }
    };

    /**
     * Return GQL Query string by parsing the field information of the type
     */
    _proto_.makeQuery = function(name, kind, options) {
        this.getTypes();
        var nativeQueryField = __nativeQueryField(this._schema._queryType, name);
        var nativeType = __nativeType(this._schema, name);
        Utils.__assertArgs({ "name": nativeQueryField, "type": nativeType });
        var deadCirculation = new __deadCirculation();
        var __each = function(type) {
            return Object.values(type._fields).map(function(F) {
                if (F.type.constructor.name === 'GraphQLObjectType') {
                    var obj = {};
                    if (deadCirculation.isCircle(type.name, F.type.name))
                        return null;
                    obj[F.name] = __each(F.type);
                    return obj;
                } else if (F.type.constructor.name === 'GraphQLList') {
                    var obj = {};
                    if (deadCirculation.isCircle(type.name, F.type.name))
                        return null;
                    obj[F.name] = __each(F.type.ofType);
                    return obj;
                } else {
                    return F.name;
                }
            }).filter(f => f);
        };
        var fields = __each(nativeType);
        return gqlBuilder.query({
            operation: nativeQueryField.name,
            fields: fields
        });
    };

    /**
     *  Print the Schema Definition By the schema structure object which is extracted by GraphQL introspection
     */
    _proto_.schema = function() {
        return printSchema(this._schema);
    };

    return GQLUtil;
}());

/**
 *  fetch GQL introspection string, URL : GQL endpoint
 *   or GW URL (canvas-bus/graphql/{service})
 */
function fetchIntrospectionQuery(url, insecure, token) {
    const agent = /^https:\/\//i.test(url) && insecure ? new https.Agent({ rejectUnauthorized: false }) : undefined;
    const body = JSON.stringify({ query: introspectionQuery });
    const method = "POST";
    const headers = {
        Accept: "application/json",
        "Content-Type": "application/json",
        "Authorization": "Bearer " + token
    };
    try {
        return fetch(url, {
            agent,
            method,
            headers,
            body
        });
    } catch (e) {
        throw new Error(`Error while fetching introspection query: ${e.message}`);
    }
}

module.exports = {
    GQLUtil,
    fetchIntrospectionQuery
};