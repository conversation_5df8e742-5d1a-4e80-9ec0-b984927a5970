iden# price

## 产品说明

*

## 如何安装

* 无数据库。依赖于OAuth应用的正确配置和各种其他平台级服务（API）。

### gradle 构建命令

```shell script
gradle clean build      
-POAUTH2_URI=https://cloud.ketanyun.cn/sso   
-POAUTH2_CLIENT_ID=XXX   
-POAUTH2_CLIENT_SECRET=XXX   
-PAPI_GATEWAY_URI=https://cloud.ketanyun.cn/canvas-bus    
-PSSO_URL=/sso/apis/v2 
-PBUS_URL=/bus/graphql/builtin 
-POAUTH_AUTHORITY_URL=/sso/oauth2 
-POAUTH_TOKEN_URL=/sso/oauth2/token 
-POAUTH_INTROSPECT_URL=/sso/oauth2/introspect 


```

注意：

- https://cloud.ketanyun.cn为参考值，产品部署时需要替换为真实域名

### Env 说明

| 环境变量参数               | 说明                                                                                 | 参数格式举例                                      |
|----------------------|------------------------------------------------------------------------------------|---------------------------------------------|
| OAUTH_CLIENT_ID      | Oauth授权认证的client id. 勾选Scope：客户端授权：data openid introspect int 隐式授权：data acl openid | xxxx                                        |
| OAUTH_CLIENT_SECRET  | Oauth授权认证的client secret.                                                           | xxxx                                        |
| OAUTH_INTROSPECT_URL | OAuth内省地址                                                                          | /sso/oauth2/introspect                      |
| OAUTH_TOKEN_URL      | OAuth获取Token地址                                                                     | /sso/oauth2/token                           |
| OAUTH_AUTHORITY_URL  | OAuth地址                                                                            | /sso/oauth2                                 |
| SSO_LOGOUT_URL       | 登出地址                                                                               | https://xxx.ketanyun.cn/sso/logout          |
| BUS_URL              | 网关地址                                                                               | https://xxx.ketanyun.cn/bus/graphql/builtin |
| PRICE_DB_URL        | 数据库地址                                                                              |                                             |
| PRICE_DB_USERNAME  | 数据库用户名                                                                             |                                             |
| PRICE_DB_PASSWORD  | 数据库密码                                                                              |                                             |
| SERVICE_ORIGIN       |                                                                                    | https://XXX.ketanyun.cn                     |
|MCP_SERVER_URL | MCP服务地址                                                                            | https://mcp.api-inference.modelscope.cn/    |
|MCP_SERVER_SSE_ENDPOINT | MCP服务token                                                                         | /sse/aeb29563a33d42                         |
|COPILOT_ID | 主智能体ID                                                                             | 01JWARMTWA7V4T05BV2MRP34MR                                            |
|COPILOT_URL |                                                                                    | https://price-agent.ceibs.edu/bus/graphql/copilot                                            |
|BRAINSTORMING_COPILOT | 头脑风暴智能体 ｜XXXX｜                                                                     



