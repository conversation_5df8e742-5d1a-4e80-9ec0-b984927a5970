import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

buildscript {
    repositories {
        maven {
            url 'https://maven.aliyun.com/repository/gradle-plugin/'
        }
        maven {
            url("https://maven.aliyun.com/nexus/content/groups/public")
        }
        mavenCentral()
    }
    dependencies {
        classpath "gradle.plugin.com.google.cloud.tools:jib-gradle-plugin:2.8.0"
    }
}

plugins {
    id "com.google.cloud.tools.jib" version "3.3.1"
    id 'org.springframework.boot' version '3.2.0'
    id 'io.spring.dependency-management' version '1.1.4'
    id 'java'
}

repositories {
    /*maven {
        url = uri("https://central.sonatype.com/repository/maven-snapshots/")
        name = "Central Portal Snapshots"
        content {
            it.snapshotsOnly()
        }
    }

    maven {
        url = uri("https://repo.spring.io/milestone")
        name = "Spring Milestones"
        content {
            it.releasesOnly()
        }
    }

    maven {
        url = uri("https://repo.spring.io/snapshot")
        name = "Spring Snapshots"
        content {
            it.snapshotsOnly()
        }
        mavenCentral()
    }*/
    mavenLocal() //直接使用本地maven仓库
    maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
    maven { url 'https://nexus.qtgl.com.cn/repository/maven-snapshots' }
    maven { url 'https://nexus.qtgl.com.cn/repository/maven-public' }
    maven { url 'https://maven.aliyun.com/repository/public' }
}


group 'ceibs'
version '1.0-SNAPSHOT'
sourceCompatibility = 17
targetCompatibility = 17


dependencies {
    implementation project(':ide')

    implementation 'org.springframework.boot:spring-boot-starter'
    implementation('org.springframework.boot:spring-boot-starter-tomcat')
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-thymeleaf")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    //implementation 'org.springframework.boot:spring-boot-starter-websocket'

    // Spring AI MCP相关依赖
    //implementation 'org.springframework.ai:spring-ai-starter-mcp-client:1.1.0-SNAPSHOT'
    //implementation 'org.springframework.ai:spring-ai-starter-mcp-client-webflux:1.1.0-SNAPSHOT'
    //implementation 'org.springframework.ai:spring-ai-starter-model-anthropic:1.0.0-RC1'
    //implementation 'org.springframework.ai:spring-ai-bom:1.1.0-SNAPSHOT'
    //implementation 'org.springframework.ai:spring-ai-starter-mcp-server-webflux:1.0.0-RC1'
    implementation 'io.modelcontextprotocol.sdk:mcp:0.11.2'
    implementation 'io.modelcontextprotocol.sdk:mcp-spring-webflux:0.11.2'
    implementation 'com.itextpdf:itext7-core:7.2.3'

   // implementation 'com.alibaba.cloud.ai:spring-ai-alibaba-core:1.0.0.1'
    //implementation 'com.alibaba.cloud.ai:spring-ai-alibaba-mcp:1.0.0.1'


    implementation(group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0')
    implementation(group: 'commons-beanutils', name: 'commons-beanutils', version: '1.9.4')
    implementation(group: 'commons-beanutils', name: 'commons-beanutils-bean-collections', version: '1.8.3')
    implementation(group: 'commons-collections', name: 'commons-collections', version: '3.2.2')

    implementation(group: 'commons-io', name: 'commons-io', version: '2.14.0')
    implementation(group: 'commons-pool', name: 'commons-pool', version: '1.5.6')
    implementation(group: 'commons-dbcp', name: 'commons-dbcp', version: '1.4')

    implementation(group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5.14')
    implementation 'com.google.guava:guava:32.1.2-jre'
    implementation 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'
    implementation group: 'org.apache.oltu.oauth2', name: 'org.apache.oltu.oauth2.client', version: '1.0.0'
    // fastjson
    implementation group: 'com.alibaba', name: 'fastjson', version: '1.2.83'
    implementation group: 'com.graphql-java', name: 'graphql-java', version: '20.0'
    //mysql
    implementation group: 'mysql', name: 'mysql-connector-java', version: '8.0.33'
    //jpa
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-jpa'
    implementation group: 'org.apache.poi', name: 'poi', version: '5.2.4'
    implementation group: 'org.apache.poi', name: 'poi-ooxml', version: '5.4.0'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.apache.httpcomponents:fluent-hc:4.5.14'
    implementation 'ognl:ognl:3.4.2'

    implementation 'org.snakeyaml:snakeyaml-engine:2.6'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.3'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.15.3'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.3'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.15.3'

    // 添加Jakarta EE API（替代javax）
    implementation 'jakarta.servlet:jakarta.servlet-api:6.0.0'
    implementation 'jakarta.annotation:jakarta.annotation-api:2.1.1'

    implementation("org.apache.httpcomponents:httpmime:4.5.14")

}

configurations.all {
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        if (details.requested.group == 'com.fasterxml.jackson.core') {
            details.useVersion '2.15.3'
        }
    }
}

processResources {
    doLast {
        copy {
            from("${project(':ide').projectDir}/dist")
            into("${buildDir}/resources/main/static")
        }
    }
}

jib {
    from {
        image = "${JIB_FROM_IMAGE}"
        auth {
            username = "${jibFromUsername}"
            password = "${jibFromPassword}"
        }
        platforms {
            platform {
                architecture = "amd64"
                os = "linux"
            }
            platform {
                architecture = "arm64"
                os = "linux"
            }
        }
    }
    to {
        image = "${JIB_TO_IMAGE}"
        auth {
            username = "${jibToUsername}"
            password = "${jibToPassword}"
        }
    }
    container {
        mainClass = 'ceibs.price.PriceApplication'
        filesModificationTime = ZonedDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)
    }
    allowInsecureRegistries = false
}
