<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset=utf-8>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <meta http-equiv=cache-control content=no-store>
    <meta http-equiv=pragma content=no-cache>
    <link rel="icon" type="image/png" th:href="@{'/resources/logo.png'}">
    <link th:href="@{'/resources/css/index.css?'+${props.hash}}" rel="stylesheet">
    <title>智能定价</title>
    <script>
        window.Global = {
            contextPath: '[[${props.contextPath}]]',
            oauth2_uri: '[[${props.oauth2_uri}]]',
            oauth2_logout_uri: '[[${props.oauth2_logout_uri}]]',
            client_id: '[[${props.client_id}]]',
            version: '[[${props.version}]]',
            api_gateway_uri: '[[${props.api_gateway_uri}]]',
            consoleDomain:'',
            COPILOT_URL:'[[${props.copilot_url}]]',
            COPILOT_ASSEMBLE_URL:'[[${props.copilot_assemble_url}]]',
            COPILOT_BUS_URL:'[[${props.copilot_bus_url}]]',
            WSS_ORIGIN: '[[${props.wss_origin}]]',
            copilot_brainstorming_id: '[[${props.copilot_brainstorming_id}]]',
            preview_url: '[[${props.preview_url}]]',
            agent_id: '[[${props.agent_id}]]',

        }
        window.__toCtxUrl = (filename) => {
            return window.Global.contextPath + filename
        }
    </script>
</head>

<body>
    <div id=app style="height: 100vh;"></div>
    <script type="module" th:src="@{'/resources/js/index.js?'+${props.hash}}"></script>
</body>

</html>