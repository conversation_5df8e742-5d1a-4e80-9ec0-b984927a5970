apiVersion: "ketanyun.cn/v1alpha1"
kind: "QTable"
spec:
  designer:
    name: "TableDesigner"
    version: "1.0"
  tenant:
    name: "cloud.ketanyun.cn"
    multitenancy: false
  sheets: []
  permission:
    roles:
      - name: "Owner"
        text: "创建人"
        objects:
          - kind: "sheet"
            name: "ALL"
            operations: "rwx"
          - kind: "view"
            name: "ALL"
            operations: "rwx"
          - kind: "field"
            name: "ALL"
            operations: "rwx"
        subjects:
          - userFilter:
              filter: "${OWNER_OPEN_ID}"
              text: "${OWENR_NAME}"
      - name: "Admin"
        text: "可分级授权"
        objects:
          - kind: "sheet"
            name: "ALL"
            operations: "rwx"
          - kind: "view"
            name: "ALL"
            operations: "rwx"
          - kind: "field"
            name: "ALL"
            operations: "rwx"
      - name: "Editor"
        text: "可编辑"
        objects:
          - kind: "sheet"
            name: "ALL"
            operations: "rw"
          - kind: "view"
            name: "ALL"
            operations: "rw"
          - kind: "field"
            name: "ALL"
            operations: "rw"
      - name: "Viewer"
        text: "仅查看"
        objects:
          - kind: "sheet"
            name: "ALL"
            operations: "r"
          - kind: "view"
            name: "ALL"
            operations: "r"
          - kind: "field"
            name: "ALL"
            operations: "r"
      - name: "Everyone"
        text: "组织内所有人"
        objects:
          - kind: "sheet"
            name: "ALL"
            operations: ""
          - kind: "view"
            name: "ALL"
            operations: ""
          - kind: "field"
            name: "ALL"
            operations: ""
        subjects:
          - userFilter:
              filter: "::*"
              text: "组织内所有人"
