server:
  servlet:
    context-path: /hub-price
  port: 8080
  forward-headers-strategy: native
  tomcat:
    remote-ip-header: X-Forwarded-For
    protocol-header: X-Forwarded-Proto
    internal-proxies: .*
  origin: ${SERVICE_ORIGIN}

spring:
  thymeleaf:
    prefix: classpath:/views/themes/
    suffix: .html
    cache: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${PRICE_DB_URL}
    username: ${PRICE_DB_USERNAME}
    password: ${PRICE_DB_PASSWORD}
    hikari:
      maximum-pool-size: 80
      minimum-idle: 5
  jpa:
    properties:
      hibernate:
        show_sql: true
        format_sql: true
        dialect: org.hibernate.dialect.MySQLDialect
    hibernate:
      ddl-auto: update
      naming:
        physicalstrategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true

    database-platform: org.hibernate.dialect.MySQLDialect
  application:
    name: price-operator
#  ai:
#    mcp:
#      client:
#        enabled: false
#        name: price-mcp-client
#        version: 1.0.0
#        request-timeout: 300s
#        type: SYNC  # or ASYNC for reactive applications
#        streamable-http:
#          connections:
#            server1:
#              url: ${MCP_SERVER_URL:https://cloud.ketanyun.cn/}
#      server:
#        enabled: true
#        name: price-mcp-server
#        version: 1.0.0
#        type: SYNC

app:
  client: ${OAUTH_CLIENT_ID}
  secret: ${OAUTH_CLIENT_SECRET}
  scope: data,introspect

sso:
  introspect:
    url: ${OAUTH_INTROSPECT_URL}
  token:
    url: ${OAUTH_TOKEN_URL}

#console:
#  data:
#    selector: ${CONSOLE_DATA_SELECTOR_URL}
#  user:
#    selector: ${CONSOLE_USER_SELECTOR_URL}

logging:
  file:
    name: /logs/price.log
  level:
    root: INFO
    org.springframework.ai: INFO
    ceibs.price: INFO
    io.modelcontextprotocol: INFO
    org.springframework.ai.mcp: INFO


tomcat:
  ajp:
    port: 8009
    enabled: false
bus:
  url: /bus/graphql/builtin
tabler:
  url: /bus/graphql/apps_tabler
preview:
  url: ${PREVIEW_URL:https://cloud.ketanyun.cn/preview-api/v3/index?url=}
copilot:
  id: ${COPILOT_ID}
  url: ${COPILOT_URL}
  brainstorming: ${COPILOT_BRAINSTORMING}


base:
  view:
    url: /hub-price/stepview?sessionId=%s#viewid=%s
tezan:
  url: ${TEZAN_URL}