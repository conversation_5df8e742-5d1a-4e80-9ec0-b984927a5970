#字段类型String
input IdFilter {
    eq: String
    neq: String
    like: String
    ieq: String
    in: [String]
    nin: [String]
    gt: String
    lt: String
    gte: String
    lte: String
}

#字段类型String
input StringFilter {
    eq: String
    neq: String
    like: String
    ieq: String
    in: [String]
    nin: [String]
}

#字段类型Int
input IntegerFilter {
    eq: Int
    neq: Int
    in: [Int]
    nin: [Int]
    gt: Int
    lt: Int
    gte: Int
    lte: Int
}

input BooleanFilter{
    eq:Boolean
    in:[Boolean]
}

input TimestampFilter {
    eq: Timestamp
    neq: Timestamp
    in: [Timestamp]
    nin: [Timestamp]
    gt: Timestamp
    lt: Timestamp
    gte: Timestamp
    lte: Timestamp
}
