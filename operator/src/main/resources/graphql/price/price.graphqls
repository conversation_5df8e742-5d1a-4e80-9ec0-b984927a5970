extend type Query {
    tools: [Tool]
    priceSessionInfo(sessionId:String!): PriceSession
    priceSessionVIew(viewId:String!): PriceSessionView
    atypicaReportExecutor(viewId:String!,report:String):PriceSessionView
    cases:[Case]

    # 当前正在上的课
    currentClass: ClassInfo
    # 分组情况
    groupUsers(classId:String!): [GroupUser]
    # 学员上课情况
    studentClassInfo(classId:String,openid:String!,startTime:Timestamp,endTime:Timestamp):[PriceSession]
    # 我的课程计划
    myClassPlan(startTime:Timestamp,endTime:Timestamp):[ClassInfo]

}
extend type Mutation {
    executeTool(toolName:String!,sessionId:String,args:Map,autoTask:Boolean): ToolResponse
    updatePriceSessionView(entity:PriceSessionViewInput!): PriceSessionView
    submitPriceSessionView(viewId:String!): PriceSessionView
    generateToCPrompt(entity:PriceSessionViewInput!):Prompt
    closeAutoTask(sessionId:String!): PriceSession
    generate(type:String!,data:String):Generate
    addCase(entity:CaseInput!): Case
    deleteCase(id:String!):Case
    updateCase(entity:CaseInput!): Case
    deleteCaseFile(id:String!): CaseFile
    updateCaseFile(entity:CaseFileInput!): CaseFile
    #排课
    addClass(entity:ClassInfoInput): ClassInfo
    updateClass(entity:ClassInfoInput): ClassInfo

}

type Generate{
    text:String
}
type Case{
    id: String
    title:String
    image:String
    description:String
    active:Boolean
    type:String
    text:String
    agentId:String
    createdAt:Timestamp
    updatedAt:Timestamp
    creator:String
    creatorName:String
    caseFiles:[CaseFile]
}

type CaseFile{
    id: String
    active:Boolean
    name:String
    url:String
    knowledgeName:String
    knowledgeStatus: String
    createdAt:Timestamp
    updatedAt:Timestamp
    creator:String
    creatorName:String
}
type Prompt{
    str: String
}
type Tool {
    name: String
    description: String
    #args: [ToolArg]
}
type ToolResponse {
    isError: Boolean
    datas: [Map]
    url: String
    errorMessage: String
}


type GroupUser{
    id: String
    createdAt: Timestamp
    updatedAt: Timestamp
    users: [User]
    groupIndex:Int
}
type User{
    openId: String
    name: String
}
type PriceSession {
    id: String
    sessionId: String
    autoTask: Boolean
    currentStep: String
    createdAt:Timestamp
    # 会话关联课程id，可以为空
    classId:String
    views:[PriceSessionView]
}
type ClassInfo{
    id: String
    # 开始时间
    startTime: Timestamp
    # 结束时间
    endTime: Timestamp
    # 作业提交截止时间
    submitDeadline: Timestamp
    # 任课老师
    teacher: String
    # 任课老师
    teacherName: String
    # 课程名称
    courseName: String
    # 课程下人员
    groupUsers: [GroupUser]

}

type PriceSessionView {
    id: String
    viewName: String
    viewDesc: String
    viewUrl :  String
    createdAt: Timestamp
    index: Int
    step: String
    data: String
    submit: Boolean
    # select:Boolean
}

input UserInput{
    openId: String
    name: String
}
input PriceSessionViewInput{
    id: String!
    viewName: String
    viewDesc: String
    viewUrl :  String
    createdAt: Timestamp
    index: Int
    step: String
    data: String
}

input CaseFileInput{
    id: String
    active:Boolean
    name:String
    url:String
    knowledgeName:String
    knowledgeStatus: String

}
input CaseInput{
    id: String
    title:String
    image:String
    description:String
    active:Boolean
    type:String
    text:String
    agentId:String
    caseFiles:[CaseFileInput]
}



input ClassInfoInput{
    id: String
    # 开始时间
    startTime: Timestamp
    # 结束时间
    endTime: Timestamp
    # 作业提交截止时间
    submitDeadline: Timestamp
    # 任课老师
    teacher: String
    # 任课老师
    teacherName: String
    # 课程名称
    courseName: String
    # 课程下人员
    groupUsers: [GroupUserInput]

}

input GroupUserInput{
    id: String
    createdAt: Timestamp
    updatedAt: Timestamp
    users: [UserInput]
    groupIndex:Int
}