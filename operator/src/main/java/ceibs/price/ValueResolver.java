package ceibs.price;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class ValueResolver {

    @Autowired
    private Environment environment;


    public String getConsoleDomain(){
        return resolveProperty("CONSOLE_DOMAIN");
    }

    public String getConsoleUserSelector(){
        return resolveProperty("CONSOLE_USER_SELECTOR_URL");
    }
    public String getConsoleDataSelector(){
        return resolveProperty("CONSOLE_DATA_SELECTOR_URL");
    }

    public String getServiceOrigin() {
        return resolveProperty("SERVICE_ORIGIN");
    }

    public String getOAuthAuthorityURL() {
        return resolveProperty("OAUTH_AUTHORITY_URL");
    }

    public String getOAuthAuthorizeURL() {
        return resolveProperty("OAUTH_AUTHORIZE_URL");
    }

    public String getOAuthTokenURL() {
        return resolveProperty("OAUTH_TOKEN_URL");
    }

    public String getOAuthIntrospectURL() {
        return resolveProperty("OAUTH_INTROSPECT_URL");
    }

    public String getOAuthClientId() {
        return resolveProperty("OAUTH_CLIENT_ID");
    }

    public String getOAuthClientSecret() {
        return resolveProperty("OAUTH_CLIENT_SECRET");
    }

    public String getWebSocketServerURI() {
        return resolveProperty("WEBSOCKET_SERVER_URI");
    }

    public String getSsoURL() {
        return resolveProperty("SSO_URL");
    }

    public String getCopilotURL() {
        return resolveProperty("COPILOT_URL");
    }

    public String getSsoLogoutURL() {
        return resolveProperty("SSO_LOGOUT_URL");
    }

    public String getBusURL() {
        return resolveProperty("BUS_URL");
    }


    public String getScope() {
        return "data introspect";
    }

    public String getCopilotBrainstormingId() {
        return resolveProperty("COPILOT_BRAINSTORMING");
    }
    public String getCopilotId() {
        return resolveProperty("COPILOT_ID");
    }
    public String getPreviewUrl() {
        return resolveProperty("PREVIEW_URL");
    }

    // e.g. /bus/graphql
    public String getBusPrefixURL() {
        String busURL = resolveProperty("BUS_URL");
        if (StringUtils.isNotBlank(busURL)) {
            if (busURL.endsWith("/"))
                busURL = busURL.substring(0, busURL.length() - 1);
            busURL = busURL.substring(0, busURL.lastIndexOf("/"));
        }
        return busURL;
    }

    public String getMcpServerUrl() {
        return resolveProperty("MCP_SERVER_URL");
    }

    public int getMcpConnectTimeout() {
        String timeout = resolveProperty("MCP_CONNECT_TIMEOUT");
        return StringUtils.isNotBlank(timeout) ? Integer.parseInt(timeout) : 5000;
    }

    public int getMcpReadTimeout() {
        String timeout = resolveProperty("MCP_READ_TIMEOUT");
        return StringUtils.isNotBlank(timeout) ? Integer.parseInt(timeout) : 10000;
    }

    public int getMcpConnectionRequestTimeout() {
        String timeout = resolveProperty("MCP_CONNECTION_REQUEST_TIMEOUT");
        return StringUtils.isNotBlank(timeout) ? Integer.parseInt(timeout) : 3000;
    }

    protected String resolveProperty(String key) {
        String value = environment.getProperty(key);
        if (StringUtils.isBlank(value))
            value = System.getProperty(key);
        return value;
    }
}
