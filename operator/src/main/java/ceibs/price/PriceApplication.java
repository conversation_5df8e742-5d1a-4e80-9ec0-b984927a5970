package ceibs.price;

import org.apache.catalina.connector.Connector;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 价格操作应用程序主入口类
 * 使用Spring Boot 3.x版本
 * 支持MCP客户端功能
 */
@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {"ceibs.price.*"})
public class PriceApplication {

    @Value("${tomcat.ajp.port:8009}")
    int ajpPort;

    @Value("${tomcat.ajp.enabled:false}")
    boolean tomcatAjpEnabled;

    /**
     * 配置Tomcat服务器
     * 如果启用AJP，则添加AJP连接器
     */
    @Bean
    public ServletWebServerFactory servletContainer() {
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory();
        if (tomcatAjpEnabled) {
            Connector ajpConnector = new Connector("AJP/1.3");
            ajpConnector.setPort(ajpPort);
            ajpConnector.setSecure(false);
            ajpConnector.setAllowTrace(false);
            ajpConnector.setScheme("http");
            tomcat.addAdditionalTomcatConnectors(ajpConnector);
        }
        return tomcat;
    }

    /**
     * 应用程序主入口方法
     */
    public static void main(String[] args) {
        SpringApplication.run(PriceApplication.class, args);
    }
}