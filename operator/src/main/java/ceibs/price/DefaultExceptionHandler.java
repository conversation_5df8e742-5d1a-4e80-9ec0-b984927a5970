package ceibs.price;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.bind.annotation.ControllerAdvice;


@ControllerAdvice
public class DefaultExceptionHandler {

  /*  @ExceptionHandler(value = Exception.class)
    public ModelAndView defaultErrorHandler(HttpServletResponse response, Exception ex) {
        ModelAndView jsonView = new ModelAndView();
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);

        jsonView.setView(new AbstractView() {
            @Override
            protected void renderMergedOutputModel(
                    Map<String, Object> model, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
                httpServletResponse.getWriter().write(new ObjectMapper().writeValueAsString(new HashMap() {{
                    put("ok", false);
                }}));
            }
        });
        return jsonView;
    }*/
}
