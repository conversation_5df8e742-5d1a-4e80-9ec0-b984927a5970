package ceibs.price.dao;

import ceibs.price.bean.bo.CaseFile;
import ceibs.price.bean.bo.ClassInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface ClassInfoDao extends JpaRepository<ClassInfo, String> {


    @Query(value ="select * from class_info where teacher = ?1 and now() between startTime and endTime",nativeQuery = true)
    ClassInfo findCurrentClassByTeacher(String openid);

    @Query(value ="select * from class_info where  now() between startTime and endTime",nativeQuery = true)
    List<ClassInfo> findCurrentClass();


    @Query(value = "SELECT * FROM class_info WHERE (?1 < endTime AND ?2 > startTime) and teacher=?3", nativeQuery = true)
    List<ClassInfo> findOverlappingClasses(Date newStartTime, Date newEndTime,String teacher);


    @Query(value = "SELECT * FROM class_info WHERE (?1 < startTime AND ?2 > startTime) and teacher=?3", nativeQuery = true)
    List<ClassInfo> findOverlappingClassesStart(Date newStartTime, Date newEndTime,String teacher);


}
