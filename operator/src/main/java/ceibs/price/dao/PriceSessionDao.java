package ceibs.price.dao;


import ceibs.price.bean.bo.PriceSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;



/**
 * 持久层
 *
 * <AUTHOR>
 * @since 2024-09-20 15:42:25
 */
@Repository
public interface PriceSessionDao extends JpaRepository<PriceSession, String> {



    PriceSession findBySessionId(String sessionId);

    List<PriceSession> findByCreatorAndCreatedAtIsBetween(String creator, Date startTime, Date endTime);

    List<PriceSession> findByCreatorAndClassId(String creator, String classId);

}
