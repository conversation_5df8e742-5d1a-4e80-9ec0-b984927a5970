package ceibs.price.dao;


import ceibs.price.bean.bo.PriceSession;
import ceibs.price.bean.bo.PriceSessionToolLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


/**
 * 持久层
 *
 * <AUTHOR>
 * @since 2024-09-20 15:42:25
 */
@Repository
public interface PriceSessionToolLogDao extends JpaRepository<PriceSessionToolLog, String> {


    //根据sessionid 统计某toolName工具使用次数
     Integer countBySessionIdAndToolName(String sessionId, String toolName);



}

