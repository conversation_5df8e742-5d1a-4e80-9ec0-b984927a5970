package ceibs.price.dao;


import ceibs.price.bean.bo.PriceSessionView;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


/**
 * 持久层
 *
 * <AUTHOR>
 * @since 2024-09-20 15:42:25
 */
@Repository
public interface PriceSessionViewDao extends JpaRepository<PriceSessionView, String> {


    List<PriceSessionView> findBySessionIdAndDelMarkIsFalse(String sessionId);
    // 不返回 data 字段, 避免返回大对象
    @Query(value = "SELECT id,'' as data,step, sessionId, viewName, viewDesc, viewUrl, createdAt, view_index, submit, delMark " +
            " FROM price_session_view WHERE sessionId = ?1 AND delMark = FALSE", nativeQuery = true)
    List<PriceSessionView> findBySessionIdAndDelMarkIsFalseExcludeData(String sessionId);

    Optional<PriceSessionView> findById(String id);

    List<PriceSessionView> findBySessionIdAndViewNameAndDelMarkIsFalse(String sessionId, String viewName);

    PriceSessionView findFirstBySessionIdAndViewNameLikeAndDelMarkIsFalseOrderByCreatedAtDesc(String id,String viewName);

}

