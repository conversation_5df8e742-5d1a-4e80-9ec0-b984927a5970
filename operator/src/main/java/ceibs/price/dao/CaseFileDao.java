package ceibs.price.dao;

import ceibs.price.bean.bo.CaseFile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CaseFileDao extends JpaRepository<CaseFile, String> {

    List<CaseFile> findAllByActiveIsTrueAndCaseId( String caseId);
    Integer deleteCaseFileByCaseId(String caseId);

}
