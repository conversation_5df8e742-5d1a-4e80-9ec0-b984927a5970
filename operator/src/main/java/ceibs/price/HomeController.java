package ceibs.price;


import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;


import java.util.HashMap;
import java.util.Map;

@Controller
public class HomeController {

    private static final String RESOURCES_HASH = RandomStringUtils.randomAlphabetic(8);

    @Autowired
    ValueResolver valueResolver;

    @RequestMapping(value = {
            "/", "",
            "/{regex:(?!resources).*}/**"},
            headers = "Upgrade!=websocket")
    public String home(HttpServletRequest request) {
        Map props = new HashMap();
        props.put("contextPath", request.getContextPath());
        props.put("oauth2_uri", getUrl(request, valueResolver.getOAuthAuthorityURL()));
        props.put("oauth2_logout_uri", getUrl(request, valueResolver.getSsoLogoutURL()));
        props.put("client_id", valueResolver.getOAuthClientId());
        props.put("api_gateway_uri", getUrl(request, valueResolver.getBusPrefixURL()));
        props.put("version", StatusController.getClassVersion());
        props.put("copilot_url", "/copilot-api/internal-graphql");
        props.put("copilot_assemble_url", "/bus/graphql/copilot_assemble");
        props.put("copilot_bus_url", "/bus/graphql/copilot");
        props.put("copilot_brainstorming_id", valueResolver.getCopilotBrainstormingId());
        String wss = valueResolver.getServiceOrigin().replace("http", "ws");
        props.put("wss_origin", wss);
        props.put("hash", RESOURCES_HASH);
        props.put("preview_url", valueResolver.getPreviewUrl());
        System.out.println("========================");
        System.out.println("preview_url:"+valueResolver.getPreviewUrl());
        props.put("agent_id", valueResolver.getCopilotId());

        //props.put("consoleDomain", valueResolver.getConsoleDomain());
        //props.put("console_data_selector_url", getUrl(request, valueResolver.getConsoleDataSelector()));
        //props.put("console_user_selector_url", getUrl(request, valueResolver.getConsoleUserSelector()));

        request.setAttribute("props", props);
        return "index";
    }

    public String getUrl(String baseUri, String url) {
        if (url.startsWith("http"))
            return url;
        else if (url.startsWith("/"))
            return baseUri + url;
        return url;
    }

    public String getUrl(HttpServletRequest request, String url) {
        UriComponents uriComponents = UriComponentsBuilder.fromUriString(request.getRequestURL().toString()).build();
        String baseUri = String.format("%s://%s", uriComponents.toUri().getScheme(), uriComponents.toUri().getAuthority());
        return getUrl(baseUri, url);
    }



}
