package ceibs.price.bean;

import lombok.Data;


/**
 * <FileName> Token
 * <Desc> token实体
 *
 * <AUTHOR>
 */
@Data
public class Token {
    /**
     * token
     */
    private String accessToken;
    /**
     * 过期时间
     */
    private Long expiresIn;


    private Long ago;


    public Token(String accessToken, Long expiresIn, Long ago) {
        this.accessToken = accessToken;
        this.expiresIn = expiresIn;

        this.ago = ago;
    }

    public Token() {
    }

    public boolean isExpire() {
        Long now = System.currentTimeMillis();
        return now >= (ago + expiresIn * 1000 - 120000);
    }
}

