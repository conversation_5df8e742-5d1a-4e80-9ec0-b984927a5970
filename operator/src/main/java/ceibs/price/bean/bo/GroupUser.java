package ceibs.price.bean.bo;


import ceibs.price.bean.User;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 分组情况
 */
@Entity
@Data
@Table(name = "group_user")
public class GroupUser implements Serializable {
    @Id
    private String id;

    // 组下人员，json 格式
    private String usersJson;
    private Date createdAt;
    private Date updatedAt;
    private String creator;
    private String creatorName;

    private Integer groupIndex;
    // 所属排课
    private String classId;

    @Transient
    private List<User> users;
}
