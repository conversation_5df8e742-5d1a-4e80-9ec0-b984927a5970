package ceibs.price.bean.bo;


import ceibs.price.bean.User;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 课程安排表
 */
@Entity
@Data
@Table(name = "class_info")
public class ClassInfo implements Serializable {
    @Id
    private String id;

    // 课程名称
    private String courseName;
    private Date startTime;
    private Date endTime;
    private Date createdAt;
    private Date updatedAt;
    private String creator;
    private String creatorName;
    private String teacher;
    private String teacherName;

    @Transient
    private List<GroupUser> groupUsers;

}
