package ceibs.price.bean.bo;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Entity
@Data
@Table(name = "price_session")
public class PriceSession  implements Serializable {
    @Id
    private String id;

    private String sessionId;
    // 当前所在阶段 1-1 格式
    private String currentStep;
    private Date createdAt;
    // 当前会话是否处于自动任务状态中
    private Boolean autoTask;
    // 会话创建人
    private String creator;
    // 创建人姓名
    private String creatorName;
    // 关联课程
    private String classId;
    //  是否提交
    private Boolean submit;
    // 提交时间
    private Date submitTime;

    @Transient
    private List<PriceSessionView> views;



}