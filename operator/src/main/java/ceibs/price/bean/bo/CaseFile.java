package ceibs.price.bean.bo;


import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Entity
@Data
@Table(name = "case_file")
public class CaseFile implements Serializable {
    @Id
    private String id;

    private String caseId;
    private Boolean active;
    private String name;
    private String url;
    //private String text;
    private Date createdAt;
    private Date updatedAt;
    private String creator;
    private String creatorName;
    private Integer fileIndex;

    // 知识库id
    private String knowledgeName;
    // 知识库状态【上传完成后，只有在知识库状态可用的情况下，才能启用作为案例】
    private String knowledgeStatus;

}
