package ceibs.price.bean.bo;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Entity
@Data
@Table(name = "price_session_tool_log")
public class PriceSessionToolLog implements Serializable {
    @Id
    private String id;
    private String sessionId;
    // 调用工具名称
    private String toolName;
    private Date createdAt;

    // 工具返回的数据
    private String data;
    


}