package ceibs.price.bean.bo;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: RCC
 * @CreateTime:2025-08-28 12:02
 * @Description TODO
 */
@Entity
@Data
@Table(name = "cases")
public class Case {

    @Id
    private String id;

    private String title;
    private String image;
    private String description;
    private Boolean active;
    private String text;
    private String type;
    private String pdfUrl;
    private Date createdAt;
    private Date updatedAt;
    private String creator;
    private String creatorName;

    // 智能体id
    private String agentId;

    @Transient
    private List<CaseFile> caseFiles;

}
