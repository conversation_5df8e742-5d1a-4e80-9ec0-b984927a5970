package ceibs.price.bean.bo;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



@Entity
@Data
@Table(name = "price_session_view")
public class PriceSessionView implements Serializable {
    @Id
    private String id;

    private String sessionId;

    private String viewName;
    private String viewDesc;
    private String viewUrl;
    private Date createdAt;
    // 排序
    @Column(name = "view_index")
    private Integer index;
    //  是否提交
    private Boolean submit;
    //  是否删除
    private Boolean delMark;
    //
    private String data;
    private String step;

}