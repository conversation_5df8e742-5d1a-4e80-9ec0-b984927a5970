package ceibs.price.bean.graphql;

import ceibs.price.bean.bo.PriceSession;
import ceibs.price.bean.bo.PriceSessionView;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Data
@AllArgsConstructor
public class ToolResponse {

    private Boolean isError;
    private List<Map<String,Object>> datas;
    private String url;
    private String errorMessage;
    //private PriceSessionView  view;



}
