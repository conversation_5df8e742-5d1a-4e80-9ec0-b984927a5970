package ceibs.price.bean.yaml.tabler;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
public class View {
    private String name;
    private String kind; // enum: grid, card
    private String text;
    private List<Column> columns;
    private Grouping grouping;
    private Filtering filtering;
    private Ordering ordering;


    public View(String kind){
        this.name= UUID.randomUUID().toString();
        this.kind=kind;
        this.text="";
        List<Column> columns=new ArrayList<>();
        this.columns=columns;

        Grouping grouping=new Grouping();
        this.grouping=grouping;

        Filtering filtering=new Filtering();
        this.filtering=filtering;

        Ordering ordering=new Ordering();
        this.ordering=ordering;
    }
}
