package ceibs.price.bean.yaml.sitemap;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class Menu {
    private List<MenuItem> items;


    Menu(JSONArray sheets) {
        List<MenuItem> items = Lists.newArrayList();
        sheets.forEach(e -> {
            // 一个sheet 会拆成多个菜单。每个菜单对应权限[r] [w]
            JSONObject sheet = (JSONObject) e;

            MenuItem menuItemRead = new MenuItem(sheet,"r");
            MenuItem menuItemWrite = new MenuItem(sheet,"w");
            items.add(menuItemRead);
            items.add(menuItemWrite);
            // view
            JSONArray views = sheet.getJSONArray("views");


        });
        this.items = items;


    }
}
