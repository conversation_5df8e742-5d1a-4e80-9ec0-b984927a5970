package ceibs.price.bean.yaml.sitemap;

import ceibs.price.bean.yaml.Metadata;
import ceibs.price.bean.yaml.YamlBase;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;

@Data
@EqualsAndHashCode(callSuper = true)
public class QSitemap extends YamlBase {


    private Spec spec;

    public QSitemap(JSONObject tabler, String partId, String tenantCode) {
        this.apiVersion = tabler.getString("apiVersion");
        this.kind = "QSitemap";
        // 具体表格 在 sitemap里 唯一标识
        Metadata metadata = new Metadata();
        metadata.setName(partId);
        HashMap<Object, Object> lableMap = new HashMap<>();
        lableMap.put("service", "maker");
        metadata.setLabels(lableMap);

        this.metadata = metadata;
        this.spec = new Spec(tabler, partId, tenantCode);


    }
}























