package ceibs.price.bean.yaml.sitemap;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class Spec {
    private Tenant tenant;
    private App app;
    private Menu menu;
    private List<Role> roles;
    // private List<Feature> features;


    Spec(JSONObject tabler, String partId, String tenantCode) {
        Tenant tenant = new Tenant();
        tenant.setName(tenantCode);
        tenant.setMultitenancy(false);
        this.tenant = tenant;


        App app = new App();
        app.setName("tabler-qapp-tabler");
        app.setAutoActivate(false);
        app.setAutoUpgrade(false);
        // app.setTags("tabler");
        app.setFlushPrivilege("res");
        app.setPrimary(false);
        this.app = app;
        JSONObject spec = tabler.getJSONObject("spec");
        JSONArray sheets = spec.getJSONArray("sheets");
        Menu menu = new Menu(sheets);
        this.menu = menu;
        if (spec.containsKey("permission")&&spec.getJSONObject("permission").containsKey("roles")) {
            JSONArray rolesArray = spec.getJSONObject("permission").getJSONArray("roles");
            List<Role> roles = new ArrayList<>();
            rolesArray.forEach(e -> {
                JSONObject roleJson = (JSONObject) e;
                Role role = new Role(this.menu.getItems(), roleJson, partId, tenantCode);
                roles.add(role);
            });
            this.roles = roles;
        }

    }
}