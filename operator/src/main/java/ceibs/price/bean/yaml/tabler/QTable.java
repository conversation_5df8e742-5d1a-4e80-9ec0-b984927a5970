package ceibs.price.bean.yaml.tabler;

import com.alibaba.fastjson.JSONObject;

import ceibs.price.bean.yaml.Metadata;
import ceibs.price.bean.yaml.YamlBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class QTable extends YamlBase {

    private Spec spec;

    public QTable(JSONObject tabler, String partId, String tenantCode,String opt){
        // 列头
        List<String> title = new ArrayList<>();

        this.kind = "QTabler";
        // 具体表格 在 sitemap里 唯一标识
        Metadata metadata = new Metadata();
        metadata.setName(partId);
        HashMap<Object, Object> lableMap = new HashMap<>();
        lableMap.put("service","tabler");
        metadata.setLabels(lableMap);

        this.metadata = metadata;
        this.spec = new Spec(tenantCode,opt);

    }
}
