package ceibs.price.bean.yaml.sitemap;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

/**
 * @Author: RCC
 * @CreateTime:2025-04-21 10:59
 * @Description TODO
 */
@Data
public class RoleObject {
    private String name;
    private String text;
    //private Boolean limitDeptAccess;
    private List<String> limitDataFilters;

    public RoleObject(){}
    public RoleObject(JSONObject roleObject){

        this.name = roleObject.getString("name");
        //this.text = roleObject.getString("text");
        //this.limitDeptAccess = roleObject.getBoolean("limitDeptAccess");
        //this.limitDataFilters = roleObject.getJSONArray("limitDataFilters").toJavaList(String.class);

    }
}
