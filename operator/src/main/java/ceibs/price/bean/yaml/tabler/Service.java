package ceibs.price.bean.yaml.tabler;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Data
@NoArgsConstructor
public class Service {
    private String name;
    private String dataType;
    private List<Field> fields;
    private List<DataPermission> dataPermissions;

    public Service(String dataType,List<Field> fields){
        this.name="hub-tabler-default";
        this.dataType=dataType;
        this.fields=fields;
        List<DataPermission> dataPermissions =new ArrayList<>();
        DataPermission dataPermission =new DataPermission();
        dataPermission.setOperation("rwx");
        dataPermissions.add(dataPermission);
        this.dataPermissions = dataPermissions;
    }
}
