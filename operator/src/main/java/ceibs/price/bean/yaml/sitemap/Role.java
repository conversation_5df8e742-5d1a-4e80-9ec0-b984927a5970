package ceibs.price.bean.yaml.sitemap;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: RCC
 * @CreateTime:2025-04-21 10:59
 * @Description TODO
 */
@Data
public class Role {
    private String name;
    private String text;
    private String description;
    private String ring;
    private List<Subject> subjects;
    private List<RoleObject> objects;


    Role(List<MenuItem> menuItems, JSONObject role, String partId, String tenantCode) {
        // role name 在整个应用中必须唯一。 所以 role name= partName+tenantCode+roleName
        String roleName = role.getString("name");
        this.name = roleName.concat("-" + partId).concat("-" + tenantCode);
        this.text = role.getString("text");
        this.description = role.getString("text");
        this.ring = "user";
        if (roleName.equals("Owner") || roleName.equals("Admin") || roleName.equals("Editor") || roleName.equals("Viewer") || roleName.equals("Everyone")) {
            this.ring = "system";
        }
        List<Subject> tablerSubjects = new ArrayList<>();
        if (role.containsKey("subjects")) {
            for (Object o : role.getJSONArray("subjects")) {
                JSONObject subjectObject = (JSONObject) o;
                Subject subject = new Subject();
                subject.setUserFilter(subjectObject.getJSONObject("userFilter").getString("filter"));
                tablerSubjects.add(subject);
            }
            this.subjects = tablerSubjects;
        }


        List<RoleObject> objects = new ArrayList<>();
        JSONArray objectsArray = role.getJSONArray("objects");
        // sheets to map
        objectsArray.forEach(e -> {
            JSONObject objectJson = (JSONObject) e;
            // 分析tabler 角色对应的 sheet、view、field 信息。 根据信息转换成 角色 和 菜单(sheet)的关系
            String kind = objectJson.getString("kind");
            if ("sheet".equals(kind)) {
                String name = objectJson.getString("name");
                //todo 是否有行限定
                String filter = objectJson.getString("filter");

                // 对应的权限 rwx
                String operations = objectJson.getString("operations");
                if ("ALL".equalsIgnoreCase(name)) {
                    for (MenuItem menuItem : menuItems) {
                        // 如果是只读权限且菜单名以 "-r" 结尾，或者有写/授权权限，则加入
                        if ((operations.equalsIgnoreCase("r") && menuItem.getName().endsWith("-r"))
                                || (operations.contains("w") || operations.contains("x"))) {
                            RoleObject roleObject = new RoleObject();
                            roleObject.setName(menuItem.getName());
                            roleObject.setText(menuItem.getText());
                            objects.add(roleObject);
                        }
                    }
                } else {
                    // 具体菜单
                    if (operations.equalsIgnoreCase("r")) {
                        for (MenuItem menuItem : menuItems) {
                            if (menuItem.getName().endsWith("-r") && menuItem.getName().startsWith(name)) {
                                RoleObject roleObject = new RoleObject();
                                roleObject.setName(menuItem.getName());
                                roleObject.setText(menuItem.getText());
                                objects.add(roleObject);
                            }
                        }
                    }
                    if (operations.contains("w") || operations.contains("x")) {
                        for (MenuItem menuItem : menuItems) {
                            if (menuItem.getName().startsWith(name)) {
                                RoleObject roleObject = new RoleObject();
                                roleObject.setName(menuItem.getName());
                                roleObject.setText(menuItem.getText());
                                objects.add(roleObject);
                            }
                        }
                    }

                }


            }
            if ("view".equals(kind)) {

            }
        });

        this.objects = objects;


    }
}
