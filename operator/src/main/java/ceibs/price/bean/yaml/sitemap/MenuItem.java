package ceibs.price.bean.yaml.sitemap;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.*;

@Data
public class MenuItem {
    private String name;
    private String text;
    private String description;
    private String code;
    private String icon;
    private String parent;
    private Boolean visible;
    // private Entry entry;
    // private List<Attribute> attributes;
    // private Action action;
    private List<Access> accesses;
    // private List<Feature> features;


    public MenuItem(JSONObject sheet, String opt) {
        opt = opt.toLowerCase();
        String optDesc = "写";
        String optName = "mutation";

        if (opt.equalsIgnoreCase("r")) {
            optDesc = "读";
            optName = "query";
        }
        this.name = sheet.getString("name").concat("-").concat(opt);
        this.text = sheet.getString("text").concat("-".concat(optDesc));
        this.description = sheet.getString("text");
        this.code = "";
        this.icon = "";
        this.parent = "";
        this.visible = true;
        // menuItemRead.setEntry(new Entry());
        // menuItemRead.setAttributes(Lists.newArrayList());
        // menuItemRead.setAction(new Action());
        // menuItemRead.setFeatures(Lists.newArrayList());
        List<Access> accesses = Lists.newArrayList();
        // todo data 下 service 是数组。关联代码表要授权
        JSONObject sheetDataService = sheet.getJSONObject("data").getJSONObject("service");
        String typeFilter = sheetDataService.getString("name") + "/" + sheetDataService.getString("dataType");

        Access access = new Access();
        access.setName(UUID.randomUUID().toString());
        access.setTypeFilter(typeFilter);
        access.setText("");
        List<Operation> operations = Lists.newArrayList();
        Operation operation = new Operation();
        operation.setKind(optName);
        operation.setName("[" + opt + "]");
        operation.setFilters(null);
        operation.setDataFilters(null);
        operation.setAttributes(new OperationAttributes());
        operations.add(operation);
        access.setOperations(operations);
        accesses.add(access);

        // 判断字段是否有引用,对印用类型授权读取
        if (opt.equalsIgnoreCase("r")) {
            JSONArray fields = sheetDataService.getJSONArray("fields");
            for (int i = 0; i < fields.size(); i++) {
                JSONObject field = fields.getJSONObject(i);
                if (field.containsKey("description")) {
                    String description = field.getString("description");
                    if (description == null) continue;
                    String[] split = description.split("\n");
                    for (String s : split) {
                        if (s.startsWith("@reference")) {
                            Map<String, String> referenceMap = parseReference(s);
                            if (referenceMap.isEmpty()) continue;

                            Access accessReference = new Access();
                            accessReference.setName(UUID.randomUUID().toString());
                            accessReference.setTypeFilter(referenceMap.get("value"));
                            accessReference.setText("");

                            List<Operation> operationsReference = new ArrayList<>();
                            Operation operationReference = new Operation();
                            operationReference.setKind(optName);
                            operationReference.setName("[" + opt + "]");
                            operationReference.setFilters(null);
                            operationReference.setDataFilters(null);
                            operationReference.setAttributes(new OperationAttributes());
                            operationsReference.add(operationReference);

                            accessReference.setOperations(operationsReference);
                            accesses.add(accessReference);
                        }
                    }
                }
            }
        }
        this.accesses = accesses;
    }
    private static Map<String, String> parseReference(String input) {
        Map<String, String> result = new HashMap<>();
        try {
            // 提取括号内的内容
            int start = input.indexOf('(');
            int end = input.indexOf(')');
            if (start == -1 || end == -1 || end <= start) return result;

            String content = input.substring(start + 1, end);
            String[] pairs = content.split(",");

            for (String pair : pairs) {
                String[] kv = pair.trim().split("=", 2); // 最多分割成两部分
                if (kv.length == 2) {
                    result.put(kv[0].trim(), kv[1].trim());
                }
            }
        } catch (Exception e) {
            // 忽略解析失败的情况，保持程序健壮性
            return result;
        }
        return result;
    }
}