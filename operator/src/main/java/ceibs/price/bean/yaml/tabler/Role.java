package ceibs.price.bean.yaml.tabler;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class Role {
    private String name; // 角色编码（预设角色：Owner/Admin/Editor/Viewer/Everyone）
    private String text; // 角色名称
    private List<Subject> subjects; // 角色人员构成
    private List<PermissionObject> objects; // 权限对象

    public Role(String name, String text, List<Subject> subjects, List<PermissionObject> objects) {
        this.name = name;
        this.text = text;
        this.subjects = subjects;
        this.objects = objects;
    }

    // Getter and Setter
}
