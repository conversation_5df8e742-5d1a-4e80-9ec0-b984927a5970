package ceibs.price.bean.yaml.tabler;

import com.alibaba.fastjson.JSONArray;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


@lombok.Data
@NoArgsConstructor
public class Sheet {
    private String name;
    private String text;
    private Data data;
    private List<View> views;


    public static Sheet buildCompetitorSheet(String viewId) {
        Sheet sheet = new Sheet();
        sheet.name = UUID.randomUUID().toString();
        sheet.text = "竞争对手信息";

        // 构建data部分
        Data data = new Data();
        Service service = new Service();
        service.setName("hub-price");
        service.setDataType("price_competitor");

        List<Field> fields = new ArrayList<>();
        fields.add(new Field("id", "id", "主键ID", "ID"));
        fields.add(new Field("market_share", "market_share", "市场占有率描述", "String"));
        fields.add(new Field("name", "name", "竞争对手名称", "String"));
        fields.add(new Field("pricing_strategy", "pricing_strategy", "定价策略描述", "String"));
        fields.add(new Field("product_features", "product_features", "产品特色描述", "String"));
        fields.add(new Field("round", "round", "轮次", "Int"));
        fields.add(new Field("session_id", "session_id", "会话ID，用于关联同一批数据", "String"));

        service.setFields(fields);
        data.setService(service);
        sheet.setData(data);

        // 构建views部分
        List<View> views = new ArrayList<>();
        View view = new View();
        view.setName(viewId);
        view.setKind("grid");

        List<Column> columns = new ArrayList<>();

        Column column1 = new Column();
        column1.setName(UUID.randomUUID().toString());
        column1.setText("主键ID");
        Field f1 = new Field();
        f1.setName("id");
        column1.setField(f1);
        Input i1 = new Input();
        i1.setName(UUID.randomUUID().toString());
        i1.setKind("Text");
        i1.setVisible(false);
        column1.setInput(i1);

        Column column2 = new Column();
        column2.setName(UUID.randomUUID().toString());
        column2.setText("市场占有率描述");
        Field f2 = new Field();
        f2.setName("market_share");
        column2.setField(f2);
        Input i2 = new Input();
        i2.setName(UUID.randomUUID().toString());
        i2.setKind("Text");
        i2.setVisible(true);
        column2.setInput(i2);

        Column column3 = new Column();
        column3.setName(UUID.randomUUID().toString());
        column3.setText("竞争对手名称");
        Field f3 = new Field();
        f3.setName("name");
        column3.setField(f3);
        Input i3 = new Input();
        i3.setName(UUID.randomUUID().toString());
        i3.setKind("Text");
        i3.setVisible(true);
        column3.setInput(i3);

        Column column4 = new Column();
        column4.setName(UUID.randomUUID().toString());
        column4.setText("定价策略描述");
        Field f4 = new Field();
        f4.setName("pricing_strategy");
        column4.setField(f4);
        Input i4 = new Input();
        i4.setName(UUID.randomUUID().toString());
        i4.setKind("Text");
        i4.setVisible(true);
        column4.setInput(i4);

        Column column5 = new Column();
        column5.setName(UUID.randomUUID().toString());
        column5.setText("产品特色描述");
        Field f5 = new Field();
        f5.setName("product_features");
        column5.setField(f5);
        Input i5 = new Input();
        i5.setName(UUID.randomUUID().toString());
        i5.setKind("Text");
        i5.setVisible(true);
        column5.setInput(i5);

        Column column6 = new Column();
        column6.setName(UUID.randomUUID().toString());
        column6.setText("轮次");
        Field f6 = new Field();
        f6.setName("round");
        column6.setField(f6);
        Input i6 = new Input();
        i6.setName(UUID.randomUUID().toString());
        i6.setKind("Number");
        i6.setVisible(false);
        column6.setInput(i6);

        Column column7 = new Column();
        column7.setName(UUID.randomUUID().toString());
        column7.setText("session_id");
        Field f7 = new Field();
        f7.setName("session_id");
        column7.setField(f7);
        Input i7 = new Input();
        i7.setName(UUID.randomUUID().toString());
        i7.setKind("Text");
        i7.setVisible(false);
        column7.setInput(i7);

        columns.add(column1);
        columns.add(column2);
        columns.add(column3);
        columns.add(column4);
        columns.add(column5);
        columns.add(column6);
        columns.add(column7);

        view.setColumns(columns);
        views.add(view);
        sheet.setViews(views);

        return sheet;
    }


}
