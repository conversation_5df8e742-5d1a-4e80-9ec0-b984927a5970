package ceibs.price.bean.yaml.tabler;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import ceibs.price.bean.yaml.sitemap.Tenant;


@Data
@NoArgsConstructor
public class Spec {
    private App app;
    private Designer designer;
    private Layout layout;
    private List<Sheet> sheets;
    private Tenant tenant;
    private Permission permission;

    // Getters and Setters

    public Spec(String tenantCode,String opt) {

        Designer designer = new Designer();
        designer.setName("TableDesigner");
        designer.setVersion("1.0");
        this.designer = designer;

        Tenant tenant = new Tenant();
        tenant.setName(tenantCode);
        tenant.setMultitenancy(true);
        this.tenant = tenant;

        List<Sheet> sheets = new ArrayList<>();



        this.sheets = sheets;

        Permission permission = new Permission(null);
        this.permission = permission;

    }
}
