package ceibs.price.config;

import io.modelcontextprotocol.client.McpAsyncClient;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.HttpClientStreamableHttpTransport;
import io.modelcontextprotocol.spec.McpClientTransport;
import io.modelcontextprotocol.spec.McpSchema;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.time.Duration;
import java.util.ArrayList;


/**
 * <AUTHOR>
 * Created on 2025-05-07
 */
@EnableScheduling
@Configuration
@EnableConfigurationProperties
@Slf4j
public class McpConfig3 {
    /**
     * MCP客户端的配置属性，用于存储MCP客户端的相关配置信息。
     * key : 服务名称-版本号-服务器地址
     */
    private McpAsyncClient mcpAsyncClient;




/*    @Value("${spring.ai.mcp.client.request-timeout}")
    private Duration requestTimeout;*/





    private boolean createClientByServerUrl(String serverUrl) {
        if (StringUtils.isBlank(serverUrl)) {
            log.warn("serverUrl is blank, please check the configuration file.");
            return false;
        }

        try {
            // 使用正确的传输层配置
            final McpClientTransport transport = HttpClientStreamableHttpTransport
                    .builder(serverUrl)
                    .endpoint("/mcp")
                    .build();


            // 创建异步客户端
            final McpClient.AsyncSpec asyncSpec = McpClient
                    .async(transport)
                    .initializationTimeout(Duration.ofSeconds(50)) // 增加初始化超时时间
                    .requestTimeout(Duration.ofSeconds(500))
                    .roots(new ArrayList<>());

            // 初始化客户端
            mcpAsyncClient = asyncSpec.build();
            McpSchema.ListToolsResult toolsResult = mcpAsyncClient.listTools().block();
            log.info("MCP工具列表: {}", toolsResult);
            //McpSchema.CallToolRequest callToolRequest=new McpSchema.CallToolRequest("findNameById", Map.of("id", "123123"));
            log.info("MCP客户端初始化成功");
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("创建MCP客户端失败: {}，serverUrl={}", ex.getMessage(), serverUrl, ex);
            return false;
        }
    }


    /**
     * 在Bean初始化时创建MCP客户端。
     */
    @PostConstruct
    public void init() {
        log.info("MCP初始化开始...");
        boolean success = createClientByServerUrl("https://priceagent.ceibs.edu");
        if (success) {
            log.info("MCP初始化完成，共初始化1个MCP客户端");
        } else {
            log.error("MCP客户端初始化失败");
            throw new RuntimeException("MCP客户端初始化失败");
        }
    }



    public McpAsyncClient getMcpSyncClient() {
        // 获取第一个MCP客户端
        return this.mcpAsyncClient;
    }
}