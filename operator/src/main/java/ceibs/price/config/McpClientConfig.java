/*
package ceibs.price.config;

import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.ai.mcp.customizer.McpSyncClientCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.util.List;
import java.util.logging.Logger;

*/
/**
 * MCP客户端配置类
 * 配置同步MCP客户端以及客户端定制器
 *//*

@Configuration
public class McpClientConfig {
    
    private static final Logger logger = Logger.getLogger(McpClientConfig.class.getName());
    
    */
/**
     * 配置同步MCP客户端定制器
     * 自定义MCP客户端的行为，包括请求超时、工具变更通知等
     *//*

   */
/*@Bean
    public McpSyncClientCustomizer customMcpSyncClientCustomizer() {
        return new McpSyncClientCustomizer() {
            @Override
            public void customize(String serverConfigurationName, McpClient.SyncSpec spec) {
                // 设置请求超时时间
                //spec.requestTimeout(Duration.ofSeconds(30)); // 增加超时时间
                spec.sampling((McpSchema.CreateMessageRequest messageRequest) -> {
                    logger.info("MCP服务器采样请求: " + messageRequest.messages().size() + " 条消息");
                    return null; // 应该返回有效的CreateMessageResult
                });
                // 添加工具变更通知消费者
                spec.toolsChangeConsumer((List<McpSchema.Tool> tools) -> {
                    logger.info("MCP服务器工具发生变更: " + tools.size() + " 个工具可用");
                    tools.forEach(tool -> logger.info("工具: " + tool.name()));
                });
                
                // 添加资源变更通知消费者
                spec.resourcesChangeConsumer((List<McpSchema.Resource> resources) -> {
                    logger.info("MCP服务器资源发生变更: " + resources.size() + " 个资源可用");
                });
                
                // 添加日志消费者
                spec.loggingConsumer((McpSchema.LoggingMessageNotification log) -> {
                    switch (log.level()) {
                        case ERROR -> logger.severe(log.logger());
                        //case WARN -> logger.warning(log.logger());
                        case INFO -> logger.info(log.logger());
                        case DEBUG -> logger.fine(log.logger());
                        default -> logger.info(log.logger());
                    }
                });
            }
        };
    }
*//*

    */
/**
     * 打印MCP客户端信息
     *//*

    @Bean
    public Object printMcpClientInfo(@Autowired(required = false) List<McpSyncClient> mcpSyncClients) {
        if (mcpSyncClients != null && !mcpSyncClients.isEmpty()) {
            logger.info("已配置 " + mcpSyncClients.size() + " 个MCP同步客户端");
            mcpSyncClients.forEach(client -> {
                logger.info("MCP客户端: " + client.getClientInfo().name());
                logger.info("MCP服务器: " + client.getServerInfo().name());
            });
        } else {
            logger.warning("未找到任何MCP同步客户端，可能是连接失败或配置错误");
        }
        return null;
    }
}*/
