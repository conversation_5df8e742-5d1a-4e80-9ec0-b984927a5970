/*
package ceibs.price.config;

import ceibs.price.service.CaseFileService;
import io.modelcontextprotocol.client.McpAsyncClient;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.client.transport.HttpClientStreamableHttpTransport;
import io.modelcontextprotocol.spec.McpSchema;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.mcp.client.common.autoconfigure.properties.McpStreamableHttpClientProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Map;


@EnableScheduling
@Configuration
@EnableConfigurationProperties
@Slf4j
public class McpConfigSN {


    @Autowired
    private McpStreamableHttpClientProperties mcpStreamableHttpClientProperties;
    @Value("${spring.ai.mcp.client.request-timeout}")
    private Duration requestTimeout;


    @Resource
    private CaseFileService caseFileService;


    private boolean createClientByServerUrl(String serverUrl) {
        if (StringUtils.isBlank(serverUrl)) {
            log.warn("serverUrl is blank, please check the configuration file.");
            return false;
        }
        try {
            final HttpClientStreamableHttpTransport transport = HttpClientStreamableHttpTransport.builder(serverUrl).build();
            // final McpSyncClient newClient = McpClient.sync(transport).initializationTimeout(Duration.ofSeconds(500)).requestTimeout(requestTimeout).build();
            final McpClient.AsyncSpec capabilities = McpClient
                    .async(transport)
                    .initializationTimeout(Duration.ofSeconds(10))
                    .requestTimeout(Duration.ofSeconds(10))
                    .roots(new ArrayList<>());
            McpAsyncClient newClient = capabilities.build();
         */
/*   // 初始化新客户端并记录结果*//*

            Mono<McpSchema.ListToolsResult> listToolsResultMono = newClient.listTools();
            log.info("<----------------> sync MCP Initialized 完成: {}", "1");


            log.info("sync MCP Initialized 完成: {}", 1);
            //   this.mcpSyncClientMap.put(serverUrl, newClient);
        } catch (Exception ex) {
            log.error("<----------------> createClientByServerUrl 链接 MCP 客户端失败: {}， serverUrl={}",
                    ex.getLocalizedMessage(), serverUrl);
            return false;
        }
        return true;
    }

    private synchronized void initMcpClient() {
        Map<String, McpStreamableHttpClientProperties.ConnectionParameters> connections = this.mcpStreamableHttpClientProperties.getConnections();
        if (connections.isEmpty()) {
            log.warn("initMcpClient <---------------->MCP 没有配置连接信息，请检查配置文件");
            return;
        }
        connections.forEach((key, sseParameter) -> {
            String serverUrl = sseParameter.url();
            this.createClientByServerUrl(serverUrl);
        });
    }


    */
/**
     * 在Bean初始化时创建MCP客户端。
     *//*

    @PostConstruct
    public void init() {
        log.info("<---------------->MCP Initializing...");
        // 在Bean初始化时强制执行
        initMcpClient();


    }


    public McpSyncClient getMcpSyncClient() {
        // 获取第一个MCP客户端
        return null;
    }
}*/
