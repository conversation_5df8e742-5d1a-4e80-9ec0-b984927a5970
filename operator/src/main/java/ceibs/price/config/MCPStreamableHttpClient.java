package ceibs.price.config;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * MCP Streamable HTTP 客户端 - 修正版本
 */
@Slf4j
@Component
public class MCPStreamableHttpClient {

    private static final int DEFAULT_TIMEOUT = 60000;
    private static final int STREAM_TIMEOUT = 300000;
    private static final String MCP_PROTOCOL_VERSION = "2025-06-18";
    private static final String MCP_SESSION_HEADER = "Mcp-Session-Id";
    private static final String MCP_PROTOCOL_VERSION_HEADER = "MCP-Protocol-Version";

    private final ThreadLocal<String> threadSessionId = new ThreadLocal<>();
    private final ThreadLocal<CloseableHttpClient> threadHttpClient = new ThreadLocal<>();
    private final java.util.concurrent.atomic.AtomicLong requestIdCounter = new java.util.concurrent.atomic.AtomicLong(1);

    /**
     * 初始化MCP会话
     */
    public String initializeMCPSession(String mcpUrl) {
        try {
            // 创建HTTP客户端
            CloseableHttpClient httpClient = createHttpClient();
            threadHttpClient.set(httpClient);

            // 构建初始化请求
            String initRequestBody = buildInitializeRequestBody();

            // 记录请求详情用于调试
            log.info("初始化请求URL: {}", mcpUrl);
            log.info("初始化请求体: {}", initRequestBody);

            HttpPost httpPost = createHttpPost(mcpUrl, initRequestBody, null, true);

            // 记录请求头部用于调试
            logRequestHeaders(httpPost, "初始化请求");

            // 执行初始化请求
            HttpResponse response = httpClient.execute(httpPost);

            // 记录响应详情
            logResponseDetails(response, "初始化响应");

            // 从响应头部获取会话ID
            String sessionId = response.getFirstHeader(MCP_SESSION_HEADER) != null ?
                    response.getFirstHeader(MCP_SESSION_HEADER).getValue() : null;

            if (sessionId != null) {
                threadSessionId.set(sessionId);
                log.info("MCP会话初始化成功，会话ID: {}", sessionId);
                return sessionId;
            } else {
                log.warn("MCP会话初始化成功，但未返回会话ID");
                return null;
            }

        } catch (Exception e) {
            log.error("MCP会话初始化失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 调用MCP工具并返回完整响应
     */
    public String callMCPToolComplete(String mcpUrl, String toolName, Object parameters) {
        String sessionId = getCurrentSessionId();
        CloseableHttpClient httpClient = getCurrentHttpClient();

        if (sessionId == null || httpClient == null) {
            throw new IllegalStateException("MCP会话未初始化，请先调用 initializeMCPSession");
        }

        try {
            // 构建请求体
            String requestBody = buildMCPToolRequestBody(toolName, parameters);

            // 记录请求详情用于调试
            log.info("工具调用请求URL: {}", mcpUrl);
            log.info("工具调用请求体: {}", requestBody);
            log.info("当前会话ID: {}", sessionId);

            // 创建POST请求，包含会话ID
            HttpPost httpPost = createHttpPost(mcpUrl, requestBody, sessionId, false);

            // 记录请求头部用于调试
            logRequestHeaders(httpPost, "工具调用请求");

            // 执行请求
            HttpResponse response = httpClient.execute(httpPost);

            // 记录响应详情
            logResponseDetails(response, "工具调用响应");

            // 获取完整响应
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                log.info("工具调用响应体: {}", responseBody);
                return responseBody;
            }

            return null;

        } catch (Exception e) {
            log.error("调用MCP工具失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用MCP工具失败", e);
        }
    }

    /**
     * 调用MCP工具（流式响应）
     */
    public int callMCPToolStream(String mcpUrl, String toolName, Object parameters, Consumer<String> streamConsumer) {
        String sessionId = getCurrentSessionId();
        CloseableHttpClient httpClient = getCurrentHttpClient();

        if (sessionId == null || httpClient == null) {
            throw new IllegalStateException("MCP会话未初始化，请先调用 initializeMCPSession");
        }

        try {
            // 构建请求体
            String requestBody = buildMCPToolRequestBody(toolName, parameters);

            // 记录请求详情
            log.info("流式工具调用请求URL: {}", mcpUrl);
            log.info("流式工具调用请求体: {}", requestBody);
            log.info("当前会话ID: {}", sessionId);

            // 创建POST请求，包含会话ID
            HttpPost httpPost = createHttpPost(mcpUrl, requestBody, sessionId, false);

            // 记录请求头部
            logRequestHeaders(httpPost, "流式工具调用请求");

            // 执行请求
            HttpResponse response = httpClient.execute(httpPost);

            // 记录响应详情
            logResponseDetails(response, "流式工具调用响应");

            // 处理流式响应
            processStreamResponse(response, streamConsumer);

            return response.getStatusLine().getStatusCode();

        } catch (Exception e) {
            log.error("调用MCP工具失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用MCP工具失败", e);
        }
    }

    /**
     * 异步调用MCP工具
     */
    public CompletableFuture<Integer> callMCPToolStreamAsync(String mcpUrl, String toolName, Object parameters, Consumer<String> streamConsumer) {
        return CompletableFuture.supplyAsync(() -> {
            return callMCPToolStream(mcpUrl, toolName, parameters, streamConsumer);
        });
    }

    /**
     * 获取当前线程的会话ID
     */
    private String getCurrentSessionId() {
        return threadSessionId.get();
    }

    /**
     * 获取当前线程的HTTP客户端
     */
    private CloseableHttpClient getCurrentHttpClient() {
        return threadHttpClient.get();
    }

    /**
     * 获取下一个请求ID
     */
    private long getNextRequestId() {
        return requestIdCounter.getAndIncrement();
    }

    /**
     * 创建HTTP客户端
     */
    private CloseableHttpClient createHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(DEFAULT_TIMEOUT)
                .setSocketTimeout(STREAM_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_TIMEOUT)
                .build();

        return HttpClients.custom()
                .setDefaultRequestConfig(config)
                .build();
    }

    /**
     * 创建HTTP POST请求 - 修正头部设置
     */
    private HttpPost createHttpPost(String url, String requestBody, String sessionId, boolean isInitialization) {
        HttpPost httpPost = new HttpPost(url);

        // 修正：严格按照MCP规范设置头部
        httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
        httpPost.setHeader("Accept", "application/json, text/event-stream");
        httpPost.setHeader("Cache-Control", "no-cache");
        httpPost.setHeader("Connection", "keep-alive");

        // 设置MCP协议版本头部
        httpPost.setHeader(MCP_PROTOCOL_VERSION_HEADER, MCP_PROTOCOL_VERSION);

        // 设置会话ID头部（除了初始化请求）
        if (!isInitialization && sessionId != null && !sessionId.trim().isEmpty()) {
            httpPost.setHeader(MCP_SESSION_HEADER, sessionId);
        }

        // 设置请求体 - 确保正确的字符编码
        if (requestBody != null && !requestBody.isEmpty()) {
            StringEntity entity = new StringEntity(requestBody, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
        }

        return httpPost;
    }

    /**
     * 构建初始化请求体
     */
    private String buildInitializeRequestBody() {
        java.util.Map<String, Object> request = new java.util.HashMap<>();
        request.put("jsonrpc", "2.0");
        request.put("id", getNextRequestId());
        request.put("method", "initialize");

        java.util.Map<String, Object> params = new java.util.HashMap<>();
        params.put("protocolVersion", "2025-06-18");

        java.util.Map<String, Object> capabilities = new java.util.HashMap<>();
        capabilities.put("roots", new java.util.HashMap<>());
        capabilities.put("sampling", new java.util.HashMap<>());
        capabilities.put("tools", new java.util.HashMap<>());
        params.put("capabilities", capabilities);

        java.util.Map<String, Object> clientInfo = new java.util.HashMap<>();
        clientInfo.put("name", "price-MCP-Client");
        clientInfo.put("version", "1.0.0");
        params.put("clientInfo", clientInfo);

        request.put("params", params);

        String requestBody = JSON.toJSONString(request);
        log.debug("构建的初始化请求体: {}", requestBody);
        return requestBody;
    }

    /**
     * 构建MCP工具调用请求体 - 修正格式
     */
    private String buildMCPToolRequestBody(String toolName, Object parameters) {
        try {
            // 确保参数是有效的JSON对象
            Object arguments;
            if (parameters == null) {
                arguments = new java.util.HashMap<>();
            } else if (parameters instanceof String) {
                // 如果是JSON字符串，尝试解析为对象
                String paramStr = (String) parameters;
                if (paramStr.trim().isEmpty()) {
                    arguments = new java.util.HashMap<>();
                } else {
                    try {
                        arguments = JSON.parseObject(paramStr);
                        if (arguments == null) {
                            arguments = new java.util.HashMap<>();
                        }
                    } catch (Exception e) {
                        log.warn("无法解析参数JSON字符串: {}, 错误: {}", paramStr, e.getMessage());
                        arguments = new java.util.HashMap<>();
                    }
                }
            } else {
                // 如果是对象，直接使用
                arguments = parameters;
            }

            // 构建完整的请求体，确保arguments是对象而不是字符串
            java.util.Map<String, Object> request = new java.util.LinkedHashMap<>();
            request.put("jsonrpc", "2.0");
            request.put("id", getNextRequestId());
            request.put("method", "tools/call");

            java.util.Map<String, Object> params = new java.util.LinkedHashMap<>();
            params.put("name", toolName);
            params.put("arguments", arguments);
            request.put("params", params);

            // 使用紧凑格式生成JSON，避免格式问题
            String requestBody = JSON.toJSONString(request,
                    com.alibaba.fastjson.serializer.SerializerFeature.WriteMapNullValue,
                    com.alibaba.fastjson.serializer.SerializerFeature.WriteNullStringAsEmpty);
            log.info("构建的工具调用请求体: {}", requestBody);

            // 验证生成的JSON是否有效
            try {
                JSON.parseObject(requestBody);
            } catch (Exception e) {
                log.error("生成的JSON无效: {}, 错误: {}", requestBody, e.getMessage());
                throw new RuntimeException("生成的JSON请求体无效", e);
            }

            return requestBody;

        } catch (Exception e) {
            log.error("构建MCP工具调用请求体失败: {}", e.getMessage(), e);
            throw new RuntimeException("构建请求体失败", e);
        }
    }

    /**
     * 处理流式响应
     */
    private void processStreamResponse(HttpResponse response, Consumer<String> streamConsumer) throws IOException {
        HttpEntity entity = response.getEntity();
        if (entity == null) {
            log.warn("响应实体为空");
            return;
        }

        try (InputStream inputStream = entity.getContent();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) {
                    continue;
                }

                log.debug("收到流式数据行: {}", line);

                // 处理SSE格式数据
                if (line.startsWith("data: ")) {
                    String data = line.substring(6);
                    if (!data.trim().isEmpty()) {
                        streamConsumer.accept(data);
                    }
                } else if (line.startsWith("event: ")) {
                    String event = line.substring(7);
                    log.debug("收到事件: {}", event);
                } else if (line.startsWith("id: ")) {
                    String id = line.substring(4);
                    log.debug("事件ID: {}", id);
                } else {
                    streamConsumer.accept(line);
                }
            }
        }
    }

    /**
     * 记录请求头部用于调试
     */
    private void logRequestHeaders(HttpPost httpPost, String requestType) {
        log.info("=== {} 请求头部 ===", requestType);
        Header[] allHeaders = httpPost.getAllHeaders();
        log.info("请求方法: {}", httpPost.getMethod());
        for (Header allHeader : allHeaders) {
            log.info("{}: {}", allHeader.getName(), allHeader.getValue());
        }

        log.info("==================");
    }

    /**
     * 记录响应详情用于调试
     */
    private void logResponseDetails(HttpResponse response, String responseType) {
        log.info("=== {} 详情 ===", responseType);
        log.info("状态码: {}", response.getStatusLine().getStatusCode());
        log.info("状态行: {}", response.getStatusLine().toString());

        if (response.getEntity() != null) {
            log.info("内容类型: {}", response.getEntity().getContentType());
            log.info("内容长度: {}", response.getEntity().getContentLength());
        }

        log.info("响应头部:");
        Header[] allHeaders = response.getAllHeaders();
        for (Header header : allHeaders) {
            log.info("{}: {}", header.getName(), header.getValue());
        }
        log.info("==================");
    }

    /**
     * 获取可用工具列表 - 用于调试
     */
    public String listAvailableTools(String mcpUrl) {
        String sessionId = getCurrentSessionId();
        CloseableHttpClient httpClient = getCurrentHttpClient();

        if (sessionId == null || httpClient == null) {
            throw new IllegalStateException("MCP会话未初始化，请先调用 initializeMCPSession");
        }

        try {
            java.util.Map<String, Object> request = new java.util.LinkedHashMap<>();
            request.put("jsonrpc", "2.0");
            request.put("id", getNextRequestId());
            request.put("method", "tools/list");
            request.put("params", new java.util.HashMap<>());

            String requestBody = JSON.toJSONString(request, false);
            log.info("工具列表请求体: {}", requestBody);

            HttpPost httpPost = createHttpPost(mcpUrl, requestBody, sessionId, false);
            HttpResponse response = httpClient.execute(httpPost);

            logResponseDetails(response, "工具列表响应");

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                log.info("工具列表响应体: {}", responseBody);
                return responseBody;
            }

            return null;

        } catch (Exception e) {
            log.error("获取工具列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取工具列表失败", e);
        }
    }

    /**
     * 清理当前线程的资源
     */
    public void cleanupCurrentThread() {
        CloseableHttpClient httpClient = threadHttpClient.get();
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.warn("关闭HTTP客户端失败: {}", e.getMessage());
            }
        }

        threadSessionId.remove();
        threadHttpClient.remove();
    }
}