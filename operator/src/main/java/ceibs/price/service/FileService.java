package ceibs.price.service;

import ceibs.price.utils.DataBusUtil;

import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.Charset;


@Service
@Slf4j
public class FileService {

    private final String FILE_API_URL = "https://cloud.ketanyun.cn/bus/graphql/file_v2";

    public String putFileByGql(byte[] file, String fileName, String token) throws IOException {

        // 通过 fluent.Request 发送post请求，设置请求头为multipart/form-data，参数为operations 和 map
        // operations 为json字符串，包含query和variables，其中variables中的file为null
        // map 为json字符串，包含0: ["variables.file"]
        // 0 为文件的索引，对应operations中的variables.file
        // @a.txt 为文件路径
        String operations = "{ \"query\": \"mutation ($file: Upload!) { upload(file: $file) { uri } }\", \"variables\": { \"file\": null } }";
        String map = "{\"0\":[\"variables.file\"]}";

        MultipartEntityBuilder builder = MultipartEntityBuilder.create()
                .setMode(HttpMultipartMode.BROWSER_COMPATIBLE)
                .setCharset(Charset.forName("utf-8"));
        builder.addTextBody("operations", operations, ContentType.MULTIPART_FORM_DATA);
        builder.addTextBody("map", map, ContentType.MULTIPART_FORM_DATA);
        builder.addBinaryBody("0", file, ContentType.MULTIPART_FORM_DATA, fileName);

        log.info("put file to fileAPI-V2:" + FILE_API_URL + "?access_token=" + token);
        String content = Request.Post(FILE_API_URL + "?access_token=" + token)
                .body(builder.build())
                .execute().returnContent().asString();
        if (null != content && null != JSONObject.parseObject(content).getJSONObject("data")) {
            return JSONObject.parseObject(content).getJSONObject("data").getJSONObject("upload").getString("uri");
        }
        throw new IOException("上传文件失败,上传路径为:" + FILE_API_URL);

    }

}
