package ceibs.price.service;

import ceibs.price.utils.DataBusUtil;
import ceibs.price.utils.TokenUtil;
import ceibs.price.utils.UrlUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class TablerService {


    @Resource
    DataBusUtil dataBusUtil;
    @Value("${tabler.url}")
    String tablerUrl;


    public JSONObject getSessionPart(String sessionId, String token) {
        String graphql = "query userParts($sessionId: String, $filter: PartFilter) {\n" +
                "  userParts(filter: $filter, sessionId: $sessionId) {\n" +
                "    kind\n" +
                "    name\n" +
                "    tags\n" +
                "    text\n" +
                "    description\n" +
                "    versions {\n" +
                "      name\n" +
                "      __typename\n" +
                "    }\n" +
                "    client {\n" +
                "      name\n" +
                "      text\n" +
                "      __typename\n" +
                "    }\n" +
                "    __typename\n" +
                "  }\n" +
                "}";
        JSONObject params = new JSONObject();
        params.put("query", graphql);
        JSONObject variables = new JSONObject();
        JSONObject partFilter = new JSONObject();
        JSONObject partFilterEq = new JSONObject();
        partFilterEq.put("eq", "workshop");
        partFilter.put("data", partFilterEq);
        variables.put("filter", partFilter);
        variables.put("sessionId", sessionId);
        params.put("variables", variables);
        // 根据url 获取请求地址
        String url = new StringBuffer(UrlUtil.getUrl(tablerUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        JSONArray userParts = resultJson.getJSONObject("data").getJSONArray("userParts");
        return !userParts.isEmpty() ? userParts.getJSONObject(0) : null;

    }


    public JSONObject createPart(String sessionId, String token) {
        String createPartGraphql = "mutation createPart($sessionId:String,$kind: String, $description: String, $tags: String, $text: String, $client: ClientInput) {\n" +
                "  createPart(\n" +
                "    kind: $kind\n" +
                "    sessionId: $sessionId\n" +
                "    description: $description\n" +
                "    tags: $tags\n" +
                "    text: $text\n" +
                "    client: $client\n" +
                "  ) {\n" +
                "    name\n" +
                "    versions {name}" +
                "  }\n" +
                "}";
        JSONObject createPartVariables = new JSONObject();
        createPartVariables.put("sessionId", sessionId);
        createPartVariables.put("kind", "QTable");
        createPartVariables.put("tags", "智能体");
        createPartVariables.put("text", "智能定价课程");
        JSONObject params = new JSONObject();
        params.put("query", createPartGraphql);
        params.put("variables", createPartVariables);
        // 根据url 获取请求地址
        String url = new StringBuffer(UrlUtil.getUrl(tablerUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        return resultJson.getJSONObject("data").getJSONObject("createPart");


    }


    public JSONObject createVersion(String partId, String token, String content) {
        String createPartVersion = "mutation updatePartVersion($name: ID!, $sources: [PartSourceInput!]) {\n" +
                "  updatePartVersion(name: $name, sources: $sources) {\n" +
                "    name\n" +
                "    sources { name }" +
                "  }\n" +
                "}";
        JSONObject createPartVersionVariables = new JSONObject();
        createPartVersionVariables.put("name", partId);
        JSONArray sources = new JSONArray();
        JSONObject source = new JSONObject();
        source.put("name", UUID.randomUUID().toString());
        source.put("kind", "QTable");
        source.put("content", content);
        sources.add(source);
        createPartVersionVariables.put("sources", sources);

        JSONObject params = new JSONObject();
        params.put("query", createPartVersion);
        params.put("variables", createPartVersionVariables);
        // 根据url 获取请求地址
        String url = new StringBuffer(UrlUtil.getUrl(tablerUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        return resultJson.getJSONObject("data").getJSONObject("updatePartVersion");


    }

    /* {
               "name": "d26d2836-b1e2-4be5-be4d-959561cb50e7",
                   "patches": [
               {
                   "name": "a557eff5-7307-49ae-ae8f-9c489192d42f",
                       "text": "新增Sheet_智能表格_2025-05-20 12:22:33",
                       "source": "b37d69ed-f85a-4e9f-9a1e-43947d3c79fd",
                       "ops": [
                   {
                       "op": "add",
                           "path": "/spec/sheets/@245db3c5-22a7-4a20-a5b3-648b29ae5bcd",
                           "value": "{\"name\":\"245db3c5-22a7-4a20-a5b3-648b29ae5bcd\",\"text\":\"123\",\"data\":{\"service\":{\"name\":\"hub-tabler-default\",\"dataType\":\"price_landscape_strength\",\"fields\":[{\"name\":\"competitive_landscape_id\",\"text\":\"competitive_landscape_id\",\"description\":\"外键，关联竞争格局主表ID\",\"dataType\":\"String\"},{\"name\":\"id\",\"text\":\"id\",\"description\":\"主键ID\",\"dataType\":\"ID\"},{\"name\":\"round\",\"text\":\"round\",\"description\":\"轮次\",\"dataType\":\"Int\"},{\"name\":\"session_id\",\"text\":\"session_id\",\"description\":\"会话ID，用于关联同一批数据\",\"dataType\":\"String\"},{\"name\":\"strength\",\"text\":\"strength\",\"description\":\"优势描述\",\"dataType\":\"String\"}]}},\"views\":[{\"name\":\"d5917cc6-f67a-4aeb-9e39-86fb7986d3a4\",\"kind\":\"grid\",\"columns\":[{\"name\":\"27c8377b-1114-4a6e-bbc8-502f8863209d\",\"text\":\"competitive_landscape_id\",\"field\":{\"name\":\"competitive_landscape_id\"},\"input\":{\"name\":\"fd162f8d-f0eb-416d-90b6-d4c50268d037\",\"kind\":\"Text\",\"visible\":true}},{\"name\":\"26d97a9b-48f0-4a41-8ec1-25725ac9fc7d\",\"text\":\"主键ID\",\"field\":{\"name\":\"id\"},\"input\":{\"name\":\"818a268a-96fa-48fc-b113-b9f2d29f977a\",\"kind\":\"Text\",\"visible\":true}},{\"name\":\"3fae3d31-c371-4bb9-806b-256e83157ed6\",\"text\":\"轮次\",\"field\":{\"name\":\"round\"},\"input\":{\"name\":\"41ce7614-9644-4688-8e25-9dbeed28d419\",\"kind\":\"Number\",\"visible\":true}},{\"name\":\"88f61f88-235d-4b95-bf4a-7a5bdefaf0ad\",\"text\":\"session_id\",\"field\":{\"name\":\"session_id\"},\"input\":{\"name\":\"ef888046-08c5-4cdd-a462-30e937315962\",\"kind\":\"Text\",\"visible\":true}},{\"name\":\"bc5b7fa0-8628-4574-afa5-a7dbc0047861\",\"text\":\"优势描述\",\"field\":{\"name\":\"strength\"},\"input\":{\"name\":\"80224cdb-8cd2-4157-815c-8b68de7e7840\",\"kind\":\"Text\",\"visible\":true}}]}]}"
                   }
                      ]
               }
                  ]
           }
   */
    public JSONObject patchPartVersion(String token, JSONObject variables) {
        String patchPartVersion = "mutation patchPartVersion($name: ID!, $patches: [PartSourcePatchInput!]) {\n" +
                "  patchPartVersion(name: $name, patches: $patches) {\n" +
                "    name\n" +
                "    __typename\n" +
                "  }\n" +
                "}";
        JSONObject params = new JSONObject();
        params.put("query", patchPartVersion);
        params.put("variables", variables);
        // 根据url 获取请求地址
        String url = new StringBuffer(UrlUtil.getUrl(tablerUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        return resultJson;
    }


}
