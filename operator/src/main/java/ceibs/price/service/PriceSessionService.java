package ceibs.price.service;

import java.util.*;


import ceibs.price.bean.User;
import ceibs.price.bean.bo.ClassInfo;
import ceibs.price.bean.bo.PriceSession;
import ceibs.price.bean.bo.PriceSessionToolLog;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.dao.ClassInfoDao;
import ceibs.price.dao.PriceSessionDao;
import ceibs.price.dao.PriceSessionToolLogDao;
import ceibs.price.dao.PriceSessionViewDao;

import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PriceSessionService {

    @Autowired
    private PriceSessionDao priceSessionDao;
    @Autowired
    private PriceSessionViewDao priceSessionViewDao;
    @Autowired
    private PriceSessionToolLogDao priceSessionToolLogDao;
    @Autowired
    private ClassInfoDao classInfoDao;


    public Integer countToolBySession(String sessionId, String toolName) {
        return priceSessionToolLogDao.countBySessionIdAndToolName(sessionId, toolName);

    }

    /**
     * 关闭会话的自动任务
     * @param sessionId
     * @return
     */
    public PriceSession closeAutoTask(String sessionId){
        PriceSession priceSession = priceSessionDao.findBySessionId(sessionId);
        priceSession.setAutoTask(false);
        return priceSessionDao.save(priceSession);
    }


    /**
     * 提交当前页面+会话 作为成绩
     */
    @Transactional
    public PriceSessionView submitPriceSession(PriceSession session, PriceSessionView view) {
        session.setSubmit(true);
        view.setSubmit(true);
        priceSessionDao.save(session);
        return priceSessionViewDao.save(view);
    }

    /**
     * 根据id 获取页面数据
     */
    public PriceSessionView getPriceSessionView(String viewId) {
        Optional<PriceSessionView> byId = priceSessionViewDao.findById(viewId);
        return byId.get();
    }

    /**
     * 保存页面数据
     */
    public PriceSessionView savePriceSessionView(PriceSessionView view) {
        return  priceSessionViewDao.save(view);
    }

    public List<PriceSessionView> findBySessionIdAndViewNameAndDelMarkIsFalse(String sessionId, String viewName) {
        return priceSessionViewDao.findBySessionIdAndViewNameAndDelMarkIsFalse(sessionId, viewName);
    }

    public PriceSessionView getMaxPriceSessionView(String sessionId,String viewName){
        return priceSessionViewDao.findFirstBySessionIdAndViewNameLikeAndDelMarkIsFalseOrderByCreatedAtDesc(sessionId,viewName);
    }

    public PriceSession findBySessionId(String sessionId) {
        PriceSession priceSession = priceSessionDao.findBySessionId(sessionId);
        return priceSession;
    }
    /**
     * 根据会话查询所有页面
     *
     * @param sessionId 会话id
     * @return 页面列表
     */
    public PriceSession getAllPriceSessionView(String sessionId) {
        PriceSession priceSession = priceSessionDao.findBySessionId(sessionId);
        List<PriceSessionView> priceSessionViews = priceSessionViewDao.findBySessionIdAndDelMarkIsFalseExcludeData(sessionId);
        priceSession.setViews(priceSessionViews);
        return priceSession;
    }

    @Transactional
    public PriceSession savePriceSession(String sessionId,String classId, String toolName, List<PriceSessionView> priceSessionViews, Boolean isAutoTask, User opt) {
        PriceSession priceSession = priceSessionDao.findBySessionId(sessionId);
        if (priceSession == null) {
            priceSession = new PriceSession();
            priceSession.setId(UUID.randomUUID().toString());
            priceSession.setSessionId(sessionId);
            priceSession.setCreatedAt(new Date());
            priceSession.setCreator(opt.getOpenId());
            priceSession.setCreatorName(opt.getName());
        }
        priceSession.setClassId(classId);
        priceSession.setAutoTask(null==isAutoTask?false:isAutoTask);

        priceSession = priceSessionDao.save(priceSession);
        // 新增工具调用日志
        PriceSessionToolLog priceSessionToolLog = new PriceSessionToolLog();
        priceSessionToolLog.setId(UUID.randomUUID().toString());
        priceSessionToolLog.setSessionId(sessionId);
        priceSessionToolLog.setToolName(toolName);
        priceSessionToolLog.setCreatedAt(new Date());
        priceSessionToolLogDao.save(priceSessionToolLog);

        priceSessionViewDao.saveAll(priceSessionViews);
        return priceSession;

    }

    public PriceSessionView updatePriceSessionView(Map entity) {
        Optional<PriceSessionView> byId = priceSessionViewDao.findById((String) entity.get("id"));
        if (byId.isEmpty()) {
            throw new RuntimeException("id不存在");
        }
        PriceSessionView priceSessionView = byId.get();
        priceSessionView.setData((String) entity.get("data"));
        priceSessionView.setDelMark(entity.containsKey("delMark") ? (Boolean) entity.get("delMark") : priceSessionView.getDelMark());
        priceSessionViewDao.save(priceSessionView);
        return priceSessionView;
    }

    public PriceSessionView updatePriceSessionViewByBean(PriceSessionView view) {
        if (view.getId() == null)
            throw new RuntimeException("id不存在");
        return priceSessionViewDao.save(view);
    }


}
