package ceibs.price.service;


import ceibs.price.bean.bo.Case;
import ceibs.price.config.McpConfig3;
import ceibs.price.dao.CaseDao;
import ceibs.price.utils.AlwaysOnLoadingCache;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import io.modelcontextprotocol.client.McpAsyncClient;
import io.modelcontextprotocol.spec.McpSchema;


import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * MCP服务类
 * 处理与MCP服务器的交互，提供对MCP工具和资源的访问
 */
@Service
@EnableAsync
public class McpService {

    private static final Logger logger = Logger.getLogger(McpService.class.getName());

/*
    @Autowired
    private final McpSyncClient mcpSyncClient;
*/

    @Autowired
    private McpConfig3 mcpConfig;


    /**
     * 获取所有可用的MCP工具
     *
     * @return 工具列表
     */
    public List<McpSchema.Tool> getAllTools() {
        McpAsyncClient mcpSyncClient = mcpConfig.getMcpSyncClient();
        if (null == mcpSyncClient) {
            logger.warning("无法获取MCP工具：未找到已配置的MCP客户端");
            return Collections.emptyList();
        }
        try {
            McpSchema.ListToolsResult listToolsResult = mcpSyncClient.listTools().block();
            List<McpSchema.Tool> tools = listToolsResult.tools();
            logger.info("获取到 " + tools.size() + " 个MCP工具");
            return tools;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "获取MCP工具时发生错误", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取所有可用的MCP资源
     *
     * @return 资源列表
     */
    public List<McpSchema.Resource> getAllResources() {
        McpAsyncClient mcpSyncClient = mcpConfig.getMcpSyncClient();
        if (null == mcpSyncClient) {
            logger.warning("无法获取MCP资源：未找到已配置的MCP客户端");
            return Collections.emptyList();
        }

        List<McpSchema.Resource> resources = new ArrayList<>();

        try {
            // 使用第一个客户端获取资源列表
            try {
                McpSchema.ListResourcesResult listResourcesResult = mcpSyncClient.listResources().block();
                resources.addAll(listResourcesResult.resources());
            } catch (Exception e) {
                logger.log(Level.WARNING, "从客户端获取资源时出错: " + mcpSyncClient.getClientInfo().name(), e);
            }

            logger.info("获取到 " + resources.size() + " 个MCP资源");
            return resources;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "获取MCP资源时发生错误", e);
            return Collections.emptyList();
        }
    }

    /**
     * 执行MCP工具调用
     *
     * @param toolId     工具ID
     * @param parameters 参数
     * @return 工具调用结果
     */
    public Object executeToolCall(String toolId, Object parameters) {
        McpAsyncClient mcpSyncClient = mcpConfig.getMcpSyncClient();
        if (null == mcpSyncClient) {
            logger.warning("无法执行工具调用：未找到已配置的MCP客户端");
            return null;
        }

        if (!StringUtils.hasText(toolId)) {
            logger.warning("无法执行工具调用：工具ID为空");
            return null;
        }

        logger.info("执行工具调用: " + toolId);
        try {
            // 调用MCP工具
            String jsonString = JSON.toJSONString(parameters);
            Mono<McpSchema.CallToolResult> callToolResultMono = mcpSyncClient.callTool(new McpSchema.CallToolRequest(toolId, jsonString));
            McpSchema.CallToolResult callToolResult = callToolResultMono.block();
            return callToolResult;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "执行工具调用时发生错误: " + e.getMessage(), e);
            return null;
        }
    }

    @Resource
    private CaseDao caseDao;

    public LoadingCache<String, List<Map<String, Object>>> cache = new AlwaysOnLoadingCache<>
            (1000, 120, TimeUnit.MINUTES, new CacheLoader<String, List<Map<String, Object>>>() {
                public List<Map<String, Object>> load(String key) throws Exception {
                    return loadData(key);
                }
            });

    private List<Map<String, Object>> loadData(String key) {
        logger.info("缓存获取接口数据 " + key);
        McpAsyncClient mcpSyncClient = mcpConfig.getMcpSyncClient();
        List<Map<String, Object>> datas = new ArrayList<>();
        JSONObject args = new JSONObject();
        String toolName = key;
        if (key.equals("analyze_vrio") || key.equals("analyze_competition")) {
            Case caseFile = caseDao.findFirstByActiveIsTrue();
            if (null == caseFile) {
                return datas;
            }
            args.put("file_url", caseFile.getPdfUrl());
        }
        McpSchema.CallToolResult result = mcpSyncClient.callTool(new McpSchema.CallToolRequest(toolName, args)).block();
        List<McpSchema.Content> contentList = result.content();
        McpSchema.Content content = contentList.get(0);
        if (content instanceof McpSchema.TextContent textContent) {
            String text = textContent.text();
            logger.info("缓存获取到接口数据 " + text);
            // text to map
            Object parse = JSON.parse(text);
            if (parse instanceof JSONObject jsonObject) {
                // to Map
                Map map = jsonObject.toJavaObject(Map.class);
                datas.add(map);
            }
        }
        return datas;

    }
    @PostConstruct
    public void init() {
        // 应用启动后自动执行
        try {
            logger.info("开始换成竞争分析");
            getCache("analyze_competition");
            logger.info("竞争分析初始化完成");
        }catch (Exception e){
            logger.log(Level.SEVERE, "获取缓存数据时发生错误: " + e.getMessage(), e);
        }

    }


    public List<Map<String, Object>> getCache(String key) throws ExecutionException {
        // 如果获取的是空，则重新加载一次数据
        if (cache.get(key).size() == 0) {
            List<Map<String, Object>> maps = loadData(key);
            if(!maps.isEmpty()){
                cache.put(key, maps);
            }
            return maps;
        }
        return cache.get(key);
    }

    @Async
    public void refreshCache(String key) {
        cache.refresh(key);
    }
} 