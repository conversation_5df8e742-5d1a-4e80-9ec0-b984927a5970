package ceibs.price.service;


import ceibs.price.utils.DataBusUtil;
import ceibs.price.utils.UrlUtil;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Service
public class CopilotApiService {


    @Resource
    DataBusUtil dataBusUtil;
    @Value("${copilot.id}")
    private String copilotId;

    @Value("${copilot.brainstorming}")
    private String copilotBrainstormingId;

    @Value("${copilot.url}")
    String copilotApiUrl;


    public JSONObject generateToCPrompt(String dinwei, String content, List<Map<String, Object>> cache, String token) throws ExecutionException {
        JSONObject params = new JSONObject();
        // to json string
        if (null == content) {
            content = JSON.toJSONString(cache);
        }
        JSONObject cailiao = new JSONObject();
        // 材料信息只提取部分：
        JSONObject myJson = JSONArray.parseArray(content).getJSONObject(0).getJSONObject("本公司");
        // myJson.remove("VRIO 资源禀赋");
        myJson.remove("定位");
        myJson.remove("客群");
        cailiao.put("本公司信息", myJson);
        Object jzds = JSONArray.parseArray(content).getJSONObject(0).get("竞争对手");
        cailiao.put("竞争对手信息", JSON.toJSONString(jzds));


        // 构造 data 字段内容
        JSONObject data = new JSONObject();
        data.put("cailiao", cailiao.toJSONString());
        data.put("dinwei", dinwei);

        // 构造整个 GraphQL 查询体
        JSONObject requestBody = new JSONObject();
        {
            JSONObject generate = new JSONObject();
            generate.put("copilot", copilotId);
            generate.put("prompt", "1a18c0e5-4e2f-46e1-b9b2-cb24b631d3dd");
            generate.put("data", data.toJSONString()); // 保持为字符串
            requestBody.put("query", String.format("{ generate(copilot: %s, prompt: %s, data: %s) { choices { message { text } } } }",
                    JSON.toJSONString(generate.getString("copilot")),
                    JSON.toJSONString(generate.getString("prompt")),
                    JSON.toJSONString(generate.getString("data"))));
        }

        String query = getQuery(requestBody);
        params.put("query", query);
        // params.put("variables","");
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        if (resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").isEmpty()) {
            return null;
        }
        return resultJson.getJSONObject("data").getJSONObject("generate");

    }


    /**
     * 精简头脑风暴对话
     *
     * @param history
     * @param token
     * @return
     * @throws ExecutionException
     */
    public String simplifyBrainstorming(JSONArray history, String token) throws ExecutionException {
        JSONObject params = new JSONObject();
        // 构造 data 字段内容
        JSONObject data = new JSONObject();
        data.put("history", history);

        // 构造整个 GraphQL 查询体
        JSONObject requestBody = new JSONObject();
        {
            JSONObject generate = new JSONObject();
            generate.put("copilot", copilotId);
            generate.put("prompt", "44ca1c77-b193-429c-9212-fb92f7afb0a8");
            generate.put("data", data.toJSONString()); // 保持为字符串
            requestBody.put("query", String.format("{ generate(copilot: %s, prompt: %s, data: %s) { choices { message { text } } } }",
                    JSON.toJSONString(generate.getString("copilot")),
                    JSON.toJSONString(generate.getString("prompt")),
                    JSON.toJSONString(generate.getString("data"))));
        }

        String query = getQuery(requestBody);
        params.put("query", query);
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        if (resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").isEmpty()) {
            return null;
        }
        return resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text");
    }


    /**
     * 精简聊天记录，得到已提到过的主题
     *
     * @param history
     * @param token
     * @return
     * @throws ExecutionException
     */
    public String simplifyBrainstormingTopic(JSONArray history, String token) throws ExecutionException {
        JSONObject params = new JSONObject();
        // 构造 data 字段内容
        JSONObject data = new JSONObject();
        data.put("history", history);

        // 构造整个 GraphQL 查询体
        JSONObject requestBody = new JSONObject();
        {
            JSONObject generate = new JSONObject();
            generate.put("copilot", copilotBrainstormingId);
            generate.put("prompt", "8a9817cd-e9b3-4d0f-bd86-cb6d38d12600");
            generate.put("data", data.toJSONString()); // 保持为字符串
            requestBody.put("query", String.format("{ generate(copilot: %s, prompt: %s, data: %s) { choices { message { text } } } }",
                    JSON.toJSONString(generate.getString("copilot")),
                    JSON.toJSONString(generate.getString("prompt")),
                    JSON.toJSONString(generate.getString("data"))));
        }

        String query = getQuery(requestBody);
        params.put("query", query);
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        if (resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").isEmpty()) {
            return null;
        }
        return resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text");
    }


    /**
     * 提取用户感兴趣的战略主题信息
     *
     * @param history
     * @param token
     * @return
     * @throws ExecutionException
     */
    public String extractTheTopic(String jzfx,JSONObject slwd,JSONArray history, String token)  {
        JSONObject params = new JSONObject();
        // 构造 data 字段内容
        JSONObject data = new JSONObject();
        data.put("jzfx", jzfx);
        data.put("slwd", null==slwd?"":slwd.toString());
        data.put("history", history);
        // 构造整个 GraphQL 查询体
        JSONObject requestBody = new JSONObject();
        {
            JSONObject generate = new JSONObject();
            generate.put("copilot", copilotBrainstormingId);
            generate.put("prompt", "941edb96-afb0-44e1-9ce3-c1eb1c8b5614");
            generate.put("data", data.toJSONString()); // 保持为字符串
            requestBody.put("query", String.format("{ generate(copilot: %s, prompt: %s, data: %s) { choices { message { text } } } }",
                    JSON.toJSONString(generate.getString("copilot")),
                    JSON.toJSONString(generate.getString("prompt")),
                    JSON.toJSONString(generate.getString("data"))));
        }

        String query = getQuery(requestBody);
        params.put("query", query);
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        if (resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").isEmpty()) {
            return null;
        }
        return resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text");
    }

    /**
     * 生成用户 竞争分析报告
     *
     * @param history
     * @param token
     * @return
     * @throws ExecutionException
     */
    public String generateUserCompetitionAnalyzeReport(JSONArray history, String token) throws ExecutionException {
        JSONObject params = new JSONObject();
        // 构造 data 字段内容
        JSONObject data = new JSONObject();
        data.put("history", history);
        // 构造整个 GraphQL 查询体
        JSONObject requestBody = new JSONObject();
        {
            JSONObject generate = new JSONObject();
            generate.put("copilot", copilotId);
            generate.put("prompt", "88b755b6-f978-4331-9fb5-380b352c6861");
            generate.put("data", data.toJSONString()); // 保持为字符串
            requestBody.put("query", String.format("{ generate(copilot: %s, prompt: %s, data: %s) { choices { message { text } } } }",
                    JSON.toJSONString(generate.getString("copilot")),
                    JSON.toJSONString(generate.getString("prompt")),
                    JSON.toJSONString(generate.getString("data"))));
        }

        String query = getQuery(requestBody);
        params.put("query", query);
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        if (resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").isEmpty()) {
            return null;
        }
        return resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text");
    }


    /**
     * 识别用户输入，生成对应的 竞争分析维度
     *
     * @param userInput 用户输入
     * @param anli      案例
     * @param token     token
     * @return
     */
    public String identifyCompeteDim(String userInput, String anli, String token) throws ExecutionException {
        JSONObject params = new JSONObject();
        // 构造 data 字段内容
        JSONObject data = new JSONObject();
        data.put("input", userInput);
        data.put("anli", anli);
        // 构造整个 GraphQL 查询体
        JSONObject requestBody = new JSONObject();
        {
            JSONObject generate = new JSONObject();
            generate.put("copilot", copilotId);
            generate.put("prompt", "91e877dc-08db-4311-bc61-2485434535b4");
            generate.put("data", data.toJSONString()); // 保持为字符串
            requestBody.put("query", String.format("{ generate(copilot: %s, prompt: %s, data: %s) { choices { message { text } } } }",
                    JSON.toJSONString(generate.getString("copilot")),
                    JSON.toJSONString(generate.getString("prompt")),
                    JSON.toJSONString(generate.getString("data"))));
        }

        String query = getQuery(requestBody);
        params.put("query", query);
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        if (resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").isEmpty()) {
            return null;
        }
        return resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text");
    }

    /**
     * 生成头脑风暴岗位
     *
     * @param token
     * @return
     * @throws ExecutionException
     */
    public String generateBrainstormingUserType(String data, String token) throws ExecutionException {
        JSONObject params = new JSONObject();
        // 构造整个 GraphQL 查询体
        JSONObject requestBody = new JSONObject();
        {
            JSONObject generate = new JSONObject();
            generate.put("copilot", copilotBrainstormingId);
            generate.put("prompt", "35c9c7ac-5b6c-4b46-9f10-fba77b555d94");
            generate.put("data", data); // 保持为字符串
            requestBody.put("query", String.format("{ generate(copilot: %s, prompt: %s, data: %s) { choices { message { text } } } }",
                    JSON.toJSONString(generate.getString("copilot")),
                    JSON.toJSONString(generate.getString("prompt")),
                    JSON.toJSONString(generate.getString("data"))));
        }

        String query = getQuery(requestBody);
        params.put("query", query);
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        if (resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").isEmpty()) {
            return null;
        }
        return resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text");
    }

    /**
     * 生成头脑风暴岗位
     *
     * @param ysc   已生成项
     * @param token
     * @return
     * @throws ExecutionException
     */
    public String generateBrainstormingTags(String anli, String ysc, String token) throws ExecutionException {
        JSONObject params = new JSONObject();
        // 构造 data 字段内容
        JSONObject data = new JSONObject();
        data.put("anli", anli);
        data.put("ysc", ysc);
        // 构造整个 GraphQL 查询体
        JSONObject requestBody = new JSONObject();
        {
            JSONObject generate = new JSONObject();
            generate.put("copilot", copilotBrainstormingId);
            generate.put("prompt", "cec6573a-ad26-460a-8328-39cecb62fe8a");
            generate.put("data", data.toJSONString()); // 保持为字符串
            requestBody.put("query", String.format("{ generate(copilot: %s, prompt: %s, data: %s) { choices { message { text } } } }",
                    JSON.toJSONString(generate.getString("copilot")),
                    JSON.toJSONString(generate.getString("prompt")),
                    JSON.toJSONString(generate.getString("data"))));
        }

        String query = getQuery(requestBody);
        params.put("query", query);
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        if (resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").isEmpty()) {
            return null;
        }
        return resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text");
    }


    public String generateSelfIntroduce(String data, String token) throws ExecutionException {
        JSONObject params = new JSONObject();
        // 构造整个 GraphQL 查询体
        JSONObject requestBody = new JSONObject();
        {
            JSONObject generate = new JSONObject();
            generate.put("copilot", copilotBrainstormingId);
            generate.put("prompt", "95cd0b75-65b0-40ae-ba54-2adb8254d2bd");
            generate.put("data", data); // 保持为字符串
            requestBody.put("query", String.format("{ generate(copilot: %s, prompt: %s, data: %s) { choices { message { text } } } }",
                    JSON.toJSONString(generate.getString("copilot")),
                    JSON.toJSONString(generate.getString("prompt")),
                    JSON.toJSONString(generate.getString("data"))));
        }

        String query = getQuery(requestBody);
        params.put("query", query);
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        if (resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").isEmpty()) {
            return null;
        }
        return resultJson.getJSONObject("data").getJSONObject("generate").getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text");
    }


    /**
     * 创建知识库
     *
     * @return
     */
    public JSONArray createKnowledges(String copilotId, String fileName, String fileUrl,String categoryId, String token) {
        String graphql = """
                    mutation createKnowledges($copilot: ID!, $knowledges: [KnowledgeInput]) {
                        createKnowledges(copilot: $copilot, knowledges: $knowledges) {
                            name
                            kind
                            text
                            doc {
                              name
                              text
                              kind
                              uri
                              content
                              status
                              statusDesc
                              reference
                              userDownloadable
                              sort
                            }
                            state
                            category {
                              name
                              text
                              filterable
                              sort
                              description
                            }
                        }
                    }
                """;
        String variables = """
                        {
                                "copilot": "${copilotId}",
                                "knowledges": [
                                {
                                    "doc": {
                                    "text": "${fileName}",
                                    "kind": "docx",
                                    "uri": "${fileUrl}",
                                    "userDownloadable": true
                                },
                                    "kind": "doc",
                                    "category": "${categoryId}"
                                }
                          ]
                            }
                """;
        variables = variables.replace("${copilotId}", copilotId).replace("${fileName}", fileName)
                .replace("${fileUrl}", fileUrl).replace("${categoryId}", categoryId);
        JSONObject params = new JSONObject();
        params.put("query", graphql);
        params.put("variables", JSON.parseObject(variables));
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        return resultJson.getJSONObject("data").getJSONArray("createKnowledges");
    }

    /**
     * 根据智能体查询 知识库
     *
     * @param copilotId 智能体ID
     * @param category  类型id
     * @return
     */
    public JSONArray getKnowledgeByAgent(String copilotId, String category, String token) {
        String graphql = """
                    query knowledges($copilot: ID!, $keyword: String, $first: Int, $offset: Int, $filter: KnowledgeFilter) {
                       knowledges(
                         copilot: $copilot
                         keyword: $keyword
                         first: $first
                         offset: $offset
                         filter: $filter
                       ) {
                         edges {
                           node {
                             name
                             kind
                             text
                             state
                             faq {
                               name
                               question
                               answer
                               common
                               sort
                               doc {
                                 name
                                 text
                               }
                             }
                             doc {
                               name
                               text
                               content
                               kind
                               uri
                               status
                               statusDesc
                               reference
                               chunks
                               splitter {
                                 chunkSize
                                 chunkOverlap
                                 separators
                               }
                               userDownloadable
                               sort
                               ontology {
                                 name
                                 kind
                                 attr
                               }
                             }
                             updateTime
                             createTime
                             creator {
                               text
                               openid
                             }
                             category {
                               name
                               text
                             }
                           }
                         }
                         totalCount
                       }
                     }
                """;
        String variables = """
                {
                  "copilot": "${copilotId}",
                  "first": 10,
                  "offset": 0,
                  "filter": {
                    "kind": {
                      "eq": "faq"
                    },
                    "category": {
                      "eq": "${category}"
                    }
                  },
                  "keyword": ""
                }
                """;
        variables = variables.replace("${copilotId}", copilotId).replace("${category}", category);
        JSONObject params = new JSONObject();
        params.put("query", graphql);
        params.put("variables", JSON.parseObject( variables));
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        JSONArray jsonArray = resultJson.getJSONObject("data").getJSONObject("knowledges").getJSONArray("edges");
        return jsonArray;
    }


    /**
     * 查询智能体知识库有哪些分类
     *
     * @param copilotId
     * @return
     */
    public JSONArray getKnowledgeCategory(String copilotId, String token) {
        String graphql = """
                query  {
                  categories(copilot: "${copilotId}") {
                    name
                    text
                    filterable
                    sort
                    description
                  }
                }
                
                """;
        graphql = graphql.replace("${copilotId}", copilotId);
        JSONObject params = new JSONObject();
        params.put("query", graphql);
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        JSONArray jsonArray = resultJson.getJSONObject("data").getJSONArray("categories");
        return jsonArray;
    }


    /**
     * 删除知识库
     *
     * @param copilotId
     * @return
     */
    public JSONArray deleteKnowledge(String copilotId, String knowledgeId, String token) {
        String graphql = """
                mutation deleteKnowledges($copilot: ID!, $kind: String!, $names: [ID!]) {
                  deleteKnowledges(copilot: $copilot, kind: $kind, names: $names) {
                    name
                  }
                }
                """;
        String variables = """
                {
                  "copilot": "${copilotId}",
                  "kind": "doc",
                  "names": [
                    "${knowledgeId}"
                  ]
                }
                """;
        variables = variables.replace("${copilotId}", copilotId);
        JSONObject params = new JSONObject();
        params.put("query", graphql);
        params.put("variables", JSON.parseObject(variables));
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        JSONArray jsonArray = resultJson.getJSONObject("data").getJSONArray("deleteKnowledges");
        return jsonArray;
    }

    /**
     * 修改知识库状态 ，启用/禁用
     *
     * @param copilotId
     * @param knowledgeId
     * @param token
     * @return
     */
    public JSONArray updateKnowledgeStatus(String copilotId, String knowledgeId, Boolean enable, String token) {
        String graphql = """
                mutation updateKnowledgeState($copilot: ID!, $enabled: Boolean!, $kind: String!, $names: [ID!]) {
                  updateKnowledgeState(
                    copilot: $copilot
                    enabled: $enabled
                    kind: $kind
                    names: $names
                  ) {
                    name
                  }
                }
                """;
        String variables = """
                {
                  "copilot": "${copilotId}",
                  "kind": "doc",
                  "names": [
                    "${knowledgeId}"
                  ],
                  "enabled": ${enable}
                }
                """;
        variables = variables.replace("${copilotId}", copilotId).replace("${knowledgeId}", knowledgeId).replace("${enable}", enable ? "true" : "false");
        JSONObject params = new JSONObject();
        params.put("query", graphql);
        params.put("variables", JSON.parseObject(variables));
        String url = new StringBuffer(UrlUtil.getUrl(copilotApiUrl)).append("?access_token=").append(token).toString();
        String s = dataBusUtil.sendPostRequest(url, params);
        JSONObject resultJson = JSONObject.parseObject(s);
        if (resultJson.containsKey("errors")) {
            throw new RuntimeException(resultJson.getJSONArray("errors").getJSONObject(0).getString("message"));
        }
        JSONArray jsonArray = resultJson.getJSONObject("data").getJSONArray("updateKnowledgeState");
        return jsonArray;
    }

    private static String getQuery(JSONObject requestBody) {
        return requestBody.getString("query");
    }

}
