package ceibs.price.service;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.ClassInfo;
import ceibs.price.bean.bo.GroupUser;
import ceibs.price.bean.bo.PriceSession;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.dao.ClassInfoDao;
import ceibs.price.dao.GroupUserDao;
import ceibs.price.dao.PriceSessionDao;
import ceibs.price.dao.PriceSessionViewDao;
import com.alibaba.fastjson.JSONArray;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.*;


/**
 * 当前上课情况一览
 */
@Service
@Slf4j
public class CurrentClassViewService {


    @Resource
    ClassInfoDao classInfoDao;
    @Resource
    GroupUserDao groupUserDao;
    @Resource
    PriceSessionDao priceSessionDao;
    @Resource
    PriceSessionViewDao priceSessionViewDao;


    /**
     * 根据 老师 openid 查下当前是否有无排课
     */
    public ClassInfo findCurrentClassByTeacher(String teacherOpenid) {
        ClassInfo currentClass = classInfoDao.findCurrentClassByTeacher(teacherOpenid);
        if (null == currentClass) {
            throw new RuntimeException("当前没有排课");
        }
        List<GroupUser> all = groupUserDao.findAllByClassId(currentClass.getId());
        for (GroupUser groupUser : all) {
            List<User> users = JSONArray.parseArray(groupUser.getUsersJson(), User.class);
            groupUser.setUsers(users);
        }
        currentClass.setGroupUsers(all);
        return currentClass;
    }

    public List<ClassInfo> findCurrentClass() {
        List<ClassInfo> currentClass1 = classInfoDao.findCurrentClass();
        for (ClassInfo currentClass : currentClass1) {
            List<GroupUser> all = groupUserDao.findAllByClassId(currentClass.getId());
            for (GroupUser groupUser : all) {
                List<User> users = JSONArray.parseArray(groupUser.getUsersJson(), User.class);
                groupUser.setUsers(users);
            }
            currentClass.setGroupUsers(all);
        }
        return currentClass1;
    }

    /**
     * 查看当前分组情况
     */
    public List<GroupUser> getCurrentGroup(String classId) {
        List<GroupUser> all = groupUserDao.findAllByClassId(classId);
        // 排序
        all.sort(Comparator.comparingInt(GroupUser::getGroupIndex));
        for (GroupUser groupUser : all) {
            List<User> users = JSONArray.parseArray(groupUser.getUsersJson(), User.class);
            groupUser.setUsers(users);
        }

        return all;
    }

    /**
     * 查看当前上课情况
     *
     * @param classId   课程 id
     * @param openid    人员id
     * @param startTime
     * @param endTime
     */
    public PriceSession getPriceSessionByUser(String classId, String openid, Date startTime, Date endTime) {
        Optional<ClassInfo> byId = classInfoDao.findById(classId);
        List<PriceSession> priceSessions = new ArrayList<>();
        if (null != startTime && null != endTime) {
            //
            priceSessions = priceSessionDao.findByCreatorAndCreatedAtIsBetween(openid, startTime, endTime);
        } else if (!byId.isEmpty()) {
            priceSessions = priceSessionDao.findByCreatorAndClassId(openid, classId);
        } else {
            throw new RuntimeException("课程不存在,请确认上课时间信息");
        }
        // 从 当前课产生的所有会话中，选出 标记过提交的 & 创建时间最新的记录
        PriceSession priceSession = priceSessions.stream().filter(p->null!=p.getSubmit()&&p.getSubmit()&&null!=p.getSubmitTime())
                .max(Comparator.comparing(PriceSession::getSubmitTime)).orElse(null);
        if (null == priceSession) {
           return null;
        }
        List<PriceSessionView> priceSessionViews = priceSessionViewDao.findBySessionIdAndDelMarkIsFalseExcludeData(priceSession.getSessionId());
        // 从当前会话页面中，挑选出标记过提交的
        priceSessionViews = priceSessionViews.stream().filter(p->null!=p.getSubmit()&&p.getSubmit()).toList();
        priceSession.setViews(priceSessionViews);

        return priceSession;

    }

}
