package ceibs.price.service;


import ceibs.price.bean.bo.Case;
import ceibs.price.bean.bo.CaseFile;
import ceibs.price.dao.CaseDao;
import ceibs.price.dao.CaseFileDao;
import ceibs.price.utils.CertifiedConnector;
import ceibs.price.utils.TxtToPdfUtil;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class CaseService {


    @Resource
    private CaseDao caseDao;

    @Resource
    private CaseFileDao caseFileDao;


    public Case findCaseById(String id) {
        Optional<Case> byId = caseDao.findById(id);
        if (byId.isEmpty()) {
            throw new RuntimeException("案例不存在");
        }
        return byId.get();
    }

    public CaseFile findCaseFileById(String id) {
        Optional<CaseFile> byId = caseFileDao.findById(id);
        if (byId.isEmpty()) {
            throw new RuntimeException("案例文件不存在");
        }
        return byId.get();
    }

    public Case findFirstByActiveIsTrue() {
        Case firstCase = caseDao.findFirstByActiveIsTrue();
        List<CaseFile> caseFiles = caseFileDao.findAllByActiveIsTrueAndCaseId(firstCase.getId());
        firstCase.setCaseFiles(caseFiles);
        return firstCase;
    }

    public List<Case> getAllCase() {
        List<Case> all = caseDao.findAll();
        for (Case caseFile : all) {
            List<CaseFile> caseFiles = caseFileDao.findAllByActiveIsTrueAndCaseId(caseFile.getId());
            caseFile.setCaseFiles(caseFiles);
        }
        return all;
    }


    /**
     * 新增案例
     */
    @Transactional
    public Case addCase(Case caseBo) {
        Case save = caseDao.save(caseBo);
        List<CaseFile> caseFiles = caseBo.getCaseFiles();
        caseFileDao.saveAll(caseFiles);
        return save;
    }

    @Transactional
    public Case delCase(String id) {
        Optional<Case> byId = caseDao.findById(id);
        if (byId.isEmpty()) {
            throw new RuntimeException("案例不存在");
        }
        Case caseBo = byId.get();
        Integer i = caseFileDao.deleteCaseFileByCaseId(caseBo.getId());
        caseDao.deleteById(caseBo.getId());
        return caseBo;
    }

    @Transactional
    public CaseFile updateCaseFile( CaseFile caseFile) {
        //
        CaseFile oldCaseFile = caseFileDao.getById(caseFile.getId());
        String oldCaseId = oldCaseFile.getCaseId();
        caseFile.setCaseId(oldCaseId);
        CaseFile save = caseFileDao.save(caseFile);
        return  save;
    }
    @Transactional
    public CaseFile delCaseFile(String id) {
        Optional<CaseFile> byId = caseFileDao.findById(id);
        if (byId.isEmpty()) {
            throw new RuntimeException("案例文件不存在");
        }
        caseFileDao.deleteById( id);
        return byId.get();
    }

    /**
     * 修改案例
     */

    @Transactional
    public Case updateCase(Case oldCase,Case caseBo, List<CaseFile> caseFiles) {
        //
        boolean oldActive = oldCase.getActive();
        caseBo.setPdfUrl(caseBo.getPdfUrl());
        if (oldActive!=(caseBo.getActive())&&caseBo.getActive()) {
            // 检测是否需要更新 txt 的pdf
            //  如果新旧 txt 值不一样,则上传新的pdf
            if (!oldCase.getText().equals(caseBo.getText())||oldCase.getPdfUrl()==null) {
                // 上传新的 txt
                try {
                    String text = caseBo.getText();
                    byte[] bytes = TxtToPdfUtil.convertTxtContentToPdf(text);
                    String requestToken = CertifiedConnector.getRequestToken();
                    JSONObject jsonObject = putFileByGql(bytes, caseBo.getTitle()+".pdf", requestToken);
                    caseBo.setPdfUrl(jsonObject.getString("uri"));
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException("转换案例完整文件失败");
                }
            }
        }
        caseBo.setUpdatedAt(new Date());
        Case newCase = caseDao.save(caseBo);
        caseFileDao.saveAll(caseFiles);

        return newCase;
    }

    @Value("${SERVICE_ORIGIN}")
    private String serviceOrigin;

    public JSONObject putFileByGql(byte[] file, String fileName, String token) {
        String fileGqlUrl = serviceOrigin + "/bus/graphql/file_v2";
        try {
            // 通过 fluent.Request 发送post请求，设置请求头为multipart/form-data，参数为operations 和 map
            // operations 为json字符串，包含query和variables，其中variables中的file为null
            // map 为json字符串，包含0: ["variables.file"]
            // 0 为文件的索引，对应operations中的variables.file
            // @a.txt 为文件路径
            String operations = "{ \"query\": \"mutation ($file: Upload!) { upload(file: $file) { uri } }\", \"variables\": { \"file\": null } }";
            String map = "{\"0\":[\"variables.file\"]}";

            MultipartEntityBuilder builder = MultipartEntityBuilder.create()
                    .setMode(HttpMultipartMode.BROWSER_COMPATIBLE)
                    .setCharset(Charset.forName("utf-8"));
            builder.addTextBody("operations", operations, ContentType.MULTIPART_FORM_DATA);
            builder.addTextBody("map", map, ContentType.MULTIPART_FORM_DATA);
            builder.addBinaryBody("0", file, ContentType.MULTIPART_FORM_DATA, fileName);

            log.info("put file to fileAPI-V2:" + fileGqlUrl + "?access_token=" + token);
            String content = Request.Post(fileGqlUrl + "?access_token=" + token)
                    .body(builder.build())
                    .execute().returnContent().asString();
            if (null != content && null != JSONObject.parseObject(content).getJSONObject("data")) {
                JSONObject object = JSONObject.parseObject(content).getJSONObject("data").getJSONObject("upload");
                log.info("put file to fileAPI-V2:" + object);
                object.put("name", fileName);
                return object;
            }
        } catch (Exception ioException) {
            ioException.printStackTrace();
            log.error("put file error:{}", ioException);
            throw new RuntimeException("上传文件失败,上传路径为:" + fileGqlUrl);
        }
        return null;

    }


}
