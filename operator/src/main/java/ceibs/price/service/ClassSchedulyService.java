package ceibs.price.service;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.ClassInfo;
import ceibs.price.bean.bo.GroupUser;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.dao.ClassInfoDao;
import ceibs.price.dao.GroupUserDao;
import ceibs.price.dao.PriceSessionDao;
import ceibs.price.dao.PriceSessionViewDao;
import ceibs.price.utils.DateUtil;
import com.alibaba.fastjson.JSONArray;
import jakarta.annotation.Resource;
import jakarta.transaction.Transactional;
import org.apache.http.client.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * @Author: RCC
 * @CreateTime:2025-08-18 10:49
 * @Description 排课管理
 */
@Service
public class ClassSchedulyService {


    @Resource
    ClassInfoDao classInfoDao;
    @Resource
    GroupUserDao groupUserDao;
    @Resource
    PriceSessionDao priceSessionDao;
    @Resource
    PriceSessionViewDao priceSessionViewDao;


    /**
     * 新增课程安排
     */
    @Transactional
    public ClassInfo addClass(ClassInfo newClassInfo) {
        List<GroupUser> groupUsers = newClassInfo.getGroupUsers();
        // 检查新开课程时间段 查询是否有重叠的课程时间
        List<ClassInfo> classInfos = classInfoDao.findOverlappingClasses(newClassInfo.getStartTime(), newClassInfo.getEndTime(), newClassInfo.getTeacher());
        if (classInfos.size() > 0) {
            // 存在重叠课程
            throw new RuntimeException("当前创建课程时间重复，请重新确认时间");
        }
        // todo 检查是否有 组员同时活跃在多门课程

        //
        ClassInfo save = classInfoDao.save(newClassInfo);
        for (GroupUser groupUser : groupUsers) {
            groupUser.setClassId(save.getId());
            groupUserDao.save(groupUser);
        }
        return save;
    }

    /**
     * 修改课程安排
     */
    @Transactional
    public ClassInfo updateClass(ClassInfo newClassInfo) {
        List<GroupUser> groupUsers = newClassInfo.getGroupUsers();
        //
        Optional<ClassInfo> byId = classInfoDao.findById(newClassInfo.getId());
        if (byId.isEmpty()) {
            throw new RuntimeException("课程不存在");
        }
        // 检查修改开课程时间段 查询是否有重叠的课程时间
        List<ClassInfo> classInfos = classInfoDao.findOverlappingClasses(newClassInfo.getStartTime(), newClassInfo.getEndTime(), newClassInfo.getTeacher());
        if (classInfos.size() > 0) {
            for (ClassInfo classInfo : classInfos) {
                if (!classInfo.getId().equals(newClassInfo.getId())) {
                    throw new RuntimeException("当前修改课程时间重复，请重新确认时间");
                }
            }
        }
        // todo 检查是否有 组员同时活跃在多门课程


        classInfoDao.save(newClassInfo);
        groupUserDao.saveAll(groupUsers);
        return newClassInfo;

    }


    /**
     * 获取某个老师 排课表
     * @param teacherOpenid
     */
    public List<ClassInfo> getClassByTeacher(String teacherOpenid, Date startTime,Date endTime) {
        return classInfoDao.findOverlappingClassesStart( startTime, endTime,teacherOpenid);
    }

}
