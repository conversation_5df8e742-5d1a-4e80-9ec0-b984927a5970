package ceibs.price;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Controller
public class StatusController {

    private static Long LAST_TIMESTAMP = System.currentTimeMillis();

    private static String version;

    @GetMapping("/version")
    @ResponseBody
    public Object version() {
        return new HashMap() {{
            put("version", getClassVersion());
        }};
    }

    @RequestMapping("/status")
    @ResponseBody
    public Object status(HttpServletRequest request) throws Exception {
        return getStatus(request);
    }

    @RequestMapping("/healthz")
    @ResponseBody
    public Object healthz(HttpServletRequest request) throws Exception {
        return getStatus(request);
    }

    public Object getStatus(HttpServletRequest request) throws Exception {
        Map status = new HashMap();
        Map headers = new HashMap();
        Enumeration es = request.getHeaderNames();
        while (es.hasMoreElements()) {
            String name = es.nextElement().toString();
            if (name.toUpperCase().startsWith("X-")) {
                headers.put(name, request.getHeader(name));
            }
        }
        headers.put("remote", request.getRemoteAddr() + "/" + request.getRemoteHost() + "/" + request.getRemotePort());
        status.put("headers", headers);
        status.put("version", getClassVersion());
        status.put("rt", getSystemRuntime());
        status.put("last", printDate(LAST_TIMESTAMP));
        status.put("timestamp", printDate(new Date().getTime()));
        status.put("requestURL", request.getRequestURL().toString());
        return status;
    }

    public static String getClassVersion(String pattern) {
        Date created = new Date();
        ClassLoader cl = Thread.currentThread().getContextClassLoader();
        try {
            String path = cl.getResource("application.properties").getPath();
            if ("\\".equalsIgnoreCase(File.separator)) {
                path = path.replaceAll("/", "\\\\");
                if (path.substring(0, 1).equals("\\"))
                    path = path.substring(1);
            } else if ("/".equalsIgnoreCase(File.separator)) {
                path = path.replaceAll("\\\\", "/");
            }
            if (null != path) {
                long time = Files.readAttributes(Paths.get(path), BasicFileAttributes.class).creationTime().toMillis();
                created = new Date(time);
            }
        } catch (Exception e) {
            //log.error("Failed to obtain class attributes.", e);
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(created);
    }

    public static String getClassVersion() {
        if (version == null)
            return (version = getClassVersion("yyyyMMdd"));
        return version;
    }

    private static String printDate(Long timestamp) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
            return "(" + (System.currentTimeMillis() - timestamp) + ")" + sdf.format(new Date(timestamp));
        } catch (Exception e) {
            return timestamp == null ? null : timestamp.toString();
        }
    }

    private Map getSystemRuntime() {
        try {
            Map rt = new HashMap();
            Runtime runtime = Runtime.getRuntime();
            rt.put("vm_total", runtime.totalMemory() / (1024 * 1024));
            rt.put("vm_free", runtime.freeMemory() / (1024 * 1024));
            rt.put("vm_max", runtime.maxMemory() / (1024 * 1024));
            ThreadGroup parentThread;
            int totalThread = 0;
            for (parentThread = Thread.currentThread().getThreadGroup(); parentThread
                    .getParent() != null; parentThread = parentThread.getParent()) {
                totalThread = parentThread.activeCount();
            }
            rt.put("vm_total_thread", totalThread);
            return rt;
        } catch (Exception e) {
            return new HashMap();
        }
    }








}
