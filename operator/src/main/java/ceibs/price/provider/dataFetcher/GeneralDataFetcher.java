package ceibs.price.provider.dataFetcher;


import ceibs.price.bean.bo.Case;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.service.CaseService;
import ceibs.price.service.CopilotApiService;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.CertifiedConnector;
import com.alibaba.fastjson.JSONObject;
import graphql.schema.DataFetcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GeneralDataFetcher {


    @Autowired
    CopilotApiService copilotApiService;
    @Autowired
    PriceSessionService priceSessionService;
    @Autowired
    CaseService caseService;
    @Autowired
    McpService mcpService;

    public DataFetcher generateToCPrompt() {
        return dataFetchingEvn -> {
            PriceSessionView priceSessionView = priceSessionService.updatePriceSessionView(dataFetchingEvn.getArgument("entity"));
            JSONObject data = JSONObject.parseObject(priceSessionView.getData());

            String requestToken = CertifiedConnector.getRequestToken();
            // 根据data 选择项，进行提示词生成
            JSONObject prompt = new JSONObject();
            JSONObject jsonObject = copilotApiService.generateToCPrompt(data.getString("checkData"),null,mcpService.getCache("analyze_competition"), requestToken);
            prompt.put("str", jsonObject.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text"));
            /*prompt.put("str","请帮我根据以下诉求进行消费者调研，并输出分析报告：\n" +
                    "\n" +
                    "背景：丽江金茂璞修·雪山酒店遭遇了比较大的经营困境，均入住率不到三成，远低于丽江其他高端酒店的一半水平。\n" +
                    "\n" +
                    "酒店优势：酒店硬件一流，在雪山之中自然景观无与伦比\n" +
                    "酒店劣势：之前定位模糊，既想做超五星又想兼顾养生度假，却没抓住核心吸引力，游客来了觉得‘豪华但没特色’；加上离古城远，缺少持续客流\n" +
                    "\n" +
                    "现在我有3个核心诉求：\n" +
                    "1. 现在酒店希望利用雪山资源，做“天文主题”的自身定位，通过天文主题重构消费者价值感知，突破区位劣势；请帮我找到哪些消费者群体会对天文主题感兴趣，并有意愿入住酒店\n" +
                    "\n" +
                    "2. 请结合这个定位，与丽江古城的其他知名度假酒店如悦榕庄、安缦等国际品牌相对比：提取人有我无 (竞争劣势)，人有我有（同质化），人有我优（直面竞争），人无我有（差异化）的价值要素；\n" +
                    "\n" +
                    "3. 请帮我分析这些消费者群体，分别对2中分析出的哪些价值要素敏感；“人有我无”的部分是否要投入精力追赶，如果不做任何动作，是否会对支付意愿打折扣；以及“人有我优”、“人无我有”的核心优势有多大的溢价，用户的支付意愿有多强");*/
            return prompt;
        };
    }


    public DataFetcher generate() {
        return dataFetchingEvn -> {
            String type = dataFetchingEvn.getArgument("type");
            String requestToken = CertifiedConnector.getRequestToken();
            Object data = dataFetchingEvn.getArgument("data");
            JSONObject result = new JSONObject();
            switch (type) {
                case "generateBrainstormingUserType":
                    result.put("text", copilotApiService.generateBrainstormingUserType(data.toString(), requestToken));
                    return result;
                case "generateBrainstormingTags":
                    JSONObject jsonObject = JSONObject.parseObject(data.toString());
                    Case firstByActiveIsTrue = caseService.findFirstByActiveIsTrue();
                    result.put("text", copilotApiService.generateBrainstormingTags(firstByActiveIsTrue.getText(), jsonObject.getString("ysc"), requestToken));
                    return result;
                case "generateSelfIntroduce":
                    result.put("text", copilotApiService.generateSelfIntroduce(data.toString(), requestToken));
                    return result;
                case "":
                    result.put("text", copilotApiService.generateSelfIntroduce(data.toString(), requestToken));
                    return result;
                default:
                    return null;
            }

        };
    }
}
