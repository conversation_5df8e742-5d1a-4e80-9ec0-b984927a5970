package ceibs.price.provider.dataFetcher;

import java.util.*;


import ceibs.price.bean.User;
import ceibs.price.bean.bo.ClassInfo;
import ceibs.price.bean.bo.GroupUser;
import ceibs.price.bean.bo.PriceSession;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.CurrentClassViewService;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.CertifiedConnector;
import ceibs.price.utils.mcpTool.DynamicToolExecutorFactory;
import ceibs.price.utils.mcpTool.ToolExecutor;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import graphql.schema.DataFetcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class PriceFetcher {

    @Autowired
    McpService mcpService;
    @Autowired
    private DynamicToolExecutorFactory toolExecutorFactory;
    @Autowired
    private PriceSessionService priceSessionService;
    @Autowired
    private CurrentClassViewService currentClassViewService;


    public DataFetcher tools() {
        return dataFetchingEvn -> {

            return mcpService.getAllTools();
        };
    }

    public DataFetcher executeTool() {
        return dataFetchingEvn -> {
            String requestToken = CertifiedConnector.getRequestToken();
            User opt = CertifiedConnector.getOpt();


            String toolName = dataFetchingEvn.getArgument("toolName");
            String sessionId = dataFetchingEvn.getArgument("sessionId");
            Boolean autoTask = dataFetchingEvn.getArgument("autoTask");
            Map<String, Object> args = dataFetchingEvn.getArgument("args");
            ToolExecutor executor = toolExecutorFactory.getExecutor(toolName);
            //
            log.info("tool agrs:{}", args);

            String classId = null;
            PriceSession oldSession = priceSessionService.findBySessionId(sessionId);
            if (oldSession != null) {
                classId = oldSession.getClassId();
            } else {
                // 当前会话第一次调用，则检查当前是否有活跃课程
                List<ClassInfo> currentClass = currentClassViewService.findCurrentClass();
                // 确认当前人 属于哪个活跃课程
                classInfo:for (ClassInfo aClass : currentClass) {
                    List<GroupUser> groupUsers = aClass.getGroupUsers();
                    for (GroupUser groupUser : groupUsers) {
                        List<User> users = groupUser.getUsers();
                        for (User user : users) {
                            if (user.getOpenId().equals(opt.getOpenId())) {
                                classId = aClass.getId();
                                break classInfo;
                            }
                        }
                    }
                }
            }
            // 封装返回值
            ToolResponse toolResponse = (ToolResponse) executor.execute(sessionId, classId, args, requestToken, opt, autoTask);
            return toolResponse;
        };
    }

    public DataFetcher closeAutoTask() {
        return dataFetchingEvn -> {
            String sessionId = dataFetchingEvn.getArgument("sessionId");
            return priceSessionService.closeAutoTask(sessionId);
        };
    }


    public DataFetcher priceSessionInfo() {
        return dataFetchingEvn -> {
            String session = dataFetchingEvn.getArgument("sessionId");
            PriceSession priceSession = priceSessionService.getAllPriceSessionView(session);
            return priceSession;
        };
    }

    public DataFetcher priceSessionVIew() {
        return dataFetchingEvn -> {
            String viewId = dataFetchingEvn.getArgument("viewId");
            PriceSessionView priceSessionView = priceSessionService.getPriceSessionView(viewId);
            return priceSessionView;
        };
    }

    /**
     * 学生标记提交这份页面作为结果给老师
     *
     * @return
     */
    public DataFetcher submitPriceSessionView() {
        return dataFetchingEvn -> {
            /**
             * todo 如果会话创建时候没有关联课程，则提示需要新开会重新生成
             */
            /*
                todo  只有在课程时间范围内，应该才能提交
                todo  同一个报告只能提交一份
             */
            List<ClassInfo> currentClass = currentClassViewService.findCurrentClass();
            if (currentClass.size() == 0) {
                throw new RuntimeException("当前没有活跃课程,无需提交报告");
            }
            String viewId = dataFetchingEvn.getArgument("viewId");
            PriceSessionView priceSessionView = priceSessionService.getPriceSessionView(viewId);
            String sessionId = priceSessionView.getSessionId();
            PriceSession priceSession = priceSessionService.findBySessionId(sessionId);
            priceSessionService.submitPriceSession(priceSession, priceSessionView);
            return priceSessionView;
        };
    }


    public DataFetcher atypicaReportExecutor() {
        return dataFetchingEvn -> {
            String viewId = dataFetchingEvn.getArgument("viewId");
            String report = dataFetchingEvn.getArgument("report");
            PriceSessionView priceSessionView = priceSessionService.getPriceSessionView(viewId);
            User opt = CertifiedConnector.getOpt();

            String classId = null;
            PriceSession oldSession = priceSessionService.findBySessionId(priceSessionView.getSessionId());
            if (oldSession != null) {
                classId = oldSession.getClassId();
            } else {
                // 当前会话第一次调用，则检查当前是否有活跃课程
                List<ClassInfo> currentClass = currentClassViewService.findCurrentClass();
                // 确认当前人 属于哪个活跃课程
                classInfo:
                for (ClassInfo aClass : currentClass) {
                    List<GroupUser> groupUsers = aClass.getGroupUsers();
                    for (GroupUser groupUser : groupUsers) {
                        List<User> users = groupUser.getUsers();
                        for (User user : users) {
                            if (user.getOpenId().equals(opt.getOpenId())) {
                                classId = aClass.getId();
                                break classInfo;
                            }
                        }
                    }
                }
            }

            ToolExecutor executor = toolExecutorFactory.getExecutor("atypica_report_analyse");
            JSONObject data = new JSONObject();
            data.put("report", report);
            List<Map<String, Object>> datas = (List<Map<String, Object>>) executor.execute(priceSessionView.getSessionId(), classId, data, CertifiedConnector.getRequestToken(), CertifiedConnector.getOpt(), false);
            //
            JSONObject viewData = JSONObject.parseObject(priceSessionView.getData());
            viewData.put("reportExtraction", datas);
            priceSessionView.setData(viewData.toJSONString());
            priceSessionService.updatePriceSessionViewByBean(priceSessionView);
            return priceSessionView;
        };
    }

    public DataFetcher updatePriceSessionView() {
        return dataFetchingEvn -> {
            PriceSessionView priceSessionView = priceSessionService.updatePriceSessionView(dataFetchingEvn.getArgument("entity"));
            return priceSessionView;
        };

    }


}
