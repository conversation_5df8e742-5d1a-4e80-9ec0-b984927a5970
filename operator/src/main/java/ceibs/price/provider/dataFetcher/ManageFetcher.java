package ceibs.price.provider.dataFetcher;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.Case;
import ceibs.price.bean.bo.CaseFile;
import ceibs.price.bean.bo.PriceSession;
import ceibs.price.service.CaseService;
import ceibs.price.service.ClassSchedulyService;
import ceibs.price.service.CurrentClassViewService;
import ceibs.price.service.McpService;
import ceibs.price.utils.CertifiedConnector;
import graphql.schema.DataFetcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


@Slf4j
@Service
public class ManageFetcher {

    @Autowired
    CaseService caseService;
    @Autowired
    CurrentClassViewService currentClassViewService;
    @Autowired
    ClassSchedulyService classSchedulyService;
    @Autowired
    McpService mcpService;






    /*     案例管理     */
    public DataFetcher cases() {
        return dataFetchingEvn -> {
            return caseService.getAllCase();
        };
    }

    public DataFetcher addCase() {
        return dataFetchingEvn -> {
            User opt = CertifiedConnector.getOpt();
            Map<String, Object> arguments = dataFetchingEvn.getArguments();
            Map<String, Object> map = (Map<String, Object>) arguments.get("entity");
            Case caseBo = new Case();
            BeanUtils.populate(caseBo, map);
            caseBo.setId(UUID.randomUUID().toString());
            caseBo.setCreatedAt(new Date());
            caseBo.setCreator(opt.getOpenId());
            //
            List<LinkedHashMap<String, Object>> caseFilesHashMap = (List<LinkedHashMap<String, Object>>) map.get("caseFiles");
            // to List<CaseFile>
            List<CaseFile> caseFiles = new ArrayList<>();
            for (LinkedHashMap<String, Object> caseFileHashMap : caseFilesHashMap) {
                CaseFile caseFile = new CaseFile();
                BeanUtils.populate(caseFile, caseFileHashMap);
                caseFile.setId(UUID.randomUUID().toString());
                caseFile.setCaseId(caseBo.getId());
                caseFile.setActive(true);
                caseFile.setCreator(opt.getOpenId());
                caseFile.setCreatorName(opt.getName());
                caseFile.setCreatedAt(new Date());
                caseFiles.add(caseFile);
            }
            caseBo.setCaseFiles(caseFiles);
            Case aCase = caseService.addCase(caseBo);
            return caseBo;
        };
    }
    public DataFetcher updateCase() {
        return dataFetchingEvn -> {
            User opt = CertifiedConnector.getOpt();
            Map<String, Object> arguments = dataFetchingEvn.getArguments();
            Map<String, Object> map = (Map<String, Object>) arguments.get("entity");
            Case caseBo = new Case();
            BeanUtils.populate(caseBo, map);
            caseBo.setUpdatedAt(new Date());
            caseBo.setCreator(opt.getOpenId());

            //
            List<LinkedHashMap<String, Object>> caseFilesHashMap = (List<LinkedHashMap<String, Object>>) map.get("caseFiles");
            // to List<CaseFile>
            List<CaseFile> caseFiles = new ArrayList<>();
            for (LinkedHashMap<String, Object> caseFileHashMap : caseFilesHashMap) {
                CaseFile caseFile = new CaseFile();
                BeanUtils.populate(caseFile, caseFileHashMap);
                caseFile.setCaseId(caseBo.getId());
                caseFile.setActive(true);
                caseFile.setUpdatedAt(new Date());
                caseFile.setCreator(opt.getOpenId());
                caseFile.setCreatorName(opt.getName());
                caseFiles.add(caseFile);
            }

            Case oldCase = caseService.findCaseById(caseBo.getId());
            boolean oldActive = oldCase.getActive();
            Case newCase = caseService.updateCase(oldCase,caseBo,caseFiles);
            newCase.setCaseFiles(caseFiles);

            if (oldActive!=(newCase.getActive())&&newCase.getActive()) {
                // 更新 竞争分析缓存
                mcpService.refreshCache("analyze_competition");
                log.info("调用异步 竞争分析缓存 完成");
            }
            return newCase;
        };
    }

    public DataFetcher deleteCase() {
        return dataFetchingEvn -> {
            Object id = dataFetchingEvn.getArgument("id");
            return caseService.delCase(id.toString());
        };
    }

    public DataFetcher updateCaseFile() {
        return dataFetchingEvn -> {
            User opt = CertifiedConnector.getOpt();
            Map<String, Object> arguments = dataFetchingEvn.getArguments();
            Map<String, Object> map = (Map<String, Object>) arguments.get("entity");
            CaseFile caseFile = new CaseFile();
            BeanUtils.populate(caseFile, map);
            caseFile.setUpdatedAt(new Date());
            caseFile.setCreator(opt.getOpenId());
            caseService.updateCaseFile(caseFile);
            return caseFile;
        };
    }

    public DataFetcher deleteCaseFile() {
        return dataFetchingEvn -> {
            Object id = dataFetchingEvn.getArgument("id");
            return caseService.delCaseFile(id.toString());
        };
    }


    /*     课程情况预览     */
    public DataFetcher currentClass(){
        return dataFetchingEvn -> {
            User opt = CertifiedConnector.getOpt();
            return currentClassViewService.findCurrentClassByTeacher(opt.getOpenId());
        };
    }

    public DataFetcher groupUsers(){
        return dataFetchingEvn -> {
            User opt = CertifiedConnector.getOpt();
            String classId = (String)dataFetchingEvn.getArgument("classId");
            return currentClassViewService.getCurrentGroup(classId);
        };
    }

    public DataFetcher studentClassInfo(){
        return dataFetchingEvn -> {
            String classId = (String)dataFetchingEvn.getArgument("classId");
            String openid = (String)dataFetchingEvn.getArgument("openid");
            Date startTime = (Date)dataFetchingEvn.getArgument("startTime");
            Date endTime = (Date)dataFetchingEvn.getArgument("endTime");
            User opt = CertifiedConnector.getOpt();
            PriceSession priceSession = currentClassViewService.getPriceSessionByUser(classId, openid, startTime, endTime);
            List<PriceSession> priceSessionByUser =new ArrayList<>();
            priceSessionByUser.add(priceSession);
            return priceSessionByUser;
        };
    }


    public DataFetcher myClassPlan(){
        return dataFetchingEvn -> {
            User opt = CertifiedConnector.getOpt();
            Date startTime = (Date)dataFetchingEvn.getArgument("startTime");
            Date endTime = (Date)dataFetchingEvn.getArgument("endTime");
            return  classSchedulyService.getClassByTeacher(opt.getOpenId(), startTime, endTime);
        };
    }








}
