package ceibs.price.provider;

import ceibs.price.provider.dataFetcher.ManageFetcher;
import ceibs.price.provider.dataFetcher.GeneralDataFetcher;
import ceibs.price.provider.dataFetcher.PriceFetcher;
import ceibs.price.utils.graphql.GraphQLConfig;
import graphql.schema.idl.TypeRuntimeWiring;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static graphql.schema.idl.TypeRuntimeWiring.newTypeWiring;

@Component
public class PriceProvider {

    @Autowired
    private PriceFetcher fetcher;
    @Autowired
    private ManageFetcher caseFileFetcher;
    @Autowired
    GeneralDataFetcher generalDataFetcher;

   public TypeRuntimeWiring.Builder buildQueryRuntimeWiring() {
        return newTypeWiring("Query")
                .dataFetcher("tools", fetcher.tools())
                .dataFetcher("priceSessionInfo", fetcher.priceSessionInfo())
                .dataFetcher("priceSessionVIew", fetcher.priceSessionVIew())
                .dataFetcher("atypicaReportExecutor", fetcher.atypicaReportExecutor())
                .dataFetcher("cases", caseFileFetcher.cases())
                .dataFetcher("currentClass", caseFileFetcher.currentClass())
                .dataFetcher("groupUsers", caseFileFetcher.groupUsers())
                .dataFetcher("studentClassInfo", caseFileFetcher.studentClassInfo())
                .dataFetcher("myClassPlan", caseFileFetcher.myClassPlan())
                ;

    }


    public TypeRuntimeWiring.Builder buildMutationRuntimeWiring() {
        TypeRuntimeWiring.Builder builder = newTypeWiring("Mutation")
                .dataFetcher("executeTool", fetcher.executeTool())
                .dataFetcher("updatePriceSessionView", fetcher.updatePriceSessionView())
                .dataFetcher("generateToCPrompt", generalDataFetcher.generateToCPrompt())
                .dataFetcher("generate", generalDataFetcher.generate())
                .dataFetcher("closeAutoTask", fetcher.closeAutoTask())
                .dataFetcher("addCase", caseFileFetcher.addCase())
                .dataFetcher("updateCase", caseFileFetcher.updateCase())
                .dataFetcher("deleteCase", caseFileFetcher.deleteCase())
                .dataFetcher("updateCaseFile", caseFileFetcher.updateCaseFile())
                .dataFetcher("deleteCaseFile", caseFileFetcher.deleteCaseFile())
                .dataFetcher("submitPriceSessionView", fetcher.submitPriceSessionView());
        return builder;

    }


    @Autowired
    private GraphQLConfig graphQLConfig;

    @PostConstruct
    private void init() {
        String key = this.getClass().getName();
        graphQLConfig.builderConcurrentMap.put(key + "-Query", buildQueryRuntimeWiring());
        graphQLConfig.builderConcurrentMap.put(key + "-Mutation", buildMutationRuntimeWiring());
    }

}