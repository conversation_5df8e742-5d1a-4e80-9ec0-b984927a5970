package ceibs.price.controller;

import ceibs.price.service.McpService;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * MCP控制器
 * 提供REST API端点来访问MCP功能
 */
@RestController
@RequestMapping("/api/mcp")
public class McpController {
    
    private static final Logger logger = Logger.getLogger(McpController.class.getName());
    
    /*private final McpService mcpService;
    
    @Autowired
    public McpController(McpService mcpService) {
        this.mcpService = mcpService;
        logger.info("MCP控制器已初始化");
    }
    */
    @Autowired
    private McpService mcpService;
    /**
     * 获取所有可用的MCP工具
     * @return 工具列表
     */
    @GetMapping("/tools")
    public ResponseEntity<List<McpSchema.Tool>> getAllTools() {
        logger.info("请求获取所有MCP工具");
        List<McpSchema.Tool> tools = mcpService.getAllTools();
        return ResponseEntity.ok(tools);
    }
    
    /**
     * 获取所有可用的MCP资源
     * @return 资源列表
     */
    @GetMapping("/resources")
    public ResponseEntity<List<McpSchema.Resource>> getAllResources() {
        logger.info("请求获取所有MCP资源");
        List<McpSchema.Resource> resources = mcpService.getAllResources();
        return ResponseEntity.ok(resources);
    }
    
    /**
     * 执行MCP工具调用
     * @param toolId 工具ID
     * @param parameters 参数
     * @return 工具调用结果
     */
    @PostMapping("/tools/{toolId}/execute")
    public ResponseEntity<Object> executeToolCall(
            @PathVariable String toolId,
            @RequestBody Map<String, Object> parameters) {
        
        logger.info("请求执行MCP工具: " + toolId);
        Object result = mcpService.executeToolCall(toolId, parameters);
        
        if (result == null) {
            return ResponseEntity.badRequest().body(Map.of("error", "工具执行失败"));
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 健康检查端点
     * @return 健康状态信息
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        // 获取工具列表来验证MCP连接是否正常
        List<McpSchema.Tool> tools = mcpService.getAllTools();
        
        Map<String, Object> health = Map.of(
            "status", "UP",
            "tools_count", tools.size(),
            "message", "MCP客户端运行正常"
        );
        
        return ResponseEntity.ok(health);
    }
} 