package ceibs.price.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;


@Log4j2
public class DataUtil {



    public static String getDataByGraphql(String url,String query,String variables) throws IOException {
        log.info("url:{}",url);
        HttpClient httpclient = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);
        JSONObject request = new JSONObject();
        request.put("query", query);
        String realVariables = variables.replace("${offset}", "0").replace("${first}", "1");
        request.put("variables", JSON.parseObject(realVariables));
        // Request parameters and other properties.
        StringEntity requestEntity = new StringEntity(request.toString(), "UTF-8");
        requestEntity.setContentType("application/json");
        post.setEntity(requestEntity);
        // Execute and get the response.
        HttpResponse response = httpclient.execute(post);
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            String result = EntityUtils.toString(entity);
            return result;
        }
        return null;

    }
}
