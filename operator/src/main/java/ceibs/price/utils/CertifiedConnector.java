package ceibs.price.utils;

import ceibs.price.ValueResolver;
import ceibs.price.bean.User;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.oltu.oauth2.common.utils.OAuthUtils;
import java.util.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;



@Component
@Slf4j
public class CertifiedConnector {


    private static CertifiedConnector certifiedConnector;

    ValueResolver valueResolver;


    @Resource
    ValueResolver superValueResolver;



    @PostConstruct
    public void init() {
        certifiedConnector = this;
        certifiedConnector.valueResolver = superValueResolver;
    }


    public static String introspect() throws Exception {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String uri= UrlUtil.getUrl(certifiedConnector.valueResolver.getOAuthIntrospectURL());

        Assert.hasText(uri, "OAUTH2_URI not set");
        Assert.hasText(certifiedConnector.valueResolver.getOAuthClientId(), "OAUTH2_CLIENT_ID not set");
        Assert.hasText(certifiedConnector.valueResolver.getOAuthClientSecret(), "OAUTH2_CLIENT_SECRET not set");

        String token = OAuthUtils.getAuthHeaderField(request.getHeader("Authorization"));

        if (StringUtils.isBlank(token))
            token = request.getParameter("access_token");


        Assert.hasText(token, "Missing required args '[access_token]'");

        String plainCreds = certifiedConnector.valueResolver.getOAuthClientId() + ":" + certifiedConnector.valueResolver.getOAuthClientSecret();
        byte[] plainCredsBytes = plainCreds.getBytes();
        String base64Creds = Base64.getEncoder().encodeToString(plainCredsBytes);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Basic " + base64Creds);

        HttpEntity<String> httpEntity = new HttpEntity<>("body", headers);
        String url = String.format("%s?token=%s", uri, token);
        if (uri.indexOf("/oauth2/introspect") > -1)
            url = String.format("%s?token=%s", uri, token);
        try {
            log.info("introspect base64Creds:" + base64Creds);
            log.info("introspect url:" + url);
            ResponseEntity responseEntity = new RestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);
            Map introspection = new ObjectMapper().readValue(responseEntity.getBody().toString(), Map.class);
            if (introspection != null && (Boolean) introspection.get("active")) {
                if (introspection.containsKey("sub") && introspection.get("sub") != null) {
                    request.getSession().setAttribute("sub", introspection.get("sub"));
                }
                if (introspection.containsKey("name") && introspection.get("name") != null) {
                    request.getSession().setAttribute("name", introspection.get("name"));
                }
                return introspection.containsKey("tenant") ? introspection.get("tenant").toString() : request.getParameter("domain");
            } else {
                throw new Exception(JSON.toJSONString(introspection));
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }
    public static String introspect(String token) throws Exception {
        String uri= UrlUtil.getUrl(certifiedConnector.valueResolver.getOAuthIntrospectURL());
        Assert.hasText(uri, "OAUTH2_URI not set");
        Assert.hasText(token, "Missing required args '[access_token]'");

        String plainCreds = certifiedConnector.valueResolver.getOAuthClientId() + ":" + certifiedConnector.valueResolver.getOAuthClientSecret();
        byte[] plainCredsBytes = plainCreds.getBytes();
        String base64Creds = Base64.getEncoder().encodeToString(plainCredsBytes);


        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Basic " + base64Creds);

        HttpEntity<String> httpEntity = new HttpEntity<>("body", headers);
        String url = String.format("%s?token=%s", uri, token);
        if (uri.indexOf("/oauth2/introspect") > -1)
            url = String.format("%s?token=%s", uri, token);
        try {
            log.info("introspect base64Creds:" + base64Creds);
            log.info("introspect url:" + url);
            ResponseEntity responseEntity = new RestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);
            Map introspection = new ObjectMapper().readValue(responseEntity.getBody().toString(), Map.class);
            if (introspection != null && (Boolean) introspection.get("active")) {
                return introspection.containsKey("tenant") ? introspection.get("tenant").toString() :"";
            } else {
                throw new Exception(JSON.toJSONString(introspection));
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    public static User getOpt(){
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String id=null==request.getSession().getAttribute("sub")?"UNKONW":request.getSession().getAttribute("sub").toString();
        String name=null==request.getSession().getAttribute("name")?"UNKONW":request.getSession().getAttribute("name").toString();
        return new User(id,name,"");
    }
    public static String getRequestToken(){
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String token = OAuthUtils.getAuthHeaderField(request.getHeader("Authorization"));
        if (StringUtils.isBlank(token)) token = request.getParameter("access_token");
        Assert.hasText(token, "Missing required args '[access_token]'");
        return token;

    }

}
