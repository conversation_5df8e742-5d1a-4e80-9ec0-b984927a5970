package ceibs.price.utils;

import com.google.common.cache.*;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Map;
import java.util.concurrent.*;

/**
 * Always on in memory cache combined 2 loading caches: primary cache & expired
 * cache if not present in primary cache, use expired
 * 
 * <AUTHOR>
 * @since 2017/07/19
 */
public class AlwaysOnLoadingCache<K, V> implements LoadingCache<K, V> {

    private static Log log = LogFactory.getLog(AlwaysOnLoadingCache.class);

    /**
     * primary cache
     */
    private LoadingCache<K, V> cache;
    /**
     * expired cache for 24 hours
     */
    private LoadingCache<K, V> cacheExpired;
    /**
     * async loader
     */
    private final Map<K, ExecutorService> asyncLoaders = new ConcurrentHashMap<>();


    /**
     * create
     * @param maximumSize
     * @param expire
     * @param expireTimeUnit
     * @param loader
     */
    public AlwaysOnLoadingCache(int maximumSize, int expire, TimeUnit expireTimeUnit, CacheLoader<K, V> loader) {

        cache = CacheBuilder.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterWrite(expire, expireTimeUnit)
                .removalListener((RemovalListener<K, V>) notification -> {
                    log.info(String.format("[AlwaysOnLoadingCache] primary cache moved to expired cache, reason: %s", notification.getCause()));
                    cacheExpired.put(notification.getKey(), notification.getValue());
                    // eager load only if expired or invalided.
                    if(RemovalCause.EXPIRED.equals(notification.getCause()) || RemovalCause.EXPLICIT.equals(notification.getCause())) {
                        getAsync(notification.getKey());
                    }
                })
                .build(loader);

        cacheExpired = CacheBuilder.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterWrite(24, TimeUnit.HOURS)
                .build(new CacheLoader<K, V>() {
                    @Override
                    public V load(K key) throws Exception {
                        return cache.get(key);
                    }
                });
    }


   
    public V get(K key) throws ExecutionException {
        V value = this.cache.getIfPresent(key);
 //       System.out.println("cache1"+value);
        if (null == value) {
            try {
                value = this.cacheExpired.get(key);
//                System.out.println("cache2"+value.toString());
            } catch (ExecutionException e) {
                log.warn("[AlwaysOnLoadingCache] getExpired exception for key:" + key, e);
            }
            // use expired cache, trigger primary cache to update
            this.getAsync(key);
        }
        if (null != value) {
            return value;
        }
        // even expired cache expired... trigger primary cache.get
        log.warn("[AlwaysOnLoadingCache] even expired cache-expired for key:" + key + ", get directly (and slow)");
        return this.cache.get(key);
    }

    private synchronized Future<V> getAsync(final K key) {
        ExecutorService asyncLoader = this.asyncLoaders.get(key);
        if (null == asyncLoader) {
            // NOTICE: one size queue
            asyncLoader = new ThreadPoolExecutor(1, 1,
                    0L, TimeUnit.MILLISECONDS,
                    new ArrayBlockingQueue<Runnable>(1, true));
            // create executor for each tenant
            this.asyncLoaders.put(key, asyncLoader);
        }
        int queueSize = ((ThreadPoolExecutor) asyncLoader).getQueue().size();
        // already queue for refresh, skip load request
        if (queueSize > 0) {
            return null;
        }
        try {
            return asyncLoader.submit(new Callable<V>() {
                @Override
                public V call() {
                    try {
                        // eager load cache
                        long then = System.currentTimeMillis();
                        V result = cache.get(key);
                        if (log.isInfoEnabled()) {
                            log.info(String.format("[AlwaysOnLoadingCache] async get complete. cost %dms", System.currentTimeMillis() - then));
                        }
                        return result;
                    } catch (ExecutionException ignored) {
                        log.info("[AlwaysOnLoadingCache] async get exception.", ignored);
                        return null;
                    }
                }
            });
        } catch (RejectedExecutionException ignored) {
            if (log.isInfoEnabled()) {
                log.info(String.format("[CodeServices] submit async get task RejectedExecutionException ignored for key:%s", key), ignored);
            }
            return null;
        }
    }

    @Override
    public V getUnchecked(K key) {
        return this.cache.getUnchecked(key);
    }

    @Override
    public ImmutableMap<K, V> getAll(Iterable<? extends K> keys) throws ExecutionException {
        return this.cache.getAll(keys);
    }

    @Override
    public V apply(K key) {
        return this.cache.apply(key);
    }

    @Override
    public void refresh(K key) {
        this.cache.refresh(key);
 
       
    }

    @Override
    public V getIfPresent(Object key) {
    	 V v = this.cache.getIfPresent(key);
         if (null != v) {
             return v;
         }
         try {
             @SuppressWarnings("unchecked")
             K k = (K) key;
             this.getAsync(k);
         } catch (Exception ignored) {
         }
         return this.cacheExpired.getIfPresent(key);
    }

    @Override
    public V get(K key, Callable<? extends V> valueLoader) throws ExecutionException {
        return this.cache.get(key, valueLoader);
    }

    @Override
    public ImmutableMap<K, V> getAllPresent(Iterable<?> keys) {
        return this.cache.getAllPresent(keys);
    }

    @Override
    public void put(K key, V value) {
        this.cache.put(key, value);
    }

    @Override
    public void putAll(Map<? extends K, ? extends V> m) {
        this.cache.putAll(m);
    }

    @Override
    public void invalidate(Object key) {
        this.cache.invalidate(key);
    }

    @Override
    public void invalidateAll(Iterable<?> keys) {
        this.cache.invalidateAll(keys);
    }

    @Override
    public void invalidateAll() {
        this.cache.invalidateAll();
    }

    @Override
    public long size() {
        return this.cache.size();
    }

    @Override
    public CacheStats stats() {
        return this.cache.stats();
    }

    @Override
    public ConcurrentMap<K, V> asMap() {
        return this.cache.asMap();
    }

    @Override
    public void cleanUp() {
        this.cache.cleanUp();
        this.cacheExpired.cleanUp();
        for(ExecutorService executor : this.asyncLoaders.values()) {
            executor.shutdownNow();
        }
    }


}
