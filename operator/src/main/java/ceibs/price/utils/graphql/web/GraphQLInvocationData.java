package ceibs.price.utils.graphql.web;

import graphql.Assert;
import graphql.PublicSpi;

import java.util.Collections;
import java.util.Map;

@PublicSpi
public class GraphQLInvocationData {
    private final String query;
    private final String operationName;
    private final Map<String, Object> variables;

    public GraphQLInvocationData(String query, String operationName, Map<String, Object> variables) {
        this.query = (String) Assert.assertNotNull(query, () -> {
            return "query must be provided";
        });
        this.operationName = operationName;
        this.variables = variables != null ? variables : Collections.emptyMap();
    }

    public String getQuery() {
        return this.query;
    }

    public String getOperationName() {
        return this.operationName;
    }

    public Map<String, Object> getVariables() {
        return this.variables;
    }
}
