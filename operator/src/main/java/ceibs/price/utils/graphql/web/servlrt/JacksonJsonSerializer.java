package ceibs.price.utils.graphql.web.servlrt;

import ceibs.price.utils.graphql.web.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class JacksonJsonSerializer implements JsonSerializer {
    private ObjectMapper objectMapper;

    @Autowired
    public JacksonJsonSerializer(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public String serialize(Object object) {
        try {
            return this.objectMapper.writeValueAsString(object);
        } catch (IOException var3) {
            throw new RuntimeException("Error serializing object to JSON: " + var3.getMessage(), var3);
        }
    }

    public <T> T deserialize(String json, Class<T> requiredType) {
        try {
            return this.objectMapper.readValue(json, requiredType);
        } catch (IOException var4) {
            throw new RuntimeException("Error deserializing object from JSON: " + var4.getMessage(), var4);
        }
    }
}