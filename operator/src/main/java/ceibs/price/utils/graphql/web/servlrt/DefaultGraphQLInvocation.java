package ceibs.price.utils.graphql.web.servlrt;


import ceibs.price.utils.graphql.web.ExecutionInputCustomizer;
import ceibs.price.utils.graphql.web.GraphQLInvocation;
import ceibs.price.utils.graphql.web.GraphQLInvocationData;
import graphql.ExecutionInput;
import graphql.ExecutionResult;
import graphql.GraphQL;
import graphql.Internal;
import org.dataloader.DataLoaderRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.WebRequest;

import java.util.concurrent.CompletableFuture;

@Component
@Internal
@Lazy
public class DefaultGraphQLInvocation implements GraphQLInvocation {
    @Autowired
    GraphQL graphQL;
    @Autowired(required = false)
    DataLoaderRegistry dataLoaderRegistry;
    @Autowired
    ExecutionInputCustomizer executionInputCustomizer;

    public DefaultGraphQLInvocation() {
    }

    public CompletableFuture<ExecutionResult> invoke(GraphQLInvocationData invocationData, WebRequest webRequest) {
        ExecutionInput.Builder executionInputBuilder = ExecutionInput.newExecutionInput().query(invocationData.getQuery()).operationName(invocationData.getOperationName()).variables(invocationData.getVariables());
        if (this.dataLoaderRegistry != null) {
            executionInputBuilder.dataLoaderRegistry(this.dataLoaderRegistry);
        }

        ExecutionInput executionInput = executionInputBuilder.build();
        CompletableFuture<ExecutionInput> customizedExecutionInput = this.executionInputCustomizer.customizeExecutionInput(executionInput, webRequest);
        GraphQL var10001 = this.graphQL;
        var10001.getClass();
        return customizedExecutionInput.thenCompose(var10001::executeAsync);
    }
}

