package ceibs.price.utils.graphql.web.servlrt;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;


@Configuration
@ConditionalOnWebApplication
@ComponentScan(
        basePackageClasses = {GraphQLController.class}
)
public class GraphQLEndpointConfiguration {
    @Autowired
    ApplicationContext applicationContext;

    public GraphQLEndpointConfiguration() {
    }

    @PostConstruct
    public void init() {
    }
}
