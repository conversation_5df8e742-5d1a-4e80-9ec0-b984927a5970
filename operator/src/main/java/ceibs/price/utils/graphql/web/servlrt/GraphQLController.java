package ceibs.price.utils.graphql.web.servlrt;


import ceibs.price.utils.CertifiedConnector;
import ceibs.price.utils.graphql.web.ExecutionResultHandler;
import ceibs.price.utils.graphql.web.GraphQLInvocation;
import ceibs.price.utils.graphql.web.GraphQLInvocationData;
import ceibs.price.utils.graphql.web.JsonSerializer;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import graphql.ExecutionResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.Internal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.InvalidMediaTypeException;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@RestController
@Internal
@Slf4j
public class GraphQLController {
    static String APPLICATION_GRAPHQL_VALUE = "application/graphql";
    static MediaType APPLICATION_GRAPHQL;
    @Autowired
    GraphQLInvocation graphQLInvocation;
    @Autowired
    ExecutionResultHandler executionResultHandler;
    @Autowired
    JsonSerializer jsonSerializer;

    public GraphQLController() {
    }

    @RequestMapping(
            value = {"${graphql.url:graphql}"},
            method = {RequestMethod.POST},
            produces = {"application/json"},
            headers = "Upgrade!=websocket"
    )
    public Object graphqlPOST(@RequestHeader(value = "Content-Type", required = false) String contentType, @RequestParam(value = "query", required = false) String query, @RequestParam(value = "operationName", required = false) String operationName, @RequestParam(value = "variables", required = false) String variablesJson, @RequestBody(required = false) String body, WebRequest webRequest) throws IOException {
        MediaType mediaType = null;
        String teantCode=null;
        try {
            teantCode = CertifiedConnector.introspect();

        }catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "introspect error:{}",e);
        }

        if (!StringUtils.isEmpty(contentType)) {
            try {
                mediaType = MediaType.parseMediaType(contentType);
            } catch (InvalidMediaTypeException var9) {
            }
        }

        if (body == null) {
            body = "";
        }

        if (MediaType.APPLICATION_JSON.equalsTypeAndSubtype(mediaType)) {
            GraphQLRequestBody request = (GraphQLRequestBody) this.jsonSerializer.deserialize(body, GraphQLRequestBody.class);
            if (request.getQuery() == null) {
                request.setQuery("");
            }
            log.info("query:{}", request.getQuery());
            log.debug("variables:{}", request.getVariables());
            Map<String,Object> variables = request.getVariables();
            if(null==variables){
                variables=new HashMap<>();
                variables.put("tenant_code",teantCode);
            }else{
                variables.put("tenant_code",teantCode);
            }
            request.setVariables(variables);
            return this.executeRequest(request.getQuery(), request.getOperationName(), request.getVariables(), webRequest);
        } else if (query != null) {
            return this.executeRequest(query, operationName, this.convertVariablesJson(variablesJson), webRequest);
        } else if (APPLICATION_GRAPHQL.equalsTypeAndSubtype(mediaType)) {
            return this.executeRequest(body, (String) null, (Map) null, webRequest);
        } else {
            throw new ResponseStatusException(HttpStatus.UNPROCESSABLE_ENTITY, "Could not process GraphQL request");
        }
    }

    @RequestMapping(
            value = {"${graphql.url:graphql}"},
            method = {RequestMethod.GET},
            produces = {"application/json"},
            headers = "Upgrade!=websocket"
    )
    public Object graphqlGET(@RequestParam("query") String query, @RequestParam(value = "operationName", required = false) String operationName, @RequestParam(value = "variables", required = false) String variablesJson, WebRequest webRequest) {
        return this.executeRequest(query, operationName, this.convertVariablesJson(variablesJson), webRequest);
    }

    private Map<String, Object> convertVariablesJson(String jsonMap) {
        return jsonMap == null ? Collections.emptyMap() : (Map) this.jsonSerializer.deserialize(jsonMap, Map.class);
    }

    private Object executeRequest(String query, String operationName, Map<String, Object> variables, WebRequest webRequest) {
        GraphQLInvocationData invocationData = new GraphQLInvocationData(query, operationName, variables);
        CompletableFuture<ExecutionResult> executionResult = this.graphQLInvocation.invoke(invocationData, webRequest);
        return this.executionResultHandler.handleExecutionResult(executionResult);
    }

    static {
        APPLICATION_GRAPHQL = MediaType.parseMediaType(APPLICATION_GRAPHQL_VALUE);
    }

    @RequestMapping(value = "${graphql.url:graphql}",
            method = RequestMethod.POST,
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            headers = "Upgrade!=websocket")
    public Object graphqlUpload(@RequestParam String operations,
                                @RequestParam String map,
                                MultipartHttpServletRequest request) {
        JSONObject operationObject = JSON.parseObject(operations);
        String query = (String) operationObject.get("query");
        Map<String, Object> variables = new HashMap<>();
        Object vars = operationObject.get("variables");
        if (vars instanceof JSONObject) {
            for (String key : ((JSONObject) vars).keySet()) {
                variables.put(key, ((JSONObject) vars).get(key));
            }
        }
        JSONObject mapObject = JSON.parseObject(map);
        if (null != mapObject) {
            for (String key : mapObject.keySet()) {
                Object val = mapObject.get(key);
                @SuppressWarnings("unchecked")
                Iterable<String> namePath = (Iterable<String>) val;
                if (namePath == null || !namePath.iterator().hasNext()) {
                    throw new IllegalArgumentException("map");
                }
                String[] namePaths = namePath.iterator().next().split("\\.");
                if (namePaths.length < 1) {
                    throw new IllegalArgumentException("map: var path length < 1");
                }
                String valName = namePaths[1];
                if (variables.containsKey(valName)) {
                    // get file
                    MultipartFile file = request.getFile(key);
                    Object varValue = variables.get(valName);
                    if (null == varValue) {  // single value
                        variables.put(valName, file);
                    } else if (varValue instanceof List) {   // array value
                        @SuppressWarnings("unchecked")
                        List<MultipartFile> files = (List<MultipartFile>) varValue;
                        files.set(Integer.parseInt(namePaths[2]), file);
                    } else {    // not supported
                        throw new IllegalArgumentException("variables." + valName);
                    }
                }
            }
        }

        CompletableFuture<ExecutionResult> executionResult = graphQLInvocation.invoke(new GraphQLInvocationData(query, "", variables), null);
        return executionResultHandler.handleExecutionResult(executionResult);
    }
}
