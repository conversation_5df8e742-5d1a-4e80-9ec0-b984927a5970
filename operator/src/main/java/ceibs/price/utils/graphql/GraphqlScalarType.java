package ceibs.price.utils.graphql;

import ceibs.price.utils.DateUtil;
import graphql.GraphQLContext;
import graphql.execution.CoercedVariables;
import graphql.language.*;
import graphql.schema.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

public class GraphqlScalarType {


    public static Logger logger = LoggerFactory.getLogger(GraphqlScalarType.class);


    private static final BigInteger LONG_MAX = BigInteger.valueOf(9223372036854775807L);
    private static final BigInteger LONG_MIN = BigInteger.valueOf(-9223372036854775808L);
    private static final BigInteger INT_MAX = BigInteger.valueOf(2147483647L);
    private static final BigInteger INT_MIN = BigInteger.valueOf(-2147483648L);
    private static final BigInteger BYTE_MAX = BigInteger.valueOf(127L);
    private static final BigInteger BYTE_MIN = BigInteger.valueOf(-128L);
    private static final BigInteger SHORT_MAX = BigInteger.valueOf(32767L);
    private static final BigInteger SHORT_MIN = BigInteger.valueOf(-32768L);


    private static String typeName(Object input) {
        if (input == null) {
            return "null";
        }

        return input.getClass().getSimpleName();
    }

    private static boolean isNumberIsh(Object input) {
        return input instanceof Number || input instanceof String;
    }

    public static final GraphQLScalarType GraphQLUnixTime = GraphQLScalarType.newScalar()
            .name("UnixTime")
            .description("Unix timestamp in seconds since epoch")
            .coercing(new Coercing<Date, Long>() {
                @Override
                public Long serialize(Object dataFetcherResult) throws CoercingSerializeException {
                    if (dataFetcherResult == null) {
                        throw new CoercingSerializeException("Cannot serialize null as UnixTime");
                    }
                    if (dataFetcherResult instanceof Date) {
                        return ((Date) dataFetcherResult).getTime() / 1000; // Date → 秒级时间戳
                    }
                    throw new CoercingSerializeException("Expected a Date object but got: " + dataFetcherResult.getClass().getName());
                }

                @Override
                public Long serialize(Object dataFetcherResult, GraphQLContext graphQLContext, Locale locale) throws CoercingSerializeException {
                    return serialize(dataFetcherResult);
                }

                @Override
                public Date parseValue(Object input) throws CoercingParseValueException {
                    if (input instanceof Number) {
                        long timestamp = ((Number) input).longValue();
                        if (String.valueOf(timestamp).length() == 10) {
                            return new Date(timestamp * 1000); // 秒 → Date
                        } else if (String.valueOf(timestamp).length() == 13) {
                            return new Date(timestamp); // 毫秒 → Date
                        }
                    } else if (input instanceof String) {
                        try {
                            long timestamp = Long.parseLong((String) input);
                            if (String.valueOf(timestamp).length() == 10) {
                                return new Date(timestamp * 1000);
                            } else if (String.valueOf(timestamp).length() == 13) {
                                return new Date(timestamp);
                            }
                        } catch (NumberFormatException ignored) {
                        }
                    }
                    throw new CoercingParseValueException("Invalid value for UnixTime: " + input);
                }

                @Override
                public Date parseValue(Object input, GraphQLContext graphQLContext, Locale locale) throws CoercingParseValueException {
                    return parseValue(input);
                }

                @Override
                public Date parseLiteral(Object input) throws CoercingParseLiteralException {
                    if (!(input instanceof IntValue)) {
                        throw new CoercingParseLiteralException("Expected AST type 'IntValue'");
                    }
                    BigInteger value = ((IntValue) input).getValue();
                    if (value.toString().length() == 10) {
                        return new Date(value.longValueExact() * 1000);
                    } else if (value.toString().length() == 13) {
                        return new Date(value.longValueExact());
                    }
                    throw new CoercingParseLiteralException("UnixTime expects 10 or 13-digit integer literal");
                }

                @Override
                public Date parseLiteral(Value<?> input, CoercedVariables variables, GraphQLContext graphQLContext, Locale locale) throws CoercingParseLiteralException {
                    if (!(input instanceof IntValue)) {
                        throw new CoercingParseLiteralException("Expected AST type 'IntValue'");
                    }
                    BigInteger value = ((IntValue) input).getValue();
                    if (value.toString().length() == 10) {
                        return new Date(value.longValueExact() * 1000);
                    } else if (value.toString().length() == 13) {
                        return new Date(value.longValueExact());
                    }
                    throw new CoercingParseLiteralException("UnixTime expects 10 or 13-digit integer literal");
                }
            })
            .build();

    public static final GraphQLScalarType GraphQLTimestamp =
            GraphQLScalarType.newScalar().name("Timestamp").description("A time value represented as a Unix timestamp in milliseconds")
                    .coercing(new Coercing<Date, Long>() {
                        private Long convertImpl(Object input) {
                            if (input instanceof Long) {
                                return (Long) input;
                            } else if (input instanceof String) {
                                Date date = DateUtil.parseDate(input.toString());
                                if (date == null) {
                                    date = DateUtil.parseShortDate(input.toString());
                                }
                                return date != null ? date.getTime() : null;
                            } else if (input instanceof Timestamp) {
                                return ((Timestamp) input).getTime();
                            } else if (input instanceof Date) {
                                return ((Date) input).getTime();
                            } else if (isNumberIsh(input)) {
                                try {
                                    return new BigDecimal(input.toString()).longValueExact();
                                } catch (NumberFormatException | ArithmeticException ex) {
                                    return null;
                                }
                            } else if (input instanceof LocalDateTime) {
                                return ((LocalDateTime) input).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
                            } else {
                                return null;
                            }
                        }

                        @Override
                        public Long serialize(Object dataFetcherResult) throws CoercingSerializeException {
                            Long result = convertImpl(dataFetcherResult);
                            if (result == null) {
                                throw new CoercingSerializeException(
                                        "Unable to serialize " + (dataFetcherResult == null ? "null" : dataFetcherResult.getClass().getName()) + " as a timestamp"
                                );
                            }
                            return result;
                        }

                        @Override
                        public Long serialize(Object dataFetcherResult, GraphQLContext graphQLContext, Locale locale) throws CoercingSerializeException {
                            return serialize(dataFetcherResult);
                        }

                        @Override
                        public Date parseValue(Object input) throws CoercingParseValueException {
                            Long ms = convertImpl(input);
                            if (ms == null) {
                                throw new CoercingParseValueException(
                                        "Unable to parse " + (input == null ? "null" : input.getClass().getName()) + " as a timestamp"
                                );
                            }
                            return new Date(ms);
                        }

                        @Override
                        public Date parseValue(Object input, GraphQLContext graphQLContext, Locale locale) throws CoercingParseValueException {
                            return parseValue(input);
                        }

                        @Override
                        public Date parseLiteral(Object input) throws CoercingParseLiteralException {
                            if (!(input instanceof IntValue)) {
                                throw new CoercingParseLiteralException("Expected AST type 'IntValue' but was '" + typeName(input) + "'");
                            }
                            BigInteger value = ((IntValue) input).getValue();
                            if (value.toString().length() == 13) {
                                return new Date(value.longValueExact());
                            } else {
                                throw new CoercingParseLiteralException("Timestamp is expected to be milliseconds since epoch (13 digits)");
                            }
                        }

                        @Override
                        public Date parseLiteral(Value<?> input, CoercedVariables variables, GraphQLContext graphQLContext, Locale locale) throws CoercingParseLiteralException {
                            if (!(input instanceof IntValue)) {
                                throw new CoercingParseLiteralException("Expected AST type 'IntValue' but was '" + typeName(input) + "'");
                            }
                            BigInteger value = ((IntValue) input).getValue();
                            if (value.toString().length() == 13) {
                                return new Date(value.longValueExact());
                            } else {
                                throw new CoercingParseLiteralException("Timestamp is expected to be milliseconds since epoch (13 digits)");
                            }
                        }
                    }).build();


    public static final GraphQLScalarType GraphQLMap =
            GraphQLScalarType.newScalar().name("Map")
                    .coercing(new Coercing<Map, Map>() {
                        @Override
                        public Map serialize(Object input) throws CoercingSerializeException {
                            if (input instanceof Map) {
                                return (Map) input;
                            }
                            return null;
                        }

                        @Override
                        public Map parseValue(Object input) throws CoercingParseValueException {
                            if (input instanceof Map) {
                                return (Map) input;
                            }
                            return null;
                        }

                        @Override
                        public Map parseLiteral(Object input) throws CoercingParseLiteralException {
                            return mapParseLiteral(input);
                        }
                    }).build();

    private static Map mapParseLiteral(Object input) {
        if (!(input instanceof ObjectValue)) {
            throw new CoercingParseLiteralException("Expected AST type 'ObjectValue' but was '" + typeName(input) + "'.");
        } else {
            try {
                ObjectValue value = (ObjectValue) input;
                Map source = new HashMap();
                return GraphqlScalarType.parseValue(source, value);
            } catch (Exception ex) {
                return null;
            }
        }
    }


    public static Map parseValue(Map<String, Object> source, ObjectValue objectValue) {
        if (objectValue != null) {
            List<ObjectField> fields = objectValue.getObjectFields();
            for (ObjectField field : fields) {
                String name = field.getName();
                Value value = field.getValue();
                source.put(name, parseValue(value));
            }
        }
        return source;
    }

    public static Object parseValue(Value val) {
        if (val instanceof ObjectValue) {
            return parseValue(new HashMap(), (ObjectValue) val);
        } else if (val instanceof BooleanValue) {
            return ((BooleanValue) val).isValue();
        } else if (val instanceof StringValue) {
            return ((StringValue) val).getValue();
        } else if (val instanceof IntValue) {
            return ((IntValue) val).getValue();
        } else if (val instanceof FloatValue) {
            return ((FloatValue) val).getValue();
        } else if (val instanceof NullValue) {
            return null;
        } else if (val instanceof ArrayValue) {
            List<Value> values = ((ArrayValue) val).getValues();
            List arrays = new ArrayList();
            if (values != null || !values.isEmpty()) {
                for (Value value : values) {
                    arrays.add(parseValue(value));
                }
            }
            return arrays;
        } else {
            return null;
        }
    }


}
