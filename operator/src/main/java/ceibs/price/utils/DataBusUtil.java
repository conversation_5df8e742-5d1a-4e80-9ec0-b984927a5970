package ceibs.price.utils;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class DataBusUtil {


    @Resource
    TokenUtil tokenUtil;
    @Value("${bus.url}")
    private String busUrl;
    private final Map<String, String> endpointCache = new ConcurrentHashMap<>();

    /**
     * 传递域名及要发现的服务名称即可
     */
    public String invokeUrlByName(String domain, String serverName) {
        String cachedEndpoint = endpointCache.get(serverName+"_"+domain);
        if (cachedEndpoint != null) {
            log.info("从缓存中获取服务地址: {} -> {}", serverName, cachedEndpoint);
            return cachedEndpoint;
        }
        // 获取token
        String key = tokenUtil.getToken(domain);
        // 根据url 获取请求地址
        String url = UrlUtil.getUrl(busUrl + "?access_token=" + key + "&domain=" + domain);
        JSONObject params = new JSONObject();
        String graphql = "query services($filter: Filter) { services(filter: $filter) { endpoints { endPoint } } }";
        JSONObject variables = new JSONObject();
        variables.put("filter", JSONObject.parseObject("{\"name\":{\"eq\":\""+serverName+"\"}}"));
        params.put("query", graphql);
        params.put("variables", variables);
        log.info("----------invokeUrl :{}, params:{}", url, params);
        String response = sendPostRequest(url, params);
        if (response == null || response.contains("errors")) {
            throw new RuntimeException("获取数据错误");
        }
        JSONObject jsonResponse = JSONArray.parseObject(response);
        JSONArray services = jsonResponse.getJSONObject("data").getJSONArray("services");
        if (services.isEmpty()) {
            throw new RuntimeException("请求资源" + serverName + "地址失败");
        }
        JSONArray endpoints = services.getJSONObject(0).getJSONArray("endpoints");
        if (endpoints.isEmpty()) {
            throw new RuntimeException("请求资源地址" + serverName + "失败");
        }
        String endPoint = endpoints.getJSONObject(0).getString("endPoint");
        // 写入缓存
        endpointCache.put(serverName+"_"+domain, endPoint);
        log.info("服务地址已缓存: {} -> {}", serverName, endPoint);
        return endPoint;
    }


    public String sendPostRequest(String url, JSONObject params) {
        try {
            url = UrlUtil.getUrl(url);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("url解析异常{}",url, e);
        }

        log.info(" post url:" + url);
        String s = null;
        String paramsString = convertToJsonString(params);
        try {
            s = Request.Post(url).bodyString(paramsString, ContentType.APPLICATION_JSON).execute().returnContent().asString();
        } catch (IOException e) {
            // 请求失败后尝试 休息2秒后重试， 连续重试三次都失败则抛出异常
            int retry = 0;
            while (retry < 3) {
                try {
                    Thread.sleep(2000);
                    log.error("post请求失败,准备重试， url:" + url + "; message:" + e.getMessage() + "; params:" + paramsString);
                    s = Request.Post(url).bodyString(paramsString, ContentType.APPLICATION_JSON).execute().returnContent().asString();
                    break;
                } catch (Exception ex) {
                    retry++;
                    log.error("post请求失败， url:" + url + "; message:" + ex.getMessage() + "; params:" + paramsString);
                }
            }

        }
        log.debug(" post response:" + s);
        return s;
    }

    public String convertToJsonString(JSONObject jsonObject) {
        // 使用 SerializerFeature.WriteMapNullValue 保留 null 值
        return jsonObject.toJSONString(jsonObject,SerializerFeature.WriteMapNullValue);
    }


}
