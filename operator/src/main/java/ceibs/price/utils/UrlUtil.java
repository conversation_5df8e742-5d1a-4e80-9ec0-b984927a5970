package ceibs.price.utils;

import ceibs.price.ValueResolver;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


@Slf4j
@Component
public class UrlUtil implements InitializingBean {

    private static UrlUtil util;
    @Resource
    ValueResolver valueResolver;

    @Value("${SERVICE_ORIGIN}")
    String superOriginUrl;
    public static String originUrl;

    @PostConstruct
    public void init() {
        util = this;
        originUrl = superOriginUrl;
    }


    /**
     * 可根据相对路径转成完整路径
     *
     * @param url
     * @return
     */
    public static String getUrl(String url) {
        String personalUrl = url;
        log.info("url:{}", personalUrl);
        log.info("originUrl:{}", originUrl);
        if (!isUrl(personalUrl)) {
            personalUrl = personalUrl.startsWith("/") ? personalUrl : ("/" + personalUrl);
            personalUrl = originUrl + personalUrl;
        }
        log.info("return Url:{}", personalUrl);
        return personalUrl;
    }

    private static boolean isUrl(String pInput) {
        if (pInput == null) {
            return false;
        }
        pInput = pInput.trim().toLowerCase();
        if (pInput.startsWith("http://") || pInput.startsWith("https://")) {
            return true;
        }
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        UrlUtil.originUrl = valueResolver.getServiceOrigin();
    }

    public void setOriginUrl(String originUrl) {
        UrlUtil.originUrl = originUrl;
    }


    public static String getURLIncludeContextPath(HttpServletRequest request) {
        return getLocation(request, "");
    }

    public static String getLocation(HttpServletRequest request, String location) {
        StringBuffer loc = new StringBuffer();
        loc.append(request.getRequestURL().toString());
        String uri = request.getRequestURI();
        String path = request.getContextPath();
        loc.delete(loc.length() - uri.length(), loc.length()).append(path).append(location);
        // return replaceSchemaByHeader(request, loc.toString());
        return loc.toString();
    }

}
