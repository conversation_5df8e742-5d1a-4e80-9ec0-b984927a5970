package ceibs.price.utils;

import org.apache.commons.io.IOUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public class JsonFileUtil {
    public static   String loadJsonFromClasspath(String path) throws Exception {
        try (InputStream is = JsonFileUtil.class.getClassLoader().getResourceAsStream(path)) {
            if (is == null) {
                throw new RuntimeException("File not found in classpath: " + path);
            }
            return IOUtils.toString(is, StandardCharsets.UTF_8);
        }
    }
}
