package ceibs.price.utils;

import ceibs.price.ValueResolver;
import ceibs.price.bean.Token;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.oltu.oauth2.client.OAuthClient;
import org.apache.oltu.oauth2.client.request.OAuthClientRequest;
import org.apache.oltu.oauth2.client.response.OAuthJSONAccessTokenResponse;
import org.apache.oltu.oauth2.common.exception.OAuthProblemException;
import org.apache.oltu.oauth2.common.exception.OAuthSystemException;
import org.apache.oltu.oauth2.common.message.types.GrantType;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@Component
public class TokenUtil {

    @Resource
    ValueResolver valueResolver;
    Map<String,Token> tokenMap = new HashMap<>();


    public String getToken(String domain)  {
        String sso = UrlUtil.getUrl(valueResolver.getOAuthTokenURL());

        Token token = tokenMap.get(domain);
        if (null != token) {
            int i = token.getExpiresIn().compareTo(System.currentTimeMillis());
            if (i > 0) {
                return token.getAccessToken();
            }
        }

        String scope = valueResolver.getScope();
        OAuthClientRequest oAuthClientRequest = null;
        try {
            oAuthClientRequest = OAuthClientRequest
                    .tokenLocation(sso).setGrantType(GrantType.CLIENT_CREDENTIALS)
                    .setClientId(valueResolver.getOAuthClientId()).setClientSecret(valueResolver.getOAuthClientSecret())
                    .setScope(scope.replace(",", " ")).buildBodyMessage();
        } catch (OAuthSystemException e) {
            log.error("token 获取 : ->" + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("token 获取失败:" + e.getMessage());
        }
        OAuthClient oAuthClient = new OAuthClient(new SSLConnectionClient());
        OAuthJSONAccessTokenResponse oAuthClientResponse = null;
        try {
            oAuthClientResponse = oAuthClient.accessToken(oAuthClientRequest, "POST", OAuthJSONAccessTokenResponse.class);
        } catch (OAuthSystemException | OAuthProblemException e) {
            log.error("token 获取" + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("token 获取失败:" + e.getMessage());
        }
        assert oAuthClientResponse != null;
        String accessToken = oAuthClientResponse.getAccessToken();
        long exp = System.currentTimeMillis() + (oAuthClientResponse.getExpiresIn() * 1000 - (10 * 60 * 1000));
        tokenMap.put(domain, new Token(oAuthClientResponse.getAccessToken(), exp, System.currentTimeMillis()));
        return accessToken;
    }
}
