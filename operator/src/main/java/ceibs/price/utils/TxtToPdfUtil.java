package ceibs.price.utils;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

@Service
public class TxtToPdfUtil {


    /**
     * 将txt文件内容转换为pdf字节数组
     * @param txtContent txt文件内容
     * @return PDF文件的字节数组
     * @throws IOException 当处理出现错误时抛出
     */
    public static byte[] convertTxtContentToPdf(String txtContent) throws IOException {
        txtContent = txtContent
                .replace("\r\n", "\n")  // 统一换行符
                .replace("\r", "\n")
                .trim();

        // 创建临时文件用于输出
        Path tempPdf = Files.createTempFile("output", ".pdf");

        // 创建PDF文档
        PdfWriter writer = new PdfWriter(tempPdf.toFile());
        PdfDocument pdf = new PdfDocument(writer);
        Document document = new Document(pdf);
        // 将文本内容按行分割并添加到PDF中
        String[] lines = txtContent.split("\n");
        for (String line : lines) {
            document.add(new Paragraph(line));
        }
        // 关闭文档
        document.close();
        // 读取生成的PDF文件并返回字节数组
        byte[] pdfBytes = Files.readAllBytes(tempPdf);
        // 删除临时文件
        Files.delete(tempPdf);
        return pdfBytes;
    }
}
