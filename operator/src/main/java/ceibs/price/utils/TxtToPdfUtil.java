package ceibs.price.utils;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

@Service
public class TxtToPdfUtil {


    /**
     * 将txt文件内容转换为pdf字节数组
     * @param txtContent txt文件内容
     * @return PDF文件的字节数组
     * @throws IOException 当处理出现错误时抛出
     */
    public static byte[] convertTxtContentToPdf(String txtContent) throws IOException {
        txtContent = txtContent
                .replace("\r\n", "\n")  // 统一换行符
                .replace("\r", "\n")
                .trim();

        // 创建临时文件用于输出
        Path tempPdf = Files.createTempFile("output", ".pdf");

        try {
            // 创建PDF文档
            PdfWriter writer = new PdfWriter(tempPdf.toFile());
            PdfDocument pdf = new PdfDocument(writer);
            Document document = new Document(pdf);

            // 尝试创建支持中文的字体，如果失败则使用默认字体
            com.itextpdf.kernel.font.PdfFont font = null;
            try {
                // 尝试使用内置的中文字体
                font = com.itextpdf.kernel.font.PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H", com.itextpdf.kernel.font.PdfFontFactory.EmbeddingStrategy.PREFER_NOT_EMBEDDED);
            } catch (Exception e1) {
                try {
                    // 备选方案：使用SimSun字体
                    font = com.itextpdf.kernel.font.PdfFontFactory.createFont("SimSun", "Identity-H", com.itextpdf.kernel.font.PdfFontFactory.EmbeddingStrategy.PREFER_NOT_EMBEDDED);
                } catch (Exception e2) {
                    // 最后备选：使用默认字体，但不过滤中文字符
                    font = null;
                }
            }

            // 将文本内容按行分割并添加到PDF中
            String[] lines = txtContent.split("\n");
            for (String line : lines) {
                try {
                    if (!line.trim().isEmpty()) {
                        Paragraph paragraph = new Paragraph(line);
                        if (font != null) {
                            paragraph.setFont(font);
                        }
                        document.add(paragraph);
                    } else {
                        // 保留空行
                        Paragraph emptyParagraph = new Paragraph(" ");
                        if (font != null) {
                            emptyParagraph.setFont(font);
                        }
                        document.add(emptyParagraph);
                    }
                } catch (Exception e) {
                    // 如果某一行有问题，尝试不设置字体
                    try {
                        document.add(new Paragraph(line));
                    } catch (Exception e2) {
                        // 最后的备选方案：添加错误提示
                        System.err.println("处理行时出错: " + line + ", 错误: " + e2.getMessage());
                        document.add(new Paragraph("[此行包含无法显示的字符]"));
                    }
                }
            }

            // 安全关闭文档
            try {
                document.close();
            } catch (Exception e) {
                // 如果关闭时出错，尝试强制关闭
                try {
                    pdf.close();
                } catch (Exception e2) {
                    throw new IOException("PDF文档关闭失败", e2);
                }
            }

            // 读取生成的PDF文件并返回字节数组
            byte[] pdfBytes = Files.readAllBytes(tempPdf);
            return pdfBytes;

        } finally {
            // 确保删除临时文件
            try {
                Files.deleteIfExists(tempPdf);
            } catch (IOException e) {
                // 忽略删除文件时的异常
            }
        }
    }


}
