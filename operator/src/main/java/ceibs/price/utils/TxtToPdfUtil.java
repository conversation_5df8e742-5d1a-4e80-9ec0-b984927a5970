package ceibs.price.utils;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

@Service
public class TxtToPdfUtil {


    /**
     * 将txt文件内容转换为pdf字节数组
     * @param txtContent txt文件内容
     * @return PDF文件的字节数组
     * @throws IOException 当处理出现错误时抛出
     */
    public static byte[] convertTxtContentToPdf(String txtContent) throws IOException {
        txtContent = txtContent
                .replace("\r\n", "\n")  // 统一换行符
                .replace("\r", "\n")
                .trim();

        // 创建临时文件用于输出
        Path tempPdf = Files.createTempFile("output", ".pdf");

        try {
            // 创建PDF文档
            PdfWriter writer = new PdfWriter(tempPdf.toFile());
            PdfDocument pdf = new PdfDocument(writer);
            Document document = new Document(pdf);

            // 简化处理：不设置任何字体，让iText使用默认处理
            // 这样可以避免字体相关的所有问题
            String[] lines = txtContent.split("\n");
            for (String line : lines) {
                try {
                    if (!line.trim().isEmpty()) {
                        document.add(new Paragraph(line));
                    } else {
                        // 保留空行
                        document.add(new Paragraph(" "));
                    }
                } catch (Exception e) {
                    // 如果某一行有问题，记录但继续处理
                    System.err.println("处理行时出错: " + line + ", 错误: " + e.getMessage());
                    try {
                        // 尝试添加一个简化的段落
                        document.add(new Paragraph("[无法处理的内容行]"));
                    } catch (Exception e2) {
                        // 如果连这个都失败，就跳过这一行
                        System.err.println("跳过问题行: " + e2.getMessage());
                    }
                }
            }

            // 安全关闭文档 - 按正确顺序关闭
            try {
                document.close();
            } catch (Exception e) {
                // 如果document关闭失败，直接忽略，因为可能已经被自动关闭了
                System.err.println("Document关闭时出现异常（可忽略）: " + e.getMessage());
            }

            // 不需要手动关闭pdf，document.close()会自动处理

            // 读取生成的PDF文件并返回字节数组
            byte[] pdfBytes = Files.readAllBytes(tempPdf);
            return pdfBytes;

        } finally {
            // 确保删除临时文件
            try {
                Files.deleteIfExists(tempPdf);
            } catch (IOException e) {
                // 忽略删除文件时的异常
            }
        }
    }


}
