package ceibs.price.utils;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

@Service
public class TxtToPdfUtil {


    /**
     * 将txt文件内容转换为pdf字节数组
     * @param txtContent txt文件内容
     * @return PDF文件的字节数组
     * @throws IOException 当处理出现错误时抛出
     */
    public static byte[] convertTxtContentToPdf(String txtContent) throws IOException {
        txtContent = txtContent
                .replace("\r\n", "\n")  // 统一换行符
                .replace("\r", "\n")
                .trim();

        // 创建临时文件用于输出
        Path tempPdf = Files.createTempFile("output", ".pdf");

        try {
            // 创建PDF文档
            PdfWriter writer = new PdfWriter(tempPdf.toFile());
            PdfDocument pdf = new PdfDocument(writer);
            Document document = new Document(pdf);

            // 设置支持中文的字体
            com.itextpdf.kernel.font.PdfFont font;
            try {
                // 尝试使用系统中文字体
                font = com.itextpdf.kernel.font.PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H");
            } catch (Exception e) {
                try {
                    // 如果上面的字体不可用，尝试使用其他中文字体
                    font = com.itextpdf.kernel.font.PdfFontFactory.createFont("STSongStd-Light", "UniGB-UCS2-H");
                } catch (Exception e2) {
                    try {
                        // 尝试使用NotoSansCJK字体（如果可用）
                        font = com.itextpdf.kernel.font.PdfFontFactory.createFont("NotoSansCJK-Regular", "Identity-H");
                    } catch (Exception e3) {
                        try {
                            // 尝试使用Arial Unicode MS字体
                            font = com.itextpdf.kernel.font.PdfFontFactory.createFont("ArialUnicodeMS", "Identity-H");
                        } catch (Exception e4) {
                            // 最后的备选方案 - 使用默认字体
                            font = com.itextpdf.kernel.font.PdfFontFactory.createFont();
                        }
                    }
                }
            }

            // 将文本内容按行分割并添加到PDF中
            String[] lines = txtContent.split("\n");
            for (String line : lines) {
                Paragraph paragraph = new Paragraph(line);
                paragraph.setFont(font);
                document.add(paragraph);
            }

            // 关闭文档
            document.close();

            // 读取生成的PDF文件并返回字节数组
            byte[] pdfBytes = Files.readAllBytes(tempPdf);
            return pdfBytes;

        } finally {
            // 确保删除临时文件
            try {
                Files.deleteIfExists(tempPdf);
            } catch (IOException e) {
                // 忽略删除文件时的异常
            }
        }
    }
}
