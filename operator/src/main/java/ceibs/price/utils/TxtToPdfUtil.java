package ceibs.price.utils;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

@Service
public class TxtToPdfUtil {


    /**
     * 将txt文件内容转换为pdf字节数组
     * @param txtContent txt文件内容
     * @return PDF文件的字节数组
     * @throws IOException 当处理出现错误时抛出
     */
    public static byte[] convertTxtContentToPdf(String txtContent) throws IOException {
        txtContent = txtContent
                .replace("\r\n", "\n")  // 统一换行符
                .replace("\r", "\n")
                .trim();

        // 创建临时文件用于输出
        Path tempPdf = Files.createTempFile("output", ".pdf");

        try {
            // 创建PDF文档
            PdfWriter writer = new PdfWriter(tempPdf.toFile());
            PdfDocument pdf = new PdfDocument(writer);
            Document document = new Document(pdf);

            // 将文本内容按行分割并添加到PDF中（不设置字体，使用系统默认）
            String[] lines = txtContent.split("\n");
            for (String line : lines) {
                try {
                    // 过滤掉可能导致问题的字符
                    String cleanLine = filterProblematicCharacters(line);
                    if (!cleanLine.trim().isEmpty()) {
                        // 不设置字体，让iText使用默认处理
                        Paragraph paragraph = new Paragraph(cleanLine);
                        document.add(paragraph);
                    } else if (line.trim().isEmpty()) {
                        // 保留空行
                        document.add(new Paragraph(" "));
                    }
                } catch (Exception e) {
                    // 如果某一行有问题，记录错误但继续处理其他行
                    System.err.println("处理行时出错: " + line + ", 错误: " + e.getMessage());
                    // 添加一个错误提示行
                    document.add(new Paragraph("[此行包含无法显示的字符]"));
                }
            }

            // 安全关闭文档
            try {
                document.close();
            } catch (Exception e) {
                // 如果关闭时出错，尝试强制关闭
                try {
                    pdf.close();
                } catch (Exception e2) {
                    throw new IOException("PDF文档关闭失败", e2);
                }
            }

            // 读取生成的PDF文件并返回字节数组
            byte[] pdfBytes = Files.readAllBytes(tempPdf);
            return pdfBytes;

        } finally {
            // 确保删除临时文件
            try {
                Files.deleteIfExists(tempPdf);
            } catch (IOException e) {
                // 忽略删除文件时的异常
            }
        }
    }

    /**
     * 过滤可能导致PDF生成问题的字符
     * 由于使用默认字体，主要保留ASCII字符和基本符号
     * @param text 原始文本
     * @return 过滤后的文本
     */
    private static String filterProblematicCharacters(String text) {
        if (text == null) {
            return "";
        }

        // 使用更保守的字符过滤策略
        StringBuilder filtered = new StringBuilder();
        for (char c : text.toCharArray()) {
            if (isSafeCharacter(c)) {
                filtered.append(c);
            } else if (isChineseCharacter(c)) {
                // 中文字符转换为拼音或保留原字符（让用户决定）
                // 这里先保留原字符，如果还有问题再考虑转换
                filtered.append(c);
            } else {
                // 其他不安全字符替换为空格
                filtered.append(' ');
            }
        }
        return filtered.toString();
    }

    /**
     * 判断字符是否安全（不会导致字形缺失）
     */
    private static boolean isSafeCharacter(char c) {
        return (c >= 32 && c <= 126) ||  // ASCII可打印字符
               c == '\t' || c == '\n' || c == '\r' || c == ' '; // 基本空白字符
    }

    /**
     * 判断是否为中文字符
     */
    private static boolean isChineseCharacter(char c) {
        return (c >= 0x4E00 && c <= 0x9FFF) || // CJK统一汉字
               (c >= 0x3400 && c <= 0x4DBF) || // CJK扩展A
               (c >= 0xF900 && c <= 0xFAFF);   // CJK兼容汉字
    }
}
