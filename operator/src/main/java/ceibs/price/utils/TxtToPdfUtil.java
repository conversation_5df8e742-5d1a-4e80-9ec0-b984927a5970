package ceibs.price.utils;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

@Service
public class TxtToPdfUtil {


    /**
     * 将txt文件内容转换为pdf字节数组
     * @param txtContent txt文件内容
     * @return PDF文件的字节数组
     * @throws IOException 当处理出现错误时抛出
     */
    public static byte[] convertTxtContentToPdf(String txtContent) throws IOException {
        txtContent = txtContent
                .replace("\r\n", "\n")  // 统一换行符
                .replace("\r", "\n")
                .trim();

        // 创建临时文件用于输出
        Path tempPdf = Files.createTempFile("output", ".pdf");

        try {
            // 创建PDF文档
            PdfWriter writer = new PdfWriter(tempPdf.toFile());
            PdfDocument pdf = new PdfDocument(writer);
            Document document = new Document(pdf);

            // 设置支持中文的字体
            com.itextpdf.kernel.font.PdfFont font = null;

            // 字体选择策略：优先使用内置字体，避免字形缺失问题
            String[] fontOptions = {
                "STSong-Light,UniGB-UCS2-H",
                "STSongStd-Light,UniGB-UCS2-H",
                "SimSun,Identity-H",
                "Microsoft YaHei,Identity-H",
                "Arial Unicode MS,Identity-H"
            };

            for (String fontOption : fontOptions) {
                try {
                    String[] parts = fontOption.split(",");
                    font = com.itextpdf.kernel.font.PdfFontFactory.createFont(parts[0], parts[1]);
                    break; // 成功创建字体，跳出循环
                } catch (Exception e) {
                    // 继续尝试下一个字体
                    continue;
                }
            }

            // 如果所有字体都失败，使用默认字体
            if (font == null) {
                try {
                    // 使用Helvetica作为最后的备选方案
                    font = com.itextpdf.kernel.font.PdfFontFactory.createFont("Helvetica");
                } catch (Exception e) {
                    // 使用最基本的默认字体
                    font = com.itextpdf.kernel.font.PdfFontFactory.createFont();
                }
            }

            // 将文本内容按行分割并添加到PDF中
            String[] lines = txtContent.split("\n");
            for (String line : lines) {
                // 过滤掉可能导致问题的字符
                String cleanLine = filterProblematicCharacters(line);
                if (!cleanLine.trim().isEmpty()) {
                    Paragraph paragraph = new Paragraph(cleanLine);
                    paragraph.setFont(font);
                    document.add(paragraph);
                } else if (line.trim().isEmpty()) {
                    // 保留空行
                    document.add(new Paragraph(" ").setFont(font));
                }
            }

            // 关闭文档
            document.close();

            // 读取生成的PDF文件并返回字节数组
            byte[] pdfBytes = Files.readAllBytes(tempPdf);
            return pdfBytes;

        } finally {
            // 确保删除临时文件
            try {
                Files.deleteIfExists(tempPdf);
            } catch (IOException e) {
                // 忽略删除文件时的异常
            }
        }
    }

    /**
     * 过滤可能导致PDF生成问题的字符
     * @param text 原始文本
     * @return 过滤后的文本
     */
    private static String filterProblematicCharacters(String text) {
        if (text == null) {
            return "";
        }

        // 移除控制字符，但保留常用的空白字符
        StringBuilder filtered = new StringBuilder();
        for (char c : text.toCharArray()) {
            // 保留可打印字符、中文字符、常用标点符号和空白字符
            if (Character.isLetterOrDigit(c) ||
                Character.isWhitespace(c) ||
                isPrintableSymbol(c) ||
                isChineseCharacter(c)) {
                filtered.append(c);
            } else {
                // 将不支持的字符替换为空格或问号
                filtered.append('?');
            }
        }
        return filtered.toString();
    }

    /**
     * 判断是否为可打印的符号字符
     */
    private static boolean isPrintableSymbol(char c) {
        return (c >= 32 && c <= 126) || // ASCII可打印字符
               (c >= 0x2000 && c <= 0x206F) || // 常用标点符号
               (c >= 0x3000 && c <= 0x303F);   // CJK符号和标点
    }

    /**
     * 判断是否为中文字符
     */
    private static boolean isChineseCharacter(char c) {
        return (c >= 0x4E00 && c <= 0x9FFF) || // CJK统一汉字
               (c >= 0x3400 && c <= 0x4DBF) || // CJK扩展A
               (c >= 0xF900 && c <= 0xFAFF);   // CJK兼容汉字
    }
}
