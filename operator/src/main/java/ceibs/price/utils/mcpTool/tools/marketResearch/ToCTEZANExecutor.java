package ceibs.price.utils.mcpTool.tools.marketResearch;


import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.CopilotApiService;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.JsonFileUtil;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 打开特赞页面
 */

@ToolHandler("to_c_market_analysis")
@Component
public class ToCTEZANExecutor implements ToolExecutor {

    @Autowired
    private PriceSessionService priceSessionService;
    private static String TOOL_NAME = "to_c_market_analysis";
    @Value("${base.view.url}")
    private String baseViewUrl;
    @Value("${TEZAN_URL}")
    private String tezanUrl;
    @Autowired
    CopilotApiService copilotApiService;
    @Autowired
    McpService mcpService;

    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) {
        Boolean error = false;
        String prompt = "";
        Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
        List<Map<String, Object>> datas = new ArrayList<>();
        Map marketPositioning = (Map) args.get("marketPositioning");
        try {
            // 获取竞争分析报告
            PriceSessionView jzfxView = priceSessionService.getMaxPriceSessionView(sessionId, "竞争分析");
            List<Map<String, Object>> cache = mcpService.getCache("analyze_competition");
            JSONObject jsonObject = copilotApiService.generateToCPrompt(marketPositioning.get("eq").toString(),jzfxView.getData(),cache, requestToken);
            prompt = jsonObject.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text");
            Map map = new HashMap();
            map.put("text", prompt);
            datas.add(map);
        } catch (Exception e) {
            e.printStackTrace();
            return new ToolResponse(true, null, null, "生成提示词失败");
        }
        // 根据定位生成提示词
        JSONObject viewData = new JSONObject();
        viewData.put("marketPositioning", marketPositioning.get("eq").toString());
        // 读取  模板
        String template = "";
        try {
            template = JsonFileUtil.loadJsonFromClasspath("2cMarkdown.md");
        } catch (Exception e) {
            return new ToolResponse(true, null, null, "加载模板失败");
        }
        prompt=prompt.concat("\n--- markdown---").concat(template).concat("\n--- markdown---");
        viewData.put("prompt", prompt);
        viewData.put("currentUrl", tezanUrl);
        viewData.put("started", false);
        String viewId = UUID.randomUUID().toString();
        if(count==0){
            PriceSessionView priceSessionView = new PriceSessionView();
            priceSessionView.setId(viewId);
            priceSessionView.setSessionId(sessionId);
            priceSessionView.setViewName("市场分析");
            priceSessionView.setViewDesc("市场分析");
            priceSessionView.setViewUrl(UrlUtil.getUrl("/hub-price/atypica"));
            priceSessionView.setCreatedAt(new Date());
            priceSessionView.setIndex(count);
            priceSessionView.setData(JSON.toJSONString(viewData));
            priceSessionView.setDelMark(false);
            priceSessionView.setStep("2-1");
            List<PriceSessionView> views = new ArrayList<>();
            views.add(priceSessionView);
            priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, autoTask,opt);
        }else{
            //
            PriceSessionView view = priceSessionService.getMaxPriceSessionView(sessionId, "市场分析");
            viewId=view.getId();
            view.setData(JSON.toJSONString(viewData));
            view.setCreatedAt(new Date());
            priceSessionService.updatePriceSessionViewByBean(view);
        }



        return new ToolResponse(error, datas, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);
    }

}
