package ceibs.price.utils.mcpTool.tools.costPricing;


import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 成本定价
 */

@ToolHandler("calculate_cost_pricing")
@Component
public class CostPricingExecutor implements ToolExecutor {

    @Autowired
    private PriceSessionService priceSessionService;
    private static String TOOL_NAME = "calculate_cost_pricing";
    @Value("${base.view.url}")
    private String baseViewUrl;
    @Autowired
    private McpService mcpService;

    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) {
        args = new HashMap<>();

        PriceSessionView financialStatement = priceSessionService.getMaxPriceSessionView(sessionId, "财务报表");
        if (null == financialStatement || financialStatement.getData().isEmpty()) {
            return new ToolResponse(true, null, null, "请先进行财务报表分析");
        }

        JSONObject financialStatementData = JSONObject.parseObject(financialStatement.getData());
        JSONObject financialData = null;
        JSONObject economicData = null;
        if(financialStatementData.containsKey("financial")&& !financialStatementData.getJSONArray("financial").isEmpty()){
            financialData = financialStatementData.getJSONArray("financial").getJSONObject(0);
        }
        if(financialStatementData.containsKey("economic")&& !financialStatementData.getJSONArray("economic").isEmpty()){
            economicData = financialStatementData.getJSONArray("economic").getJSONObject(0);
        }
        if (financialData == null && economicData== null) {
            return new ToolResponse(true, null, null, "请先进行财务报表分析");
        }

        // 有市场分析分析数据，则将市场分析数据一同计算
        PriceSessionView marketSegments = priceSessionService.getMaxPriceSessionView(sessionId, "市场分析提取");
        if (null != marketSegments) {
            JSONObject marketSegmentsData = JSONArray.parseArray(marketSegments.getData()).getJSONObject(0);
            if (marketSegmentsData.getBoolean("flag")) {
                // 获取市场分析
                JSONObject extractedData = marketSegmentsData.getJSONObject("extracted_data");
                args.put("market_data", extractedData);
            }
        }
        try {
            Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);

            String viewId = UUID.randomUUID().toString();
            List<Map<String, Object>> data;
            if(null==marketSegments){
                data = getData(args, financialData, economicData);
            }else{
                data = getData(args, financialData, economicData, marketSegments);
            }
            if(count==0){
                PriceSessionView view = new PriceSessionView();
                view.setId(viewId);
                view.setSessionId(sessionId);
                view.setViewName("成本定价");
                view.setViewDesc("成本定价");
                view.setViewUrl(UrlUtil.getUrl("/hub-price/costPricing"));
                view.setCreatedAt(new Date());
                view.setIndex(2 + count);
                view.setData(JSON.toJSONString(data));
                view.setDelMark(false);
                view.setStep("3-1");
                List<PriceSessionView> views = new ArrayList<>();
                views.add(view);
                priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, autoTask, opt);
            }else{
                PriceSessionView view = priceSessionService.getMaxPriceSessionView(sessionId, "成本定价");
                viewId=view.getId();
                view.setData(JSON.toJSONString(data));
                view.setCreatedAt(new Date());
                priceSessionService.updatePriceSessionViewByBean(view);
            }
            return new ToolResponse(false, data, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);
        } catch (Exception e) {
            e.printStackTrace();
            return new ToolResponse(true, null, null, "成本定价失败，请重试");
        }
    }

    public List<Map<String, Object>> getData(Map<String, Object> args, JSONObject financialData, JSONObject economicData, PriceSessionView marketSegments) {
        String toolName = "calculate_cost_pricing";
        List<Map<String, Object>> datas = new ArrayList<>();
        Map<String, Object> data = new HashMap<>();
        args.put("financial_data", financialData);
        McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall(toolName, args);
        List<McpSchema.Content> contentList = result.content();
        McpSchema.Content content = contentList.get(0);
        if (content instanceof McpSchema.TextContent textContent) {
            String text = textContent.text();
            // text to map
            Object parse = JSON.parse(text);
            if (parse instanceof JSONObject jsonObject) {
                // to Map
                Map map = jsonObject.toJavaObject(Map.class);
                if (map.containsKey("error")) {
                    throw new RuntimeException("成本定价失败：" + map.get("error").toString());
                }
                // 如果有报错
                data.putAll(map);
            }
        }
        // 第二条曲线
        args.put("financial_data", economicData);
        result = (McpSchema.CallToolResult) mcpService.executeToolCall(toolName, args);
        contentList = result.content();
        content = contentList.get(0);
        if (content instanceof McpSchema.TextContent textContent) {
            String text = textContent.text();
            // text to map
            Object parse = JSON.parse(text);
            if (parse instanceof JSONObject jsonObject) {
                // to Map
                Map map = jsonObject.toJavaObject(Map.class);
                if (map.containsKey("error")) {
                    throw new RuntimeException("成本定价失败：" + map.get("error").toString());
                }
                data.put("base_cases_economic", map.get("base_cases"));
            }
        }
        if(null!=marketSegments){
            data.put("market_segments_view_id", marketSegments.getId());
        }

        datas.add(data);
        return datas;
    }


    public List<Map<String, Object>> getData(Map<String, Object> args, JSONObject financialData, JSONObject economicData) {
        String toolName = "calculate_cost_pricing_by_financial_data";
        List<Map<String, Object>> datas = new ArrayList<>();
        Map<String, Object> data = new HashMap<>();
        args.put("financial_data", financialData);
        McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall(toolName, args);
        List<McpSchema.Content> contentList = result.content();
        McpSchema.Content content = contentList.get(0);
        if (content instanceof McpSchema.TextContent textContent) {
            String text = textContent.text();
            // text to map
            Object parse = JSON.parse(text);
            if (parse instanceof JSONObject jsonObject) {
                // to Map
                Map map = jsonObject.toJavaObject(Map.class);
                if (map.containsKey("error")) {
                    throw new RuntimeException("成本定价失败：" + map.get("error").toString());
                }
                // 如果有报错
                data.putAll(map);
            }
        }
        // 第二条曲线
        args.put("financial_data", economicData);
        result = (McpSchema.CallToolResult) mcpService.executeToolCall(toolName, args);
        contentList = result.content();
        content = contentList.get(0);
        if (content instanceof McpSchema.TextContent textContent) {
            String text = textContent.text();
            // text to map
            Object parse = JSON.parse(text);
            if (parse instanceof JSONObject jsonObject) {
                // to Map
                Map map = jsonObject.toJavaObject(Map.class);
                if (map.containsKey("error")) {
                    throw new RuntimeException("成本定价失败：" + map.get("error").toString());
                }
                data.put("base_cases_economic", map.get("base_cases"));
            }
        }
        datas.add(data);
        return datas;
    }
}
