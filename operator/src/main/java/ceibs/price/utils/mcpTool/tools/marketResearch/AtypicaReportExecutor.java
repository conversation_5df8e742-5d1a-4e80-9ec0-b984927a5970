package ceibs.price.utils.mcpTool.tools.marketResearch;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.FileService;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 对特赞报告进行分析
 * 成本定价前置步骤1
 */
@ToolHandler("extract_market_segments")
@Component
public class AtypicaReportExecutor implements ToolExecutor {

    @Autowired
    private McpService mcpService;
    @Autowired
    private PriceSessionService priceSessionService;
    @Autowired
    private FileService fileService;

    @Value("${base.view.url}")
    private String baseViewUrl;


    private static String TOOL_NAME = "extract_market_segments";

    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) {
        String viewData=null;
        Boolean error = false;
        List<Map<String, Object>> datas = new ArrayList<>();
        Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
        if (null == autoTask || !autoTask) {
            // 获取最近一份 特赞报告内容
            PriceSessionView marketAnalysis = priceSessionService.getMaxPriceSessionView(sessionId, "市场分析");
            if (null == marketAnalysis) {
                return new ToolResponse(true, null, null, "分析失败，请先进行市场分析");
            }
            String marketAnalysisData = marketAnalysis.getData();
            boolean reports = JSON.parseObject(marketAnalysisData).containsKey("reports");
            if (!reports) {
                return new ToolResponse(true, null, null, "提取失败，没有找到有效的市场分析报告,请确认2C市场分析是否结束");
            }
            args = new HashMap<>();
            args.put("content", JSON.parseObject(marketAnalysisData).getString("reports"));
            try {
                McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall(TOOL_NAME, args);
                List<McpSchema.Content> contentList = result.content();
                McpSchema.Content content = contentList.get(0);
                if (content instanceof McpSchema.TextContent textContent) {
                    String text = textContent.text();
                    // text to map
                    Object parse = JSON.parse(text);
                    if (parse instanceof JSONObject jsonObject) {
                        // to Map
                        JSONArray market_segments = jsonObject.getJSONObject("extracted_data").getJSONArray("market_segments");
                        for (Object marketSegment : market_segments) {
                            JSONObject marketSegmentMap = (JSONObject) marketSegment;
                            marketSegmentMap.put("market_percent", 100);
                        }

                        Map map = jsonObject.toJavaObject(Map.class);
                        datas.add(map);
                    }
                }
                viewData=JSON.toJSONString(datas);
            } catch (Exception e) {
                e.printStackTrace();
                return new ToolResponse(true, null, null, "分析失败，请重试");
            }
        } else {
            viewData = """
                    [{"extracted_data":{"market_segments":[
                    {"consumer_profile":"高净值商务精英","market_percent":100,"market_size":32000,"price_probability_pairs":[[3000,0.95],[4500,0.9],[6000,0.85],[8000,0.75]],"price_demand_pairs":[[3000,30000],[4500,29000],[6000,27000],[8000,24000]],"description":"35-45岁，高收入人群，对价格不敏感，但极度注重'物有所值'"},
                    {"consumer_profile":"内容驱动型专业旅行者","market_percent":100,"market_size":58000,"price_probability_pairs":[[3000,0.85],[4500,0.6],[6000,0.35],[8000,0.15]],"price_demand_pairs":[[3000,49000],[4500,35000],[6000,20000],[8000,9000]],"description":"25-35岁，自由摄影师/旅行博主，以'出片率'为核心决策标准"},
                    {"consumer_profile":"专业摄影师","market_percent":100,"market_size":25000,"price_probability_pairs":[[3000,0.9],[4500,0.75],[6000,0.5],[8000,0.25]],"price_demand_pairs":[[3000,23000],[4500,19000],[6000,13000],[8000,6000]],"description":"30-50岁，专业摄影师，深度体验，注重独特性和摄影价值"},
                    {"consumer_profile":"高端休闲客群","market_percent":100,"market_size":45000,"price_probability_pairs":[[3000,0.95],[4500,0.85],[6000,0.65],[8000,0.3]],"price_demand_pairs":[[3000,43000],[4500,38000],[6000,29000],[8000,14000]],"description":"35-50岁，跨国企业高管，追求高品质的在地体验"},
                    {"consumer_profile":"特殊场合情侣/夫妇","market_percent":100,"market_size":68000,"price_probability_pairs":[[3000,0.9],[4500,0.75],[6000,0.45],[8000,0.2]],"price_demand_pairs":[[3000,61000],[4500,51000],[6000,31000],[8000,14000]],"description":"30-40岁，高收入专业人士，来自一线城市，为纪念日/蜜月等特殊场合选择酒店"}]},"flag":true}]
                    """;
            // to datas
            JSONArray objects = JSON.parseArray(viewData);
            List<Map> tempList = objects.toJavaList(Map.class);
            for (Map map : tempList) {
                datas.add((Map<String, Object>) map);
            }
        }

        String viewId = UUID.randomUUID().toString();
        if(count==0){
            PriceSessionView priceSessionView = new PriceSessionView();
            priceSessionView.setId(viewId);
            priceSessionView.setSessionId(sessionId);
            priceSessionView.setViewName("市场分析提取");
            priceSessionView.setViewDesc("市场分析提取");
            priceSessionView.setViewUrl(UrlUtil.getUrl("/hub-price/marketsegments"));
            priceSessionView.setCreatedAt(new Date());
            priceSessionView.setIndex(0);
            priceSessionView.setData(viewData);
            priceSessionView.setDelMark(false);
            priceSessionView.setStep("2-1");
            List<PriceSessionView> views = new ArrayList<>();
            views.add(priceSessionView);
            priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, autoTask, opt);
        }else{
            PriceSessionView priceSessionView = priceSessionService.getMaxPriceSessionView(sessionId, "市场分析提取");
            viewId=priceSessionView.getId();
            priceSessionView.setData(viewData);
            priceSessionView.setCreatedAt(new Date());
            priceSessionService.updatePriceSessionViewByBean(priceSessionView);
        }

        return new ToolResponse(error, datas, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);
    }

}
