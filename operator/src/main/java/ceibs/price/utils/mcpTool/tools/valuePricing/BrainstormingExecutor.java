package ceibs.price.utils.mcpTool.tools.valuePricing;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.JsonFileUtil;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Author: RCC
 * @CreateTime:2025-06-16 17:51
 * @Description 头脑风暴技能
 */
@ToolHandler("brainstorming")
@Component
public class BrainstormingExecutor implements ToolExecutor {

    @Autowired
    PriceSessionService priceSessionService;

    @Value("${base.view.url}")
    private String baseViewUrl;

    @Value("${copilot.brainstorming}")
    private String brainstormingBots;


    private static final String TOOL_NAME = "brainstorming";


    /**
     * 触发技能直接打开头脑风暴新页面
     *
     * @param sessionId
     * @param args
     * @param requestToken
     * @param opt
     * @param autoTask
     * @return
     * @throws Exception
     */
    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) throws Exception {
        try {
            Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
            if(count==0){
                // 默认岗位+标签
                //String userTypeTags = copilotApiService.generateBrainstormingUserTypeTags(caseFile.getText(), requestToken);
                String userTypeTags = JsonFileUtil.loadJsonFromClasspath("userTypeTags.json");
                List<PriceSessionView> views = new ArrayList<>();
                String viewId = UUID.randomUUID().toString();
                PriceSessionView priceSessionView = new PriceSessionView();
                priceSessionView.setId(viewId);
                priceSessionView.setSessionId(sessionId);
                priceSessionView.setViewName("头脑风暴");
                priceSessionView.setViewDesc("头脑风暴");
                priceSessionView.setViewUrl(UrlUtil.getUrl("/hub-price/brainstormingBot"));
                priceSessionView.setCreatedAt(new Date());
                priceSessionView.setIndex(0);
                // priceSessionView2.setSelected();
                priceSessionView.setDelMark(false);
                priceSessionView.setData(userTypeTags);
                priceSessionView.setStep("2-1");
                views.add(priceSessionView);
                // 保存本次产生的相关数据
                priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, autoTask, opt);
                return new ToolResponse(false, null, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);
            }else{
                PriceSessionView maxPriceSessionView = priceSessionService.getMaxPriceSessionView(sessionId, "头脑风暴");
                return new ToolResponse(false, null, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, maxPriceSessionView.getId()), null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ToolResponse(true, null, null, "打开头脑风暴失败，请重试");

        }
    }

/*    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) throws Exception {

        try {
            // 获取 历史聊天记录
            JSONArray chatHistory =JSON.parseArray(args.get("chatHistory").toString());
            // 取最后一条聊天记录
            JSONObject lastChat = chatHistory.getJSONObject(chatHistory.size() - 1);
            String qestion = lastChat.getString("text").replaceFirst("头脑风暴", "");
            String simplifyHistory = copilotApiService.simplifyBrainstorming( chatHistory, requestToken);
            JSONObject jsonObject = copilotApiService.generateBrainstorming(qestion, simplifyHistory, requestToken);
            List<Map<String, Object>> datas = new ArrayList<>();
            Map data = new HashMap();
            data.put("text", jsonObject.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text"));
            datas.add(data);
            return new ToolResponse(false, datas, null, null);
        } catch (Exception e) {
            e.printStackTrace();
            return new ToolResponse(true, null, null, "头脑风暴失败，请重试");

        }
    }*/


}
