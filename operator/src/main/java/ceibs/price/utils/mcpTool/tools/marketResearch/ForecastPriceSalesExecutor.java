package ceibs.price.utils.mcpTool.tools.marketResearch;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 对特赞报告进行分析
 * 预测价格销量
 */
@ToolHandler("forecast_price_sales")
@Component
public class ForecastPriceSalesExecutor implements ToolExecutor {

    @Autowired
    private McpService mcpService;
    @Autowired
    private PriceSessionService priceSessionService;

    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) {
        String data;
        if (null == autoTask || !autoTask) {
            // 获取最近一份 特赞报告内容
            PriceSessionView marketAnalysis = priceSessionService.getMaxPriceSessionView(sessionId, "市场分析提取");
            if (null == marketAnalysis) {
                return new ToolResponse(true, null, null, "计算失败，请先进行市场分析提取");
            }
            data = marketAnalysis.getData();
        } else {
            // 自动任务固定报告
            data = """
                      [ {
                        "extracted_data" : {
                          "market_segments" : [ {
                            "consumer_profile" : "高净值商务精英",
                            "market_size" : 32000,
                            "price_probability_pairs" : [ [ 3000, 0.95 ], [ 4500, 0.9 ], [ 6000, 0.85 ], [ 8000, 0.75 ] ],
                            "price_demand_pairs" : [ [ 3000, 30000 ], [ 4500, 29000 ], [ 6000, 27000 ], [ 8000, 24000 ] ],
                            "description" : "35-45岁，高收入人群，对价格不敏感，但极度注重'物有所值'"
                          }, {
                            "consumer_profile" : "内容驱动型专业旅行者",
                            "market_size" : 58000,
                            "price_probability_pairs" : [ [ 3000, 0.85 ], [ 4500, 0.6 ], [ 6000, 0.35 ], [ 8000, 0.15 ] ],
                            "price_demand_pairs" : [ [ 3000, 49000 ], [ 4500, 35000 ], [ 6000, 20000 ], [ 8000, 9000 ] ],
                            "description" : "25-35岁，自由摄影师/旅行博主，以'出片率'为核心决策标准"
                          }, {
                            "consumer_profile" : "专业摄影师",
                            "market_size" : 25000,
                            "price_probability_pairs" : [ [ 3000, 0.9 ], [ 4500, 0.75 ], [ 6000, 0.5 ], [ 8000, 0.25 ] ],
                            "price_demand_pairs" : [ [ 3000, 23000 ], [ 4500, 19000 ], [ 6000, 13000 ], [ 8000, 6000 ] ],
                            "description" : "30-50岁，专业摄影师，深度体验，注重独特性和摄影价值"
                          }, {
                            "consumer_profile" : "高端休闲客群",
                            "market_size" : 45000,
                            "price_probability_pairs" : [ [ 3000, 0.95 ], [ 4500, 0.85 ], [ 6000, 0.65 ], [ 8000, 0.3 ] ],
                            "price_demand_pairs" : [ [ 3000, 43000 ], [ 4500, 38000 ], [ 6000, 29000 ], [ 8000, 14000 ] ],
                            "description" : "35-50岁，跨国企业高管，追求高品质的在地体验"
                          }, {
                            "consumer_profile" : "特殊场合情侣/夫妇",
                            "market_size" : 68000,
                            "price_probability_pairs" : [ [ 3000, 0.9 ], [ 4500, 0.75 ], [ 6000, 0.45 ], [ 8000, 0.2 ] ],
                            "price_demand_pairs" : [ [ 3000, 61000 ], [ 4500, 51000 ], [ 6000, 31000 ], [ 8000, 14000 ] ],
                            "description" : "30-40岁，高收入专业人士，来自一线城市，为纪念日/蜜月等特殊场合选择酒店"
                          } ]
                        },
                        "flag" : true
                      } ]
                    """;
        }
        JSONArray datas = JSON.parseArray(data);
        JSONObject dataJson = datas.getJSONObject(0);
        // 分析后，同时进行 成本定价计算
        Map<String, Object> args2 = new HashMap<>();
        args2.put("market_data", dataJson.get("extracted_data"));

        Map<String, Object> data1 = getData(args2);
        List<Map<String, Object>> datas1 = new ArrayList<>();
        datas1.add(data1);
        return new ToolResponse(false, datas1, null, null);
    }


    public Map<String, Object> getData(Map<String, Object> args) {
        McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall("calculate_cost_pricing_by_predict_data", args);
        List<McpSchema.Content> contentList = result.content();
        McpSchema.Content content = contentList.get(0);
        if (content instanceof McpSchema.TextContent textContent) {
            String text = textContent.text();
            // text to map
            Object parse = JSON.parse(text);
            if (parse instanceof JSONObject jsonObject) {
                // to Map
                Map map = jsonObject.toJavaObject(Map.class);
                if (map.containsKey("error")) {
                    throw new RuntimeException("成本定价失败：" + map.get("error").toString());
                }
                // 如果有报错
                return map;
            }
        }
        return null;
    }
}
