/*
package ceibs.price.utils.mcpTool.tools.competitionAnalyze;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.bean.yaml.tabler.Sheet;
import ceibs.price.service.CopilotApiService;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;


*/
/**
 * 竞争分析工具2, 区别在于是基于用户历史聊天记录来进行总结生成报告。
 *//*

@ToolHandler("analyze_competition")
@Component
public class CompetitiveAnalysis2Executor implements ToolExecutor {

    @Autowired
    private McpService mcpService;
    @Autowired
    PriceSessionService priceSessionService;
    private static final String TOOL_NAME = "analyze_competition";

    @Value("${base.view.url}")
    private String baseViewUrl;
    @Autowired
    private CopilotApiService copilotApiService;


    */
/**
     * 每次触发
     *
     * @param sessionId    会话id
     * @param args         参数 （能识别当下是需要 标准答案，还是用户和大模型交互后的答案）
     * @param requestToken 用户token
     * @param opt          内省操作人
     * @param autoTask     当前是否为自动任务
     * @return
     * @throws Exception
     *//*

    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) throws Exception {
        List<Map<String, Object>> datas = mcpService.getCache(TOOL_NAME);
        Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
        List<PriceSessionView> views = new ArrayList<>();
        PriceSessionView maxPriceSessionView = priceSessionService.getMaxPriceSessionView(sessionId, "竞争分析标准报告");
        if (null == maxPriceSessionView) {
            // 创建报告
            String viewId = UUID.randomUUID().toString();
            PriceSessionView priceSessionView = new PriceSessionView();
            priceSessionView.setId(viewId);
            priceSessionView.setSessionId(sessionId);
            priceSessionView.setViewName("竞争分析标准报告");
            priceSessionView.setViewDesc("竞争分析标准报告");
            priceSessionView.setViewUrl(UrlUtil.getUrl("/hub-price/matchview"));
            priceSessionView.setCreatedAt(new Date());
            priceSessionView.setIndex(count);
            //priceSessionView2.setSelected();
            priceSessionView.setDelMark(false);
            priceSessionView.setData(JSON.toJSONString(datas));
            priceSessionView.setStep("1-1");
            maxPriceSessionView=priceSessionView;
            views.add(priceSessionView);
        }
        if (null!=args&&args.containsKey("type") && args.get("type").equals("USER")) {
            // 基于聊天记录，生成新报告
            JSONArray chatHistory = JSON.parseArray(args.get("chatHistory").toString());
            String json = copilotApiService.generateUserCompetitionAnalyzeReport(chatHistory, requestToken);
            String viewId = UUID.randomUUID().toString();
            PriceSessionView priceSessionView = new PriceSessionView();
            priceSessionView.setId(viewId);
            priceSessionView.setSessionId(sessionId);
            priceSessionView.setViewName("竞争分析用户报告");
            priceSessionView.setViewDesc("竞争分析用户报告");
            priceSessionView.setViewUrl(UrlUtil.getUrl("/hub-price/matchview"));
            priceSessionView.setCreatedAt(new Date());
            priceSessionView.setIndex(count);
            //priceSessionView2.setSelected();
            priceSessionView.setDelMark(false);
            priceSessionView.setData(json);
            priceSessionView.setStep("1-1");
            maxPriceSessionView=priceSessionView;
            views.add(priceSessionView);
            //
        }


        // 保存本次产生的相关数据
        priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, autoTask, opt);


        return new ToolResponse(false, datas, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, maxPriceSessionView.getId()), null);
    }

}
*/
