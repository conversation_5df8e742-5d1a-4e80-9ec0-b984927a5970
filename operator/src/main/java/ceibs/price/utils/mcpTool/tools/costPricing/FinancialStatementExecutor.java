package ceibs.price.utils.mcpTool.tools.costPricing;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.AlwaysOnLoadingCache;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;


/**
 * 财务报表提取
 * 成本定价前置步骤2
 */
@ToolHandler("extract_financial_data_from_excel")
@Component
public class FinancialStatementExecutor implements ToolExecutor {

    @Autowired
    private McpService mcpService;
    @Autowired
    private PriceSessionService priceSessionService;

    @Value("${base.view.url}")
    private String baseViewUrl;
    @Value("${preview.url}")
    private String previewUrl;



    private static final String TOOL_NAME = "extract_financial_data_from_excel";
    private static final String EXCEL_URL = "https://cloud.ketanyun.cn/file/a51f704e-2021-4c9e-9662-d9f2d7f2044a";

    // 缓存财务报表数据，缓存时间为120分钟
    public LoadingCache<String, JSONObject> cache = new AlwaysOnLoadingCache<>
            (1000, 120, TimeUnit.MINUTES, new CacheLoader<String, JSONObject>() {
                public JSONObject load(String key) throws Exception {
                    return loadData(key);
                }
            });

    private JSONObject loadData(String key) {
        try {
            JSONObject viewData = new JSONObject();
            viewData.put("excelPreviewUrl", previewUrl.concat(key));
            Map<String, Object> args = new HashMap<>();
            args.put("excel_path", key);
            // 根据财务报表请求两次数据
            args.put("perspective", "financial");
            List<Map<String, Object>> datas = new ArrayList<>();
            List<Map<String, Object>> datas2 = new ArrayList<>();
            McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall(TOOL_NAME, args);
            List<McpSchema.Content> contentList = result.content();
            McpSchema.Content content = contentList.get(0);
            if (content instanceof McpSchema.TextContent textContent) {
                String text = textContent.text();
                // text to map
                Object parse = JSON.parse(text);
                if (parse instanceof JSONObject jsonObject) {
                    // to Map
                    Map map = jsonObject.toJavaObject(Map.class);
                    datas.add(map);
                }
                viewData.put("financial", datas);
            }
            //
            args.put("perspective", "economic");
            result = (McpSchema.CallToolResult) mcpService.executeToolCall(TOOL_NAME, args);
            contentList = result.content();
            content = contentList.get(0);
            if (content instanceof McpSchema.TextContent textContent) {
                String text = textContent.text();
                // text to map
                Object parse = JSON.parse(text);
                if (parse instanceof JSONObject jsonObject) {
                    // to Map
                    Map map = jsonObject.toJavaObject(Map.class);
                    datas2.add(map);
                }
                viewData.put("economic", datas2);
            }
            return viewData;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("分析财务报表数据失败，请重试！");
        }

    }

    /**
     * 根据默认财务报表excle，提取财务数据
     *
     * @param sessionId
     * @param args
     * @param requestToken
     * @param opt
     * @param autoTask
     * @return
     */
    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) throws ExecutionException {
        Boolean error = false;
        String viewId;
        String excelUrl = args.containsKey("excel_path") ? args.get("excel_path").toString() : EXCEL_URL;
        Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
        PriceSessionView excleView = new PriceSessionView();
        JSONObject viewData = new JSONObject();
        viewData.put("excelPreviewUrl", previewUrl.concat(excelUrl));
        //  arg 有视角参数，则证明需要计算，如果没有，只需要打开财务报表页面. 如果已经有对应页面了，则直接返回对应页面
        if (0 == count) {
            viewId = UUID.randomUUID().toString();
            if (null != args && !args.containsKey("onlyOpen")) {
                // 第一次打开，并且没有 onlyOpen 参数，则直接计算
                viewData = cache.get(excelUrl);
            } else {
                // 第一次打开 ，有onlyOpen，则直接打开页面

            }
            excleView.setId(viewId);
            excleView.setSessionId(sessionId);
            excleView.setViewName("财务报表");
            excleView.setViewDesc("财务报表");
            excleView.setViewUrl(UrlUtil.getUrl("/hub-price/financialstatements"));
            excleView.setCreatedAt(new Date());
            excleView.setIndex(1);
            excleView.setData(JSON.toJSONString(viewData));
            excleView.setDelMark(false);
            excleView.setStep("3-1");
            List<PriceSessionView> views = new ArrayList<>();
            views.add(excleView);
            priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, autoTask,opt);
        } else {
            // 不是第一次打开
            excleView = priceSessionService.getMaxPriceSessionView(sessionId, "财务报表");
            //
            if (null == excleView) {
                // 异常情况下，可能为空，兼容处理
                excleView = new PriceSessionView();
                excleView.setId(UUID.randomUUID().toString());
                excleView.setSessionId(sessionId);
                excleView.setViewName("财务报表");
                excleView.setViewDesc("财务报表");
                excleView.setViewUrl(UrlUtil.getUrl("/hub-price/financialstatements"));
                excleView.setCreatedAt(new Date());
                excleView.setIndex(1);
                excleView.setData(JSON.toJSONString(viewData));
                excleView.setDelMark(false);
                excleView.setStep("3-1");
            }
            viewId = excleView.getId();
            if (null != args && !args.containsKey("onlyOpen")) {
                // 有视角参数，则进行计算，更新页面数据
                viewData = cache.get(excelUrl);
                excleView.setData(JSON.toJSONString(viewData));
            } else {
                // 没有视角参数，返回页面信息
            }
            priceSessionService.updatePriceSessionViewByBean(excleView);
        }
        Map data = viewData.toJavaObject(Map.class);
        List<Map<String, Object>> datas = new ArrayList<>();
        datas.add(data);
        return new ToolResponse(error, datas, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);
    }


}
