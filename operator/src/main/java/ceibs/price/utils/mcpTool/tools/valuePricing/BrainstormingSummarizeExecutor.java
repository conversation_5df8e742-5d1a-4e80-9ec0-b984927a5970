package ceibs.price.utils.mcpTool.tools.valuePricing;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.CopilotApiService;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: RCC
 * @CreateTime:2025-06-16 17:51
 * @Description 头脑风暴总结
 */
@ToolHandler("brainstorming_summarize")
@Component
public class BrainstormingSummarizeExecutor implements ToolExecutor {

    @Autowired
    CopilotApiService copilotApiService;
    @Autowired
    private McpService mcpService;
    @Autowired
    private PriceSessionService priceSessionService;
    private static String TOOL_NAME = "brainstorming_summarize";

    @Value("${base.view.url}")
    private String baseViewUrl;

    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) throws Exception {

        try {
            // 获取 头脑风暴view
            String parentViewId = args.get("viewId").toString();
            PriceSessionView parentView = priceSessionService.getPriceSessionView(parentViewId);
            // 竞争分析报告
            String jzfx = getJZFX(args);
            // 获取 收敛维度
            JSONObject   summaryDimensionJSON = getSummaryDimension(args);
            // 获取 历史聊天记录
            JSONArray chatHistory = JSON.parseArray(args.get("chatHistory").toString());
            // 总结主题
            String s = copilotApiService.extractTheTopic(jzfx, summaryDimensionJSON, chatHistory, requestToken);
            if (null == s) {
                throw new RuntimeException("头脑风暴主题总结失败，请重试");
            }
            JSONArray summaryTopic;
            // 推荐主题
            JSONArray tjTopic = new JSONArray();
            try {
                 summaryTopic = JSONArray.parseArray(s);
            } catch (Exception e) {
                throw new RuntimeException("头脑风暴主题总结数据格式有误，请重新进行头脑风暴主题总结");
            }
            // 只需要 type ="推荐主题的内容"
            String parentSessionId = args.get("parentSessionId").toString();
            // 只需要 推荐主题
            for (Object o : summaryTopic) {
                JSONObject jsonObject = (JSONObject)o;
                if ("推荐主题".equals(jsonObject.getString("type"))) {
                    tjTopic.add(jsonObject);
                }
            }
            List<Map<String, Object>> differentiated = differentiated(parentSessionId, tjTopic);
            JSONObject oldData = JSONObject.parseObject(parentView.getData());
            oldData.put("differentiated", differentiated);
            oldData.put("chatSessionId", sessionId);
            oldData.put("summaryDimension", summaryDimensionJSON);
            oldData.put("summaryTopic", summaryTopic);
            parentView.setData(oldData.toJSONString());
            priceSessionService.updatePriceSessionViewByBean(parentView);
            List<Map<String, Object>> results = new ArrayList<>();
            JSONObject result = new JSONObject();
            result.put("differentiated", differentiated);
            result.put("summaryTopic", summaryTopic);
            results.add(result.toJavaObject(Map.class));
            return new ToolResponse(false, results, null, null);

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("头脑风暴主题总结失败，请重试:"+e.getMessage());
        }
    }

    public List<Map<String, Object>> differentiated(String parentSessionId, JSONArray topicList) {
        List<Map<String, Object>> datas = new ArrayList<>();
        try {
            // 获取竞争分析提取信息
            PriceSessionView jzfx = priceSessionService.getMaxPriceSessionView(parentSessionId, "竞争分析");
            if (null == jzfx) {
                throw new RuntimeException("分析失败，请先进行竞争分析");
            }
            JSONObject input = new JSONObject();
            input.put("competition", jzfx.getData());
            input.put("history", "");
            input.put("theme_list", topicList.toJSONString());

            McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall("analyze_differentiation", input);
            if(null== result){
                throw new RuntimeException("分析失败，请重试");
            }
            List<McpSchema.Content> contentList = result.content();
            McpSchema.Content content = contentList.get(0);

            if (content instanceof McpSchema.TextContent textContent) {
                String text = textContent.text();
                // text to map
                Object parse = JSON.parse(text);
                if (parse instanceof JSONArray array) {
                    // jsonArray to List<Map>
                    datas = array.stream()
                            .filter(JSONObject.class::isInstance)
                            .map(o -> (Map<String, Object>) ((JSONObject) o).toJavaObject(Map.class))
                            .collect(Collectors.toList());
                }
                if (parse instanceof JSONObject object) {
                    //
                    datas = Collections.singletonList(object.toJavaObject(Map.class));
                }
            }
            return datas;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("分析失败，请重试");
        }
    }

    private String getJZFX(Map<String, Object> args) {
        try {
            String parentSessionId = args.get("parentSessionId").toString();
            PriceSessionView parentView = priceSessionService.getMaxPriceSessionView(parentSessionId, "竞争分析");
            return parentView.getData();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("无法获取竞争分析报告，请确认是否已经进行过竞争分析。");
        }
    }

    private JSONObject getSummaryDimension(Map<String, Object> args) {
        if( args.containsKey("summaryDimension")){
            // 检验数据格式，要求能转换成JSONObject
            try {
                return JSONObject.parseObject(JSON.toJSONString(args.get("summaryDimension")));
            } catch (Exception e) {
                throw new RuntimeException();
            }
        }
        return null;
    }


}
