package ceibs.price.utils.mcpTool.tools.competitivePricing;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.Case;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.bean.yaml.tabler.Sheet;
import ceibs.price.service.CaseService;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

@ToolHandler("analyze_competition")
@Component
public class CompetitiveAnalysisExecutor implements ToolExecutor {

    @Autowired
    private McpService mcpService;
    @Autowired
    CaseService caseService;
    @Autowired
    PriceSessionService priceSessionService;
    private static final String TOOL_NAME = "analyze_competition";

    @Value("${base.view.url}")
    private String baseViewUrl;

    @Override
    public Object execute(String sessionId, String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) throws Exception {
        // 标准答案
        List<Map<String, Object>> datas = new ArrayList<>();
        Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
        Boolean error = false;
        PriceSessionView competitiveDim = priceSessionService.getMaxPriceSessionView(sessionId, "行业竞争维度定义");
        if (null != competitiveDim) {
            String data = competitiveDim.getData();
            if (data != null && JSONObject.parseObject(competitiveDim.getData()).containsKey("fields")) {
                args.put("cache", false);
            }
        }
        // 如果是自动执行、则直接以标准答案返回(并非完整演示入口，有区别)
        if ((null != autoTask && autoTask) || (args.containsKey("cache") && (Boolean) args.get("cache"))) {
            datas = mcpService.getCache(TOOL_NAME);
        } else {
            try {
                Case caseBo = caseService.findFirstByActiveIsTrue();
                // 获取上一步骤定义的竞争维度

                String dimensions = null;
                if (null != competitiveDim) {
                    JSONArray fields = JSONObject.parseObject(competitiveDim.getData()).getJSONArray("fields");
                    Set<String> dimensionSet = new HashSet<>();
                    // 提取已有的字段名
                    for (Object fieldObj : fields) {
                        JSONObject field = (JSONObject) fieldObj;
                        String name = field.getString("name");
                        dimensionSet.add(name);
                    }

                    // 定义需要确保存在的字段
                    List<String> required1Fields = Arrays.asList("公司名称", "地理位置", "平均价格");
                    // 检查是否缺失，若缺失则追加
                    for (String requiredField : required1Fields) {
                        if (!dimensionSet.contains(requiredField)) {
                            dimensionSet.add(requiredField);
                        }
                    }
                    // 最终拼接结果
                    dimensions = String.join(",", dimensionSet);
                }

                Map<String, Object> toolArgs = new HashMap<>();
                toolArgs.put("file_url", caseBo.getPdfUrl());
                toolArgs.put("dimensions", dimensions);
                McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall(TOOL_NAME, toolArgs);
                List<McpSchema.Content> contentList = result.content();
                McpSchema.Content content = contentList.get(0);
                if (content instanceof McpSchema.TextContent textContent) {
                    String text = textContent.text();
                    // text to map
                    Object parse = JSON.parse(text);
                    if (parse instanceof JSONObject jsonObject) {
                        // to Map
                        Map map = jsonObject.toJavaObject(Map.class);
                        datas.add(map);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("生成竞争分析报告失败，请重新试试");
            }
        }
        List<PriceSessionView> views = new ArrayList<>();
        String viewId = UUID.randomUUID().toString();
        if (count == 0) {
            PriceSessionView view = new PriceSessionView();
            view.setId(viewId);
            view.setSessionId(sessionId);
            view.setViewName("竞争分析");
            view.setViewDesc("竞争分析");
            view.setViewUrl(UrlUtil.getUrl("/hub-price/matchview"));
            view.setCreatedAt(new Date());
            view.setIndex(count);
            // priceSessionView2.setSelected();
            view.setDelMark(false);
            view.setData(JSON.toJSONString(datas));
            view.setStep("1-1");
            views.add(view);

        } else {
            // 更新
            PriceSessionView view = priceSessionService.getMaxPriceSessionView(sessionId, "竞争分析");
            viewId=view.getId();
            view.setCreatedAt(new Date());
            view.setData(JSON.toJSONString(datas));
            views.add(view);
        }
        // 保存本次产生的相关数据
        priceSessionService.savePriceSession(sessionId, classId, TOOL_NAME, views, autoTask, opt);
        return new ToolResponse(error, datas, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);


    }

    /**
     * 构建工具1.1 会产生的表格 patch
     *
     * @return patch  json
     */
    public JSONArray ops(List<PriceSessionView> views, String sessionId) {
        JSONArray ops = new JSONArray();

        JSONObject op1 = new JSONObject();
        String sheetId = UUID.randomUUID().toString();
        String viewId = UUID.randomUUID().toString();
        Sheet competitorSheet = Sheet.buildCompetitorSheet(viewId);
        op1.put("op", "add");
        op1.put("path", "/spec/sheets/@" + sheetId);
        op1.put("value", JSON.toJSONString(competitorSheet));
        ops.add(op1);

        PriceSessionView priceSessionView = new PriceSessionView();
        priceSessionView.setId(UUID.randomUUID().toString());
        priceSessionView.setSessionId(sessionId);
        priceSessionView.setViewName("竞争分析");
        priceSessionView.setViewUrl("/hub-price/" + sheetId + "/" + viewId);
        priceSessionView.setCreatedAt(new Date());
        priceSessionView.setIndex(0);
        // priceSessionView.setSelected();
        // priceSessionView.setData();
        priceSessionView.setStep("1-1");
        views.add(priceSessionView);


/*
        JSONObject op2 = new JSONObject();
        String sheetId2 = UUID.randomUUID().toString();
        Sheet sheet2 = Sheet.buildCompetitorSheet();
        op2.put("op", "add");
        op2.put("path", "/spec/sheets/@" + sheetId2);
        op2.put("value", JSON.toJSONString(sheet2));
        ops.add(op2);

        JSONObject op3 = new JSONObject();
        String sheetId3 = UUID.randomUUID().toString();
        Sheet sheet3 = Sheet.buildCompetitorSheet();
        op3.put("op", "add");
        op3.put("path", "/spec/sheets/@" + sheetId3);
        op3.put("value", JSON.toJSONString(sheet3));
        ops.add(op3);*/

        return ops;
    }

    public String loadYamlContent() throws Exception {
        ClassPathResource resource = new ClassPathResource("yaml/default.yaml");
        return new String(Files.readAllBytes(Paths.get(resource.getURI())));
    }
}
