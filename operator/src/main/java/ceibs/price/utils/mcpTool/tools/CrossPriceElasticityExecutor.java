package ceibs.price.utils.mcpTool.tools;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 计算交叉价格弹性（一个产品需求对另一个产品价格变化的敏感度）
 * Args:
 * prices: 价格列表
 * quantities: 销量列表
 */
@ToolHandler("calculate_cross_price_elasticity")
@Component
public class CrossPriceElasticityExecutor implements ToolExecutor {

    @Autowired
    private McpService mcpService;
    @Autowired
    private PriceSessionService priceSessionService;
    private static String TOOL_NAME = "calculate_cross_price_elasticity";


    @Value("${base.view.url}")
    private String baseViewUrl;

    private static final String DEFAULT_DATA = "{\"prices_a\":[100.5,95],\"quantities_a\":[10,10],\"prices_b\":[100.5,95],\"quantities_b\":[10,10]}";


    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt,Boolean autoTask) {
        Boolean error = false;
        List<Map<String, Object>> datas = new ArrayList<>();
        Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
        // 如果当前会话第一次打开该页面，则同时打开对应的 excle页面，如果有参数，则使用参数计算生成图表页面
        PriceSessionView excleView = new PriceSessionView();
        if (0 == count) {
            String viewId = UUID.randomUUID().toString();
            excleView.setId(viewId);
            excleView.setSessionId(sessionId);
            excleView.setViewName("交叉弹性计算价格、销量列表");
            excleView.setViewDesc("交叉弹性计算价格、销量列表");
            excleView.setViewUrl(UrlUtil.getUrl("/hub-price/charts/mixelasticities_edit"));
            excleView.setCreatedAt(new Date());
            excleView.setIndex(0);
            //priceSessionView.setSelected();
            excleView.setDelMark(false);
            excleView.setData(DEFAULT_DATA);
            /*if (args != null && !args.isEmpty()) {
                // 第一次也有可能直接给了参数，则直接使用参数计算
                excleView.setData(JSONObject.toJSONString(args.get("args")));
            }*/
            excleView.setStep("4-3");
            List<PriceSessionView> views = new ArrayList<>();
            views.add(excleView);
            priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views,autoTask,opt);
        } else {
            // 如果不是第一次，则更新数据,并且给了新的数据
            List<PriceSessionView> priceSessionViews = priceSessionService.findBySessionIdAndViewNameAndDelMarkIsFalse(sessionId, "点弹性计算价格、销量列表");
            excleView = priceSessionViews.get(0);
        }

        try {
            // 每次技能触发，都恒定根据 excleView 的数据进行计算
            McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall(TOOL_NAME, JSONObject.parseObject(excleView.getData()));
            List<McpSchema.Content> contentList = result.content();
            McpSchema.Content content = contentList.get(0);
            if (content instanceof McpSchema.TextContent textContent) {
                String text = textContent.text();
                // text to map
                Object parse = JSON.parse(text);
                if (parse instanceof JSONObject jsonObject) {
                    // to Map
                    Map map = jsonObject.toJavaObject(Map.class);
                    Map mainMap = new HashMap();
                    Map excleMap = JSONObject.parseObject(excleView.getData()).toJavaObject(Map.class);
                    mainMap.put("excleData", excleMap);
                    mainMap.put("echatsData", map);
                    datas.add(mainMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ToolResponse(true, null, null, "计算失败，请重试");
        }
        String viewId = UUID.randomUUID().toString();
        PriceSessionView priceSessionView = new PriceSessionView();
        priceSessionView.setId(viewId);
        priceSessionView.setSessionId(sessionId);
        priceSessionView.setViewName("交叉价格弹性计算");
        priceSessionView.setViewDesc("交叉价格弹性计算");
        priceSessionView.setViewUrl(UrlUtil.getUrl("/hub-price/charts/mixelasticities"));
        priceSessionView.setCreatedAt(new Date());
        priceSessionView.setIndex(1 + count);
        //priceSessionView.setSelected();
        priceSessionView.setDelMark(false);
        priceSessionView.setData(JSON.toJSONString(datas));
        priceSessionView.setStep("4-3");
        List<PriceSessionView> views = new ArrayList<>();
        views.add(priceSessionView);
        priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views,autoTask,opt);

        return new ToolResponse(error, datas, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);
    }


}
