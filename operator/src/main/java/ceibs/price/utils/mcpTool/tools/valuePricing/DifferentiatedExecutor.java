package ceibs.price.utils.mcpTool.tools.valuePricing;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.CopilotApiService;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.modelcontextprotocol.spec.McpSchema;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 分析产品差异化价值
 */
@ToolHandler("analyze_differentiation")
@Component
public class DifferentiatedExecutor implements ToolExecutor {

    @Autowired
    private McpService mcpService;
    @Autowired
    private PriceSessionService priceSessionService;

    @Value("${base.view.url}")
    private String baseViewUrl;

    private static String TOOL_NAME = "analyze_differentiation";

    @Autowired
    CopilotApiService copilotApiService;

    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt,Boolean autoTask) {
        List<Map<String, Object>> datas = new ArrayList<>();
        try {

            // 获取竞争分析提取信息
            PriceSessionView jzfx = priceSessionService.getMaxPriceSessionView(sessionId, "竞争分析");
            if(null==jzfx){
                return new ToolResponse(true, null, null, "分析失败，请先进行竞争分析");
            }
            // 头脑风暴总结
            PriceSessionView tnfb = priceSessionService.getMaxPriceSessionView(sessionId, "头脑风暴主题总结");
            if(null == tnfb || StringUtil.isBlank(tnfb.getData())){
                return new ToolResponse(true, null, null, "分析失败，请先进行头脑风暴主题总结");
            }
            // 如果data 格式不对，不是jsonarray string
            try {
                JSONArray.parseArray(tnfb.getData());
            } catch (Exception e) {
                return new ToolResponse(true, null, null, "分析失败，头脑风暴主题总结数据格式有误，请重新进行头脑风暴主题总结");
            }
            JSONArray topicList = JSONArray.parseArray(tnfb.getData());
            // 移除掉topicList未选中的
            Iterator<Object> iterator = topicList.iterator();
            while (iterator.hasNext()) {
                Object item = iterator.next();
                if (item instanceof JSONObject json) {
                    Map<String, Object> map = json.toJavaObject(Map.class);
                    if (!map.containsKey("checked") || !(Boolean) map.get("checked")) {
                        iterator.remove(); // 安全删除
                    }
                }
            }
         /*   if(topicList.isEmpty()){
                return new ToolResponse(true, null, null, "分析失败，请先进行头脑风暴总结并选择需要进行差异化分析的主题");
            }*/

            // 获取 历史聊天记录
            JSONArray chatHistoryArray =JSON.parseArray(args.get("chatHistory").toString());
            //String simplifyHistory = copilotApiService.simplifyBrainstorming( chatHistoryArray, requestToken);
            JSONObject input =new JSONObject();
            input.put("competition",jzfx.getData());
            input.put("history",chatHistoryArray.toJSONString());
            input.put("theme_list",topicList.toJSONString());

            McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall(TOOL_NAME, input);
            List<McpSchema.Content> contentList = result.content();
            McpSchema.Content content = contentList.get(0);

            if (content instanceof McpSchema.TextContent textContent) {
                String text = textContent.text();
                // text to map
                Object parse = JSON.parse(text);
                if (parse instanceof JSONArray array) {
                    // jsonArray to List<Map>
                    datas= array.stream()
                            .filter(JSONObject.class::isInstance)
                            .map(o -> (Map<String, Object>) ((JSONObject) o).toJavaObject(Map.class))
                            .collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ToolResponse(true, null, null, "分析失败，请重试");
        }
        Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
        Boolean error = false;
        String viewId = UUID.randomUUID().toString();
        PriceSessionView priceSessionView = new PriceSessionView();
        priceSessionView.setId(viewId);
        priceSessionView.setSessionId(sessionId);
        priceSessionView.setViewName("产品差异化价值");
        priceSessionView.setViewDesc("分析产品差异化价值");
        priceSessionView.setViewUrl(UrlUtil.getUrl("/hub-price/differentiation"));
        priceSessionView.setCreatedAt(new Date());
        priceSessionView.setIndex(1+count);
        JSONArray checkData = new JSONArray();
       /* if(autoTask){
            JSONObject jsonObject = (JSONObject) datas.get(0);
            // 遍历 key
            for (String key : jsonObject.keySet()) {
                JSONArray value = (JSONArray) jsonObject.get(key);
                checkData.add(value.get(0));
                if(checkData.size()==2){
                    break;
                }
            }
        }*/
        priceSessionView.setData(JSON.toJSONString(datas));
        //priceSessionView.setSelected();
        priceSessionView.setDelMark(false);
        priceSessionView.setStep("1-4");
        List<PriceSessionView> views = new ArrayList<>();
        views.add(priceSessionView);
        priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views,autoTask,opt);
        return new ToolResponse(error, datas, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);
    }
}
