package ceibs.price.utils.mcpTool.tools.competitivePricing;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

@ToolHandler("analyze_vrio")
@Component
public class VRIOExecutor implements ToolExecutor {

    @Autowired
    private McpService mcpService;
    private final static String TOOL_NAME = "analyze_vrio";

    @Value("${base.view.url}")
    private String baseViewUrl;
    @Autowired
    private PriceSessionService priceSessionService;

    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) {
        List<Map<String, Object>> datas = new ArrayList<>();
        try {
            if (null == autoTask && !autoTask) {
                McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall(TOOL_NAME, args);
                List<McpSchema.Content> contentList = result.content();
                McpSchema.Content content = contentList.get(0);
                if (content instanceof McpSchema.TextContent textContent) {
                    String text = textContent.text();
                    // text to map
                    Object parse = JSON.parse(text);
                    if (parse instanceof JSONObject jsonObject) {
                        // to Map
                        Map map = jsonObject.toJavaObject(Map.class);
                        datas.add(map);
                    }
                }
            } else {
                datas = mcpService.getCache(TOOL_NAME);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ToolResponse(true, null, null, "提取失败，请重试");
        }

        Boolean error = false;
        String viewId = UUID.randomUUID().toString();
        PriceSessionView priceSessionView = new PriceSessionView();
        priceSessionView.setId(viewId);
        priceSessionView.setSessionId(sessionId);
        priceSessionView.setViewName("VRIO分析");
        priceSessionView.setViewDesc("VRIO分析");
        priceSessionView.setViewUrl(UrlUtil.getUrl("/hub-price/simplematch"));
        priceSessionView.setCreatedAt(new Date());
        priceSessionView.setIndex(0);
        // priceSessionView.setSelected();
        priceSessionView.setDelMark(false);
        priceSessionView.setData(JSON.toJSONString(datas));
        priceSessionView.setStep("1-2");
        List<PriceSessionView> views = new ArrayList<>();
        views.add(priceSessionView);
        priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, autoTask,opt);
        return new ToolResponse(error, datas, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);
    }
}
