package ceibs.price.utils.mcpTool.tools.valuePricing;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.CopilotApiService;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 分析产品差异化价值
 */
@ToolHandler("analyze_differentiation_demo")
@Component
public class DifferentiatedDemoExecutor implements ToolExecutor {

    @Autowired
    private McpService mcpService;
    @Autowired
    private PriceSessionService priceSessionService;

    @Value("${base.view.url}")
    private String baseViewUrl;

    private static String TOOL_NAME = "analyze_differentiation_demo";

    @Autowired
    CopilotApiService copilotApiService;


    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) {
        List<Map<String, Object>> datas = new ArrayList<>();
        String DEMO_DATE = """
                [{"主题":"财务视角的客户细分主题","人无我有 - 功能":[{"描述":"提供雪山景观别墅，功能独特，满足高端度假游客对私密性和景观的需求。","价值元素":"雪山景观别墅","类型":"功能型","打分":[9,2]}],"人有我劣":[{"描述":"相比丽江和府洲际度假酒店，缺乏大型会议设施，对商务会议客群吸引力较低。","价值元素":"商务会议设施","类型":"功能型","打分":[3,8]}],"人无我有 - 体验":[{"描述":"结合玉龙雪山文化，提供独特的文化体验活动，如雪山徒步、纳西文化体验等。","价值元素":"雪山文化体验","类型":"体验型","打分":[9,3]}],"人无我有 - 信任":[{"描述":"拥有专业酒店资产管理团队，提供高标准的服务和管理，增强客户信任。","价值元素":"专业酒店资产管理团队","类型":"信任型","打分":[8,4]}],"人有我优":[{"描述":"依托玉龙雪山独特地理位置，提供高端度假体验，吸引追求自然美和深度体验的高端度假游客。","价值元素":"高端度假体验","类型":"体验型","打分":[8,5]}]},{"主题":"成本控制与优化主题","人无我有 - 功能":[{"描述":"独特的雪山景观资源，可开发高附加值的体验项目，如观星活动等。","价值元素":"雪山景观资源","类型":"功能型","打分":[9,3]}],"人有我劣":[{"描述":"相比丽江金茂隐逸酒店·凯悦臻选，设施利用率较低，影响收入来源。","价值元素":"设施利用率","类型":"功能型","打分":[4,7]}],"人无我有 - 体验":[{"描述":"提供专业的雪山徒步体验，结合当地文化，打造独特的户外活动。","价值元素":"雪山徒步体验","类型":"体验型","打分":[8,2]}],"人无我有 - 信任":[{"描述":"通过环保认证和可持续发展实践，增强客户对酒店的信任和认可。","价值元素":"环保认证","类型":"信任型","打分":[7,4]}],"人有我优":[{"描述":"通过本地采购食材降低成本，同时提升餐饮的新鲜度和地域特色。","价值元素":"本地采购食材","类型":"功能型","打分":[7,5]}]},{"主题":"财务管理创新主题","人无我有 - 功能":[{"描述":"采用创新运营模式，如结合当地文化开发特色体验项目，提升收入来源。","价值元素":"创新运营模式","类型":"功能型","打分":[8,3]}],"人有我劣":[{"描述":"相比使用现代财务管理工具的酒店，财务透明度和数据分析能力较弱。","价值元素":"财务透明度","类型":"信任型","打分":[4,7]}],"人无我有 - 体验":[{"描述":"提供高度个性化的服务，如定制化的雪山文化体验，增强客户满意度。","价值元素":"个性化服务","类型":"体验型","打分":[9,4]}],"人无我有 - 信任":[{"描述":"通过专业资产管理团队，提升酒店整体运营效率和客户信任度。","价值元素":"专业资产管理","类型":"信任型","打分":[8,5]}],"人有我优":[{"描述":"通过精准定位高端度假游客，优化财务策略，提升市场竞争力。","价值元素":"精准定位能力","类型":"功能型","打分":[8,5]}]}]
                """;
        JSONArray data = JSONObject.parseArray(DEMO_DATE);
        // data to Map
        /*Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("data", data);
        datas.add(data);*/


        Boolean error = false;
        String viewId = UUID.randomUUID().toString();
        PriceSessionView priceSessionView = new PriceSessionView();
        priceSessionView.setId(viewId);
        priceSessionView.setSessionId(sessionId);
        priceSessionView.setViewName("产品差异化价值");
        priceSessionView.setViewDesc("分析产品差异化价值");
        priceSessionView.setViewUrl(UrlUtil.getUrl("/hub-price/differentiation"));
        priceSessionView.setCreatedAt(new Date());
        priceSessionView.setIndex(0);
        priceSessionView.setData(DEMO_DATE);
        //priceSessionView.setSelected();
        priceSessionView.setDelMark(false);
        priceSessionView.setStep("1-4");
        List<PriceSessionView> views = new ArrayList<>();
        views.add(priceSessionView);
        priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, true, opt);
        return new ToolResponse(error, datas, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);
    }
}
