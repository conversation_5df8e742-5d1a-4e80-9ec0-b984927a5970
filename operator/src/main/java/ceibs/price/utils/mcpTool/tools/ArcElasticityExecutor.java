package ceibs.price.utils.mcpTool.tools;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.McpService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.modelcontextprotocol.spec.McpSchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 计算弧价格弹性（两个价格点之间的平均弹性）
 * Args:
 * prices: 价格列表
 * quantities: 销量列表
 */
@ToolHandler("calculate_arc_elasticity")
@Component
public class ArcElasticityExecutor implements ToolExecutor {

    @Autowired
    private McpService mcpService;
    @Autowired
    private PriceSessionService priceSessionService;
    private static String TOOL_NAME = "calculate_arc_elasticity";

    @Value("${base.view.url}")
    private String baseViewUrl;


    /**
     * 直接执行工具，用默认值进行计算，给出默认结果
     * 用户页面调整默认值后，调用该方法产生新的数据
     *
     * @param sessionId
     * @param args
     * @param requestToken
     * @param opt
     * @return
     */
    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) {

        Boolean error = false;
        Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
        JSONObject viewData = new JSONObject();
        JSONObject e_data;
        // 如果 args 有data值，则使用参数计算生成图表页面，没有数据,则使用默认值进行计算
        if (args != null && !args.isEmpty() && args.containsKey("e_data")) {
            e_data = (JSONObject) JSON.toJSON(args.get("e_data"));
            //  检查数据格式 以及完整性
            if (!e_data.containsKey("predict_price_and_sales") || !e_data.containsKey("financial_price_and_sales") || !e_data.containsKey("economic_price_and_sales")) {
                return new ToolResponse(true, null, null, "数据格式有误，请检查输入");
            }
        } else {
            e_data = new JSONObject();
            // 如果有成本计算，则以成本结果进行计算
            PriceSessionView costPricing = priceSessionService.getMaxPriceSessionView(sessionId, "成本定价");
            if (null == costPricing || costPricing.getData().isEmpty()) {
                return new ToolResponse(true, null, null, "请先进行成本定价");
            }
            // 以成本定价结果，做初始化数据
            JSONObject costPricingData = JSONArray.parseArray(costPricing.getData()).getJSONObject(0);
            e_data.put("predict_price_and_sales", costPricingData.getJSONArray("predict_price_and_sales"));
            e_data.put("financial_price_and_sales", costPricingData.getJSONArray("base_cases").getJSONObject(0).getJSONArray("price_and_base_sales"));
            e_data.put("economic_price_and_sales", costPricingData.getJSONArray("base_cases_economic").getJSONObject(0).getJSONArray("price_and_base_sales"));
        }
        if (e_data == null || e_data.isEmpty()) {
            return new ToolResponse(true, null, null, "无法获取价格销量信息，请先进行成本定价");
        }
        try {
            viewData.put("excelData", e_data);
            Map<String, Object> data = getData(e_data);
            viewData.put("chartData", data);
        }  catch (com.alibaba.fastjson.JSONException jsonEx) {
            jsonEx.printStackTrace();
            // 单独处理 JSON 解析/转换异常
            return new ToolResponse(true, null, null, "计算数据格式有误，请检查输入");
        } catch (Exception ex) {
            ex.printStackTrace();
            // 处理其他所有异常
            return new ToolResponse(true, null, null, "计算失败，请重试");
        }


        String viewId = UUID.randomUUID().toString();
        PriceSessionView priceSessionView = new PriceSessionView();
        priceSessionView.setId(viewId);
        priceSessionView.setSessionId(sessionId);
        priceSessionView.setViewName("价格弧弹性计算");
        priceSessionView.setViewDesc("价格弧弹性计算");
        priceSessionView.setViewUrl(UrlUtil.getUrl("/hub-price/charts/arcelasticities"));
        priceSessionView.setCreatedAt(new Date());
        priceSessionView.setIndex(1 + count);
        // priceSessionView.setSelected();
        priceSessionView.setDelMark(false);
        priceSessionView.setData(JSON.toJSONString(viewData));
        priceSessionView.setStep("4-1");
        List<PriceSessionView> views = new ArrayList<>();
        views.add(priceSessionView);
        priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, autoTask,opt);
        Map map = JSON.parseObject(viewData.toJSONString(), Map.class);
        List<Map<String,Object>> datas = new ArrayList<>();
        datas.add(map);
        return new ToolResponse(error, datas, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), null);
    }


    /**
     * 计算3组数据
     * 1：预测 价格、销量
     * 2：经济学财务 价格、销量
     * 3：财务价格、销量
     */
    public Map<String, Object> getData(JSONObject e_data) {
        Map<String, Object> data = new HashMap<>();
        // 预测成本数据
        JSONArray priceAndSales = e_data.getJSONArray("predict_price_and_sales");
        JSONObject args = new JSONObject();
        args.put("prices", priceAndSales.stream().map(item -> ((JSONArray) item).get(0)).toList());
        args.put("quantities", priceAndSales.stream().map(item -> ((JSONArray) item).get(1)).toList());
        // 每次技能触发，都恒定根据 excleView 的数据进行计算
        McpSchema.CallToolResult result = (McpSchema.CallToolResult) mcpService.executeToolCall(TOOL_NAME, args);
        List<McpSchema.Content> contentList = result.content();
        McpSchema.Content content = contentList.get(0);
        if (content instanceof McpSchema.TextContent textContent) {
            String text = textContent.text();
            // text to map
            Object parse = JSON.parse(text);
            if (parse instanceof JSONObject jsonObject) {
                // to Map
                Map map = jsonObject.toJavaObject(Map.class);
                data.put("predict_chart", map);
            }
        }

        priceAndSales = e_data.getJSONArray("financial_price_and_sales");
        args.put("prices", priceAndSales.stream().map(item -> ((JSONArray) item).get(0)).toList());
        args.put("quantities", priceAndSales.stream().map(item -> ((JSONArray) item).get(1)).toList());
        result = (McpSchema.CallToolResult) mcpService.executeToolCall(TOOL_NAME, args);
        contentList = result.content();
        content = contentList.get(0);
        if (content instanceof McpSchema.TextContent textContent) {
            String text = textContent.text();
            // text to map
            Object parse = JSON.parse(text);
            if (parse instanceof JSONObject jsonObject) {
                // to Map
                Map map = jsonObject.toJavaObject(Map.class);
                data.put("financial_chart", map);
            }
        }
        // 经济
        priceAndSales = e_data.getJSONArray("economic_price_and_sales");
        args.put("prices", priceAndSales.stream().map(item -> ((JSONArray) item).get(0)).toList());
        args.put("quantities", priceAndSales.stream().map(item -> ((JSONArray) item).get(1)).toList());
        result = (McpSchema.CallToolResult) mcpService.executeToolCall(TOOL_NAME, args);
        contentList = result.content();
        content = contentList.get(0);
        if (content instanceof McpSchema.TextContent textContent) {
            String text = textContent.text();
            // text to map
            Object parse = JSON.parse(text);
            if (parse instanceof JSONObject jsonObject) {
                // to Map
                Map map = jsonObject.toJavaObject(Map.class);
                data.put("economic_chart", map);
            }
        }
        return data;
    }

}
