package ceibs.price.utils.mcpTool.tools.valuePricing;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.Case;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.CaseService;
import ceibs.price.service.CopilotApiService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: RCC
 * @CreateTime:2025-06-16 17:51
 * @Description 头脑风暴发散技能
 */
@ToolHandler("brainstorming_diverges")
@Component
public class BrainstormingDivergesExecutor implements ToolExecutor {

    @Autowired
    CopilotApiService copilotApiService;
    @Autowired
    PriceSessionService priceSessionService;
    @Autowired
    CaseService caseService;

    private static final String TOOL_NAME = "brainstorming_diverges";


    /**
     * 头脑风暴发散
     * 分析出当前 @的角色，同时找到角色的详细定义
     *
     * @param sessionId    注意，该技能是被另外个智能体调用。所以sessionId 不是主智能体对话的sessionId
     * @param args
     * @param requestToken
     * @param opt
     * @param autoTask
     * @return
     * @throws Exception
     */
    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) throws Exception {
        Case caseBo = caseService.findFirstByActiveIsTrue();
        JSONArray chatHistory = JSON.parseArray(args.get("chatHistory").toString());
        // 提取人物描述
        String userInfo = getUserInfo(args);

        // 总结聊天记录主题
        String chatHistoryTopic = copilotApiService.simplifyBrainstormingTopic(chatHistory, requestToken);
        String caseText = caseBo.getText();
        String userInput = getUserInput(chatHistory);
        // 根据父级会话获取竞争分析报告
        String jzfx = getJZFX(args);
        List<Map<String, Object>> datas = new ArrayList<>();
        Map data = new HashMap();
        data.put("userInfo", userInfo);
        data.put("case", caseText);
        data.put("jzfx", jzfx);
        data.put("userInput", userInput);
        data.put("chatHistoryTopic", chatHistoryTopic);
        datas.add(data);
        return new ToolResponse(true, datas, null, null);
    }


    private String getUserInfo(Map<String, Object> args) {
        try {
            String postName = args.get("postName").toString();
            String parentSessionId = args.get("parentSessionId").toString();
            PriceSessionView parentView = priceSessionService.getMaxPriceSessionView(parentSessionId, "头脑风暴");
            JSONObject dataJson = JSONObject.parseObject(parentView.getData());
            JSONArray users = dataJson.getJSONArray("users");
            for (Object user : users) {
                JSONObject userJson = (JSONObject) user;
                JSONObject userType = userJson.getJSONObject("userType");
                if (postName.equals(userType.get("name"))) {
                    return userJson.toJSONString();
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("无法获取虚拟角色信息，请重试");
        }
    }

    private String getJZFX(Map<String, Object> args) {
        try {
            String parentSessionId = args.get("parentSessionId").toString();
            PriceSessionView parentView = priceSessionService.getMaxPriceSessionView(parentSessionId, "竞争分析");
            return parentView.getData();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("无法获取竞争分析报告，请确认是否已经进行过竞争分析。");
        }
    }

    private String getUserInput(JSONArray chatHistory) {
        // 取最后一条聊天记录
        JSONObject lastChat = chatHistory.getJSONObject(chatHistory.size() - 1);
        String text = lastChat.getString("text");
        // 用户输入部分 如果是以  "## ABC ## 123123"，则去掉## ABC ## 部分，只需要123123。
        String userInput = text.replaceAll("^##.*?##\\s*", "");
        return userInput;
    }


}
