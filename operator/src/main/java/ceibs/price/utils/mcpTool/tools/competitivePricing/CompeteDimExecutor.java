package ceibs.price.utils.mcpTool.tools.competitivePricing;


import ceibs.price.bean.User;
import ceibs.price.bean.bo.Case;
import ceibs.price.bean.bo.CaseFile;
import ceibs.price.bean.bo.PriceSession;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.CaseService;
import ceibs.price.service.CopilotApiService;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 竞争维度
 *
 * @author: 2022/10/27 11:05
 * @description: 和AI进行竞争维度提取交互
 */
@ToolHandler("competition_dimension_definition")
@Component
public class CompeteDimExecutor implements ToolExecutor {

    @Autowired
    private PriceSessionService priceSessionService;
    @Autowired
    CopilotApiService copilotApiService;
    @Autowired
    private CaseService caseService;
    private static final String TOOL_NAME = "competition_dimension_definition";

    @Value("${base.view.url}")
    private String baseViewUrl;

    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) throws Exception {
        // 获取 历史聊天记录
        JSONArray chatHistory = JSON.parseArray(args.get("chatHistory").toString());
        // 获取最后一条用户输入
        JSONObject lastChat = chatHistory.getJSONObject(chatHistory.size() - 1);
        Case caseBo= caseService.findFirstByActiveIsTrue();
        // 通过AI分析
        String competeDim = copilotApiService.identifyCompeteDim(lastChat.getString("text"), caseBo.getText(), requestToken);
        JSONObject competeDimJson = JSON.parseObject(competeDim);
        Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
        String viewId = null;
        if (count == 0) {
            List<PriceSessionView> views = new ArrayList<>();
            // 只返回一次 案例分析报告
            viewId = UUID.randomUUID().toString();
            PriceSessionView view = new PriceSessionView();
            view.setId(viewId);
            view.setSessionId(sessionId);
            view.setViewName("行业竞争维度定义");
            view.setViewDesc("行业竞争维度定义");
            view.setViewUrl(UrlUtil.getUrl("/hub-price/competitive-dimensions"));
            view.setCreatedAt(new Date());
            view.setIndex(0);
            //priceSessionView2.setSelected();
            view.setDelMark(false);
            view.setData(competeDim);
            view.setStep("1-1");
            views.add(view);
            // 保存本次产生的相关数据
            PriceSession priceSession = priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, autoTask, opt);
        } else {
            // 不是第一次，则应该更新数据
            PriceSessionView priceSessionView = priceSessionService.getMaxPriceSessionView(sessionId, "行业竞争维度定义");
            viewId = priceSessionView.getId();
            JSONObject oldData = JSON.parseObject(priceSessionView.getData());
            JSONArray oldFields = oldData.getJSONArray("fields");
            if(null==oldFields){
                oldFields=new JSONArray();
            }
            // 如果 competeDimJson 有fileds，则在原基础上追加
            if (competeDimJson.containsKey("fields")) {
                for (Object field : competeDimJson.getJSONArray("fields")) {
                    oldFields.add(field);
                }
                oldData.put("fields", oldFields);
                priceSessionView.setData(oldData.toString());
                priceSessionService.updatePriceSessionViewByBean(priceSessionView);
            }

        }

        return new ToolResponse(false, null, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, viewId), competeDimJson.getString("error"));
    }
}
