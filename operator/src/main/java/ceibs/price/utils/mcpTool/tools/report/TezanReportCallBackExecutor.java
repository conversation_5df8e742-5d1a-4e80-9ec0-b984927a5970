package ceibs.price.utils.mcpTool.tools.report;

import ceibs.price.bean.User;
import ceibs.price.bean.bo.PriceSessionView;
import ceibs.price.bean.graphql.ToolResponse;
import ceibs.price.service.PriceSessionService;
import ceibs.price.utils.UrlUtil;
import ceibs.price.utils.mcpTool.ToolExecutor;
import ceibs.price.utils.mcpTool.ToolHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 演示技能
 * 特赞报告回放
 */
@ToolHandler("tezan_report_call_back")
@Component
public class TezanReportCallBackExecutor implements ToolExecutor {

    @Autowired
    PriceSessionService priceSessionService;

    @Value("${base.view.url}")
    private String baseViewUrl;

    @Value("${copilot.brainstorming}")
    private String brainstormingBots;


    private static final String TOOL_NAME = "tezan_report_call_back";



    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) throws Exception {
        Integer count = priceSessionService.countToolBySession(sessionId, TOOL_NAME);
        String viewId = UUID.randomUUID().toString();
        PriceSessionView priceSessionView = new PriceSessionView();
        priceSessionView.setId(viewId);
        priceSessionView.setSessionId(sessionId);
        priceSessionView.setViewName("特赞报告回放");
        priceSessionView.setViewDesc("特赞报告回放");
        priceSessionView.setViewUrl(UrlUtil.getUrl("hub-price/tezanReportCallBack"));
        priceSessionView.setCreatedAt(new Date());
        priceSessionView.setIndex(count);
        priceSessionView.setData(null);
        priceSessionView.setDelMark(false);
        priceSessionView.setStep("3-1");
        List<PriceSessionView> views = new ArrayList<>();
        views.add(priceSessionView);
        priceSessionService.savePriceSession(sessionId,classId, TOOL_NAME, views, autoTask,opt);
        return new ToolResponse(false, null, UrlUtil.getUrl(baseViewUrl).formatted(sessionId, priceSessionView.getId()), null);
    }

/*    @Override
    public Object execute(String sessionId,String classId, Map<String, Object> args, String requestToken, User opt, Boolean autoTask) throws Exception {

        try {
            // 获取 历史聊天记录
            JSONArray chatHistory =JSON.parseArray(args.get("chatHistory").toString());
            // 取最后一条聊天记录
            JSONObject lastChat = chatHistory.getJSONObject(chatHistory.size() - 1);
            String qestion = lastChat.getString("text").replaceFirst("头脑风暴", "");
            String simplifyHistory = copilotApiService.simplifyBrainstorming( chatHistory, requestToken);
            JSONObject jsonObject = copilotApiService.generateBrainstorming(qestion, simplifyHistory, requestToken);
            List<Map<String, Object>> datas = new ArrayList<>();
            Map data = new HashMap();
            data.put("text", jsonObject.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("text"));
            datas.add(data);
            return new ToolResponse(false, datas, null, null);
        } catch (Exception e) {
            e.printStackTrace();
            return new ToolResponse(true, null, null, "头脑风暴失败，请重试");

        }
    }*/


}
