package ceibs.price.utils.mcpTool;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DynamicToolExecutorFactory {

    private final Map<String, ToolExecutor> executors = new HashMap<>();

    public DynamicToolExecutorFactory(List<ToolExecutor> allExecutors) {
        for (ToolExecutor executor : allExecutors) {
            Class<?> clazz = executor.getClass();
            if (clazz.isAnnotationPresent(ToolHandler.class)) {
                String name = clazz.getAnnotation(ToolHandler.class).value();
                executors.put(name, executor);
            }
        }
    }

    public ToolExecutor getExecutor(String toolName) {
        ToolExecutor executor = executors.get(toolName);
        if (executor == null) {
            throw new IllegalArgumentException("No executor found for tool: " + toolName);
        }
        return executor;
    }
}
