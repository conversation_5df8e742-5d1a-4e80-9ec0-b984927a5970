package ceibs.price.utils;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;

import java.util.Map;

@Slf4j
public class YamlUtils {

    private static final ObjectMapper yamlMapper = new ObjectMapper(new YAMLFactory());

    public static <T> T parseYaml(String yaml, Class<T> clazz) throws JsonProcessingException {
        if (yaml == null || yaml.trim().isEmpty()) {
            return null;
        }
        // 配置ObjectMapper忽略未知属性
        yamlMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 执行转换
        return yamlMapper.readValue(yaml, clazz);

    }

    public static String toYaml(Object obj) {
        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        Yaml yaml = new Yaml(options);
        return yaml.dump(obj);
    }

    public static JSONObject toJson(String yaml) throws JsonProcessingException {
        Yaml yamlParser = new Yaml();
        // 将 YAML 字符串解析为 Java 对象
        Object obj = yamlParser.load(yaml);
        ObjectMapper mapper = new ObjectMapper();
        return JSONObject.parseObject(mapper.writeValueAsString(obj));
    }

    public static String fromJson(String json) {
        Yaml yaml = new Yaml();
        // 将JSON字符串转化为map
        Map<String, Object> map = yaml.load(json);
        // 转换成 YAML 字符串
        String yamlStr = yaml.dumpAsMap(map);
        return yamlStr;
    }


}
