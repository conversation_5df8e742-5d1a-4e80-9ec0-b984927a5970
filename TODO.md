# 自动运行
- BUG优化 

# 最终报告 
- 拼接报告


# 头脑风暴
  - 黑板功能 

  - 收敛 前置步骤，引导用户 选择收敛方向 
    - 有哪些收敛方向？

  - 角色发散 需要增强 、测试 【完成】
  - 有过对话再次进入头脑风暴，应该直接加载上次聊天记录 【完成】

# 特赞报告
- 2C提示词主题 只运行一个

- 增加提示词预览页面 【有选择项目，1：市场规模 国内、海外、都要 2：。。。】

- 数据过于乐观 （尝试提示词 OR 财务报告数据调整 OR 直接对特赞报告数据打折 ） 
  - 增加 市场份额 。 总市场规模 * 我公司市场份额 * 价格点支付意愿= 价格点预测销量

# 财务报表
- 增加上传报表选项 、查默认
- 增加AI互动，能支持解析财务报表信息（多轮对话，数据基于财务报表 叠托返回的json数据）

# 管理端功能
- 案例管理 【 测试其他案例情况 、知识库如何切换？】
- 查看目前上课情况

# 成本定价
- 多增加影响因子（？？具体有哪些？ 如何添加？ 如何影响？）

# 收敛方向
1： 收敛内容 （聊天记录 or 黑板高亮）
2： 收敛维度 ： 例如 内部：[时间成本、技术成本]  外部：[市场因素、市场需求]   竞争：[差异化（和竞争对手差异化要高)、优劣势]
3： 基于 上述两点，收敛出 本公司可行性高的 主题方案。 

4：被废弃主题（因不满足收敛维度而被废弃的方案，并给出原因）


## 
排课
课有组、组有人
人触发技能的时候

#### 先判断当前会话是否已经关联过课 
- 关联过则直接沿用。
- 没关联过，看当前是否有课 
  - 有课，则看是否属于课的成员
  - 无可，跳过
#### 创建课程的时候 需要验证交叉的两堂课是否有相同的成员。



### 20250825


# 需求

## 学生上传案例 （暂不考虑）
## 历史记录导出  （增加聊天记录导出，暂不考虑）
## 所有都加黑板功能 （暂不考虑）
## 新技能 （待鲁老师提供 营销计算需求）

# 优化
## 头脑风暴小屏适配
## 岗位、标签 更加通用
## 特赞提示词  （鲁老师去沟通）


# bug

## 聊天输入框 按 删除键就一定会直接删掉技能
## 超大案例上传 (超时增加提示)
## 多文件成为案例
## 503这个页面 【FIX】
## 宠物行业收敛超时出现酒店案例bug     【FIX】



